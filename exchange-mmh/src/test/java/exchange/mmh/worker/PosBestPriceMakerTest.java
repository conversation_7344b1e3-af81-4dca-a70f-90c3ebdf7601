package exchange.mmh.worker;

import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.Symbol;
import exchange.common.util.DateUtil;
import exchange.mmh.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @author: wen.y
 * @date: 2025/2/14
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class PosBestPriceMakerTest {

	@Resource
	private PosBestPriceMaker posBestPriceMaker;

	@Test
	public void executeTest() throws Exception {
    	Symbol symbol = new Symbol();
		symbol.setId(9L);
		symbol.setTradeType(TradeType.POS);
		symbol.setCurrencyPair(CurrencyPair.ADA_JPY);
		symbol.setCreatedAt(DateUtil.yyyyMMddParse("2024-08-20"));
		symbol.setUpdatedAt(DateUtil.yyyyMMddParse("2024-08-20"));
		posBestPriceMaker.execute(symbol, null);
	}
}
