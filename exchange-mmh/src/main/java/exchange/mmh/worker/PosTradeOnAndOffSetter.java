package exchange.mmh.worker;

import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.service.CurrencyPairConfigService;
import exchange.mmh.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class PosTradeOnAndOffSetter extends Worker {

  private final CurrencyPairConfigService currencyPairConfigService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    String currencyPair = (String) params.get("currencyPair");
    String operation = (String) params.get("operation");
    if (StringUtils.isEmpty(currencyPair)) {
      log.error("PosTradeOnAndOffSetterLog : currencyPair of parameter can not be null");
      return;
    } else if (StringUtils.isEmpty(operation)) {
      log.error("PosTradeOnAndOffSetterLog : operation of parameter can not be null");
      return;
    }
    
    currencyPair = currencyPair.toUpperCase();
    log.info("PosTradeOnAndOffSetter start, currencyPair: {}", currencyPair);

    List<CurrencyPairConfig> currencyPairConfigs = currencyPairConfigService.findAllByCondition(TradeType.POS, CurrencyPair.valueOfName(currencyPair));
    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      log.error("PosTradeOnAndOffSetterLog : currencyPair {} does not exist", currencyPair);
      return;
    }
    
//    String holidays = (String) params.get("holiday");
//    if(StringUtils.isNotEmpty(holidays)) {
//      Date today = new Date();
//      String todayStr = FormatUtil.formatJst(today, FormatPattern.YYYYMMDD);
//      String[] holidayList = holidays.split(",");
//      if (Arrays.asList(holidayList).contains(todayStr)) {
//        log.info("PosTradeOnAndOffSetterLog : 今日{}は祝日です", todayStr);
//        return;
//      }
//    }
    
    if ("ON".equals(operation)) {
      CurrencyPairConfig currencyPairConfig = currencyPairConfigs.get(0);
      currencyPairConfig.setTradable(true);
      currencyPairConfigService.save(currencyPairConfig);
      log.info("PosTradeOnAndOffSetterLog, 通貨ペア： {}を取引可能に設定しました", currencyPair);
    } else {
      CurrencyPairConfig currencyPairConfig = currencyPairConfigs.get(0);
      currencyPairConfig.setTradable(false);
      currencyPairConfigService.save(currencyPairConfig);
      log.info("PosTradeOnAndOffSetterLog, 通貨ペア： {}を取引不可に設定しました", currencyPair);
    }
    
    log.info("PosTradeOnAndOffSetter finish");
  }
}
