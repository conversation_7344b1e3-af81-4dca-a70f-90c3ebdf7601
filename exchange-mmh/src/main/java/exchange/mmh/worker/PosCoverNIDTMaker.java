package exchange.mmh.worker;

import exchange.common.entity.Symbol;
import exchange.pos.service.PosNidtCoverOrderService;
import exchange.mmh.component.Worker;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PosCoverNIDTMaker extends Worker {

  private  final PosNidtCoverOrderService nidtCoverOrderService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("================nidtCoverOrderService start=============================");
    nidtCoverOrderService.sendNIDTOrder();
    log.info("================nidtCoverOrderService end  =============================");
  }
}
