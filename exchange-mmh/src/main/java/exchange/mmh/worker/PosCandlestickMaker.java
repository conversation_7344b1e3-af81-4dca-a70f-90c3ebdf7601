package exchange.mmh.worker;

import exchange.common.constant.CandlestickType;
import exchange.common.entity.Symbol;
import exchange.pos.service.PosCandlestickService;
import exchange.mmh.component.Worker;
import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PosCandlestickMaker extends Worker {

  private final PosCandlestickService posCandlestickService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) {
    Date date = new Date();

    for (CandlestickType candlestickType : CandlestickType.values()) {
      posCandlestickService.make(symbol, candlestickType, date);
    }
  }
}
