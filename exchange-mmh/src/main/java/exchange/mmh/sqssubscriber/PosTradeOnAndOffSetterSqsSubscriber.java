package exchange.mmh.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.mmh.component.SqsSubscriber;
import exchange.mmh.worker.PosTradeOnAndOffSetter;

@Component
public class PosTradeOnAndOffSetterSqsSubscriber extends SqsSubscriber<PosTradeOnAndOffSetter> {

  @Override
  public Class<PosTradeOnAndOffSetter> getWorkerClass() {
    return PosTradeOnAndOffSetter.class;
  }
}
