async:
  core-pool-size: 60
#  queue-capacity: 60
#  max-pool-size: 60
aws:
  credentials:
    access-key:
    secret-key:
    salt:
  ses:
    host: email-smtp.ap-northeast-1.amazonaws.com
    port: 587
    username:
    password:
  s3:
    kyc-bucket:
      name:
cloud:
  aws:
    credentials:
      access-key:
      secret-key:
      use-default-aws-credentials-chain: false
    region:
      auto: false
      static: ap-northeast-1
    stack:
      auto: false
common:
  log:
    outputRequestDetails: true
    console:
      appender: CONSOLE_DEFAULT
management:
  server:
    port: 8082
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,manual"
      probes:
        enabled: true
  endpoints:
    metrics:
      enabled: false
    prometheus:
      enabled: false
    web:
      base-path: /actuator
      exposure:
        include: "*"
  metrics:
    export:
      cloudwatch:
        batchSize: 20
        enabled: false
        namespace: exchange-worker
        step: 1m
      prometheus:
        enabled: false
    web:
      server:
        request:
          autotime:
            # https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready-metrics-spring-mvc
            enabled: true
          metric-name: http.server.requests
server:
  port: 8080
  shutdown: graceful
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: localhost
      port: 6379
  datasource:
    master:
      driver-class-name: org.mariadb.jdbc.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.MySQL5Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.pos.entity
      url: ***********************************************************************
      username: editor
      password: Exchange
      minimum-idle: 10
      maximum-pool-size: 66
    historical:
      driver-class-name: org.postgresql.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.PostgreSQL82Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.pos.entity
      url: *****************************************
      username: editor
      password: Exchange
      minimum-idle: 10
      maximum-pool-size: 66
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  main:
    banner-mode: off

exchange-pos:
  best-price:
    amber:
      api-host: https://be-alpha.whalefin.com
      api-path: /api/v2/trade/rfq
      access-key: c7862ecc4e65dace002ab0fc3f55c82a
      access-secret: 24d0f50a26c74016f42a4319d377c06a
    coinbook:
      api-host: http://exchange-api-service.default.svc.cluster.local:8080
      api-url: /api/v1/ticker
  base-trade:
    amber:
      api-host: https://be-alpha.whalefin.com
      api-path: /api/v2/trade/orders/spot
      access-key: c7862ecc4e65dace002ab0fc3f55c82a
      access-secret: 24d0f50a26c74016f42a4319d377c06a
    coinbook:
      api-host: http://exchange-api-service.default.svc.cluster.local:8080
      api-nidt-order: /api/v1/spot/order
      api-key-buy: 000000000000000000000000000000007f5a209a51725189245b19ba59bc0463
      secret-buy: c56a8155a192ff558cfcdc56cd7b0a698b32f795e6dca080574b4c352e2b530c
      api-key-sell: 00000000000000000000000000000000e0efcdf4a12a1ad8cc20254e05228a57
      secret-sell: 7eb252cb69e1eb97d2cae58b8d199a7569f7e6c6b8a1b8493a08bd5ea54b0a84

exchange-websocket:
  redis-pubsub-cache:
    enabled: true # true: cache the message to avoid duplicate sending
    expire-in-minutes: 5 # cache will be expired in the specified period, the message will be sent even duplicate
chainalysis:
  token: f0560979c09da1c011a28fa50820ebbcf12eb6cc048ea07f57b2613a4b829df9
  base-endpoint: https://api.chainalysis.com
  alert-level-limit: LOW

dowjones:
  base-auth-url: https://accounts.dowjones.com
  base-api-Url: https://api.dowjones.com
  username:
  password:
  client-id:
  search-type: PRECISE
  content-set: ["Watchlist"]
  filter-content-category: ["WL"]
  count: 300