cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
exchange-api:
  host: http://api.dev.cxr-inc.com
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: api.dev.cxr-inc.com
    environment: dev
  datasource:
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false

dowjones:
  device: "coinbook-dev1"
  username: <EMAIL>
  password: F98dgFjXVJuqvVNk
  client-id: Z1lpD6emDa3G2o9CVtd3tUnpuUKw6VciLvJ9XQjn