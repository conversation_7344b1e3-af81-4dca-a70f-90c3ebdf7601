async:
  core-pool-size: 60
#  queue-capacity: 60
#  max-pool-size: 60
aws:
  credentials:
    access-key:
    secret-key:
    salt:
  ses:
    host: email-smtp.ap-northeast-1.amazonaws.com
    port: 587
    username:
    password:
cloud:
  aws:
    credentials:
      access-key:
      secret-key:
      use-default-aws-credentials-chain: false
    region:
      auto: false
      static: ap-northeast-1
    stack:
      auto: false
common:
  log:
    outputRequestDetails: true
    console:
      appender: CONSOLE_DEFAULT
exchange-api:
  host: http://localhost:8080
management:
  server:
    port: 8082
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,manual"
      probes:
        enabled: true
  endpoints:
    metrics:
      enabled: false
    prometheus:
      enabled: false
    web:
      base-path: /actuator
      exposure:
        include: "*"
  metrics:
    export:
      cloudwatch:
        batchSize: 20
        enabled: false
        namespace: exchange-api
        step: 1m
      prometheus:
        enabled: false
    web:
      server:
        request:
          autotime:
            # https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready-metrics-spring-mvc
            enabled: true
          metric-name: http.server.requests
server:
  port: 8080
  servlet:
    session:
      timeout: 1d
      cookie:
        max-age: 7d
        name: JSESSIONID
        path: /
        secure: true
  shutdown: graceful
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: localhost
      port: 6379
  datasource:
    master:
      driver-class-name: org.mariadb.jdbc.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.MySQL5Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.pos.entity
      url: ***********************************************************************
      username: exchange
      password: Exchange123
      minimum-idle: 10
      maximum-pool-size: 66
    historical:
      driver-class-name: org.postgresql.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.PostgreSQL82Dialect
      packages-to-scan: exchange.admin.entity,exchange.common.entity,exchange.spot.entity,exchange.pos.entity
      url: *****************************************
      username: exchange
      password: Exchange123
      minimum-idle: 10
      maximum-pool-size: 66
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  main:
    banner-mode: off
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant-path-matcher
  session:
    store-type: none
  web:
    resources:
      add-mappings: false
swagger:
  enabled: true

coin:
  cus:
    host: https://service-stg.coinbook.co.jp
    host-external: https://service-stg.coinbook.co.jp

chainalysis:
  token: f0560979c09da1c011a28fa50820ebbcf12eb6cc048ea07f57b2613a4b829df9
  base-endpoint: https://api.chainalysis.com
  alert-level-limit: LOW

dowjones:
  base-auth-url: https://accounts.dowjones.com
  base-api-Url: https://api.dowjones.com
  username:
  password:
  client-id:
  search-type: PRECISE
  content-set: ["Watchlist"]
  filter-content-category: ["WL"]
  count: 300