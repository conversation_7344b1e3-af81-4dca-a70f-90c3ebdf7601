# REST APIs

## 仕様
* クライアントからアクセスされるAPI群です。

### リクエストの仕様について
* 適切なHTTPメソッドを設定して下さい。
* リクエストパラメーターについて
  * GET, DELETEはクエリパラメーターにて指定して下さい。
  * POST, PUTはリクエストボディにて指定して下さい。

### レスポンスの仕様について
* 通信の成否は全てレスポンスのHTTPステータスコードで判別します。

### エラーコードについて
* https://github.com/cxrinc/cb-exchange-common/blob/master/constant/ErrorCode.java
* エラーコードについては現在追加＆体系化中で、スプリント4以降で一気に仕上げる予定です。

## Public APIs

### healthcheck for k8s
```
GET /healthcheck
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
OK
```

### healthcheck for external
```
GET /app/healthcheck
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
OK
```

### candlestick
```
GET /app/v1/candlestick
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
candlestickType | string | YES | ローソク足の種類。ISO8601に準拠しています。 ex) 1分足: PT1M, 1時間足: PT1H, 日足: P1D, 週足: P1W, 月足: P1M
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
#### response
```json
{
  "symbolId": 1,
  "candlesticks": [
    {
      "open": 3633849,
      "high": 3878414,
      "low": 3615752,
      "close": 3802653,
      "volume": 94.71371641,
      "time": *************
    },
    ...
  ],
  "timestamp": *************
}
```

### country
```
GET /app/v1/country
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "isoAlpha2Code": "JP",
    "callingCode": "81",
    "areaCode": "",
    "label": "Japan",
    "jpyLabel": "日本"
  },
  ...
]
```

### currency
```
GET /app/v1/currency
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "id": 1,
    "currency": "JPY",
    "depositFee": 1.00000000000000000000,
    "withdrawalFee": 2.00000000000000000000,
    "transactionFee": 3.00000000000000000000,
    "maxOrderAmountPerDay": 300000000.0000000000,
    "depositable": true,
    "withdrawable": true,
    "enabled": true,
    "iconUrl": "/images/JPY.svg",
    "createdAt": 1599697860996,
    "updatedAt": 1599697860996,
    "precision": 0
  },
  {
    "id": 2,
    "currency": "BTC",
    "depositFee": 1.00000000000000000000,
    "withdrawalFee": 1.00000000000000000000,
    "transactionFee": 1.00000000000000000000,
    "maxOrderAmountPerDay": 100.0000000000,
    "depositable": true,
    "withdrawable": true,
    "enabled": true,
    "iconUrl": "/images/JPY.svg",
    "createdAt": 1599697860996,
    "updatedAt": 1599697860996,
    "precision": 8
  },
  ...
]
```

### currency pair
```
GET /app/v1/currency-pair
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
tradeType | string | NO | 現状 `SPOT` のみ
currencyPair | string | NO | 通貨ペア。BTC_JPY, など
#### response
```json
[
  {
    "id": 3,
    "createdAt": 1628836443701,
    "updatedAt": 1629165125375,
    "tradeType": "SPOT",
    "currencyPair": "ETH_JPY",
    "baseIconUrl": "/images/ETH.svg",
    "quoteIconUrl": "/images/JPY.svg",
    "minOrderAmount": 0.00100000000000000000,
    "maxOrderAmount": 300.00000000000000000000,
    "limitPriceRangeRate": 0.50000000000000000000,
    "marketPriceRangeRate": 0.10000000000000000000,
    "marketAmountRangeRate": 0.90000000000000000000,
    "makerTradeFeePercent": -0.10000000000000000000,
    "takerTradeFeePercent": 0.10000000000000000000,
    "tradable": true,
    "enabled": true,
    "circuitBreakUpdatedAt": 1623566043701,
    "circuitBreakPercent": null,
    "circuitBreakCheckTimespan": null,
    "circuitBreakStopTimespan": null,
    "simpleMarketSpreadPercent": 0E-20,
    "simpleMarketFeePercent": 0E-20,
    "spikePercent": 0E-20,
    "spikeMinutes": 0,
    "spikeCount": 0
  },
  ...
]
```

### orderbook
```
GET /app/v1/orderbook
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
#### response
```json
{
  "symbolId": 1,
  "asks": [
    {
      "price": 3633849,
      "amount": 0.1
    },
    ...
  ],
  "bids": [
    {
      "price": 3633749,
      "amount": 0.1
    },
    ...
  ],
  "timestamp": *************
}
```

### symbol
```
GET /app/v1/symbol
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "id": 123,
    "tradeType": "SPOT",
    "currencyPair": "BTC_JPY",
    "startAt": null,
    "endAt": null,
    "baseCurrency": "BTC",
    "quoteCurrency": "JPY",
    "baseIconUrl": "/images/BTC.svg",
    "quoteIconUrl": "/images/JPY.svg",
    "basePrecision": 8,
    "quotePrecision": 0,
    "makerTradeFeePercent": -0.1,
    "takerTradeFeePercent": 0.1,
    "tradable": true,
    "enabled": true
  },
  ...
]
```

### spot order calculate
```
GET /app/v1/spot/order/calculate
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
amount | string | NO | amount
assetAmount | string | NO | assetAmount
#### response
```json
{
  "sellAveragePrice": 3802653,
  "buyAveragePrice": 4002653,
  "sellAssetAmount": 3802653,
  "buyAssetAmount": 4002653,
  "sellAmount": 1,
  "buyAmount": 1.1
}
```

### ticker
```
GET /app/v1/ticker
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
#### response
```json
{
  "symbolId": 1,
  "bestAsk": 3802663,
  "bestBid": 3802643,
  "open": 3633849,
  "high": 3878414,
  "low": 3615752,
  "last": 3802653,
  "volume": 94.71371641,
  "timestamp": *************
}
```

### trades
```
GET /app/v1/trades
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
#### response
```json
{
  "symbolId": 1,
  "trades": [
    {
      "id": 789,
      "orderSide": "BUY",
      "price": 3802653,
      "amount": 0.04,
      "tradedAt": 1611029775000
    },
    ...
  ]
  "timestamp": *************
}
```

## Private APIs

### get api-info
```
GET /app/v1/api-info
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "id": 123,
    "userId": 123,
    "label": "trade",
    "apiKey": "48f1ed7842a65dd05234aab2fdf06185",
    "enabled": true,
    "canGetAssetInfo": true,
    "canGetOrderInfo": true,
    "canCreateNewOrder": true,
    "canDeleteOrder": true,
    "canGetTradeInfo": true,
    "unlimited": false,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### post api-info(for mfa)
```
POST /app/v1/api-info/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
label | string | YES | APIキーに付ける名前
canGetAssetInfo | boolean | YES | アセット情報を取得する場合はtrue
canGetOrderInfo | boolean | YES | 注文情報を取得する場合はtrue
canCreateNewOrder | boolean | YES | 新規注文をする場合はtrue
canDeleteOrder | boolean | YES | 注文取り消しをする場合はtrue
canGetTradeInfo | boolean | YES | 約定情報を取得する場合はtrue
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### post api-info
```
POST /app/v1/api-info
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
label | string | YES | APIキーに付ける名前
canGetAssetInfo | boolean | YES | アセット情報を取得する場合はtrue
canGetOrderInfo | boolean | YES | 注文情報を取得する場合はtrue
canCreateNewOrder | boolean | YES | 新規注文をする場合はtrue
canDeleteOrder | boolean | YES | 注文取り消しをする場合はtrue
canGetTradeInfo | boolean | YES | 約定情報を取得する場合はtrue
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 123,
  "label": "trade",
  "apiKey": "48f1ed7842a65dd05234aab2fdf06185",
  "secret": "a2d3476ff9203ab88397c2aa23bbc3bfa9231c13e0bc82e26b53f6019a9dfce0",
  "enabled": true,
  "canGetAssetInfo": true,
  "canGetOrderInfo": true,
  "canCreateNewOrder": true,
  "canDeleteOrder": true,
  "canGetTradeInfo": true
}
```

### delete api-info(for mfa)
```
DELETE /app/v1/api-info/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | 削除するAPIキーのid
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### delete api-info
```
DELETE /app/v1/api-info
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | 削除するAPIキーのid
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 123,
  "label": "trade",
  "apiKey": "48f1ed7842a65dd05234aab2fdf06185",
  "enabled": true,
  "canGetAssetInfo": true,
  "canGetOrderInfo": true,
  "canCreateNewOrder": true,
  "canDeleteOrder": true,
  "canGetTradeInfo": true,
  "unlimited": false,
  "createdAt": *************,
  "updatedAt": *************
}
```

### asset
```
GET /app/v1/asset
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "userId": 123,
    "currency": "BTC",
    "jpyOnhandAmount": 1000000,
    "jpyLockedAmount": 300000,
    "onhandAmount": "1.00000001",
    "lockedAmount": "0.30000001",
    "unlockedAmount": "0.7",
  },
  ...
]
```

### asset all
```
GET /app/v1/asset/all
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "assetSumAsJpy": 123456,
  "assetDiffAsJpy": 123400,
  "assetDiffPercent": 123400,
  "assets": [
    {
      "userId": 123,
      "currency": "BTC",
      "jpyOnhandAmount": 1000000,
      "jpyLockedAmount": 300000,
      "onhandAmount": "1.00000001",
      "lockedAmount": "0.30000001",
      "unlockedAmount": "0.7",
    },
    ...
  ]
}
```

### asset summary
```
GET /app/v1/asset-summary
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | No | 対象通貨
dateFrom | long | No | 開始日(基準時刻は00:00(JST))
dateTo | long | No | 終了日(基準時刻は00:00(JST))

#### response
```json
[
  {
    "id": 61,
    "createdAt": 1614091875709,
    "updatedAt": 1614091875709,
    "userId": 10,
    "targetAt": 1609340400000,
    "currency": "JPY",
    "currentAmount": "100000.0",
    "jpyConversion": 1.0,
    "depositAmount": "0.1",
    "depositAmountJpy": 0.1,
    "depositFee": "0.1",
    "depositFeeJpy": 0.1,
    "withdrawalAmount": "0.1",
    "withdrawalAmountJpy": 0.1,
    "withdrawalFee": "0.1",
    "withdrawalFeeJpy": 0.1,
    "transactionFee": "0.1",
    "transactionFeeJpy": 0.1,
    "spotTradeBuyAmount": 0.1,
    "spotTradeBuyAmountJpy": 0.1,
    "spotTradeSellAmount": 0.1,
    "spotTradeSellAmountJpy": 0.1,
    "spotTradeFee": 0.1,
    "spotTradeFeeJpy": 0.1
  },
  ...
]
```

### bank
```
GET /app/v1/bank/bank-name
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response

```json
[
  {
    "bankCode": 5,
    "bankName": "三菱ＵＦＪ"
  },
    ...
]
```

### bank branch
```
GET /app/v1/bank/branch-name
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
bankCode | long | Yes | 銀行コード
#### response

```json
[
  {
    "bankId": 3,
    "branchCode": 1,
    "branchName": "東京営業部"
  },
    ...
]
```

### bank account
```
GET /app/v1/bank-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response

```json
[
  {
    "id": 2,
    "createdAt": *************,
    "updatedAt": *************,
    "userId": 2,
    "bankId": 2,
    "accountType": "NORMAL",
    "accountNumber": 1234567,
    "accountName": "テスト口座",
    "user": {
    },
    "bank": {
    }
  },
  ...
]
```

### post bank-account(for mfa)
```
POST /app/v1/bank-account/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
bankId | long | YES | 銀行ID
accountType | string | YES | NORMAL: 普通, CURRENT: 当座, 等
accountNumber | int | YES | 口座番号
accountName | string | YES | 口座名
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### post bank-account
```
POST /app/v1/bank-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
bankId | long | YES | 銀行ID
accountType | string | YES | NORMAL: 普通, CURRENT: 当座, 等
accountNumber | int | YES | 口座番号
accountName | string | YES | 口座名
mfaCode | string | YES | mfaコード
#### response

```json
[
  {
    "id": 2,
    "createdAt": *************,
    "updatedAt": *************,
    "userId": 2,
    "bankId": 2,
    "accountType": "NORMAL",
    "accountNumber": 1234567,
    "accountName": "テスト口座",
    "user": {
    },
    "bank": {
    }
  }
]
```

### delete bank-account(for mfa)
```
DELETE /app/v1/bank-account/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | ID
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### delete bank account
```
DELETE /app/v1/bank-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | ID
mfaCode | string | YES | mfaコード
#### response

```json
[
  {
    "id": 2,
    "createdAt": *************,
    "updatedAt": *************,
    "userId": 2,
    "bankId": 2,
    "accountType": "NORMAL",
    "accountNumber": 1234567,
    "accountName": "テスト口座",
    "user": {
    },
    "bank": {
    }
  }
]
```

### contact
```
POST /app/v1/contact
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
name | string | YES | ユーザー名
email | string | YES | メールアドレス
subject | string | YES | 件名
content | string | YES | 内容
recaptchaToken | string | YES | reCAPTCHAトークン
#### response
```json
```

### crypto history
```
GET /app/v1/crypto-history
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
historyType | string | No | DEPOSIT: 入金, WITHDRAWAL: 出金, 指定なし: 入出金
currency | string | NO | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
depositStatus | string | NO | FOUND, ASSIGNED, CONFIRMED, DONE
withdrawalStatus | string | NO | APPROVING, BROADCASTED, FINISHING, DONE, REJECTED, CANCELED
address | string | NO | アドレス文字
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30

#### response

```json
{
  "number": 0,
  "size": 30,
  "totalElements": 123,
  "totalPages": 4,
  "content": [
    {
      "id": 2,
      "currency": "JPY",
      "historyType": "DEPOSIT",
      "accountId": 2,
      "amount": ********.00000000000000000000,
      "fee": 0.12300000000000000000,
      "transactionId": 2,
      "destinationTag": null,
      "address": "abcxyz",
      "status": "FOUND",
      "date": "2021-05-21 14:48:50.593"
    },
    {
      "id": 1,
      "currency": "JPY",
      "historyType": "WITHDRAWAL",
      "accountId": 1,
      "amount": 1323123.00000000000000000000,
      "fee": 1231.00000000000000000000,
      "transactionId": 1,
      "destinationTag": null,
      "address": "abcxyz",
      "status": "FOUND",
      "date": "2021-05-21 14:32:00.378"
    }
  ]
}
```

### deposit account
```
GET /app/v1/deposit-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | YES | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "userId": 123,
    "currency": "BTC",
    "address": "**********************************",
    "destinationTag": null,
    "enabled": true,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### get deposit
```
GET /app/v1/deposit
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | NO | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
depositStatus | string | NO | FOUND, CONFIRMED, DONE, TRANSACTION_ERROR, SYSTEM_ERROR
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "userId": 123,
    "currency": "BTC",
    "depositAccountId": 1,
    "depositChannel": "WALLET",
    "depositPurpose": "DEPOSIT",
    "depositType": "TRANS_DEPOSIT",
    "amount": "1.5",
    "fee": "0",
    "jpyConversion": 3802653,
    "address": "**********************************",
    "transactionId": "db4b21f8a376ba4edef05e58356b7ddca38f0bf97a5ba10f6f900c3ca14414eb",
    "depositStatus": "DONE",
    "comment": null,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### get deposit page data
```
GET /app/v1/deposit/page
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | NO | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
depositStatus | string | NO | FOUND, CONFIRMED, DONE, TRANSACTION_ERROR, SYSTEM_ERROR
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "number": 0,
    "size": 30,
    "totalElements": 123,
    "totalPages": 4,
    "content": {
      "id": 123,
      "userId": 123,
      "currency": "BTC",
      "depositAccountId": 1,
      "depositChannel": "WALLET",
      "depositPurpose": "DEPOSIT",
      "depositType": "TRANS_DEPOSIT",
      "amount": "1.5",
      "fee": "0",
      "jpyConversion": 3802653,
      "address": "**********************************",
      "transactionId": "db4b21f8a376ba4edef05e58356b7ddca38f0bf97a5ba10f6f900c3ca14414eb",
      "depositStatus": "DONE",
      "comment": null,
      "createdAt": *************,
      "updatedAt": *************
    }
  },
  ...
]
```

### post fiat withdrawal(for mfa)
```
POST /app/v1/fiat-withdrawal/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
bankAccountId | long | YES | 銀行ID
amount | long | YES | 入金額
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### post fiat withdrawal
```
POST /app/v1/fiat-withdrawal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
bankAccountId | long | YES | 銀行ID
amount | long | YES | 入金額
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 2,
  "bankAccountId": 2,
  "amount": 200000,
  "fee": 0,
  "fiatWithdrawalStatus": "APPROVING",
  "comment": null,
  "createdAt": *************,
  "updatedAt": *************
}
```

### delete fiat withdrawal
```
DELETE /app/v1/fiat-withdrawal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | id of FiatWithdrawal
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 2,
  "bankAccountId": 2,
  "amount": 200000,
  "fee": 0,
  "fiatWithdrawalStatus": "CANCEL",
  "comment": null,
  "createdAt": *************,
  "updatedAt": *************
}
```

### jpy history
```
GET /app/v1/jpy-history
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
historyType | string | No | DEPOSIT: 入金, WITHDRAWAL: 出金, 指定なし: 入出金
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
depositStatus | string | NO | FOUND, ASSIGNED, CONFIRMED, DONE
withdrawalStatus | string | NO | APPROVING, BROADCASTED, FINISHING, DONE, REJECTED, CANCELED
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30

#### response

```json
{
  "number": 0,
  "size": 30,
  "totalElements": 123,
  "totalPages": 4,
  "content": [
    {
      "id": 2,
      "historyType": "DEPOSIT",
      "amount": ********.00000000000000000000,
      "fee": 0.12300000000000000000,
      "status": "FOUND",
      "date": "2021-05-21 14:48:50.593"
    },
    {
      "id": 1,
      "historyType": "WITHDRAWAL",
      "amount": 1323123.00000000000000000000,
      "fee": 1231.00000000000000000000,
      "status": "FOUND",
      "date": "2021-05-21 14:32:00.378"
    }
  ]
}
```

### upload kyc file of personal/corporate
```
POST /app/v1/file/upload/kyc
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
personalIdType1st | string | YES | 一枚目の証明書の種別。 DRIVERS_LICENSE_OR_HISTORY/MYNUMBER_CARD/RESIDENT_CARD_OR_CERTIFICATE/PASSPORT/BASIC_RESIDENCE_REGISTRATION_CARD
personalIdType2nd | string | YES | 二枚目の証明書の種別。   DRIVERS_LICENSE_OR_HISTORY/MYNUMBER_CARD/RESIDENT_CARD_OR_CERTIFICATE/PASSPORT/BASIC_RESIDENCE_REGISTRATION_CARD/HEALTH_INSURANCE/CERTIFICATE_OF_RESIDENCE/SOCIAL_INSURANCE_RECEIPT/TAX_PAYMENT_CERTIFICATE/SEAL_REGISTRATION_CERTIFICATE/UTILITY_BILL_PAYMENT_RECEIPT
corporateIdType | string | YES | 法人向け必要書類。NONE(不要(=個人))/CERTIFICATE_ONLY(履歴事項全部証明書のみ)/CERTIFICATE_AND_AGREEMENT(履歴事項全部証明書と取引担当者届出書兼同意書)
agentIdType1st | string | NO | 法人取引担当者の一枚目の証明書の種別。corporateIdTypeが"CERTIFICATE_AND_AGREEMENT"の場合のみ必要。取り得る値はpersonalIdType1stと同じ
agentIdType2nd | string | NO | 法人取引担当者の二枚目の証明書の種別。corporateIdTypeが"CERTIFICATE_AND_AGREEMENT"の場合のみ必要。取り得る値はpersonalIdType2ndと同じ
personalFiles | MultipartFile | YES | 個人用アップロードファイル(jpg/png/pdf)。必ず以下の順番で追加すること。一枚目表 （+ 裏） + 二枚目表 （+ 裏）+ セルフィー
corporateFiles | MultipartFile | NO | 法人用アップロードファイル(jpg/png/pdf)。必ず以下の順番で追加すること。corporateIdTypeがCERTIFICATE_ONLYなら 履歴事項全部証明書のリスト / CERTIFICATE_AND_AGREEMENT なら先頭に取引担当者届出書兼同意書 + 履歴事項全部証明書のリスト
agentFiles | MultipartFile | NO | 法人取引担当者個人用アップロードファイル(jpg/png/pdf)。corporateIdTypeが"CERTIFICATE_AND_AGREEMENT"の場合のみ必要。順番はpersonalFilesと同じ

#### response
```json
```

### get news unreaded
```
GET /app/v1/news
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "newsList": [
    {
      "id": 3633849,
      "newsType": "OTHER",
      "title": "米企業利益がコロナ前回復へ　10～12月、IT・必需品好調",
      "contents": "【ニューヨーク=後藤達也】米企業の2020年10～12月期決算が本格化する。",
      "link": "https://www.nikkei.com/article/DGXZQOGN174CQ0X10C21A1000000",
      "date": *************
    },
    ...
  ]
}
```

### get news readed
```
GET /app/v1/news/read
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "userId": 123,
  "readIds": [ 1, 2, 3, 4, 5 ]
}
```

### read news
```
POST /app/v1/news/read
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
newsId | long | YES | ニュースid
#### response
```json
1
```

### onetime bank account
```
GET /app/v1/onetime-bank-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response

```json
{
    "id": 1,
    "createdAt": *************,
    "updatedAt": *************,
    "userId": 1,
    "branchCode": 1,
    "branchName": "ｸｽﾉｷ",
    "accountNumber": 1234567,
    "user": {
    }
}
```

### postcodes
```
GET /app/v1/postcodes
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
postcode | int | YES | 郵便番号
#### response
* https://api-doc.postcode-jp.com/#api-v4
```json
[
  {
    "prefCode": "12",
    "cityCode": "227",
    "postcode": "2790031",
    "oldPostcode": "279",
    "hiragana": {
      "pref": "ちばけん",
      "city": "うらやすし",
      "town": "まいはま",
      "allAddress": "ちばけんうらやすしまいはま"
    },
    "halfWidthKana": {
      "pref": "ﾁﾊﾞｹﾝ",
      "city": "ｳﾗﾔｽｼ",
      "town": "ﾏｲﾊﾏ",
      "allAddress": "ﾁﾊﾞｹﾝｳﾗﾔｽｼﾏｲﾊﾏ"
    },
    "fullWidthKana": {
      "pref": "チバケン",
      "city": "ウラヤスシ",
      "town": "マイハマ",
      "allAddress": "チバケンウラヤスシマイハマ"
    },
    "generalPostcode": true,
    "officePostcode": false,
    "location": {
      "latitude": 35.63948059082031,
      "longitude": 139.88424682617188
    },
    "pref": "千葉県",
    "city": "浦安市",
    "town": "舞浜",
    "allAddress": "千葉県浦安市舞浜"
  }
]
```

### download daily report
```
GET /app/v1/report/daily/download
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
firstDay | string | YES | 開始日(yyyyMMdd)。ex) 20210101
lastDay | string | YES | 終了日(yyyyMMdd)。ex) 20210110
#### response
```csv
"【作成日】","2021/03/01"
"【カスタマーサポート】","<EMAIL>"
"【運営者】","株式会社 coinbook"
"【住所】","〒107-0052"
"","東京都港区赤坂2-18-14 赤坂STビル2階"
"","登録番号： 暗号資産交換業者 関東財務局長 第00026号"
"","加入協会： 一般社団法人日本暗号資産取引業協会"
"【アカウントID】","TESTID001"
"【対象期間】","2021/01/01〜2021/01/10"
...
```

### download monthly report
```
GET /app/v1/report/monthly/download
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
targetMonth | string | YES | 対象月(yyyyMM)。ex) 202101

#### response
```csv
"【作成日】","2021/03/01"
"【作成日】","2021/03/01"
"【カスタマーサポート】","<EMAIL>"
"【運営者】","株式会社 coinbook"
"【住所】","〒107-0052"
"","東京都港区赤坂2-18-14 赤坂STビル2階"
"","登録番号： 暗号資産交換業者 関東財務局長 第00026号"
"","加入協会： 一般社団法人日本暗号資産取引業協会"
...
```

### get spot order
```
GET /app/v1/spot/order
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderStatus | string[] | NO | WAITING, UNFILLED, PARTIALLY_FILLED, CANCELED_UNFILLED, CANCELED_PARTIALLY_FILLED
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderTypes | string[] | NO | MARKET, LIMIT, SIMPLE_MARKET
exceptOrderTypes | string[] | NO | 除外条件。MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "symbolId": 1,
    "userId": 123,
    "orderSide": "BUY",
    "orderType": "LIMIT",
    "price": 3650000,
    "averagePrice": 0,
    "amount": 0.1,
    "remainingAmount": 0.1,
    "orderStatus": "UNFILLED",
    "orderOperator": "USER",
    "orderChannel": "PC_WEB",
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### post spot order
```
POST /app/v1/spot/order
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
orderType | string | YES | MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | YES | SELL, BUY
price | decimal | NO | 注文価格。orderType=LIMITの時に指定する。
amount | decimal | YES | 注文数量
#### response
```json
```

### delete spot order
```
DELETE /app/v1/spot/order
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | YES | 注文id
#### response
```json
```

### get spot order all (include order history)
```
GET /app/v1/spot/order/all
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderStatus | string | NO | WAITING, UNFILLED, PARTIALLY_FILLED, CANCELED_UNFILLED, CANCELED_PARTIALLY_FILLED
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "symbolId": 1,
    "userId": 123,
    "orderSide": "BUY",
    "orderType": "LIMIT",
    "price": 3650000,
    "averagePrice": 0,
    "amount": 0.1,
    "remainingAmount": 0.1,
    "orderStatus": "UNFILLED",
    "orderOperator": "USER",
    "orderChannel": "PC_WEB",
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```
### get spot order page (include order history)
```
GET /app/v1/spot/order/page
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderStatus | string | NO | WAITING, UNFILLED, PARTIALLY_FILLED, CANCELED_UNFILLED, CANCELED_PARTIALLY_FILLED
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderTypes | string[] | NO | MARKET, LIMIT, SIMPLE_MARKET
exceptOrderTypes | string[] | NO | 除外条件。MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "number": 0,
    "size": 30,
    "totalElements": 123,
    "totalPages": 4,
    "content": {
    "id": 123,
      "symbolId": 1,
      "userId": 123,
      "orderSide": "BUY",
      "orderType": "LIMIT",
      "price": 3650000,
      "averagePrice": 0,
      "amount": 0.1,
      "remainingAmount": 0.1,
      "orderStatus": "UNFILLED",
      "orderOperator": "USER",
      "orderChannel": "PC_WEB",
      "createdAt": *************,
      "updatedAt": *************
    }
  },
  ...
]
```

### download spot order csv (include order history)
```
GET /app/v1/spot/order/download
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderStatus | string | NO | WAITING, UNFILLED, PARTIALLY_FILLED, CANCELED_UNFILLED, CANCELED_PARTIALLY_FILLED
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderTypes | string[] | NO | MARKET, LIMIT, SIMPLE_MARKET
exceptOrderTypes | string[] | NO | 除外条件。MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
#### response
```csv
注文ID,ユーザーID,通貨ペア,数量,売買区分,注文種別,価格,平均約定価格,未約定数量,注文ステータス,注文チャンネル,作成日時,更新日時
35911842,321,BTC_JPY,0.00100,売り,成行,5092626,5092626,0.00000,約定済み,PC_WEB,"2021/10/21 17:42:06","2021/10/21 17:42:06"
...
```

### get spot trade
```
GET /app/v1/spot/trade
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | NO | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderSide | string | NO | SELL, BUY
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderTypes | string[] | NO | MARKET, LIMIT, SIMPLE_MARKET
exceptOrderTypes | string[] | NO | 除外条件。MARKET, LIMIT, SIMPLE_MARKET
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "symbolId": 1,
    "userId": 123,
    "orderSide": "BUY",
    "orderType": "LIMIT",
    "price": 3650000,
    "amount": 0.1,
    "tradeAction": "MAKER",
    "orderId": 456,
    "fee": 365,
    "orderChannel": "PC_WEB",
    "jpyConversion": 1,
    "targetOrderId": null,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### get spot trade history
```
GET /app/v1/spot/trade/history
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | NO | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "symbolId": 1,
    "userId": 123,
    "orderSide": "BUY",
    "orderType": "LIMIT",
    "price": 3650000,
    "amount": 0.1,
    "tradeAction": "MAKER",
    "orderId": 456,
    "fee": 365,
    "orderChannel": "PC_WEB",
    "jpyConversion": 1,
    "targetOrderId": null,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### get spot trade all (include order history)
```
GET /app/v1/spot/trade/all
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "symbolId": 1,
    "userId": 123,
    "orderSide": "BUY",
    "orderType": "LIMIT",
    "price": 3650000,
    "amount": 0.1,
    "tradeAction": "MAKER",
    "orderId": 456,
    "fee": 365,
    "orderChannel": "PC_WEB",
    "jpyConversion": 1,
    "targetOrderId": null,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```
### get spot trade page (include order history)
```
GET /app/v1/spot/trade/page
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderTypes | string[] | NO | MARKET, LIMIT, SIMPLE_MARKET
exceptOrderTypes | string[] | NO | 除外条件。MARKET, LIMIT, SIMPLE_MARKET
orderSide | string | NO | SELL, BUY
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "number": 0,
    "size": 30,
    "totalElements": 123,
    "totalPages": 4,
    "content": {
      "id": 123,
      "symbolId": 1,
      "userId": 123,
      "orderSide": "BUY",
      "orderType": "LIMIT",
      "price": 3650000,
      "amount": 0.1,
      "tradeAction": "MAKER",
      "orderId": 456,
      "fee": 365,
      "orderChannel": "PC_WEB",
      "jpyConversion": 1,
      "targetOrderId": null,
      "createdAt": *************,
      "updatedAt": *************
    }
  },
  ...
]
```

### download spot trade csv (include order history)
```
GET /app/v1/spot/trade/download
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
symbolId | long | YES | symbolId
id | long | NO | 注文ID
idFrom | long | NO | 検索開始注文ID
idTo | long | NO | 検索終了注文ID
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
orderSide | string | NO | SELL, BUY
orderType | string | NO | MARKET, LIMIT, SIMPLE_MARKET
orderTypes | string[] | NO | MARKET, LIMIT, SIMPLE_MARKET
exceptOrderTypes | string[] | NO | 除外条件。MARKET, LIMIT, SIMPLE_MARKET
#### response
```csv

```


### user aggrement file
```
GET /app/v1/user-agreement-file
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "userAgreementType": "USE_API_TOKEN",
    "version": "1.0.0"
  },
  ...
]
```

### get user info for personal
```
GET /app/v1/user-info/personal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "userId": "USE_API_TOKEN",
  "firstName": "洋平",
  "lastName": "鎌倉",
  "firstKana": "ヨウヘイ",
  "lastKana": "カマクラ",
  "zipCode": "1000011",
  "prefecture": "東京都",
  "address1": "千代田区",
  "address2": "内幸町1-1-6",
  "building": "NTT日比谷ビルB2F",
  "birthday": "20180815",
  "gender": 1,
  "phoneNumber": "**********",
  "occupation": "自営業",
  "workPlace": "CXRエンジニアリング株式会社",
  "position": "CTO",
  "income": 1,
  "financialAssets": 1,
  "purpose": 1,
  "cryptoInsider": 0,
  "investmentPurposes": 1,
  "cryptoExperience": 1,
  "fxExperience": 1,
  "stocksExperience": 1,
  "fundExperience": 1,
  "applicationHistory": 1,
  "applicationHistoryOther": "その他",
  "foreignPeps": false
}
```

### post user info for personal
```
POST /app/v1/user-info/personal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
firstName | string | YES | 名
latName | string | YES | 氏
firstKana | string | YES | 名（カナ）
lastKana | string | YES | 氏（カナ）
zipCode | string | YES | 郵便番号
prefecture | string | YES | 都道府県
address1 | string | YES | 市区町村
address2 | string | YES | 町名番地
building | string | NO | 建物名
birthday | string | YES | 生年月日
gender | int | YES | 性別
phoneNumber | string | YES | 電話番号
occupation | int | YES | 職業
workPlace | string | NO | 勤務先
position | string | YES | 部署、役職
income | int | YES | 年収
financialAssets | int | YES | 金融資産
purpose |  | int | YES | 主な利用目的
cryptoInsider |  | int | YES | 暗号資産の情報取得者
investmentPurposes | int | YES | 投資目的
cryptoExperience | int | YES | 投資経験（暗号資産）
fxExperience | int | YES | 投資経験（FX）
stocksExperience | int | YES | 投資経験（株式）
fundExperience | int | YES | 投資経験（投資信託）
applicationHistory | int | YES | 申し込み経緯
applicationHistoryOther | string | YES | 申し込み経緯(その他)
foreignPeps | boolean | YES | 外国PEPs
#### response
```json
```

### get user info for corporate
```
GET /app/v1/user-info/corporate
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "userId": 123,
  "name": "CXRエンジニアリング株式会社",
  "nameKana": "シーエックアールエンジニアリングカブシキガイシャ",
  "establishedYear": 2018,
  "establishedMonth": 8,
  "establishedDay": 15,
  "accountingMonth": 12,
  "zipCode": "1000011",
  "prefecture": "東京都",
  "address1": "千代田区",
  "address2": "内幸町1-1-6",
  "building": "NTT日比谷ビルB2F",
  "phoneNumber": "**********",
  "mobilePhoneNumber": "***********",
  "faxNumber": "**********",
  "businessContent": "IT",
  "sales": 1,
  "financialAssets": 1,
  "purpose": 1,
  "investmentPurposes": 1,
  "applicationHistory": 1,
  "applicationHistoryOther": "その他",
  "ultimateBeneficialOwnership": 1,
  "personExceptRepresentative": "自営業",
  "cryptoExperience": 1,
  "fxExperience": 1,
  "stocksExperience": 1,
  "fundExperience": 1,
  "foreignPeps": false
}
```

### post user info for corporate
```
POST /app/v1/user-info/corporate
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
name | string | YES | 法人名
nameKana | string | YES | 法人名（カナ）
establishedYear | int | YES | 設立年
establishedMonth | int | YES | 設立月
establishedDay | int | YES | 設立日
accountingMonth | int | YES | 決算月
zipCode | string | YES | 郵便番号
prefecture | string | YES | 都道府県
address1 | string | YES | 市区町村
address2 | string | YES | 町名番地
building | string | NO | 建物名
phoneNumber | string | YES | 電話番号
mobilePhoneNumber | string | YES | 携帯番号
faxNumber | string | NO | FAX番号
businessContent | string | YES | 事業内容
sales | int | YES | 年商
financialAssets | int | YES | 金融資産
purpose | int | YES | 利用目的
investmentPurposes | int | YES | 投資目的
applicationHistory | int | YES | 申込経緯
applicationHistoryOther | string | YES | 申込経緯(その他)
ultimateBeneficialOwnership | boolean | YES | 実質的支配者の申告
personExceptRepresentative | boolean | YES | 取引担当者の選択
cryptoExperience | int | YES | 投資経験（暗号資産）
fxExperience | int | YES | 投資経験（FX）
stocksExperience | int | YES | 投資経験（株式）
fundExperience | int | YES | 投資経験（投資信託）
foreignPeps | boolean | YES | 外国PEPs
#### response
```json
```

### get user
```
GET /app/v1/user
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "id": 123,
  "email": "DC10012345",
  "accountNonExpired": true,
  "accountNonLocked": true,
  "credentialsNonExpired": true,
  "enabled": true,
  "userStatus": "ACTIVE",
  "kycStatus": "NONE",
  "level": 0,
  "authorities": [],
  "userInfoId": null,
  "userInfoCorporateId": null,
  "userInfo": null,
  "userInfoCorporate": null,
  "tradeUncapped": false,
  "active": true,
  "username": "DC10012345",
  "createdAt": *************,
  "updatedAt": *************
}
```

### register user(for mfa)
```
POST /app/v1/user/register/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | メールアドレス
password | string | YES | パスワード
authority | string | YES | 権限
oldUserId | int | NO | 取引所のユーザーID
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### register user
```
POST /app/v1/user/register
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | メールアドレス
password | string | YES | パスワード
authority | string | YES | 権限
oldUserId | int | NO | 取引所のユーザーID
recaptchaToken | string | YES | reCAPTCHAトークン
mfaCode | string | YES | mfaコード
#### response
```json
{
  "email": "<EMAIL>",
  "accountNonExpired": true,
  "accountNonLocked": true,
  "credentialsNonExpired": true,
  "enabled": true,
  "userStatus": "TEMPORARY",
  "kycStatus": "NONE",
  "level": 0,
  "userInfoId": null,
  "userInfoCorporateId": null,
  "oldUserId": 123
}
```

### login(for mfa)
```
POST /app/v1/user/login/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | メールアドレス
password | string | YES | パスワード
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### login
```
POST /app/v1/user/login
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | メールアドレス
password | string | YES | パスワード
recaptchaToken | string | YES | reCAPTCHAトークン
mfaCode | string | YES | mfaコード
#### response
```json
```

### verify email
```
PUT /app/v1/user/email/verify
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | 新しいメールアドレス
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### change email(for mfa)
```
PUT /app/v1/user/email/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | 新しいメールアドレス
mfaCode | string | YES | mfaコード（新しいメールアドレスに送られたもの）
#### response
```json
{
  "mfaType": "GOOGLE"
}
```

### change email
```
PUT /app/v1/user/email
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | 新しいメールアドレス
mfaCode | string | YES | mfaコード
#### response
```json
```

### change password(for mfa)
```
PUT /app/v1/user/password/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
oldPassword | string | YES | 旧パスワード
newPassword | string | YES | 新パスワード
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### change password
```
PUT /app/v1/user/password
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
oldPassword | string | YES | 旧パスワード
newPassword | string | YES | 新パスワード
mfaCode | string | YES | mfaコード
#### response
```json
```

### reset password(for mfa)
```
PUT /app/v1/user/reset-password/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | 登録済みメールアドレス
recaptchaToken | string | YES | reCAPTCHAトークン
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### reset password
```
PUT /app/v1/user/reset-password
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
email | string | YES | 登録済みメールアドレス
password | string | YES | 新しいパスワード
mfaCode | string | YES | mfaコード
#### response
```json
```

### set phoneNumber(for mfa)
```
PUT /app/v1/user-info/phone-number/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
phoneNumber | String | YES | 電話番号
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### set phoneNumber
```
PUT /app/v1/user-info/phone-number
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
phoneNumber | String | YES | 電話番号
mfaCode | String | YES | mfaコード
#### response
```json
```

### set antiPhishingCode(for mfa)
```
PUT /app/v1/user/anti-phishing-code/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
antiPhishingCode | String | YES | アンチフィッシングコード 
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### set antiPhishingCode
```
PUT /app/v1/user/anti-phishing-code
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
antiPhishingCode | String | YES | アンチフィッシングコード 
mfaCode | String | YES | mfaコード
#### response
```json
```

### get user mfa otpauth uri
```
POST /app/v1/user-mfa/otpauth-uri
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "uri": "otpauth://totp/cb.cxr-inc.com:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=cb.cxr-inc.com"
}
```

### set user mfa(for mfa)
```
POST /app/v1/user-mfa/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
mfaType | String | YES | GOOGLE, SMS
countryCode | String | YES | JP, etc
#### response
```json
{
  "mfaType": "GOOGLE"
}
```

### set user mfa
```
POST /app/v1/user-mfa
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
mfaType | String | YES | GOOGLE, SMS
countryCode | String | YES | JP, etc
mfaCode | String | YES | mfaコード
#### response
```json
```

### delete user mfa(for mfa)
```
DELETE /app/v1/user-mfa/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
mfaType | String | YES | GOOGLE, SMS
#### response
```json
{
  "mfaType": "GOOGLE"
}
```

### delete user mfa
```
DELETE /app/v1/user-mfa/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
mfaType | String | YES | GOOGLE, SMS
mfaCode | String | YES | mfaコード
#### response
```json
```

### get user condition
```
GET /app/v1/user/condition
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "userId": 123,
  "authority": "PERSONAL",
  "level": 0,
  "email": "<EMAIL>",
  "antiPhishingCode": "qwertyuiop",
  "profileRegistered": true,
  "kycStatus": "DONE",
  "histories": [
    {
      "id": 123,
      "userId": 456,
      "ip": "123.456.789.0",
      "createdAt": *************
    },
    ...
  ]
}
```

### get user level
```
GET /app/v1/user/level
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
{
  "level": 1
}
```

### get user agreement
```
GET /app/v1/user/agreement
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
#### response
```json
[
  {
    "userId": 123,
    "userAgreementType": "TERMS_OF_SERVICE",
    "version": "1.0.0",
    "enabled": true
  },
  ...
]
```

### post user agreement
```
POST /app/v1/user/agreement
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
userAgreementType | string | YES | 確認書類タイプ。ex) USE_API_TOKEN, TERMS_OF_SERVICE, DISCLOSURE_STATEMENT
version | string | YES | 確認書類のバージョン
#### response
```json
{
  "userId": 123,
  "userAgreementType": "TERMS_OF_SERVICE",
  "version": "1.0.0",
  "enabled": true,
  "createdAt": *************,
  "updatedAt": *************
}
```

### get withdrawal page data
```
GET /app/v1/withdrawal/page
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | NO | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
withdrawalStatus | string | NO | EXAMINING, BROADCASTED, FINISHING, DONE, APPROVING, REJECTED, CANCELED, TRANSACTION_ERROR, SYSTEM_ERROR
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "number": 0,
    "size": 30,
    "totalElements": 123,
    "totalPages": 4,
    "content": {
      "id": 123,
      "userId": 123,
      "currency": "BTC",
      "withdrawalChannel": "WALLET",
      "withdrawalPurpose": "DEPOSIT",
      "withdrawalType": "COMPULSORY_WITHDRAW",
      "amount": "1.5",
      "withdrawalFee": "0",
      "transactionFee": "0",
      "jpyConversion": 3802653,
      "address": "**********************************",
      "transactionId": "db4b21f8a376ba4edef05e58356b7ddca38f0bf97a5ba10f6f900c3ca14414eb",
      "withdrawalStatus": "DONE",
      "comment": null,
      "withdrawalAccountId": 1,
      "createdAt": *************,
      "updatedAt": *************
    }
  },
  ...
]
```

### get withdrawal account
```
GET /app/v1/withdrawal-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | YES | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "userId": 456,
    "currency": "BTC",
    "label": "private",
    "address": "**********************************",
    "destinationTag": null,
    "enabled": true,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### post withdrawal account(for mfa)
```
POST /app/v1/withdrawal-account/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | YES | 通貨。BTC, JPY, など。
label | long | YES | ラベル
address | long | YES | 暗号通貨のアドレス
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### post withdrawal account
```
POST /app/v1/withdrawal-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | YES | 通貨。BTC, JPY, など。
label | long | YES | ラベル
address | long | YES | 暗号通貨のアドレス
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 456,
  "currency": "BTC",
  "label": "private",
  "address": "**********************************",
  "destinationTag": null,
  "enabled": true,
  "createdAt": *************,
  "updatedAt": *************
}
```

### put withdrawal account(for mfa)
```
PUT /app/v1/withdrawal-account/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | YES | 通貨。BTC, JPY, など。
label | long | YES | ラベル
address | long | YES | 暗号通貨のアドレス
withdrawalAccountId | long | YES | id
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### PUT withdrawal account
```
PUT /app/v1/withdrawal-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | YES | 通貨。BTC, JPY, など。
label | long | YES | ラベル
address | long | YES | 暗号通貨のアドレス
withdrawalAccountId | long | YES | id
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 456,
  "currency": "BTC",
  "label": "private",
  "address": "**********************************",
  "destinationTag": null,
  "enabled": true,
  "createdAt": *************,
  "updatedAt": *************
}
```

### delete withdrawal account(for mfa)
```
DELETE /app/v1/withdrawal-account/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | id
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### DELETE withdrawal account
```
DELETE /app/v1/withdrawal-account
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | long | YES | id
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 456,
  "currency": "BTC",
  "label": "private",
  "address": "**********************************",
  "destinationTag": null,
  "enabled": true,
  "createdAt": *************,
  "updatedAt": *************
}
```

### get withdrawal
```
GET /app/v1/withdrawal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
currency | string | NO | 通貨。BTC, JPY, など。
dateFrom | long | NO | 取得開始日時
dateTo | long | NO | 取得終了日時
withdrawalStatus | string | NO | EXAMINING, BROADCASTED, FINISHING, DONE, APPROVING, REJECTED, CANCELED, TRANSACTION_ERROR, SYSTEM_ERROR
number | int | NO | ページ番号。default: 0
size | int | NO | 1ページに表示する件数。default: 30
#### response
```json
[
  {
    "id": 123,
    "userId": 123,
    "currency": "BTC",
    "withdrawalAccountId": 1,
    "withdrawalChannel": "WALLET",
    "withdrawalPurpose": "DEPOSIT",
    "withdrawalType": "COMPULSORY_WITHDRAW",
    "amount": "1.5",
    "withdrawalFee": "0",
    "transactionFee": "0",
    "jpyConversion": 3802653,
    "address": "**********************************",
    "transactionId": "db4b21f8a376ba4edef05e58356b7ddca38f0bf97a5ba10f6f900c3ca14414eb",
    "withdrawalStatus": "DONE",
    "comment": null,
    "createdAt": *************,
    "updatedAt": *************
  },
  ...
]
```

### post withdrawal(for mfa)
```
POST /app/v1/withdrawal/otpauth
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
withdrawalAccountId | long | YES | id of WithdrawalAccount
amount | decimal | YES | 出金数量
comment | decimal | YES | コメント
#### response
```json
{
  "mfaType": "EMAIL"
}
```

### post withdrawal
```
POST /app/v1/withdrawal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
withdrawalAccountId | long | YES | id of WithdrawalAccount
amount | decimal | YES | 出金数量
comment | decimal | YES | コメント
mfaCode | string | YES | mfaコード
#### response
```json
{
  "id": 123,
  "userId": 456,
  "currency": "BTC",
  "withdrawalChannel": "WALLET",
  "withdrawalType": "TRANS_WITHDRAW",
  "withdrawalPurpose": "PAYMENT",
  "amount": 1.5,
  "withdrawalFee": 0.001,
  "transactionFee": 0.0005,
  "jpyConversion": 3650000,
  "comment": null,
  "withdrawalAccountId": 1,
  "address": "**********************************",
  "transactionId": "58ba61e4f43cd006789d2255b95021979bb17f775cbb92e0c72cd711cc7ba2e2",
  "withdrawalStatus": "EXAMINING",
  "createdAt": *************,
  "updatedAt": *************
}
```

### delete withdrawal
```
DELETE /app/v1/withdrawal
```
#### parameters
Name | Type | Mandatory | Description
--- | --- | --- | ---
id | string | YES | id of Withdrawal
#### response
```json
{
  "id": 123,
  "userId": 456,
  "currency": "BTC",
  "withdrawalChannel": "WALLET",
  "withdrawalType": "TRANS_WITHDRAW",
  "withdrawalPurpose": "PAYMENT",
  "amount": 1.5,
  "withdrawalFee": 0.001,
  "transactionFee": 0.0005,
  "jpyConversion": 3650000,
  "comment": null,
  "withdrawalAccountId": 1,
  "address": "**********************************",
  "transactionId": null,
  "withdrawalStatus": "CANCELED",
  "createdAt": *************,
  "updatedAt": *************
}
```

### post profile personal validate

```
POST app/v1/user-info/personal/validate
```

#### parameters

Name | Type | Mandatory | Description
--- | --- | --- | ---
firstName | string | NO | 名字
lastName | string | NO | 名前
firstKana | string | NO | 名字(かな)
lastKana| string | NO | 名前（かな）
zipCode | string | NO | 郵便番号
prefecture | string | NO | 都道府県コード
city | string | NO | 市区町村
address | string | NO | 町番地
building | string | NO | 建物名
birthday | string | NO | 誕生日
gender | string | NO | 性別 (1:男性・2:女性・0:無回答)
phoneNumber | string | NO | 電話番号
occupation | string | NO | 職業
workPlace | string | NO | 勤務地
position | string | NO | 役職
income | string | NO | 年収
financialAssets | string | NO | 金融資産
purpose | string | NO | 主なご利用目的
cryptoInsider | string | NO | 暗号資産の情報取得者
investmentPurposes | string | NO | 投資目的
cryptoExperience | string | NO | 投資経験(暗号資産)
fxExperience | string | NO | 投資経験(FX)
stocksExperience | string | NO | 投資経験(株式投資)
fundExperience | string | NO | 投資経験(投資信託)
applicationHistory | string | NO | 申込経緯
applicationHistoryOther | string | NO | 申込経緯(その他)
foreignPeps | string | NO | 外国 peps

### post profile corporate validate

```
POST app/v1/user-info/corporate/validate
```

#### parameters

Name | Type | Mandatory | Description
--- | --- | --- | ---
name | string | NO | 会社名
nameKana | string | NO | 会社名
establishedYear | string | NO | 設立年
establishedMonth | string | NO | 設立月
establishedDay | string | NO | 設立日
accountingMonth | string | NO | 決算月
phoneNumber | string | NO | 電話番号
mobilePhoneNumber | string | NO | 携帯電話番号
faxNumber | string | NO | 電話番号
businessContent | string | NO | 事業内容
sales | string | NO | 年商
financialAssets | string | NO | 年商
purpose | string | NO | 主なご利用目的
investmentPurposes | string | NO | 投資目的
applicationHistory | string | NO | 申込経緯
applicationHistoryOther| string | NO | 申込経緯(その他)
zipCode | string | NO | 郵便番号
prefecture | string | NO | 都道府県コード
city | string | NO | 市区町村
address | string | NO | 町番地
building | string | NO | 建物名
personExceptRepresentative | string | NO | 取引担当者の選択
ultimateBeneficialOwnership | string | NO | 実質的支配者の申告
cryptoExperience | string | NO | 投資経験(暗号資産)
fxExperience | string | NO | 投資経験(FX)
stocksExperience | string | NO | 投資経験(株式投資)
fundExperience | string | NO | 投資経験(投資信託)
foreignPeps | string | NO | 外国 peps
representative firstName | string | NO | 代表 名字
representative lastName | string | NO | 代表 名前
representative firstKana | string | NO | 代表 名字(かな)
representative lastKana| string | NO | 代表 名前（かな）
representative zipCode | string | NO | 代表郵 便番号
representative prefecture | string | NO | 代表 都道府県コード
representative city | string | NO | 代表 市区町村
representative address | string | NO | 代表 町番地
representative building | string | NO | 代表 建物名
representative birthday | string | NO | 代表 誕生日
gender | string | NO | 代表 性別 (1:男性・2:女性・0:無回答)
agent firstName | string | NO | 取引担当者 名字
agent lastName | string | NO | 取引担当者 名前
agent firstKana | string | NO | 取引担当者 名字(かな)
agent lastKana| string | NO | 取引担当者 名前（かな）
agent zipCode | string | NO | 取引担当者 郵便番号
agent prefecture | string | NO | 取引担当者 都道府県コード
agent city | string | NO | 取引担当者 市区町村
agent address | string | NO | 取引担当者 町番地
agent building | string | NO | 取引担当者 建物名
agent birthday | string | NO | 取引担当者 生年月日
owners firstName | string | NO | 実質的支配者 名字
owners lastName | string | NO | 実質的支配者 名前
owners firstKana | string | NO | 実質的支配者 名字(かな)
owners lastKana| string | NO | 実質的支配者 名前（かな）
owners nationality | string | NO | 実質的支配者 国籍
owners zipCode | string | NO | 実質的支配者 郵便番号
owners prefecture | string | NO | 実質的支配者 都道府県コード
owners city | string | NO | 実質的支配者 市区町村
owners address | string | NO | 実質的支配者 町番地
owners building | string | NO | 実質的支配者 建物名
owners birthday | string | NO | 実質的支配者 生年月日
owners foreignPeps | string | NO | 実質的支配者 外国 peps

```json Post Example
{
  "name": "CXR",
  "nameKana": "シーエックスアール",
  "establishedYear": 1970,
  "establishedMonth": 12, 
  "establishedDay": 1,
  "accountingMonth": 9,
  "phoneNumber": "***********",
  "mobilePhoneNumber": "***********",
  "faxNumber": "**********",
  "businessContent": "受託開発",
  "sales": 1,
  "financialAssets": 7,
  "purpose": 1, 
  "investmentPurposes": 3,
  "applicationHistory": 1,
    "applicationHistoryOther": "その他",
  "zipCode": "1001011",
  "prefecture": 13,
  "city": "東京都千代田区",
  "address": "内幸町 1-1-6",
  "building": "NTT日比谷ビル7階 a",
  "personExceptRepresentative": true,
  "ultimateBeneficialOwnership": true,
  "cryptoExperience": 4,
  "fxExperience": 1,
  "fundExperience": 3,
  "stocksExperience": 2,
  "foreignPeps": false,
  "representative": {
    "firstName":"山田",
    "lastName": "太郎",
    "firstKana": "山田",
    "lastKana": "タロウ",
    "position": "部長",
    "nationality": "Japan",
    "zipCode": "1001011",
    "prefecture": 13,
    "city": "東京都千代田区",
    "address": "内幸町 1-1-6",
    "building": "NTT日比谷ビル7階",
    "birthday": "19800101"
  },
  "agent": {
    "firstName":"山田",
    "lastName": "太郎",
    "firstKana": "山田",
    "lastKana": "タロウ",
    "position": "部長",
    "nationality": "Japan",
    "zipCode": "1001011",
    "prefecture": 13,
    "city": "東京都千代田区",
    "address": "内幸町1-1-6",
    "building": "NTT日比谷ビル7階",
    "birthday": "19800101"
  },
  "owners": 
  [
    {
    　"firstName":"山田",
    　"lastName": "太郎",
    　"firstKana": "山田",
    "lastKana": "タロウ",
      "position": "部長",
      "nationality": "Japan",
      "zipCode": "1001011",
      "prefecture": 13,
      "city": "東京都千代田区内幸町",
      "address": "1-1-6 1",
      "building": "NTT日比谷ビル7階",
      "birthday": "19800101",
      "foreignPeps": false
    },
    {
    　"firstName":"山田",
    　"lastName": "太郎",
    　"firstKana": "山田",
    "lastKana": "タロウ",
      "position": "部長",
      "nationality": "Japan",
      "zipCode": "1001011",
      "prefecture": 13,
      "city": "東京都千代田区内幸町",
      "address": "1-1-6 1",
      "building": "NTT日比谷ビル7階",
      "foreignPeps": false
    },
  ]
}
```
#### response
```json　with error
{
    "code": 10001
}
```