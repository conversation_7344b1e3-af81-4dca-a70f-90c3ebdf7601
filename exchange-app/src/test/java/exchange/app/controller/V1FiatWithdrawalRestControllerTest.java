package exchange.app.controller;

import exchange.app.component.MfaManager;
import exchange.app.model.request.FiatWithdrawalPostForm;
import exchange.common.constant.Currency;
import exchange.common.constant.FiatWithdrawalStatus;
import exchange.common.constant.UserStatus;
import exchange.common.entity.Asset;
import exchange.common.entity.BankAccount;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.User;
import exchange.common.service.AssetService;
import exchange.common.service.BankAccountService;
import exchange.common.service.FiatWithdrawalService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"local"})
class V1FiatWithdrawalRestControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @SpyBean
    MfaManager manager;
    @MockBean
    BankAccountService bankAccountService;
    @SpyBean
    V1FiatWithdrawalRestController controller;
    @MockBean
    AssetService assetService;
    @SpyBean
    FiatWithdrawalService fiatWithdrawalService;


    @Test
    @DisplayName("顧客画面出金登録成功: fiatWithdrawalAudit登録")
    @WithMockUser("test-user")
    void post() throws Exception {
        var user = new User();
        var asset = new Asset();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        user.setUserStatus(UserStatus.ACTIVE);
        var form = new FiatWithdrawalPostForm();
        form.setMfaCode("123456");
        form.setAmount(new BigDecimal("1"));
        form.setBankAccountId(111L);
        doNothing().when(manager).authenticate(any(), any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.post(user, form);
    }

    @Test
    @DisplayName("顧客画面出金登録失敗: fiatWithdrawalAudit登録しない")
    void postTransactionFailed() throws Exception {
        var user = new User();
        var asset = new Asset();
        var form = new FiatWithdrawalPostForm();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        form.setMfaCode("123456");
        form.setAmount(new BigDecimal("1"));
        form.setBankAccountId(111L);
        doThrow(new RuntimeException()).when(fiatWithdrawalService).saveAudit(any(), any());
        doNothing().when(manager).authenticate(any(), any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.post(user, form);
    }
}