package exchange.spot.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import exchange.common.component.SesManager;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.MailNoreplyType;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.service.MailNoreplyService;
import exchange.spot.entity.SpotOrderAdaJpy;
import exchange.spot.entity.SpotTradeAdaJpy;

public class SpotTradeServiceTest {
  @Test
  void testSendTradeMailOrderTypeLimit() throws Exception {
    // mocked mailNoreplyService
    var mailNoreplyService = mock(MailNoreplyService.class);
    var sesManager = spy(new SesManager(null, null, null));
    
    var user = new User();
    user.setEmail("<EMAIL>");

    var symbol = new Symbol();
    symbol.setProperties(TradeType.SPOT, CurrencyPair.ADA_JPY);

    var spotOrder = new SpotOrderAdaJpy();
    spotOrder.setId(1L);
    spotOrder.setOrderSide(OrderSide.BUY);
    spotOrder.setOrderType(OrderType.LIMIT);
    spotOrder.setAmount(new BigDecimal("10.0"));
    spotOrder.setPrice(new BigDecimal("100.0"));
    spotOrder.setRemainingAmount(new BigDecimal(0.00000000));

    var spotTrade = new SpotTradeAdaJpy();
    spotTrade.setOrderId(1L);
    spotTrade.setAmount(new BigDecimal("5.0"));
    spotTrade.setPrice(new BigDecimal("80.0"));


    var mailtemplate = new MailNoreply();
    mailtemplate.setTitle("【coinbook】注文取消しのお知らせ");
    mailtemplate.setFromAddress("<EMAIL>");
    mailtemplate.setContents("""
      通貨ペア : ${instrumentId}
      注文番号 : ${orderId}
      売買 : ${side}
      注文方法 : ${orderType}
      注文数量 : ${size}
      注文価格 : ${price}
      約定数量 : ${trade_size}
      約定価格 : ${trade_price}
      未約定数量 : ${remaining_amount}
    """);
    doReturn(mailtemplate).when(mailNoreplyService).findOne(any(MailNoreplyType.class));

    // set capter to sesManager.send(String from, String to, String subject, String body)
    ArgumentCaptor<String> fromCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<String> toCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<String> subjectCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<String> bodyCaptor = ArgumentCaptor.forClass(String.class);
    doReturn(true).when(sesManager).send(fromCaptor.capture(), toCaptor.capture(), subjectCaptor.capture(), bodyCaptor.capture());

    // run
    new SpotTradeAdaJpyService().sendTradeMail(spotOrder, spotTrade, mailNoreplyService, sesManager, symbol, user);

    assertEquals(fromCaptor.getValue(), mailtemplate.getFromAddress());
    assertEquals(toCaptor.getValue(), user.getEmail());
    assertEquals(subjectCaptor.getValue(), mailtemplate.getTitle());

    // expected body
    var expectedBody = """
      通貨ペア : ADA_JPY
      注文番号 : 1
      売買 : BUY
      注文方法 : LIMIT
      注文数量 : 10
      注文価格 : 100.000
      約定数量 : 5
      約定価格 : 80.000
      未約定数量 : 0
    """;

    assertEquals(bodyCaptor.getValue(), expectedBody);
  }

  @Test
  void testSendTradeMailOrderTypeMarket() throws Exception {
    // mocked mailNoreplyService
    var mailNoreplyService = mock(MailNoreplyService.class);
    var sesManager = spy(new SesManager(null, null, null));
    
    var user = new User();
    user.setEmail("<EMAIL>");

    var symbol = new Symbol();
    symbol.setProperties(TradeType.SPOT, CurrencyPair.ADA_JPY);

    var spotOrder = new SpotOrderAdaJpy();
    spotOrder.setId(1L);
    spotOrder.setOrderSide(OrderSide.BUY);
    spotOrder.setOrderType(OrderType.MARKET);
    spotOrder.setAmount(new BigDecimal("10.0"));
    spotOrder.setPrice(new BigDecimal("100.0"));
    spotOrder.setRemainingAmount(new BigDecimal(0.00000000));

    var spotTrade = new SpotTradeAdaJpy();
    spotTrade.setOrderId(1L);
    spotTrade.setAmount(new BigDecimal("5.0"));
    spotTrade.setPrice(new BigDecimal("80.0"));


    var mailtemplate = new MailNoreply();
    mailtemplate.setTitle("【coinbook】注文取消しのお知らせ");
    mailtemplate.setFromAddress("<EMAIL>");
    mailtemplate.setContents("""
      通貨ペア : ${instrumentId}
      注文番号 : ${orderId}
      売買 : ${side}
      注文方法 : ${orderType}
      注文数量 : ${size}
      注文価格 : ${price}
      約定数量 : ${trade_size}
      約定価格 : ${trade_price}
      未約定数量 : ${remaining_amount}
    """);
    doReturn(mailtemplate).when(mailNoreplyService).findOne(any(MailNoreplyType.class));

    // set capter to sesManager.send(String from, String to, String subject, String body)
    ArgumentCaptor<String> fromCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<String> toCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<String> subjectCaptor = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<String> bodyCaptor = ArgumentCaptor.forClass(String.class);
    doReturn(true).when(sesManager).send(fromCaptor.capture(), toCaptor.capture(), subjectCaptor.capture(), bodyCaptor.capture());

    // run
    new SpotTradeAdaJpyService().sendTradeMail(spotOrder, spotTrade, mailNoreplyService, sesManager, symbol, user);

    assertEquals(fromCaptor.getValue(), mailtemplate.getFromAddress());
    assertEquals(toCaptor.getValue(), user.getEmail());
    assertEquals(subjectCaptor.getValue(), mailtemplate.getTitle());

    // expected body
    var expectedBody = """
      通貨ペア : ADA_JPY
      注文番号 : 1
      売買 : BUY
      注文方法 : MARKET
      注文数量 : 10
      注文価格 : -
      約定数量 : 5
      約定価格 : 80.000
      未約定数量 : 0
    """;

    assertEquals(bodyCaptor.getValue(), expectedBody);
  }
}
