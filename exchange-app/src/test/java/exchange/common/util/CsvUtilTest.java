package exchange.common.util;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.experimental.Accessors;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.List;
import java.util.Locale;

/**
 * @author: wen.y
 * @date: 2024/10/18
 */
public class CsvUtilTest {

	@Test
	public void test1() throws Exception {
		List<Person> personList1 = Lists.newArrayList(
				new Person().setId(1L).setName("张三").setSex("男"),
				new Person().setId(2L).setName("李四").setSex("女"),
				new Person().setId(3L).setName("王五").setSex("男")
		);
    	System.out.println(CsvUtil.toCsvString(Person.class, personList1));
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonPropertyOrder({"ID", "姓名", "性别"})
	@Accessors(chain = true)
	@Data
	public static class Person {
		@JsonProperty("ID")
		private Long id;
		@JsonProperty("姓名")
		private String name;
		@JsonProperty("性别")
		private String sex;
	}

	@Test
	public void test2() throws Exception {
		List<ReportData> reportDataList = CsvUtil.readCsvFile("/Users/<USER>/Desktop/tmp1/GETAirdropApp_1024.csv", ReportData.class, Charset.forName("MS932"));
    	System.out.println(reportDataList);
	}

	@JsonPropertyOrder({"No.", "UserID", "MailAddress", "Token", "AppDate", "Amout(NIDT 00:00JST)", "Status", "AssignedQuantity(GET)", "UpdateDate"})
	@Accessors(chain = true)
	@Data
	public static class ReportData {
		@JsonProperty("No.")
		private Long row;
		@JsonProperty("UserID")
		private Long userId;
		@JsonProperty("MailAddress")
		private String email;
		@JsonProperty("Token")
		private String cryptoToken;
		@JsonProperty("AppDate")
		private String applyTime;
		@JsonProperty("Amout(NIDT 00:00JST)")
		private String nidtAmount;
		@JsonProperty("Status")
		private String status;
		@JsonProperty("AssignedQuantity(GET)")
		private String assignedQuantity;
		@JsonProperty("UpdateDate")
		private String updateDate;
	}

	@Test
	public void test3() throws Exception {
    	CsvUtil.export(new FileOutputStream("//Users/<USER>/Desktop/tmp1/TestCsv_UTF-8.csv"), ReportData2.class,  Lists.newArrayList(
				new ReportData2().setRow(1L).setUserId(1000L).setEmail("<EMAIL>").setCryptoToken("GET").setApplyTime("2024-10-22 14:26:39"),
				new ReportData2().setRow(2L).setUserId(1001L).setEmail("<EMAIL>").setCryptoToken("GET").setApplyTime("2024-10-24 09:57:22")
		), CharsetUtil.UTF_8);
		CsvUtil.export(new FileOutputStream("//Users/<USER>/Desktop/tmp1/TestCsv_MS932.csv"), ReportData2.class,  Lists.newArrayList(
				new ReportData2().setRow(1L).setUserId(1000L).setEmail("<EMAIL>").setCryptoToken("GET").setApplyTime("2024-10-22 14:26:39"),
				new ReportData2().setRow(2L).setUserId(1001L).setEmail("<EMAIL>").setCryptoToken("GET").setApplyTime("2024-10-24 09:57:22")
		), CharsetUtil.MS932);
	}

	@JsonPropertyOrder({"No.", "ユーザーID", "メールアドレス", "通貨", "申込日時"})
	@Accessors(chain = true)
	@Data
	public static class ReportData2 {
		@JsonProperty("No.")
		private Long row;
		@JsonProperty("ユーザーID")
		private Long userId;
		@JsonProperty("メールアドレス")
		private String email;
		@JsonProperty("通貨")
		private String cryptoToken;
		@JsonProperty("申込日時")
		private String applyTime;
	}

	@Test
	public void test4() throws Exception {
    List<ReportData3> reportDataList =
        CsvUtil.readCsvFile(
            "/Users/<USER>/Desktop/tmp1/GetApplyUpdate.csv", ReportData3.class, Charset.forName("UTF-8"));
		System.out.println(reportDataList);
	}

	@Test
	public void test5() throws Exception {
		CsvUtil.export(new FileOutputStream("//Users/<USER>/Desktop/tmp1/update3.csv"), ReportData3.class,  Lists.newArrayList(
				new ReportData3().setUserId(4372L).setAssignedQuantity(BigDecimal.valueOf(100)).setRecipient("対象"),
				new ReportData3().setUserId(4507L).setAssignedQuantity(BigDecimal.valueOf(0)).setRecipient("申込却下"),
				new ReportData3().setUserId(4451L).setAssignedQuantity(BigDecimal.valueOf(20.45)).setRecipient("対象"),
				new ReportData3().setUserId(20L).setAssignedQuantity(BigDecimal.valueOf(2345678.334)).setRecipient("対象"),
				new ReportData3().setUserId(21L).setAssignedQuantity(BigDecimal.valueOf(0)).setRecipient("申込却下"),
				new ReportData3().setUserId(null).setAssignedQuantity(BigDecimal.valueOf(0)).setRecipient("対象"),
				new ReportData3().setUserId(4212L).setAssignedQuantity(null).setRecipient("対象"),
				new ReportData3().setUserId(4535L).setAssignedQuantity(BigDecimal.valueOf(0)).setRecipient(""),
				new ReportData3().setUserId(4538L).setAssignedQuantity(BigDecimal.valueOf(0)).setRecipient("hello"),
				new ReportData3().setUserId(4474L).setAssignedQuantity(BigDecimal.valueOf(-200)).setRecipient("対象"),
				new ReportData3().setUserId(4510L).setAssignedQuantity(BigDecimal.valueOf(234.31)).setRecipient("対象"),
				new ReportData3().setUserId(4510L).setAssignedQuantity(BigDecimal.valueOf(300)).setRecipient("対象"),
				new ReportData3().setUserId(4198L).setAssignedQuantity(BigDecimal.valueOf(0)).setRecipient("申込却下"),
				new ReportData3().setUserId(4198L).setAssignedQuantity(BigDecimal.valueOf(201)).setRecipient("対象")
				), CharsetUtil.MS932);
	}

	@JsonPropertyOrder({"UserID", "AssignedQuantity(GET)", "Recipient"})
	@Accessors(chain = true)
	@Data
	public static class ReportData3 {
		@JsonProperty("UserID")
		private Long userId;
		@JsonProperty("AssignedQuantity(GET)")
		@JsonDeserialize(using = CustomBigDecimalDeserializer.class)
		private BigDecimal assignedQuantity;
		@JsonProperty("Recipient")
		private String recipient;
	}

	public static class CustomBigDecimalDeserializer extends JsonDeserializer<BigDecimal> {

		@Override
		public BigDecimal deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
			String value = p.getText();

			if (value == null || value.isEmpty()) {
				return null;
			}

			try {
				// 使用 NumberFormat 解析带有千分位符的数值
				NumberFormat format = NumberFormat.getInstance(Locale.US);
				Number number = format.parse(value);
				return new BigDecimal(number.toString());
			} catch (ParseException e) {
				throw new IOException("Failed to parse BigDecimal value: " + value, e);
			}
		}
	}
}
