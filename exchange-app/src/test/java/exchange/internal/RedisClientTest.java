package exchange.internal;

import exchange.app.Application;
import exchange.common.component.RedisClient;
import exchange.common.util.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @author: wen.y
 * @date: 2024/10/9
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class RedisClientTest {

	@Autowired
	private RedissonClient redissonClient;

	@Autowired
	private RedisClient redisClient;

	@Test
	public void test1() {
		RLock lock = getLock("test1");
		try {
			if (!lock.tryLock()) {
				log.warn("get lock failed:{}", lock.getName());
				return;
			}
			log.info("get lock success:{}", lock.getName());
		} finally {
		   if (lock.isHeldByCurrentThread() && lock.isLocked()) {
			lock.unlock();
		   }
		}
	}

	private RLock getLock(String key) {
		return redissonClient.getLock("lock:test:".concat(key));
	}

	@Test
	public void setObject() {
    	redisClient.setObject("person:1", new Person(1, "张三"));
		redisClient.setObject("person:2", new Person(2, "李四"));
	}

	@Test
	public void getObject() {
		Person p1 = redisClient.getObject("person:1", Person.class);
		Person p2 = redisClient.getObject("person:2", Person.class);
		log.info("p1: {}", JsonUtil.encode(p1));
		log.info("p2: {}", JsonUtil.encode(p2));
	}


	@AllArgsConstructor
	@NoArgsConstructor
	@Data
	public static class Person {
		private Integer id;
		private String name;
	}
}
