aws:
  s3:
    kyc-bucket:
      name: kyc.cb-exchange-prd
    year-report-bucket:
      name: year-report.cb-exchange-prd
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
exchange-app:
  allowed-origin: https://service.coinbook.co.jp
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
server:
  servlet:
    session:
      cookie:
        secure: true
spring:
  config:
    domain: service.coinbook.co.jp
    environment: prd
  datasource:
    master:
      maximum-pool-size: 200
      leak-detection-threshold: 3000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
mail:
  message:
    account-created:
      key: ACCOUNT-CREATED
      base-url: https://service.coinbook.co.jp/exchange/?cid={0}&token={1}&exp={2}
    forgot-login-password:
      key: FORGOT-LOGIN-PASSWORD
      base-url: https://service.coinbook.co.jp/signIn/reset/?cid={0}&token={1}&exp={2}
coin:
  cus:
    host: https://service.coinbook.co.jp
    host-external: https://service.coinbook.co.jp
swagger:
  enabled: false
exchange-pos:
  best-price:
    amber:
      api-host: https://be.whalefin.com

dowjones:
  device: "coinbook-prd"
