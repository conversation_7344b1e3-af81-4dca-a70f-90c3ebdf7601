async:
  core-pool-size: 60
#  queue-capacity: 60
#  max-pool-size: 60
aws:
  credentials:
    access-key:
    secret-key:
    salt:
  ses:
    host: email-smtp.ap-northeast-1.amazonaws.com
    port: 587
    username:
    password:
  s3:
    kyc-bucket:
      name:
    year-report-bucket:
      name:
cloud:
  aws:
    credentials:
      access-key:
      secret-key:
      use-default-aws-credentials-chain: false
    region:
      auto: false
      static: ap-northeast-1
    stack:
      auto: false
common:
  log:
    outputRequestDetails: true
    console:
      appender: CONSOLE_DEFAULT
exchange-app:
  no-reply-email: <EMAIL>
  support-email: <EMAIL>
  allowed-origin: http://localhost:3000
exchange-common:
  account-lock:
    max-attempt: 5 # negative means max-value
    expire-seconds: 0 # negative means max-value
  sns:
    expired_minute: 60 # SMS認証コード有効時(分単位)
management:
  server:
    port: 8082
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,manual"
      probes:
        enabled: true
  endpoints:
    metrics:
      enabled: false
    prometheus:
      enabled: false
    web:
      base-path: /actuator
      exposure:
        include: "*"
  metrics:
    export:
      cloudwatch:
        batchSize: 20
        enabled: false
        namespace: exchange-app
        step: 1m
      prometheus:
        enabled: false
    web:
      server:
        request:
          autotime:
            # https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready-metrics-spring-mvc
            enabled: true
          metric-name: http.server.requests
postcode:
  url:
  api-key:
server:
  port: 8080
  shutdown: graceful
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: localhost
      port: 6379
  datasource:
    master:
      driver-class-name: org.mariadb.jdbc.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.MySQL5Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.tradingview.entity,exchange.pos.entity
      url: ***********************************************************************
      username: exchange
      password: Exchange123
      minimum-idle: 10
      maximum-pool-size: 66
    historical:
      driver-class-name: org.postgresql.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.PostgreSQL82Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.tradingview.entity,exchange.pos.entity
      url: *****************************************
      username: exchange
      password: Exchange123
      minimum-idle: 10
      maximum-pool-size: 66
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  main:
    banner-mode: off
  mvc:
    throw-exception-if-no-handler-found: true
  recaptcha:
    url:
    secret-key:
  servlet:
    multipart:
      max-file-size: 16MB
      max-request-size: 17MB
  session:
    store-type: redis
  web:
    resources:
      add-mappings: false

jwt:
  issuer: cb-exchange-server
  secret: **********
  token-ttl: 7600
  refresh-token-ttl: 604800
  cache-token-ttl: 7600
mail:
  message:
    account-created:
      key: ACCOUNT-CREATED
      base-url: https://service-dev.coinbook.co.jp/exchange/?cid={0}&token={1}&exp={2}
    forgot-login-password:
      key: FORGOT-LOGIN-PASSWORD
      base-url: https://service-dev.coinbook.co.jp/signIn/reset/?cid={0}&token={1}&exp={2}
customer:
  login-password:
    token:
      effective-time: ********
  register:
    enabled: true
  forgot-password:
    token:
      forgot-effective-time: 600000
  fiat-withdrawal:
    user-daily-withdrawal-limit: ********

sns:
  host: https://sand-api-smslink.nexlink2.jp/api/v1/delivery
  token: 5916f92b-d458-4aaf-b99c-0a09fd24a6a4
ekyc:
  secret: 10b94f00011817a999bb6ab46191ef33
  token: G1AQBouAWfEpHDYgw8kUMVxu
  api-auth-key: crA8fhBv9x
  enterprise-id: TENWCK0002
  transition-url: https://coinbook.co.jp/complete.html
  url:
    applicant-id: https://sand-nexwaykyc.nexlink2.jp/api/v1/applicants/profiles
    ekyc-url: https://ekyc-enter.dev-polaris.com/api/ekyc/v1/createUrl
    bpo-result: https://sand-nexwaykyc.nexlink2.jp/api/v1/applicants/%s/include_ekyc

refinitiv:
  host: api-worldcheck.refinitiv.com
  api-key: 0e3ac955-cdbf-454c-a7dc-135b480bb292
  secret-key: DvVA3G6jHJ6IP5pXFAOb94foBEVEhOzEzW3CwSUibZqC/VfGkFKRG6DI9z4DG4GQCqYvp6Zal2J5V6aq0AVU8w==
  group-id: 5jb7o7megh5v1fjy6i2pfcn0r
swagger:
  enabled: true


gmo:
  holder-name-kana: COIN
  client-id: Av1khkZhhRXB8ppG
  secret: 8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iq
  stg-base-endpoint: https://stg-api.gmo-aozora.com/ganb/api
  redirect-uri: /app/v1/gmo/oauth
  authorization-token: /auth/v1/token
  accounts-uri: /corporation/v1/accounts
  issue-account-uri: /corporation/v1/va/issue
  refresh-token-interval: 3600000 #1hour
  scope:
    - private:account #振込入金口座
    - private:virtual-account
    - private:transfer
    - private:bulk-transfer


coin:
  cus:
    host: https://service-stg.coinbook.co.jp
    host-external: https://service-stg.coinbook.co.jp

exchange-pos:
  best-price:
    amber:
      api-host: https://be-alpha.whalefin.com
      api-path: /api/v2/trade/rfq
      access-key: c7862ecc4e65dace002ab0fc3f55c82a
      access-secret: 24d0f50a26c74016f42a4319d377c06a
    coinbook:
      api-host: http://exchange-app-service.default.svc.cluster.local:8080
      api-url: /api/v1/ticker
wallet:
  staking-prepare-time: 14 #JST 14：00:00

exchange-websocket:
  subscription-limit-per-session: 100 # total subscription number for each client session
  redis-pubsub-subscriber:
    enabled: true # true: the webserver works as subscriber for redis pubsub message
  redis-pubsub-cache:
    enabled: true # true: cache the message to avoid duplicate sending
    expire-in-minutes: 5 # cache will be expired in the specified period, the message will be sent even duplicate
  stomp-broker: # external broker config
    enabled: false # Notice: true means using external broker - rabbitmq, DO NOT CHANGE after go-live
    host: localhost
    port: 61613
    client-login: guest
    client-passcode: guest
    single-consumer-lock-expired-in-seconds: 5
    heartbeat-client-send-interval-in-milliseconds: 10000 # only available when the stomp-broker.enabled = false - simp broker, change WebSocketConfig to support configuration external broker
    heartbeat-server-send-interval-in-milliseconds: 20000 # only available when the stomp-broker.enabled = false - simp broker, change WebSocketConfig to support configuration external broker
    # when external broker is enabled, the message coming from redis pubsub will be consumed by all subscribers in cluster, those subscribers will send the duplicate messages to client.
    # we make sure there will be only one subscriber sending the message who can acquire the single consumer lock.
    # the lock will be expired in 5 seconds.
    # if the message are generated as the same during the 5 seconds, only the 1st message will be sent to client, the following duplicate messages will be blocked during the lock period.

chainalysis:
  token: f0560979c09da1c011a28fa50820ebbcf12eb6cc048ea07f57b2613a4b829df9
  base-endpoint: https://api.chainalysis.com
  alert-level-limit: LOW

dowjones:
  base-auth-url: https://accounts.dowjones.com
  base-api-Url: https://api.dowjones.com
  username:
  password:
  client-id:
  search-type: PRECISE
  content-set: ["Watchlist"]
  filter-content-category: ["WL"]
  count: 300