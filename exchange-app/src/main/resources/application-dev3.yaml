aws:
  s3:
    kyc-bucket:
      name: kyc.cb-exchange-dev3
    year-report-bucket:
      name: year-report.cb-exchange-dev3
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
exchange-app:
  support-email: <EMAIL>
  allowed-origin: https://exchange.dev.cxr-inc.com
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: service-dev3.coinbook.co.jp
    environment: dev3
  datasource:
    master:
      maximum-pool-size: 150
      leak-detection-threshold: 3000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
mail:
  message:
    account-created:
      base-url: https://service-dev3.coinbook.co.jp/exchange/?cid={0}&token={1}&exp={2}
    forgot-login-password:
      base-url: https://service-dev3.coinbook.co.jp/signIn/reset/?cid={0}&token={1}&exp={2}

dowjones:
  device: "coinbook-dev3"
