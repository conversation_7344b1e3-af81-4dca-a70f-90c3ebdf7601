server:
  port: 8088
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: localhost
      port: 6379
  datasource:
    master:
      url: ***********************************************************************
      username: exchange
      password: Exchange123
    historical:
      url: *****************************************
      username: exchange
      password: Exchange123
exchange-websocket:
  redis-pubsub-cache:
    enabled: false # true: cache the message to avoid duplicate sending