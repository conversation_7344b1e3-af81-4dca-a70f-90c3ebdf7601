aws:
  s3:
    kyc-bucket:
      name: kyc.cb-exchange-dev
    year-report-bucket:
      name: year-report.cb-exchange-dev
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
exchange-app:
  support-email: <EMAIL>
  allowed-origin: https://exchange.dev.cxr-inc.com
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: service-dev.coinbook.co.jp
    environment: dev
  datasource:
    master:
      leak-detection-threshold: 5000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
jwt:
  secret: aXKKijtq$8_>^bJU@{Jk&jOS)cy>3yPU

dowjones:
  device: "coinbook-dev1"
  username: <EMAIL>
  password: F98dgFjXVJuqvVNk
  client-id: Z1lpD6emDa3G2o9CVtd3tUnpuUKw6VciLvJ9XQjn