package exchange.app.controller;

import exchange.common.entity.User;
import exchange.common.entity.cryptoToken.CryptoTokenAsset;
import exchange.common.entity.cryptoToken.CryptoTokenConfig;
import exchange.common.model.response.cryptoToken.CryptoTokenAssetResponse;
import exchange.common.service.CryptoTokenAssetService;
import exchange.common.service.CryptoTokenConfigService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/crypto-token/asset")
@Timed
public class V1CryptoTokenAssetRestController {

	private final CryptoTokenConfigService cryptoTokenConfigService;
	private final CryptoTokenAssetService cryptoTokenAssetService;

	@GetMapping("/list")
	public ResponseEntity<List<CryptoTokenAssetResponse>> list(@AuthenticationPrincipal User user) throws Exception {
		List<CryptoTokenAssetResponse> assetResponseList = new ArrayList<>();

		List<CryptoTokenConfig> cryptoTokenConfigList = cryptoTokenConfigService.findAllByCondition(null, true);
		if (CollectionUtils.isEmpty(cryptoTokenConfigList)) {
			return ResponseEntity.ok(assetResponseList);
		}

		for (CryptoTokenConfig cryptoTokenConfig : cryptoTokenConfigList) {
			// CryptoAsset無しの場合は初期値で作成
			CryptoTokenAsset cryptoTokenAsset = cryptoTokenAssetService.findOrCreate(user.getId(), cryptoTokenConfig.getCryptoToken());

			CryptoTokenAssetResponse assetResponse = new CryptoTokenAssetResponse();
			assetResponse.setCryptoToken(cryptoTokenAsset.getCryptoToken());
			assetResponse.setOnhandAmount(cryptoTokenAsset.getOnhandAmount());
			assetResponse.setUserId(cryptoTokenAsset.getUserId());
			assetResponseList.add(assetResponse);
		}

		return ResponseEntity.ok(assetResponseList);
	}
}
