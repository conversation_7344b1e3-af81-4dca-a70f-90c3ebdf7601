package exchange.app.controller;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import exchange.app.component.jwt.JwtManager;
import exchange.common.component.RedisManager;
import exchange.common.constant.CommonConstants;
import exchange.common.constant.ErrorCode;
import exchange.common.exception.RefreshTokenException;
import exchange.common.service.UserService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/refresh-token")
@Timed
public class V1RefreshTokenController {

    private final JwtManager jwtManager;
    private final UserService userService;
    private final RedisManager redisManager;
    @PostMapping
    public ResponseEntity<Map<String, String>> refreshToken(@RequestBody Map<String, String> tokenMap) throws RefreshTokenException {

        if(CollectionUtils.isEmpty(tokenMap) || !tokenMap.containsKey(JwtManager.REFRESH_TOKEN)) {
            throw new RefreshTokenException(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE);
        }

        String refreshToken = tokenMap.get(JwtManager.REFRESH_TOKEN);
        String token = tokenMap.get(JwtManager.TOKEN);
        log.info("this refresh token: {}", refreshToken);
        log.info("this jwt token: {}", token);
        try {
            if(StringUtils.isNotEmpty(refreshToken)) {
                DecodedJWT decodedJWT = jwtManager.decodedJWT(refreshToken);
                if (decodedJWT == null) {
                    log.info("jwt refreshToken cannot parse: {}", token);
                    throw new AuthenticationServiceException(
                        Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
                }
                Map<String, Claim> claims = decodedJWT.getClaims();
                Claim claim = claims.get(CommonConstants.USER_ID);
                Long userId  = claim.asLong();
                String key = CommonConstants.REDIS_USER_TOCKN_PREFIX + userId;
                String oldRedisToken = redisManager.hget(key,JwtManager.REFRESH_TOKEN);
                if(!refreshToken.equals(oldRedisToken)){
                    throw new RefreshTokenException(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE);
                }
            }
            return ResponseEntity.ok(jwtManager.genericToken(refreshToken, userService::findOne));
        }catch (IllegalArgumentException e) {
            log.warn("refresh token error: {}", e.getMessage());
            throw new RefreshTokenException(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE);
        }
    }
}
