package exchange.app.controller;

import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import exchange.app.component.MfaManager;
import exchange.app.model.request.FiatWithdrawalOtpauthPostForm;
import exchange.app.model.request.FiatWithdrawalPostForm;
import exchange.common.component.SesManager;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.FiatWithdrawalStatus;
import exchange.common.constant.MailNoreplyType;
import exchange.common.entity.Asset;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.response.MfaTypeData;
import exchange.common.service.AssetService;
import exchange.common.service.BankAccountService;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.FiatWithdrawalService;
import exchange.common.service.MailNoreplyService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/fiat-withdrawal")
@Timed
@Slf4j
public class V1FiatWithdrawalRestController {

  private final AssetService assetService;

  private final BankAccountService bankAccountService;

  private final CurrencyConfigService currencyConfigService;

  private final FiatWithdrawalService fiatWithdrawalService;
  
  private final MailNoreplyService mailNoreplyService;

  private final MfaManager mfaManager;
  
  private final SesManager sesManager;
  
  @Value("${customer.fiat-withdrawal.user-daily-withdrawal-limit:********}")
  private String userDailyWithdrawalLimit;

  private void validatePost(User user, FiatWithdrawalOtpauthPostForm form) throws Exception {
    if (bankAccountService.findOne(form.getBankAccountId()) == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_BANK_ACCOUNT_NOT_FOUND);
    }

    if (!user.isWithdrawable()) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_IS_NOT_WITHDRAWABLE);
    }

    CurrencyConfig currencyConfig = currencyConfigService.findByCurrency(Currency.JPY);
    
    if (!currencyConfig.isWithdrawable() || !currencyConfig.isEnabled()) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_IS_NOT_WITHDRAWABLE);
    }

    if (form.getAmount().compareTo(currencyConfig.getMinWithdrawalAmount()) == -1) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE);
    }

    Asset asset = assetService.findOne(user.getId(), Currency.JPY);

    if (asset.getOnhandAmount().subtract(asset.getLockedAmount()).compareTo(form.getAmount())
        == -1) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE);
    }
    
    isOverUserDailyWithdrawalLimit(user.getId(), form.getAmount());
  }

  /**
   * 利用者ごとの合計一日出金上限額を超えるかどうかをチェック
   * @param id
   * @param amount 
   * @throws CustomException 
   */
  private void isOverUserDailyWithdrawalLimit(Long id, BigDecimal amount) throws CustomException {
    
    BigDecimal sumTodayAmount = fiatWithdrawalService.getSumToday(id);
    // 今回の出金額+今日既に登録した出金額の合計 > 利用者ごとの合計一日出金上限額
    if(amount.add(sumTodayAmount).compareTo(new BigDecimal(userDailyWithdrawalLimit)) > 0) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE_USER_TODAY);
    }
  }

  @PostMapping("/otpauth")
  public ResponseEntity<MfaTypeData> postOtpauth(
      @AuthenticationPrincipal User user, @Valid @RequestBody FiatWithdrawalOtpauthPostForm form)
      throws Exception {
    validatePost(user, form);
    return mfaManager.responseMfaTypeData(user);
  }

  @PostMapping
  public ResponseEntity<FiatWithdrawal> post(
      @AuthenticationPrincipal User user, @Valid @RequestBody FiatWithdrawalPostForm form)
      throws Exception {
    mfaManager.authenticate(user, form.getMfaCode());
    validatePost(user, form);

    FiatWithdrawal fiatWithdrawal = new FiatWithdrawal();
    fiatWithdrawal.setUserId(user.getId());
    fiatWithdrawal.setBankAccountId(form.getBankAccountId());
    fiatWithdrawal.setAmount(form.getAmount());
    fiatWithdrawal.setFee(getFee(form.getAmount().intValue()));
    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING);
    fiatWithdrawal.setUpdatedBy("USER");
    FiatWithdrawal fiatWithdrawalRes = fiatWithdrawalService.request(fiatWithdrawal);
    // 出金申請受付のお知らせを送信
    MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.FIAT_WITHDRAWAL_APPLY);
    boolean send = sesManager.send(mailNoreply.getFromAddress(), user.getEmail(),
        mailNoreply.getTitle(), mailNoreply.getContents());
    if (!send) {
      log.info("register user invoke the email service Exception");
      throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
    }
    return ResponseEntity.ok(fiatWithdrawalRes);
  }

  // 手数料
  private BigDecimal getFee(Integer amount) {
    List<CurrencyConfig> currencyConfigList = currencyConfigService
    .findAllByCondition(Currency.JPY, true);
    Integer resultFee = 0;
    if (currencyConfigList != null && currencyConfigList.size() > 0) {
      resultFee = currencyConfigList.get(0).getWithdrawalFee().intValue();
    }
    
    return BigDecimal.valueOf(resultFee);
  }
}
