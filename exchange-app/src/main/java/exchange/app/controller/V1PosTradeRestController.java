package exchange.app.controller;

import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.PosTradeReportData;
import exchange.common.model.response.SpotTradeReportData;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.SymbolService;
import exchange.common.service.UserService;
import exchange.common.util.DateUnit;
import exchange.pos.entity.PosTrade;
import exchange.pos.service.PosTradeService;
import io.micrometer.core.annotation.Timed;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Timed
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/v1/pos/trade")
public class V1PosTradeRestController {

  private final SymbolService symbolService;

  private final CurrencyPairConfigService currencyPairConfigService;

  private final PosTradeService posTradeService;

  private final UserService userService;

  private final CsvDownloadManager<PosTradeReportData> downloadManager;

  @GetMapping("/page")
  public ResponseEntity<PageData<PosTrade>> getPage(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "number", defaultValue = ""
          + ViewVariables.DEFAULT_NUMBER) Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    PageData<PosTrade> pg = new PageData<PosTrade>(number, size, 0, null);
    if (size == 0) {
      return ResponseEntity.ok(pg);
    }

    if (symbolId == null || user == null) {
      return ResponseEntity.ok(pg);
    }

    Symbol symbol = symbolService.findOne(symbolId);

    Long userId = user.getId();
    if (symbol == null || userId == null) {
      return ResponseEntity.ok(pg);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.POS, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(pg);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    // 取引履歴取得
    PageData<PosTrade> spotTrades =
        posTradeService.findByConditionPageData(
            symbolId,
            userId,
            id,
            idFrom,
            idTo,
            dateFrom,
            dateTo,
            null,
            orderSide,
            orderType,
            orderTypes,
            exceptOrderTypes,
            number,
            size,
            false);
    return ResponseEntity.ok(spotTrades);
  }

   @GetMapping("/page/history")
  public ResponseEntity<PageData<PosTrade>> getPageHistory(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "number", defaultValue = ""
          + ViewVariables.DEFAULT_NUMBER) Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    PageData<PosTrade> pg = new PageData<PosTrade>(number, size, 0, null);
    if (size == 0) {
      return ResponseEntity.ok(pg);
    }

    if (symbolId == null || user == null) {
      return ResponseEntity.ok(pg);
    }

    Symbol symbol = symbolService.findOne(symbolId);

    Long userId = user.getId();
    if (symbol == null || userId == null) {
      return ResponseEntity.ok(pg);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.POS, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(pg);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    // 取引履歴取得
    List<PosTrade> posTrades =
        posTradeService.findByCondition(
            symbolId,
            userId,
            id,
            idFrom,
            idTo,
            dateFrom,
            dateTo,
            null,
            orderSide,
            orderType,
            orderTypes,
            exceptOrderTypes,
            0,
            Integer.MAX_VALUE,
            false);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

    List<PosTrade> spotTradesHistory =
        posTradeService
            .findAllFromHistory(
                symbol,
                userId,
                id,
                idFrom,
                idTo,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                dateFromDate,
                dateToDate);

    // merge & sort
    posTrades.addAll(spotTradesHistory);
    // order by id desc
    posTrades.sort((x, y) -> x.getId().compareTo(y.getId()) * -1);

    // PageData作成
    //   spotTrades + spotTradesHistory合算後count
    Long count = (long) posTrades.size();
    pg = posTradeService.createPageData(posTrades, count, number, size);

    return ResponseEntity.ok(pg);
  }

  @GetMapping("/download")
  public String download(
      HttpServletResponse response,
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes)
      throws Exception {
    Long userId = user.getId();
    if (userService.findOne(userId) == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }

    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null || userId == null) {
      return null;
    }
    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs = currencyPairConfigService.findAllByCondition(TradeType.POS, symbol.getCurrencyPair(), true);
    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return null;
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<PosTradeReportData> posTradeReports = new ArrayList<PosTradeReportData>();

    // 取引履歴取得
    List<PosTrade> posTrades =
       posTradeService.findByCondition(
                symbolId,
                userId,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                null,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                0,
                Integer.MAX_VALUE,
                false);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<PosTrade> spotTradesHistory =
        posTradeService
            .findAllFromHistory(
                symbol,
                userId,
                id,
                idFrom,
                idTo,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                dateFromDate,
                dateToDate);

    // merge & sort
    posTrades.addAll(spotTradesHistory);
    // order by id desc
    posTrades.sort((x, y) -> x.getId().compareTo(y.getId()) * -1);

    for (PosTrade posTrade : posTrades) {
      posTradeReports.add(
          new PosTradeReportData().setProperties(posTrade, symbol.getCurrencyPair()));
    }
    downloadManager.download(
        response, posTradeReports, "posTrades", PosTradeReportData.getReportHeader());
    return null;
  }
}
