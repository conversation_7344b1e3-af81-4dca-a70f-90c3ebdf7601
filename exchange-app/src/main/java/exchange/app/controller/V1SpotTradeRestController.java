package exchange.app.controller;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.component.CsvDownloadManager;
import exchange.common.component.CustomLogger;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SpotTradeReportData;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.SymbolService;
import exchange.common.service.UserService;
import exchange.common.util.DateUnit;
import exchange.spot.entity.SpotTrade;
import exchange.spot.service.SpotTradeService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/spot/trade")
@Timed
public class V1SpotTradeRestController {

  private final SymbolService symbolService;
  private final UserService userService;
  private final CurrencyPairConfigService currencyPairConfigService;
  private final CsvDownloadManager<SpotTradeReportData> downloadManager;

  private static final CustomLogger log =
      new CustomLogger(V1SpotTradeRestController.class.getName());

  @GetMapping
  public ResponseEntity<List<SpotTrade>> get(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = false) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (symbolId != null && symbolId != 0) {
      List<SpotTrade> symbolList = new ArrayList<>();

      Symbol symbol = symbolService.findOne(symbolId);
      if (symbol == null) {
        return ResponseEntity.ok(symbolList);
      }

      // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
      List<CurrencyPairConfig> currencyPairConfigs =
          currencyPairConfigService.findAllByCondition(
              TradeType.SPOT, symbol.getCurrencyPair(), true);

      if (CollectionUtils.isEmpty(currencyPairConfigs)) {
        return ResponseEntity.ok(symbolList);
      }

      symbolList =
          SpotTradeService.getBean(symbol)
              .findByCondition(
                  symbolId,
                  user.getId(),
                  id,
                  idFrom,
                  idTo,
                  dateFrom,
                  dateTo,
                  null,
                  orderSide,
                  orderType,
                  orderTypes,
                  exceptOrderTypes,
                  number,
                  size,
                  false);

      // 約定相手の情報を隠す
      stripTargetInfo(symbolList);

      return ResponseEntity.ok(symbolList);
    } else {
      List<SpotTrade> allList = new ArrayList<>();

      // 有効(enabled=true)な通貨ペアのリスト
      currencyPairConfigService
          .findAllByCondition(TradeType.SPOT, null, true)
          .forEach(
              currencyPairConfig -> {
                Symbol symbol =
                    symbolService.findByCondition(
                        TradeType.SPOT, currencyPairConfig.getCurrencyPair());

                if (symbol != null) {
                  allList.addAll(
                      SpotTradeService.getBean(symbol)
                          .findByCondition(
                              symbol.getId(),
                              user.getId(),
                              id,
                              idFrom,
                              idTo,
                              dateFrom,
                              dateTo,
                              null,
                              orderSide,
                              orderType,
                              orderTypes,
                              exceptOrderTypes,
                              number,
                              size,
                              true));
                }
              });

      // 約定相手の情報を隠す
      stripTargetInfo(allList);

      return ResponseEntity.ok(allList);
    }
  }

  @GetMapping("/history")
  public ResponseEntity<List<SpotTrade>> getFromHistory(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = false) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    Long dateToDate = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    if (symbolId != null && symbolId != 0) {
      log.info(this.getClass().getSimpleName(), "get trades for symbol-id:" + symbolId);
      List<SpotTrade> historicalSymbolList = new ArrayList<>();
      Symbol symbol = symbolService.findOne(symbolId);
      if (symbol == null) {
        return ResponseEntity.ok(historicalSymbolList);
      }

      // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
      List<CurrencyPairConfig> currencyPairConfigs =
          currencyPairConfigService.findAllByCondition(
              TradeType.SPOT, symbol.getCurrencyPair(), true);

      if (CollectionUtils.isEmpty(currencyPairConfigs)) {
        return ResponseEntity.ok(historicalSymbolList);
      }

      historicalSymbolList =
          SpotTradeService.getBean(symbol)
              .findFromHistory(
                  symbol,
                  user.getId(),
                  orderSide,
                  orderType,
                  null,
                  new Date(dateFrom),
                  new Date(dateToDate),
                  number,
                  size);

      // 約定相手の情報を隠す
      stripTargetInfo(historicalSymbolList);

      return ResponseEntity.ok(historicalSymbolList);
    } else {
      log.info(this.getClass().getSimpleName(), "get trades for all symbol-ids");
      List<SpotTrade> historicalAllList = new ArrayList<>();

      // 有効(enabled=true)な通貨ペアのリスト
      currencyPairConfigService
          .findAllByCondition(TradeType.SPOT, null, true)
          .forEach(
              currencyPairConfig -> {
                Symbol symbol =
                    symbolService.findByCondition(
                        TradeType.SPOT, currencyPairConfig.getCurrencyPair());

                if (symbol != null) {
                  historicalAllList.addAll(
                      SpotTradeService.getBean(symbol)
                          .findFromHistory(
                              symbol,
                              user.getId(),
                              orderSide,
                              orderType,
                              null,
                              new Date(dateFrom),
                              new Date(dateToDate),
                              number,
                              size));
                }
              });

      // 約定相手の情報を隠す
      stripTargetInfo(historicalAllList);

      return ResponseEntity.ok(historicalAllList);
    }
  }

  @GetMapping("/all")
  public ResponseEntity<List<SpotTrade>> getAll(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    List<SpotTrade> spotTrades = new ArrayList<>();
    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null) {
      return ResponseEntity.ok(spotTrades);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(spotTrades);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    // 取引履歴取得
    spotTrades =
        SpotTradeService.getBean(symbol)
            .findAllByCondition(
                symbolId,
                user.getId(),
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                null,
                orderSide,
                orderType);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<SpotTrade> spotTradesHistory =
        SpotTradeService.getBean(symbol)
            .findAllFromHistory(
                symbol,
                user.getId(),
                id,
                idFrom,
                idTo,
                orderSide,
                orderType,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                dateFromDate,
                dateToDate);

    // merge & sort
    spotTrades.addAll(spotTradesHistory);
    // order by id desc
    spotTrades.sort((x, y) -> x.getId().compareTo(y.getId()) * -1);

    int count = spotTrades.size();
    int offset = number * size;

    if (count <= offset) {
      return ResponseEntity.ok(new ArrayList<>());
    }
    size = Math.min(count - offset, size);
    List<SpotTrade> subList = spotTrades.subList(offset, offset + size);

    // 約定相手の情報を隠す
    stripTargetInfo(subList);

    return ResponseEntity.ok(subList);
  }

  @GetMapping("/page")
  public ResponseEntity<PageData<SpotTrade>> getPage(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    PageData<SpotTrade> pg = new PageData<SpotTrade>(number, size, 0, null);

    if (size == 0) {
      return ResponseEntity.ok(pg);
    }

    if (symbolId == null || user == null) {
      return ResponseEntity.ok(pg);
    }

    Symbol symbol = symbolService.findOne(symbolId);

    Long userId = user.getId();
    if (symbol == null || userId == null) {
      return ResponseEntity.ok(pg);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(pg);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    // 取引履歴取得
    List<SpotTrade> spotTrades =
        SpotTradeService.getBean(symbol)
            .findByCondition(
                symbolId,
                userId,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                null,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                0,
                Integer.MAX_VALUE,
                false);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<SpotTrade> spotTradesHistory =
        SpotTradeService.getBean(symbol)
            .findAllFromHistory(
                symbol,
                userId,
                id,
                idFrom,
                idTo,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                dateFromDate,
                dateToDate);

    // merge & sort
    spotTrades.addAll(spotTradesHistory);
    // order by id desc
    spotTrades.sort((x, y) -> x.getId().compareTo(y.getId()) * -1);

    // 約定相手の情報を隠す
    stripTargetInfo(spotTrades);

    // PageData作成
    //   spotTrades + spotTradesHistory合算後count
    Long count = (long) spotTrades.size();
    pg = SpotTradeService.getBean(symbol).createPageData(spotTrades, count, number, size);

    return ResponseEntity.ok(pg);
  }

  // 約定相手の情報を隠す
  private void stripTargetInfo(List<SpotTrade> list) {
    list.forEach(
        (spotTrade) -> {
          // 約定相手のidを隠す
          spotTrade.setTargetUserId(null);
          // 約定相手の注文idを隠す
          spotTrade.setTargetOrderId(null);
        });
  }

  @GetMapping("/download")
  public String download(
      HttpServletResponse response,
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes)
      throws Exception {
    Long userId = user.getId();
    if (userService.findOne(userId) == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }

    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null || userId == null) {
      return null;
    }
    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);
    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return null;
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<SpotTradeReportData> spotTradeReports = new ArrayList<SpotTradeReportData>();

    // 取引履歴取得
    List<SpotTrade> spotTrades =
        SpotTradeService.getBean(symbol)
            .findByCondition(
                symbolId,
                userId,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                null,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                0,
                Integer.MAX_VALUE,
                false);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<SpotTrade> spotTradesHistory =
        SpotTradeService.getBean(symbol)
            .findAllFromHistory(
                symbol,
                user.getId(),
                id,
                idFrom,
                idTo,
                orderSide,
                orderType,
                orderTypes,
                exceptOrderTypes,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                dateFromDate,
                dateToDate);

    // merge & sort
    spotTrades.addAll(spotTradesHistory);
    List<SpotTrade> spotTradesSorted =
        spotTrades
            .stream()
            .sorted(Comparator.comparing(SpotTrade::getCreatedAt).reversed())
            .collect(Collectors.toList());

    // 約定相手の情報を隠す
    stripTargetInfo(spotTradesSorted);
    for (SpotTrade spotTrade : spotTradesSorted) {
      spotTradeReports.add(
          new SpotTradeReportData().setProperties(spotTrade, symbol.getCurrencyPair()));
    }
    downloadManager.download(
        response, spotTradeReports, "spotTrades", SpotTradeReportData.getReportHeader());
    return null;
  }
}
