package exchange.app.controller;

import exchange.common.entity.UserAuthority;
import exchange.common.entity.UserInfoCorporateAgent;
import exchange.common.entity.UserInfoCorporateRepresentative;
import exchange.common.service.SMSAttemptService;
import exchange.common.service.UserAuthorityService;
import exchange.common.service.UserInfoCorporateAgentService;
import exchange.common.service.UserInfoCorporateRepresentativeService;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.app.component.MfaManager;
import exchange.app.model.request.UserMfaOtpauthPostForm;
import exchange.app.model.request.UserMfaPostForm;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.MfaType;
import exchange.common.entity.User;
import exchange.common.entity.UserMfa;
import exchange.common.exception.CustomException;
import exchange.common.model.response.MfaTypeData;
import exchange.common.model.response.UriData;
import exchange.common.service.UserMfaService;
import exchange.common.service.UserService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/user-mfa")
public class V1UserMfaRestController {

  private final MfaManager mfaManager;

  private final UserMfaService userMfaService;

  private final UserService userService;

  private final SMSAttemptService smsAttemptService;

  private final UserAuthorityService userAuthorityService;

  private final UserInfoCorporateRepresentativeService userInfoCorporateRepresentativeService;

  private final UserInfoCorporateAgentService userInfoCorporateAgentService;

  @PostMapping("/otpauth-uri")
  public ResponseEntity<UriData> postOtpauthUri(@AuthenticationPrincipal User user)
      throws Exception {
    return ResponseEntity.ok(new UriData(mfaManager.getOtpauthUri(user)));
  }

  /**
   * sent mfa_code
   * @param user
   * @param form
   * @return
   * @throws Exception
   */
  @PostMapping("/otpauth")
  public ResponseEntity<MfaTypeData> postOtpauth(
      @AuthenticationPrincipal User user, @Valid @RequestBody UserMfaOtpauthPostForm form)
      throws Exception {
    String phoneNumber = getPhoneNumber(user);
    if (smsAttemptService.isLimitExceeded(user.getId(), phoneNumber)) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_UPPER_LIMIT);
    }
    UserMfa userMfa = userMfaService.findByCondition(user.getId(), form.getMfaType());

    user = userService.findOne(user.getId());

    switch (form.getMfaType()) {
      case GOOGLE:
        if (userMfa != null && userMfa.isAuthenticated()) {
          throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_EXIST);
        }
        break;
      case SMS:
        if (userMfa != null) {
          throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_EXIST);
        }
        break;
      default:
        throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_MFA_TYPE);
    }
    ResponseEntity<MfaTypeData> mfaTypeDataResponseEntity = mfaManager.responseMfaTypeData(
        user.getEmail(), form.getMfaType());
    // Record the number of times after the SMS is sent
    smsAttemptService.countSmsSent(user.getId(), phoneNumber);
    return mfaTypeDataResponseEntity;
  }

  /**
   * validate mfa_code
   * @param user
   * @param form
   * @return
   * @throws Exception
   */
  @PostMapping
  public ResponseEntity<Object> post(
      @AuthenticationPrincipal User user, @Valid @RequestBody UserMfaPostForm form)
      throws Exception {
    mfaManager.authenticate(user, form.getMfaType(), form.getMfaCode());
    switch (form.getMfaType()) {
      case GOOGLE -> {
        UserMfa userMfa = userMfaService.findByCondition(user.getId(), form.getMfaType());
        if (userMfa == null) {
          throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_NOT_FOUND);
        }
        userMfa.setAuthenticated(true);
        userMfaService.save(userMfa);
      }
      case SMS -> userMfaService.save(new UserMfa(user.getId(), form.getMfaType(), null, true));
      default -> throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_MFA_TYPE);
    }

    return ResponseEntity.ok().build();
  }

  private UserMfa validateDelete(User user, MfaType mfaType) throws Exception {
    UserMfa userMfa = userMfaService.findByCondition(user.getId(), mfaType);

    if (userMfa == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_NOT_FOUND);
    }

    return userMfa;
  }

  @DeleteMapping("/otpauth")
  public ResponseEntity<MfaTypeData> deleteOtpauth(
      @AuthenticationPrincipal User user, @RequestParam(value = "mfaType") MfaType mfaType)
      throws Exception {
    String phoneNumber = getPhoneNumber(user);
    if (smsAttemptService.isLimitExceeded(user.getId(), phoneNumber)) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_MFA_UPPER_LIMIT);
    }
    validateDelete(user, mfaType);
    ResponseEntity<MfaTypeData> mfaTypeDataResponseEntity = mfaManager.responseMfaTypeData(
        user.getEmail(), mfaType);
    // Record the number of times after the SMS is sent
    smsAttemptService.countSmsSent(user.getId(), phoneNumber);
    return mfaTypeDataResponseEntity;
  }

  @DeleteMapping
  public ResponseEntity<Object> delete(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "mfaType") MfaType mfaType,
      @RequestParam(value = "mfaCode") String mfaCode)
      throws Exception {
    mfaManager.authenticate(user, mfaType, mfaCode);
    UserMfa userMfa = validateDelete(user, mfaType);
    userMfaService.delete(userMfa);
    return ResponseEntity.ok().build();
  }

  private String getPhoneNumber(User user) throws CustomException {
    String phoneNumber;
    UserAuthority userAuthority = userAuthorityService.findByUserId(user.getId());
    if (userAuthority.isPersonal()) {
      if (user.getUserInfo() == null) {
        throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_IS_NULL);
      }
      phoneNumber = user.getUserInfo().getPhoneNumber();
      return phoneNumber;
    } else if (userAuthority.isCorporate()) {
      if (user.getUserInfoCorporate() == null) {
        throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_IS_NULL);
      }
      UserInfoCorporateRepresentative userInfoCorporateRepresentative = userInfoCorporateRepresentativeService.findOne(
          user.getUserInfoCorporate().getRepresentativeId());
      phoneNumber = userInfoCorporateRepresentative.getPhoneNumber();
      UserInfoCorporateAgent userInfoCorporateAgent = userInfoCorporateAgentService.findOne(
          user.getUserInfoCorporate().getAgentId());

      if (userInfoCorporateAgent != null) {
        phoneNumber = userInfoCorporateAgent.getPhoneNumber();
      }
      return phoneNumber;
    } else {
      throw new CustomException(ErrorCode.REQUEST_ERROR_NOTSETUP_PHONE_NUMBER);
    }
  }
}
