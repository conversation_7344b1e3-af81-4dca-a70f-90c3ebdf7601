package exchange.app.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.config.IconConfig;
import exchange.common.constant.Currency;
import exchange.common.controller.AbstractRestController;
import exchange.common.model.response.CurrencyData;
import exchange.common.service.CurrencyConfigService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/currency")
@Timed
public class V1CurrencyRestController extends AbstractRestController {

  private final CurrencyConfigService currencyConfigService;
  private final IconConfig iconConfig;

  @GetMapping
  public ResponseEntity<List<CurrencyData>> get(HttpServletResponse response) {
    setCacheControlForPublic(response);

    // 有効な(enabled=true)通貨のみ
    // すべて無効の場合は空リストを返す
    return ResponseEntity.ok(
        currencyConfigService
            .findAllByCondition(null, true)
            .stream()
            .map(
                currencyConfig ->
                    new CurrencyData(
                        currencyConfig,
                    Currency.valueOf(currencyConfig.getCurrency().name()).getPrecision(),
                    iconConfig.getIconUrl(currencyConfig.getCurrency().name())))
            .collect(Collectors.toList()));
  }
}
