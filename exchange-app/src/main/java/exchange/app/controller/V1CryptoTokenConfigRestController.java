package exchange.app.controller;

import exchange.common.constant.CryptoToken;
import exchange.common.constant.ErrorCode;
import exchange.common.entity.cryptoToken.CryptoTokenConfig;
import exchange.common.exception.CustomException;
import exchange.common.model.response.cryptoToken.CryptoTokenConfigResponse;
import exchange.common.service.CryptoTokenConfigService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/crypto-token/config")
@Timed
public class V1CryptoTokenConfigRestController {

	private final CryptoTokenConfigService cryptoTokenConfigService;

	@GetMapping("/{cryptoToken}/detail")
	public ResponseEntity<CryptoTokenConfigResponse> detail(@PathVariable("cryptoToken") String cryptoTokenLabel)  throws Exception {
		CryptoToken cryptoToken = CryptoToken.ofLabel(cryptoTokenLabel);
		if (null == cryptoToken) {
			throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_CRYPTO_TOKEN);
		}
		CryptoTokenConfig cryptoTokenConfig = cryptoTokenConfigService.detail(cryptoToken);
		if (null == cryptoTokenConfig) {
			throw new CustomException(ErrorCode.REQUEST_ERROR_CRYPTO_TOKEN_NOT_FOUND);
		}
		return ResponseEntity.ok(
			new CryptoTokenConfigResponse(cryptoTokenConfig.getCryptoToken(), cryptoTokenConfig.getOpenApplyEndTime())
		);
	}
}
