package exchange.app.controller;

import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import exchange.app.model.request.TransferOTCPostForm;
import exchange.app.model.request.TransferPostForm;
import exchange.common.constant.ErrorCode;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.DepositAccount;
import exchange.common.entity.User;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.exception.CustomException;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.DepositAccountService;
import exchange.common.service.DepositService;
import exchange.common.service.OtcConfigService;
import exchange.common.service.WithdrawalAccountService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/transfer")
@Timed

public class V1TransferRestController {

  private final CurrencyConfigService currencyConfigService;

  private final WithdrawalAccountService withdrawalAccountService;

  private final DepositService depositService;

  private final DepositAccountService depositAccountService;

  private final OtcConfigService otcConfigService;


  @PostMapping
  public ResponseEntity<Object> post(@AuthenticationPrincipal User user,
      @Valid @RequestBody TransferPostForm form) throws Exception {
    if (!user.isWithdrawable()) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_IS_NOT_WITHDRAWABLE);
    }

    WithdrawalAccount withdrawalAccount = withdrawalAccountService.findOne(form.getTransferAccountId());
    
    List<DepositAccount> depositAccount = depositAccountService.findByAddress(withdrawalAccount.getAddress(), withdrawalAccount.getCurrency().toString());
    
    if (depositAccount == null || depositAccount.isEmpty()) {
    	throw new CustomException(ErrorCode.REQUEST_ERROR_ADDRESS_WRITE);
    }
    
    CurrencyConfig currencyConfig =
        currencyConfigService.findByCurrency(withdrawalAccount.getCurrency());
    validatePost(user, currencyConfig, form);

    depositService.transferCreate(user, currencyConfig, form.getAmount(), form.getComment(), 
        form.getTransferAccountId(), withdrawalAccount);

    return ResponseEntity.ok().build();
  }

  private void validatePost(User user, CurrencyConfig currencyConfig, TransferPostForm form)
      throws Exception {
    if (!user.isWithdrawable()) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_IS_NOT_WITHDRAWABLE);
    }

    // 振替最小金額チェック
    if (form.getAmount().compareTo(BigDecimal.ZERO) < 0) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_OUT_OF_RANGE);
    }
  }

  @PostMapping("/OTC")
  public ResponseEntity<Boolean> post(@AuthenticationPrincipal User user,
      @Valid @RequestBody TransferOTCPostForm form) throws Exception {
    Boolean flg = false;
    if (!user.isWithdrawable()) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_IS_NOT_WITHDRAWABLE);
    }

    if (depositAccountService.findByAddress(form.getOtcaddress(), null).size() == 0) {
      flg = false;
      throw new CustomException(ErrorCode.REQUEST_ERROR_OTC_DEPOSIT_ACCOUNT_NOT_FOUND);
    } else {
      depositService.transferOtcCreate(user,
          otcConfigService.findByAddress(form.getOtcaddress()).get(0).getCurrency(),
          form.getAmount(), form.getOtcaddress());
      flg = true;
    }

    return ResponseEntity.ok(flg);
  }
}
