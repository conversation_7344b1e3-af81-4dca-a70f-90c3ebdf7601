package exchange.app.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.config.IconConfig;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.controller.AbstractRestController;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.model.response.CurrencyPairData;
import exchange.common.model.response.CurrencyPairDisplayData;
import exchange.common.service.CurrencyPairConfigService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/currency-pair")
public class V1CurrencyPairRestController extends AbstractRestController {

  private final CurrencyPairConfigService currencyPairConfigService;
  private final IconConfig iconConfig;

  @GetMapping
  public ResponseEntity<List<CurrencyPairData>> get(
      HttpServletResponse response,
      @RequestParam(value = "tradeType", required = false) TradeType tradeType,
      @RequestParam(value = "currencyPair", required = false) CurrencyPair currencyPair)
      throws Exception {
    setCacheControlForPublic(response);

    // // 有効な(enabled=true)通貨ペアのconfigのみ取得
    return ResponseEntity.ok(currencyPairConfigService
        .findAllByCondition(tradeType, currencyPair, true).stream()
        .map(currencyPairConfig -> new CurrencyPairData(currencyPairConfig,
            iconConfig.getIconUrl(currencyPairConfig.getCurrencyPair().getBaseCurrency().name()),
            iconConfig.getIconUrl(currencyPairConfig.getCurrencyPair().getQuoteCurrency().name())))
        .collect(Collectors.toList()));
  }
  
  @GetMapping("/display")
  public ResponseEntity<List<CurrencyPairDisplayData>> getForDisplay(
      HttpServletResponse response,
      @RequestParam(value = "tradeType", required = false) TradeType tradeType,
      @RequestParam(value = "currencyPair", required = false) CurrencyPair currencyPair)
      throws Exception {
    // 有効な(enabled=true)通貨ペアのconfigのみ取得
    List<CurrencyPairConfig> currencyPairConfigList = currencyPairConfigService.findAllByCondition(TradeType.POS, currencyPair, true);

    return ResponseEntity.ok(currencyPairConfigList.stream()
        .map(currencyPairConfig -> new CurrencyPairDisplayData(currencyPairConfig))
        .collect(Collectors.toList()));
  }
}
