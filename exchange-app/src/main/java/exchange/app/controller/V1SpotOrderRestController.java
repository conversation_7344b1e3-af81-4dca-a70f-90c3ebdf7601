package exchange.app.controller;

import exchange.common.constant.CancelReason;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import exchange.spot.model.SpotOrderRowMapper;
import exchange.spot.predicate.SpotOrderPredicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderOperator;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.constant.ViewVariables;
import exchange.common.controller.AbstractRestController;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.request.SpotOrderForm;
import exchange.common.model.response.OrderbookData;
import exchange.common.model.response.OrderbookData.Orderbook;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SpotOrderCalculateData;
import exchange.common.model.response.SpotOrderReportData;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.OrderbookService;
import exchange.common.service.SymbolService;
import exchange.common.service.UserService;
import exchange.common.util.CalculatorUtil;
import exchange.common.util.DateUnit;
import exchange.spot.entity.SpotOrder;
import exchange.spot.service.SpotOrderService;
import exchange.spot.service.SpotTradeService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/spot/order")
@Timed
public class V1SpotOrderRestController extends AbstractRestController {

  private final SymbolService symbolService;
  private final CurrencyPairConfigService currencyPairConfigService;
  private final OrderbookService orderbookService;
  private final UserService userService;
  private final CsvDownloadManager<SpotOrderReportData> downloadManager;

  @GetMapping
  public ResponseEntity<List<SpotOrder>> get(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId") Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderStatus", required = false) List<OrderStatus> orderStatuses,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    List<SpotOrder> spotOrders = new ArrayList<>();
    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null) {
      return ResponseEntity.ok(spotOrders);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(spotOrders);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
    SpotOrderService<SpotOrder, SpotOrderPredicate<SpotOrder>, SpotOrderRowMapper<SpotOrder>> service = SpotOrderService.getBean(symbol);
    List<SpotOrder> result = service.findByCondition(
            symbolId,
            user.getId(),
            id,
            idFrom,
            idTo,
            dateFrom,
            dateTo,
            orderStatuses,
            orderType,
            orderTypes,
            exceptOrderTypes,
            orderSide,
            number,
            size,
            false);
    List<SpotOrder> sameCustomerOrder = service.findByCondition(symbolId, user.getId(), number, size, false, CancelReason.SAME_CUSTOMER);
    if(!CollectionUtils.isEmpty(sameCustomerOrder)) {
      result.addAll(sameCustomerOrder);
    }
    return ResponseEntity.ok(
            result);
  }

  @PostMapping
  public ResponseEntity<Object> post(
      HttpServletRequest request,
      @AuthenticationPrincipal User user,
      @Valid @RequestBody SpotOrderForm form)
      throws Exception {
    // userAgentを元にOrderChannel設定
    String userAgent = request.getHeader("user-agent");
    OrderChannel orderChannel = OrderChannel.PC_WEB;
    if (userAgent == null || userAgent.isEmpty()) {
      orderChannel = OrderChannel.UNKNOWN;
    } else if (userAgent.toLowerCase().contains("iphone")) {
      orderChannel = OrderChannel.IPHONE;
    } else if (userAgent.toLowerCase().contains("android")) {
      orderChannel = OrderChannel.ANDROID;
    }

    Symbol symbol = symbolService.findOne(form.getSymbolId());

    if (symbol == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_SYMBOLID, "symbolId: " + form.getSymbolId());
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND);
    }

    user = userService.findOne(user.getId());

    // execute時のエラー、トランザクションエラー、DB saveエラーは内部処理でexceptionを返す
    // lock取得失敗時のみ本処理（親処理）でexceptionを出す(return==nullはexception)

    // to do orderChannel変換後の値を設定
    if (SpotOrderService.getBean(symbol).order(symbol, user, form, orderChannel) == null) {
      throw new CustomException(
          ErrorCode.COMMON_ERROR_LOCK,
          "symbolId: " + form.getSymbolId() + " ,userId: " + user.getId());
    }

    return ResponseEntity.ok().build();
  }

  @DeleteMapping
  public ResponseEntity<Object> delete(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId") Long symbolId,
      @RequestParam(value = "id") Long id)
      throws Exception {
    Symbol symbol = symbolService.findOne(symbolId);

    if (symbol == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_SYMBOLID,
          "symbolId: " + symbolId + "userId: " + user.getId());
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND);
    }

    SpotOrder spotOrder = SpotOrderService.getBean(symbol).findOne(id);

    if (spotOrder == null) {
      throw new CustomException(
          ErrorCode.REQUEST_ERROR_INVALID_ID,
          "symbolId: " + symbolId + " ,id: " + id + "userId: " + user.getId());
    }

    if (!user.getId().equals(spotOrder.getUserId())) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }

    // execute時のエラー、トランザクションエラー、DB saveエラーは内部処理でexceptionを返す
    // lock取得失敗時のみ本処理（親処理）でexceptionを出す(return==nullはexception)
    SpotOrder spotOrderResult =
        SpotOrderService.getBean(symbol).cancel(symbol, user.getId(), id, OrderOperator.USER, CancelReason.CUSTOMER_CANCEL);

    if (spotOrderResult == null) {
      throw new CustomException(
          ErrorCode.COMMON_ERROR_LOCK, "symbolId: " + symbolId + " ,id: " + id);
    }

    return ResponseEntity.ok().build();
  }

  @GetMapping("/all")
  public ResponseEntity<List<SpotOrder>> getAll(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId") Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderStatus", required = false) List<OrderStatus> orderStatuses,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    List<SpotOrder> spotOrders = new ArrayList<>();

    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null) {
      return ResponseEntity.ok(spotOrders);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(spotOrders);
    }

    if (size == 0) {
      return ResponseEntity.ok(new ArrayList<>());
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    spotOrders =
        SpotOrderService.getBean(symbol)
            .findAllByCondition(
                symbolId,
                user.getId(),
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                orderStatuses,
                orderType,
                orderSide);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<SpotOrder> spotOrdersHistory =
        SpotOrderService.getBean(symbol)
            .findAllFromHistory(
                symbol,
                user.getId(),
                id,
                idFrom,
                idTo,
                dateFromDate,
                dateToDate,
                orderStatuses,
                orderType,
                null,
                null,
                orderSide);

    spotOrders.addAll(spotOrdersHistory);
    spotOrders.sort((x, y) -> x.getId().compareTo(y.getId()));

    int count = spotOrders.size();
    int offset = number * size;

    if (count <= offset) {
      return ResponseEntity.ok(new ArrayList<>());
    }
    size = Math.min(count - offset, size);
    return ResponseEntity.ok(spotOrders.subList(offset, offset + size));
  }

  @GetMapping("/page")
  public ResponseEntity<PageData<SpotOrder>> getPage(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId") Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderStatus", required = false) List<OrderStatus> orderStatuses,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    PageData<SpotOrder> pg = new PageData<SpotOrder>(number, size, 0, null);

    if (size == 0) {
      return ResponseEntity.ok(pg);
    }

    if (symbolId == null || user == null) {
      return ResponseEntity.ok(pg);
    }

    Symbol symbol = symbolService.findOne(symbolId);
    Long userId = user.getId();
    if (symbol == null || userId == null) {
      return ResponseEntity.ok(pg);
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(pg);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<SpotOrder> spotOrders =
        SpotOrderService.getBean(symbol)
            .findByCondition(
                symbolId,
                userId,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                orderStatuses,
                orderType,
                orderTypes,
                exceptOrderTypes,
                orderSide,
                0,
                Integer.MAX_VALUE,
                false);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<SpotOrder> spotOrdersHistory =
        SpotOrderService.getBean(symbol)
            .findAllFromHistory(
                symbol,
                userId,
                id,
                idFrom,
                idTo,
                dateFromDate,
                dateToDate,
                orderStatuses,
                orderType,
                orderTypes,
                exceptOrderTypes,
                orderSide);

    // merge & sort
    spotOrders.addAll(spotOrdersHistory);
    // order by id desc
    spotOrders.sort((x, y) -> x.getId().compareTo(y.getId()) * -1);

    // PageData作成
    //   (spotOrders + spotOrdersHistory合算後)
    Long count = (long) spotOrders.size();
    pg = SpotOrderService.getBean(symbol).createPageData(spotOrders, count, number, size);

    return ResponseEntity.ok(pg);
  }

  // 手数料(simple_market_fee_percent)は加味しない
  @GetMapping("/calculate")
  public ResponseEntity<SpotOrderCalculateData> calculate(
      HttpServletResponse response,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "orderType", defaultValue = "") String orderTypeStr,
      @RequestParam(value = "amount", required = false) String amountStr,
      @RequestParam(value = "assetAmount", required = false) String assetAmountStr)
      throws Exception {
    // public API用キャッシュ設定
    setCacheControlForPublic(response);

    OrderType orderType = OrderType.valueOfName(orderTypeStr);

    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null) {
      log.warn("SimpleSpotOrderCalculateLog,symbolId," + symbolId + ",symbolis null");
      return ResponseEntity.ok(new SpotOrderCalculateData());
    }
    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    CurrencyPairConfig currencyPairConfig =
        currencyPairConfigService.findOne(TradeType.SPOT, symbol.getCurrencyPair());
    if (currencyPairConfig == null || !currencyPairConfig.isEnabled()) {
      log.warn(
          "SimpleSpotOrderCalculateLog,symbolId,"
              + symbolId
              + ",currencyPairConfig is null or disabled");
      return ResponseEntity.ok(new SpotOrderCalculateData());
    }

    OrderbookData orderbookData = orderbookService.get(symbol);
    if (orderbookData == null
        || (CollectionUtils.isEmpty(orderbookData.getAsks())
            && CollectionUtils.isEmpty(orderbookData.getBids()))) {
      log.warn("SimpleSpotOrderCalculateLog,symbolId," + symbolId + ",orderbook is empty");
      return ResponseEntity.ok(new SpotOrderCalculateData());
    }

    // A.注文数量をもとに注文総額・平均注文価格を算出
    if (StringUtils.isNotEmpty(amountStr) && NumberUtils.isParsable(amountStr)) {
      BigDecimal amount = new BigDecimal(amountStr);
      // min注文数量チェック
      if (amount.compareTo(BigDecimal.ZERO) > 0
          && amount.compareTo(currencyPairConfig.getMinOrderAmount()) >= 0) {
        return calculateAssetAmountByAmount(
            currencyPairConfig, orderbookData, symbol, amount, orderType);
      }
    }

    // B.注文総額をもとに注文数量・平均注文価格を算出
    if (StringUtils.isNotEmpty(assetAmountStr) && NumberUtils.isParsable(assetAmountStr)) {
      BigDecimal assetAmount = new BigDecimal(assetAmountStr);
      // min注文数量チェックは内部で行う
      if (assetAmount.compareTo(BigDecimal.ZERO) > 0) {
        return calculateAmountByAssetAmount(
            currencyPairConfig, orderbookData, symbol, assetAmount, orderType);
      }
    }

    // C.ベストプライスwithスプレッドを平均価格にセットして返す(A,Bに合致しない場合)
    SpotOrderCalculateData calculateData = new SpotOrderCalculateData();
    setCalculateDataByBestPrice(
        symbol,
        OrderSide.SELL,
        orderType,
        currencyPairConfig,
        calculateData,
        orderbookData.getBids());
    setCalculateDataByBestPrice(
        symbol,
        OrderSide.BUY,
        orderType,
        currencyPairConfig,
        calculateData,
        orderbookData.getAsks());
    return ResponseEntity.ok(calculateData);
  }

  /** A.注文数量をもとに注文総額・平均注文価格を算出 */
  private ResponseEntity<SpotOrderCalculateData> calculateAssetAmountByAmount(
      CurrencyPairConfig currencyPairConfig,
      OrderbookData orderbookData,
      Symbol symbol,
      BigDecimal amount,
      OrderType orderType)
      throws Exception {

    SpotOrderCalculateData calculateData = new SpotOrderCalculateData();
    if (!setCalculateDataByAmount(
        symbol,
        OrderSide.SELL,
        orderType,
        currencyPairConfig,
        calculateData,
        orderbookData.getBids(),
        amount)) {
      // 板(bids)が空の場合 or 板以上の注文数量の場合
      // C.ベストプライスwithスプレッドを平均価格にセットして返す
      setCalculateDataByBestPrice(
          symbol,
          OrderSide.SELL,
          orderType,
          currencyPairConfig,
          calculateData,
          orderbookData.getBids());
    }

    if (!setCalculateDataByAmount(
        symbol,
        OrderSide.BUY,
        orderType,
        currencyPairConfig,
        calculateData,
        orderbookData.getAsks(),
        amount)) {
      // 板(asks)が空の場合 or 板以上の注文数量の場合
      // C.ベストプライスwithスプレッドを平均価格にセットして返す
      setCalculateDataByBestPrice(
          symbol,
          OrderSide.BUY,
          orderType,
          currencyPairConfig,
          calculateData,
          orderbookData.getAsks());
    }

    return ResponseEntity.ok(calculateData);
  }

  // A-2.売りの場合買い板bidsから、買いの場合売り板asksから算出
  private boolean setCalculateDataByAmount(
      Symbol symbol,
      OrderSide orderSide,
      OrderType orderType,
      CurrencyPairConfig currencyPairConfig,
      SpotOrderCalculateData calculateData,
      List<Orderbook> orderbooks,
      BigDecimal orderAmount)
      throws Exception {
    // 加重平均
    // 価格×数量の合計 / 数量サマリ

    if (CollectionUtils.isEmpty(orderbooks)) {
      // 板(bids or asks)が空の場合はベストプライスのみ返す
      return false;
    }

    // 注文数量が指定桁数以内であること
    // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
    if (orderAmount.scale() > symbol.getCurrencyPair().getBasePrecision()) {
      return false;
    }

    BigDecimal amountSum = BigDecimal.ZERO;
    BigDecimal assetAmountSum = BigDecimal.ZERO;

    for (Orderbook orderBook : orderbooks) {
      // 板の合計数量 > 発注数量の場合、板の合計数量=発注数量までで合算
      BigDecimal amount = orderBook.getAmount();
      if (amountSum.add(amount).compareTo(orderAmount) > 0) {
        amount = orderAmount.subtract(amountSum);
      }
      amountSum = amountSum.add(amount);
      // 発注総額は都度スプレッド加味 + 桁数処理
      assetAmountSum =
          assetAmountSum.add(
              SpotTradeService.getBean(symbol)
                  .calculateAssetAmount(
                      symbol,
                      SpotTradeService.getBean(symbol)
                          .calculatePriceWithSpread(
                              symbol,
                              currencyPairConfig,
                              orderBook.getPrice(),
                              orderSide,
                              orderType)
                          .multiply(amount)));

      // 発注数量の範囲で平均価格を算出 (板の合計数量 >= 発注数量の場合に抜ける)
      if (amountSum.compareTo(orderAmount) >= 0) {
        break;
      }
    }

    if (amountSum.compareTo(BigDecimal.ZERO) == 0 || amountSum.compareTo(orderAmount) < 0) {
      log.info(
          "DBMITO,SimpleSpotOrderCalculateLog,orderbook amountSum < orderAmount,amountSum,"
              + amountSum
              + ",orderAmount,"
              + orderAmount);
      return false;
    }

    if (orderSide == OrderSide.SELL) {
      calculateData.setSellAmount(orderAmount); // 入力値そのまま
      // 算出した注文総額は個別に桁数処理済みのためそのまま設定
      calculateData.setSellAssetAmount(assetAmountSum);
      // 注文数量から算出する表示上の平均価格は切り上げる(資産不足と見えないように多めに表示)
      calculateData.setSellAveragePrice(
          currencyPairConfig
              .getCurrencyPair()
              .getScaledPrice(CalculatorUtil.divide(assetAmountSum, amountSum), RoundingMode.UP));
    } else {
      calculateData.setBuyAmount(orderAmount); // 入力値そのまま
      // 算出した注文総額は個別に桁数処理済みのためそのまま設定
      calculateData.setBuyAssetAmount(assetAmountSum);
      // 注文数量から算出する表示上の平均価格は切り上げる(資産不足と見えないように多めに表示)
      calculateData.setBuyAveragePrice(
          currencyPairConfig
              .getCurrencyPair()
              .getScaledPrice(CalculatorUtil.divide(assetAmountSum, amountSum), RoundingMode.UP));
    }

    return true;
  }

  /** B.注文総額をもとに注文数量・平均注文価格を算出 */
  private ResponseEntity<SpotOrderCalculateData> calculateAmountByAssetAmount(
      CurrencyPairConfig currencyPairConfig,
      OrderbookData orderbookData,
      Symbol symbol,
      BigDecimal inputAssetAmount,
      OrderType orderType)
      throws Exception {

    SpotOrderCalculateData calculateData = new SpotOrderCalculateData();
    if (!setCalculateDataByAssetAmount(
        symbol,
        OrderSide.SELL,
        orderType,
        currencyPairConfig,
        calculateData,
        orderbookData.getBids(),
        inputAssetAmount)) {
      // 板(bids)が空 or 板以上の注文総額 or 算出数量が最小注文数量未満の場合
      // C.ベストプライスwithスプレッドを平均価格にセットして返す
      setCalculateDataByBestPrice(
          symbol,
          OrderSide.SELL,
          orderType,
          currencyPairConfig,
          calculateData,
          orderbookData.getBids());
    }

    if (!setCalculateDataByAssetAmount(
        symbol,
        OrderSide.BUY,
        orderType,
        currencyPairConfig,
        calculateData,
        orderbookData.getAsks(),
        inputAssetAmount)) {
      // 板(asks)が空 or 板以上の注文総額 or 算出数量が最小注文数量未満の場合
      // C.ベストプライスwithスプレッドを平均価格にセットして返す
      setCalculateDataByBestPrice(
          symbol,
          OrderSide.BUY,
          orderType,
          currencyPairConfig,
          calculateData,
          orderbookData.getAsks());
    }

    return ResponseEntity.ok(calculateData);
  }

  // B-2.売りの場合買い板bidsから、買いの場合売り板asksから算出
  private boolean setCalculateDataByAssetAmount(
      Symbol symbol,
      OrderSide orderSide,
      OrderType orderType,
      CurrencyPairConfig currencyPairConfig,
      SpotOrderCalculateData calculateData,
      List<Orderbook> orderbooks,
      BigDecimal inputAssetAmount)
      throws Exception {

    if (CollectionUtils.isEmpty(orderbooks)) {
      // 板(bids or asks)が空の場合はベストプライスのみ返す
      return false;
    }

    // 注文総額が指定桁数以内であること
    // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
    if (inputAssetAmount.scale() > symbol.getCurrencyPair().getAssetPrecision()) {
      log.info(
          "DBMITO,scale,"
              + inputAssetAmount.scale()
              + ",precision,"
              + symbol.getCurrencyPair().getAssetPrecision()
              + ",inputAssetAmount,"
              + inputAssetAmount);
      return false;
    }

    BigDecimal amountSum = BigDecimal.ZERO;
    BigDecimal assetAmountSum = BigDecimal.ZERO;
    BigDecimal assetAmountScaledDiff = BigDecimal.ZERO;

    for (Orderbook orderBook : orderbooks) {
      // 板の注文総額の合計 > 発注総額の場合、板の合計総額=発注総額までで合算
      BigDecimal amount = orderBook.getAmount();
      BigDecimal priceWithSpread =
          SpotTradeService.getBean(symbol)
              .calculatePriceWithSpread(
                  symbol, currencyPairConfig, orderBook.getPrice(), orderSide, orderType);
      BigDecimal assetAmountScaled =
          SpotTradeService.getBean(symbol)
              .calculateAssetAmount(symbol, priceWithSpread.multiply(amount));

      if (assetAmountSum.add(assetAmountScaled).compareTo(inputAssetAmount) > 0) {
        assetAmountScaled = inputAssetAmount.subtract(assetAmountSum);
        // 注文総額から算出する注文数量は切り捨て（実際の資産以上とならないようにする)
        amount =
            currencyPairConfig
                .getCurrencyPair()
                .getScaledAmount(
                    CalculatorUtil.divide(assetAmountScaled, priceWithSpread), RoundingMode.FLOOR);
        // 桁数処理を加味して再算出するため(ループ処理の都合上、再算出は最後に行う)
        assetAmountScaledDiff =
            assetAmountScaled.subtract(
                SpotTradeService.getBean(symbol)
                    .calculateAssetAmount(symbol, priceWithSpread.multiply(amount)));
        log.info(
            "DBMITO,SimpleSpotOrderCalculateLog,symbolId,"
                + symbol.getId()
                + ",assetAmountScaledDiff,"
                + assetAmountScaledDiff
                + ",beforeDiffcalc,"
                + assetAmountSum.add(assetAmountScaled)
                + ",afterDiffcalc,"
                + assetAmountSum.add(
                    SpotTradeService.getBean(symbol)
                        .calculateAssetAmount(symbol, priceWithSpread.multiply(amount))));
      }
      amountSum = amountSum.add(amount);
      assetAmountSum = assetAmountSum.add(assetAmountScaled);

      // 発注総額の範囲で平均価格を算出 (板の注文総額の合計 >= 注文総額の場合に抜ける)
      if (assetAmountSum.compareTo(inputAssetAmount) >= 0) {
        break;
      }
    }

    if (assetAmountSum.compareTo(BigDecimal.ZERO) == 0
        || assetAmountSum.compareTo(inputAssetAmount) < 0) {
      // 板の注文総額の合計以上の注文総額の場合、ベストプライスのみ返す
      log.info(
          "DBMITO,SimpleSpotOrderCalculateLog,orderbook assetAmountSum < inputAssetAmount,assetAmountSum,"
              + assetAmountSum
              + ",inputAssetAmount,"
              + inputAssetAmount);
      return false;
    }

    if (amountSum.compareTo(currencyPairConfig.getMinOrderAmount()) < 0) {
      // 算出した注文数量が最小注文数量未満の場合、ベストプライスのみ返す
      log.info(
          "DBMITO,SimpleSpotOrderCalculateLog,orderbook amountSum < minOrderAmount,amountSum,"
              + amountSum
              + ",minOrderAmount,"
              + currencyPairConfig.getMinOrderAmount());
      return false;
    }

    if (orderSide == OrderSide.SELL) {
      // 注文総額から算出する注文数量は切り捨て（実際の資産以上とならないようにする)で個別に桁数処理済み
      calculateData.setSellAmount(amountSum);
      // 入力値から桁数処理分の差分を加味
      calculateData.setSellAssetAmount(inputAssetAmount.subtract(assetAmountScaledDiff));
      // 注文総額から算出する表示上の平均価格は切り上げ（実際の資産以上と見えないようにする)
      // 入力値から桁数処理分の差分を加味
      calculateData.setSellAveragePrice(
          currencyPairConfig
              .getCurrencyPair()
              .getScaledPrice(
                  CalculatorUtil.divide(
                      inputAssetAmount.subtract(assetAmountScaledDiff), amountSum),
                  RoundingMode.UP));
    } else {
      // 注文総額から算出する注文数量は切り捨て（実際の資産以上とならないようにする)で個別に桁数処理済み
      calculateData.setBuyAmount(amountSum);
      // 入力値から桁数処理分の差分を加味
      calculateData.setBuyAssetAmount(inputAssetAmount.subtract(assetAmountScaledDiff));
      // 注文総額から算出する表示上の平均価格は切り上げ（実際の資産以上と見えないようにする)
      // 入力値から桁数処理分の差分を加味
      calculateData.setBuyAveragePrice(
          currencyPairConfig
              .getCurrencyPair()
              .getScaledPrice(
                  CalculatorUtil.divide(
                      inputAssetAmount.subtract(assetAmountScaledDiff), amountSum),
                  RoundingMode.UP));
    }

    return true;
  }

  /** C.ベストプライスwithスプレッドを平均価格にセットして返す */
  // 売りの場合買い板bidsから、買いの場合売り板asksから算出
  private void setCalculateDataByBestPrice(
      Symbol symbol,
      OrderSide orderSide,
      OrderType orderType,
      CurrencyPairConfig currencyPairConfig,
      SpotOrderCalculateData calculateData,
      List<Orderbook> orderbooks)
      throws Exception {

    if (CollectionUtils.isEmpty(orderbooks)) {
      return;
    }

    BigDecimal bestPriceWithSpread =
        SpotTradeService.getBean(symbol)
            .calculatePriceWithSpread(
                symbol, currencyPairConfig, orderbooks.get(0).getPrice(), orderSide, orderType);

    if (orderSide == OrderSide.SELL) {
      calculateData.setSellAveragePrice(bestPriceWithSpread);
    } else {
      calculateData.setBuyAveragePrice(bestPriceWithSpread);
    }
  }

  @GetMapping("/download")
  public String download(
      HttpServletResponse response,
      @AuthenticationPrincipal User user,
      @RequestParam(value = "symbolId") Long symbolId,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderStatus", required = false) List<OrderStatus> orderStatuses,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
      @RequestParam(value = "exceptOrderTypes", required = false) List<OrderType> exceptOrderTypes,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide)
      throws Exception {
    if (userService.findOne(user.getId()) == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }

    Symbol symbol = symbolService.findOne(symbolId);
    Long userId = user.getId();
    if (symbol == null || userId == null) {
      return null;
    }
    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return null;
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<SpotOrderReportData> spotOrderReports = new ArrayList<SpotOrderReportData>();

    List<SpotOrder> spotOrders =
        SpotOrderService.getBean(symbol)
            .findByCondition(
                symbolId,
                userId,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                orderStatuses,
                orderType,
                orderTypes,
                exceptOrderTypes,
                orderSide,
                0,
                Integer.MAX_VALUE,
                false);

    Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
    Date dateToDate = (dateTo == null) ? null : new Date(dateTo);
    List<SpotOrder> spotOrdersHistory =
        SpotOrderService.getBean(symbol)
            .findAllFromHistory(
                symbol,
                user.getId(),
                id,
                idFrom,
                idTo,
                dateFromDate,
                dateToDate,
                orderStatuses,
                orderType,
                orderTypes,
                exceptOrderTypes,
                orderSide);
    // merge & sort
    spotOrders.addAll(spotOrdersHistory);
    List<SpotOrder> spotOrdersSorted =
        spotOrders
            .stream()
            .sorted(Comparator.comparing(SpotOrder::getCreatedAt).reversed())
            .collect(Collectors.toList());

    for (SpotOrder spotOrder : spotOrdersSorted) {
      spotOrderReports.add(
          new SpotOrderReportData().setProperties(spotOrder, symbol.getCurrencyPair()));
    }

    // ファイル名は最終的にはクライアントで決まる
    String fileNamePrefix = "spotOrders";
    downloadManager.download(
        response, spotOrderReports, fileNamePrefix, SpotOrderReportData.getReportHeader());
    return null;
  }
}
