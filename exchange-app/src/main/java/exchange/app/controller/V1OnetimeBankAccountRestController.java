package exchange.app.controller;

import exchange.common.constant.Authority;
import exchange.common.constant.CommonConstants;
import exchange.common.entity.UserAuthority;
import exchange.common.entity.UserInfo;
import exchange.common.model.response.OneTimeAccountData;
import exchange.common.service.UserInfoService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.entity.OnetimeBankAccount;
import exchange.common.entity.User;
import exchange.common.service.OnetimeBankAccountService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/onetime-bank-account")
@Timed
@Slf4j
public class V1OnetimeBankAccountRestController {

  private final OnetimeBankAccountService onetimeBankAccountService;

  private final UserInfoService userInfoService;

  @GetMapping
  public ResponseEntity<OneTimeAccountData> get(@AuthenticationPrincipal User user)
      throws Exception {
    // 口座登録の確認
    OnetimeBankAccount onetimeBankAccount = onetimeBankAccountService.findOneByUserId(user.getId());
    OneTimeAccountData response = new OneTimeAccountData();
    if(ObjectUtils.isEmpty(onetimeBankAccount)){
      log.warn("find onetime bank account is empty userId: {}",user.getId());
      response.setAccountNumber(StringUtils.EMPTY);
      response.setBranchCode(StringUtils.EMPTY);
      response.setBranchName(StringUtils.EMPTY);
      response.setAccountNumber(StringUtils.EMPTY);
      response.setVaHolderNameKana(StringUtils.EMPTY);
      response.setNameKana(StringUtils.EMPTY);
      return ResponseEntity.ok(response);
    }
    response.setAccountNumber(onetimeBankAccount.getAccountNumber());
    response.setBranchCode(onetimeBankAccount.getBranchCode());
    response.setBranchName(onetimeBankAccount.getBranchName());
    response.setAccountNumber(onetimeBankAccount.getAccountNumber());
    response.setVaHolderNameKana(onetimeBankAccount.getVaHolderNameKana());
    List<UserAuthority> authorities = user.getAuthorities();
    String nameKana = "";
    String authority = authorities.get(0).getAuthority();
    if (Authority.CORPORATE.name().equals(authority)) {
      nameKana = user.getUserInfoCorporate().getNameKana();
    } else if (Authority.PERSONAL.name().equals(authority)) {
      UserInfo info = userInfoService.findOne(user.getUserInfoId());
      nameKana = info.getLastKana().concat(CommonConstants.SPACE).concat(info.getFirstKana());
    } else {
      log.warn("this user is not  individual  and corporate ,skip authority:{}", authority);
    }
    response.setNameKana(nameKana);
    return ResponseEntity.ok(response);
  }
}
