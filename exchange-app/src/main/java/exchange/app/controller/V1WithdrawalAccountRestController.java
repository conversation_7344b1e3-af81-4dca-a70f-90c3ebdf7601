package exchange.app.controller;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.app.component.MfaManager;
import exchange.app.model.request.WithdrawalAccountOtpauthPostForm;
import exchange.app.model.request.WithdrawalAccountOtpauthPutForm;
import exchange.app.model.request.WithdrawalAccountPostForm;
import exchange.app.model.request.WithdrawalAccountPutForm;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.Area;
import exchange.common.entity.User;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.exception.CustomException;
import exchange.common.service.WithdrawalAccountService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RequestMapping("/app/v1/withdrawal-account")
@RestController
@Timed
public class V1WithdrawalAccountRestController {

  private final MfaManager mfaManager;

  private final WithdrawalAccountService withdrawalAccountService;

  @GetMapping
  public ResponseEntity<List<WithdrawalAccount>> get(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size) {
    return ResponseEntity.ok(
        withdrawalAccountService.findByCondition(
            user.getId(), currency, dateFrom, dateTo, number, size, "OUT"));
  }
  
  @GetMapping("/withdrawalAccount-country")
  public ResponseEntity<List<Area>> getArea() throws Exception {
	String line = "";
	int key = 0;
	List<Area> country = new ArrayList<>();
	InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("CountryList.csv");
	BufferedReader br = new BufferedReader(new InputStreamReader(inputStream,"Shift-JIS"));
	while ((line = br.readLine()) != null) {
		Area area = new Area();
		area.setAreaId(key);
		area.setArea(line);
		country.add(area);
		key++;
	}
    return ResponseEntity.ok(country);
  }
  

  private ResponseEntity<List<String>> getErrorResponseEntity(BindingResult bindingResult) {
    if (!bindingResult.hasErrors()) {
      return null;
    }

    List<String> errorMessages = new ArrayList<>();
    bindingResult
        .getAllErrors()
        .forEach(objectError -> errorMessages.add(objectError.getDefaultMessage()));
    return ResponseEntity.badRequest().body(errorMessages);
  }

  @PostMapping("/otpauth")
  public ResponseEntity<?> postOtpauth(
      @AuthenticationPrincipal User user,
      @Valid @RequestBody WithdrawalAccountOtpauthPostForm form,
      BindingResult bindingResult)
      throws Exception {
    ResponseEntity<List<String>> errorResponseEntity = getErrorResponseEntity(bindingResult);

    if (errorResponseEntity != null) {
      return errorResponseEntity;
    }

    return mfaManager.responseMfaTypeData(user);
  }

  @PostMapping
  public ResponseEntity<?> post(
      @AuthenticationPrincipal User user,
      @Valid @RequestBody WithdrawalAccountPostForm form,
      BindingResult bindingResult)
      throws Exception {
    mfaManager.authenticate(user, form.getMfaCode());
    ResponseEntity<List<String>> errorResponseEntity = getErrorResponseEntity(bindingResult);

    if (errorResponseEntity != null) {
      return errorResponseEntity;
    }

    WithdrawalAccount withdrawalAccount = new WithdrawalAccount();
    withdrawalAccount.setUserId(user.getId());
    withdrawalAccount.setCurrency(Currency.valueOf(form.getCurrency()));
    withdrawalAccount.setLabel(form.getLabel());
    withdrawalAccount.setAddress(form.getAddressForPost());
    if (form.getWithdrawalAccountType().equals("ONESELF") ) {
    	withdrawalAccount.setOwnertype("お客様本人");
    	withdrawalAccount.setCountry(form.getNewcountry());
		withdrawalAccount.setArea(form.getArea());
    }else {
		withdrawalAccount.setOwnertype("お客様本人以外");
		if (form.getRecipientType().equals("PERSONAL") ) {
			withdrawalAccount.setRecipienttype("個人");
			withdrawalAccount.setLast_name(form.getLast_name());
		    withdrawalAccount.setFirst_name(form.getFirst_name());
		    withdrawalAccount.setLast_name_kana(form.getLast_name_kana());
		    withdrawalAccount.setFirst_name_kana(form.getFirst_name_kana());
		    withdrawalAccount.setFirst_name_english(form.getFirst_name_english());
		    withdrawalAccount.setLast_name_english(form.getLast_name_english());
		} else {
			withdrawalAccount.setRecipienttype("法人");
			withdrawalAccount.setLegalname(form.getLegalname());
			withdrawalAccount.setLegalname_kana(form.getLegalname_kana());
			withdrawalAccount.setLegalname_english(form.getLegalname_english());
		}
		withdrawalAccount.setCountry(form.getNewcountry());
		withdrawalAccount.setArea(form.getArea());
	}
    
    if (form.getAddressType().equals("DOMESTIC")) {
		withdrawalAccount.setAddresstype("国内取引所");
		if (!(form.getNewdomesticexchange().equals("その他"))) {
			withdrawalAccount.setExchange(form.getNewdomesticexchange());
		} else {
			withdrawalAccount.setExchange(form.getExchangeinputd());
		}
		if (form.getNewpurpose().equals("その他")) {
			withdrawalAccount.setPurpose(form.getPurposeinput());
		} else {
			withdrawalAccount.setPurpose(form.getNewpurpose());
		}
	} else if (form.getAddressType().equals("ABROAD")) {
		withdrawalAccount.setAddresstype("海外取引所");
		if (!(form.getNewabroadexchange().equals("その他"))) {
			withdrawalAccount.setExchange(form.getNewabroadexchange());
		} else {
			withdrawalAccount.setExchange(form.getExchangeinputa());
		}
		if (form.getNewpurpose().equals("その他")) {
			withdrawalAccount.setPurpose(form.getPurposeinput());
		} else {
			withdrawalAccount.setPurpose(form.getNewpurpose());
		}
	} else {
		withdrawalAccount.setAddresstype("プライベートウォレット等");
		if (form.getNewpurpose().equals("その他")) {
			withdrawalAccount.setPurpose(form.getPurposeinput());
		} else {
			withdrawalAccount.setPurpose(form.getNewpurpose());
		}
	}
    
    withdrawalAccount.setDestinationTag(form.getDestinationTag());
    return ResponseEntity.ok(withdrawalAccountService.save(withdrawalAccount));
  }

  @PutMapping("/otpauth")
  public ResponseEntity<?> putOtpauth(
      @AuthenticationPrincipal User user,
      @Valid @RequestBody WithdrawalAccountOtpauthPutForm form,
      BindingResult bindingResult)
      throws Exception {
    ResponseEntity<List<String>> errorResponseEntity = getErrorResponseEntity(bindingResult);

    if (errorResponseEntity != null) {
      return errorResponseEntity;
    }

    if (withdrawalAccountService.findOne(form.getWithdrawalAccountId()) == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_WITHDRAWAL_ACCOUNT_NOT_FOUND);
    }

    return mfaManager.responseMfaTypeData(user);
  }

  @PutMapping
  public ResponseEntity<?> put(
      @AuthenticationPrincipal User user,
      @Valid @RequestBody WithdrawalAccountPutForm form,
      BindingResult bindingResult)
      throws Exception {
    mfaManager.authenticate(user, form.getMfaCode());
    ResponseEntity<List<String>> errorResponseEntity = getErrorResponseEntity(bindingResult);

    if (errorResponseEntity != null) {
      return errorResponseEntity;
    }

    WithdrawalAccount withdrawalAccount =
        withdrawalAccountService.findOne(form.getWithdrawalAccountId());

    if (withdrawalAccount == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_WITHDRAWAL_ACCOUNT_NOT_FOUND);
    }

    withdrawalAccount.setUserId(user.getId());
    withdrawalAccount.setCurrency(Currency.valueOf(form.getCurrency()));
    withdrawalAccount.setLabel(form.getLabel());
    withdrawalAccount.setAddress(form.getAddressForPost());
    withdrawalAccount.setDestinationTag(form.getDestinationTag());
    return ResponseEntity.ok(withdrawalAccountService.save(withdrawalAccount));
  }

  private WithdrawalAccount validateDelete(User user, Long id) throws Exception {
    WithdrawalAccount withdrawalAccount = withdrawalAccountService.findOne(id);

    if (withdrawalAccount == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
    }

    if (!user.getId().equals(withdrawalAccount.getUserId())) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }

    return withdrawalAccount;
  }

  @DeleteMapping("/otpauth")
  public ResponseEntity<?> deleteOtpauth(
      @AuthenticationPrincipal User user, @RequestParam(value = "id") Long id) throws Exception {
    validateDelete(user, id);
    return mfaManager.responseMfaTypeData(user);
  }

  @DeleteMapping
  public ResponseEntity<WithdrawalAccount> delete(
      @AuthenticationPrincipal User user,
      @RequestParam(value = "id") Long id,
      @RequestParam(value = "mfaCode") String mfaCode)
      throws Exception {
    mfaManager.authenticate(user, mfaCode);
    WithdrawalAccount withdrawalAccount = validateDelete(user, id);
    withdrawalAccount.setEnabled(false);
    withdrawalAccountService.save(withdrawalAccount);
    return ResponseEntity.ok(withdrawalAccount);
  }
}
