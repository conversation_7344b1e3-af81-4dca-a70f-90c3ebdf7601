<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="exchange.common.dal.master.mapper.user.UserMapper">

    <select id="findAntiSocialCheckUserList" resultType="exchange.common.dal.master.bo.user.AntiSocialCheckUserBO">
        SELECT
            u.id,
            u.email,
            u.user_info_id,
            u.user_info_corporate_id,
            u.user_status,
            u.kyc_status,
            u.user_kyc_id,
            ui.first_name AS 'userInfoFirstName',
            ui.last_name AS 'userInfoLastName',
            uic.`name` AS 'userInfoCorporateName',
            ui.birthday AS 'userInfoBirthday',
            ui.gender AS 'userInfoGender'
        FROM user u
        LEFT JOIN user_info ui ON u.user_info_id = ui.id
        LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
        <where>
            (u.user_info_id IS NOT NULL
            AND ui.id IS NOT NULL
            AND u.kyc_status = 'DOCUMENT_CONFIRMED')
            OR
            (u.user_info_corporate_id IS NOT NULL
            AND uic.id IS NOT NULL
            AND u.kyc_status = 'DOCUMENT_RECEIVED')
        </where>
    </select>

    <select id="findDailyAntiSocialCheckUserList" resultType="exchange.common.dal.master.bo.user.AntiSocialCheckUserBO">
        SELECT
            u.id,
            u.email,
            u.user_info_id,
            u.user_info_corporate_id,
            u.user_status,
            u.kyc_status,
            u.user_kyc_id,
            ui.first_name AS 'userInfoFirstName',
            ui.last_name AS 'userInfoLastName',
            uic.`name` AS 'userInfoCorporateName',
            ui.birthday AS 'userInfoBirthday',
            ui.gender AS 'userInfoGender'
        FROM user u
        LEFT JOIN user_info ui ON u.user_info_id = ui.id
        LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
        <where>
            u.user_kyc_id IS NOT NULL
          AND (
                (u.user_info_id IS NOT NULL AND ui.id IS NOT NULL)
                OR
                (u.user_info_corporate_id IS NOT NULL AND uic.id IS NOT NULL)
            )
            <if test="userIds != null and userIds.size != 0">
                AND u.id IN
                <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findAllHasInfoUserIds" resultType="java.lang.Long">
        SELECT
            u.id
        FROM user u
        LEFT JOIN user_info ui ON u.user_info_id = ui.id
        LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
        WHERE
            ui.id IS NOT NULL
           OR
            uic.id IS NOT NULL
    </select>

    <select id="findAllHasPersonalInfoUserIds" resultType="java.lang.Long">
        SELECT
            u.id
        FROM user u
                 LEFT JOIN user_info ui ON u.user_info_id = ui.id
                 LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
        WHERE
            ui.id IS NOT NULL
    </select>

    <select id="findAllHasCorpInfoUserIds" resultType="java.lang.Long">
        SELECT
            u.id
        FROM user u
                 LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
        WHERE
            uic.id IS NOT NULL
    </select>

    <select id="findWaitUpdateDowJonesCaseUserIds" resultType="java.lang.Long">
        SELECT
        u.id
        FROM user u
        LEFT JOIN user_info ui ON u.user_info_id = ui.id
        LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
        LEFT JOIN dow_jones_sam_case djsc ON djsc.enabled = 1 AND djsc.user_id = u.id
        WHERE
        (ui.id IS NOT NULL
        OR
        uic.id IS NOT NULL)
        AND (
        djsc.id IS NULL
        OR
        djsc.user_info_sync_time &lt; u.updated_at
        )
    </select>

    <select id="findWaitUpdateDowJonesCasePersonalUserIds" resultType="java.lang.Long">
        SELECT
            u.id
        FROM user u
                 LEFT JOIN user_info ui ON u.user_info_id = ui.id
                 LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
                 LEFT JOIN dow_jones_sam_case djsc ON djsc.enabled = 1 AND djsc.user_id = u.id
        WHERE
            (ui.id IS NOT NULL)
          AND (
                djsc.id IS NULL
                OR
                djsc.user_info_sync_time &lt; u.updated_at
            )
    </select>

    <select id="findWaitUpdateDowJonesCaseCorpUserIds" resultType="java.lang.Long">
        SELECT
            u.id
        FROM user u
                 LEFT JOIN user_info ui ON u.user_info_id = ui.id
                 LEFT JOIN user_info_corporate uic ON u.user_info_corporate_id = uic.id
                 LEFT JOIN dow_jones_sam_case djsc ON djsc.enabled = 1 AND djsc.user_id = u.id
        WHERE
            (
             uic.id IS NOT NULL)
          AND (
                djsc.id IS NULL
                OR
                djsc.user_info_sync_time &lt; u.updated_at
            )
    </select>

    <select id="checkIsUserAntiSocialChecked" resultType="java.lang.Boolean">
        SELECT
            count(1)
        FROM user_kyc uk
        WHERE user_id = #{userId} and kyc_status IN ('REFINITIV_CHECKED', 'ANTI_SOCIAL_CHECKED', 'FIRST_SCREENING')
    </select>
</mapper>
