<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="exchange.common.dal.master.mapper.cryptoToken.CryptoTokenAssetMapper">

    <update id="datafix912">
        UPDATE crypto_token_asset set onhand_amount = onhand_amount + #{increment}
                                  WHERE user_id = #{userId} AND onhand_amount + #{increment} &gt;= 0
    </update>


</mapper>
