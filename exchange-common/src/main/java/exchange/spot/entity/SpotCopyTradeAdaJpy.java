package exchange.spot.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "spot_copy_trade_ada_jpy")
@ToString(callSuper = true, doNotUseGetters = true)
public class SpotCopyTradeAdaJpy extends SpotCopyTrade {

  private static final long serialVersionUID = -8375661051493424873L;
}
