package exchange.spot.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "spot_best_price_ada_jpy")
@ToString(callSuper = true, doNotUseGetters = true)
public class SpotBestPriceAdaJpy extends SpotBestPrice {

  private static final long serialVersionUID = -8375124051493428843L;
}
