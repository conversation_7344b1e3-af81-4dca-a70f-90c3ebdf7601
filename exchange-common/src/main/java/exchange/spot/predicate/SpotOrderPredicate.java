package exchange.spot.predicate;

import java.math.BigDecimal;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import exchange.common.constant.*;
import exchange.common.predicate.OrderPredicate;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotOrder_;

public abstract class SpotOrderPredicate<E extends SpotOrder> extends OrderPredicate<E> {

  public Predicate equalCancelReason(CriteriaBuilder criteriaBuilder, Root<E> root,
                                    CancelReason cancelReason) {
    return criteriaBuilder.equal(root.get(SpotOrder_.cancelReason), cancelReason);
  }

  public Predicate equalOrderStatus(CriteriaBuilder criteriaBuilder, Root<E> root,
      OrderStatus orderStatus) {
    return criteriaBuilder.equal(root.get(SpotOrder_.orderStatus), orderStatus);
  }

  public Predicate inUserIds(CriteriaBuilder criteriaBuilder, Root<E> root, List<Long> userIds) {
    return root.get(SpotOrder_.userId).in(userIds);
  }

  public Predicate inExceptUserIds(CriteriaBuilder criteriaBuilder, Root<E> root,
      List<Long> exceptUserIds) {
    return root.get(SpotOrder_.userId).in(exceptUserIds).not();
  }

  public Predicate inOrderStatus(CriteriaBuilder criteriaBuilder, Root<E> root,
      List<OrderStatus> orderStatus) {
    return root.get(SpotOrder_.orderStatus).in(orderStatus.toArray());
  }

  public Predicate equalOrderSide(CriteriaBuilder criteriaBuilder, Root<E> root,
      OrderSide orderSide) {
    return criteriaBuilder.equal(root.get(SpotOrder_.orderSide), orderSide);
  }

  public Predicate equalOrderChannel(CriteriaBuilder criteriaBuilder, Root<E> root,
      OrderChannel orderChannel) {
    return criteriaBuilder.equal(root.get(SpotOrder_.orderChannel), orderChannel);
  }

  public Predicate equalOrderType(CriteriaBuilder criteriaBuilder, Root<E> root,
      OrderType orderType) {
    return criteriaBuilder.equal(root.get(SpotOrder_.orderType), orderType);
  }

  public Predicate inOrderType(Root<E> root, OrderType... orderTypes) {
    return root.get(SpotOrder_.orderType).in((Object[]) orderTypes);
  }

  public Predicate greaterThanOrEqualToPrice(CriteriaBuilder criteriaBuilder, Root<E> root, BigDecimal price) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(SpotOrder_.price), price);
  }

  public Predicate lessThanOrEqualToPrice(CriteriaBuilder criteriaBuilder, Root<E> root, BigDecimal price) {
    return criteriaBuilder.lessThanOrEqualTo(root.get(SpotOrder_.price), price);
  }

  public Predicate notInOrderType(CriteriaBuilder criteriaBuilder, Root<E> root, OrderType... orderTypes) {
    return criteriaBuilder.not(inOrderType(root, orderTypes));
  }

  public Predicate inOrderStatus(Root<E> root, OrderStatus... orderStatuses) {
    return root.get(SpotOrder_.orderStatus).in((Object[]) orderStatuses);
  }

  public Predicate betweenPrice(CriteriaBuilder criteriaBuilder, Root<E> root, BigDecimal priceFrom,
      BigDecimal priceTo) {
    return criteriaBuilder.between(root.get(SpotOrder_.price), priceFrom, priceTo);
  }
}
