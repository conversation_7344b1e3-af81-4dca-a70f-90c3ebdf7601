package exchange.spot.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.predicate.TradePredicate;
import exchange.spot.entity.SpotTrade;
import exchange.spot.entity.SpotTrade_;

public abstract class SpotTradePredicate<E extends SpotTrade> extends TradePredicate<E> {
  public Predicate inUserIds(CriteriaBuilder criteriaBuilder, Root<E> root, List<Long> userIds) {
    return root.get(SpotTrade_.userId).in(userIds);
  }

  public Predicate inExceptUserIds(
      CriteriaBuilder criteriaBuilder, Root<E> root, List<Long> exceptUserIds) {
    return root.get(SpotTrade_.userId).in(exceptUserIds).not();
  }

  public Predicate equalOrderSide(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderSide orderSide) {
    return criteriaBuilder.equal(root.get(SpotTrade_.orderSide), orderSide);
  }

  public Predicate equalOrderType(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderType orderType) {
    return criteriaBuilder.equal(root.get(SpotTrade_.orderType), orderType);
  }

  public Predicate inOrderType(Root<E> root, OrderType... orderTypes) {
    return root.get(SpotTrade_.orderType).in((Object[]) orderTypes);
  }

  public Predicate notInOrderType(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderType... orderTypes) {
    return criteriaBuilder.not(inOrderType(root, orderTypes));
  }

  public Predicate equalOrderChannel(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderChannel orderChannel) {
    return criteriaBuilder.equal(root.get(SpotTrade_.orderChannel), orderChannel);
  }

  public Predicate equalOrderId(CriteriaBuilder criteriaBuilder, Root<E> root, Long orderId) {
    return criteriaBuilder.equal(root.get(SpotTrade_.orderId), orderId);
  }

  public Predicate equalTargetOrderId(
      CriteriaBuilder criteriaBuilder, Root<E> root, Long targetOrderId) {
    return criteriaBuilder.equal(root.get(SpotTrade_.targetOrderId), targetOrderId);
  }
}
