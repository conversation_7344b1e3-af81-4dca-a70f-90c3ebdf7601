package exchange.spot.service;

import org.springframework.stereotype.Service;
import exchange.spot.entity.SpotBestPriceOkcoinAdaJpy;
import exchange.spot.model.SpotBestPriceOkcoinAdaJpyRowMapper;
import exchange.spot.predicate.SpotBestPriceOkcoinAdaJpyPredicate;

@Service
public class SpotBestPriceOkcoinAdaJpyService
    extends SpotBestPriceOkcoinService<
        SpotBestPriceOkcoinAdaJpy,
        SpotBestPriceOkcoinAdaJpyPredicate,
        SpotBestPriceOkcoinAdaJpyRowMapper> {

  @Override
  public Class<SpotBestPriceOkcoinAdaJpy> getEntityClass() {
    return SpotBestPriceOkcoinAdaJpy.class;
  }

  @Override
  public Class<SpotBestPriceOkcoinAdaJpyRowMapper> getRowMapperClass() {
    return SpotBestPriceOkcoinAdaJpyRowMapper.class;
  }
}
