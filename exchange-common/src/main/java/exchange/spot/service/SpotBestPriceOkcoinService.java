package exchange.spot.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import exchange.common.constant.Exchange;
import exchange.common.entity.Symbol;
import exchange.common.service.BestPriceService;
import exchange.spot.entity.SpotBestPriceOkcoin;
import exchange.spot.model.SpotBestPriceOkcoinRowMapper;
import exchange.spot.predicate.SpotBestPriceOkcoinPredicate;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.BooleanUtils;

@Slf4j
public abstract class SpotBestPriceOkcoinService<
        E extends SpotBestPriceOkcoin,
        P extends SpotBestPriceOkcoinPredicate<E>,
        R extends SpotBestPriceOkcoinRowMapper<E>>
    extends BestPriceService<E, P, R> implements ApplicationContextAware {

  private static ApplicationContext APPLICATION_CONTEXT;

  private static final Exchange exchange = Exchange.OKCOIN;

  @SuppressWarnings("unchecked")
  public static <
          E extends SpotBestPriceOkcoin,
          P extends SpotBestPriceOkcoinPredicate<E>,
          R extends SpotBestPriceOkcoinRowMapper<E>>
      SpotBestPriceOkcoinService<E, P, R> getBean(Symbol symbol) {
    return (SpotBestPriceOkcoinService<E, P, R>)
        APPLICATION_CONTEXT.getBean(
            symbol.getTradeType().toLowerCamelCase()
                + "BestPrice"
                + exchange.toUpperCamelCase()
                + symbol.getCurrencyPair().toUpperCamelCase()
                + "Service");
  }

  @Override
  @SuppressFBWarnings
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    APPLICATION_CONTEXT = applicationContext;
  }

  private StringBuilder createSqlString(
      MapSqlParameterSource mapSqlParameterSource,
      Symbol symbol,
      Long id,
      Long idFrom,
      Long idTo,
      Date createdAtFrom,
      Date createdAtTo,
      Boolean isAscending,
      Integer size) {

    StringBuilder sql =
        new StringBuilder("select * from " + SpotBestPriceOkcoin.getTableName(symbol, exchange));

    // 条件指定
    sql.append(" where symbol_id = :symbol_id");
    mapSqlParameterSource.addValue("symbol_id", symbol.getId());

    if (id != null) {
      sql.append(" and id = :id");
      mapSqlParameterSource.addValue("id", id);
    } else {

      if (idFrom != null) {
        sql.append(" and id >= :id_from");
        mapSqlParameterSource.addValue("id_from", idFrom);
      }

      if (idTo != null) {
        sql.append(" and id <= :id_to");
        mapSqlParameterSource.addValue("id_to", idTo);
      }

      if (createdAtFrom != null) {
        sql.append(" and created_at >= :created_at_from");
        mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
      }

      if (createdAtTo != null) {
        sql.append(" and created_at < :created_at_to");
        mapSqlParameterSource.addValue("created_at_to", createdAtTo);
      }

      // where文はこれより前に記載

      if (isAscending != null) {
        if (BooleanUtils.isTrue(isAscending)) {
          sql.append(" order by id asc");
        } else {
          sql.append(" order by id desc");
        }
      }

      if (size != null) {
        sql.append(" limit :size");
        mapSqlParameterSource.addValue("size", size);
      }
    }

    return sql;
  }

  public List<E> findAllFromHistory(
      Symbol symbol,
      Long id,
      Long idFrom,
      Long idTo,
      Date createdAtFrom,
      Date createdAtTo,
      Boolean isAscending,
      Integer size) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

    StringBuilder sql =
        createSqlString(
            mapSqlParameterSource,
            symbol,
            id,
            idFrom,
            idTo,
            createdAtFrom,
            createdAtTo,
            isAscending,
            size);

    log.info("redshift-sql:" + sql.toString());
    log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

    return historicalTransactionManager.findFromHistory(
        sql.toString(), mapSqlParameterSource, newRowMapper());
  }

  public List<E> findLatestFromHistory(Symbol symbol, Date createdAtFrom) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

    StringBuilder sql =
        createSqlString(
            mapSqlParameterSource, symbol, null, null, null, createdAtFrom, null, false, 1);

    log.info("redshift-sql:" + sql.toString());
    log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

    return historicalTransactionManager.findFromHistory(
        sql.toString(), mapSqlParameterSource, newRowMapper());
  }

  @Override
  public void archive(Symbol symbol, BigDecimal bestAsk, BigDecimal bestBid) {

    // null許容しない(いずれかがnullでスキップ)
    if (bestAsk == null || bestBid == null) {
      return;
    }

    // 最新id取得
    List<SpotBestPriceOkcoin> spotBestPriceList =
        SpotBestPriceOkcoinService.getBean(symbol).findLatestFromHistory(symbol, null);
    Long newId =
        (CollectionUtils.isEmpty(spotBestPriceList))
            ? Long.valueOf(0)
            : spotBestPriceList.get(0).getId();
    newId++;

    // insert
    StringJoiner stringJoiner = new StringJoiner(",");

    // created_at, updated_atはnullで渡す
    stringJoiner.add("(" + newId + ", " + symbol.getId() + ", " + bestAsk + ", " + bestBid + ")");

    historicalTransactionManager.archive(
        "insert into "
            + SpotBestPriceOkcoin.getTableName(symbol, exchange)
            + " (id, symbol_id, best_ask, best_bid) values "
            + stringJoiner.toString());

    log.info(
        "archive_log_spot_best_price,symbolId,"
            + symbol.getId()
            + ",exchange,"
            + exchange
            + ",newId,"
            + newId);
  }
}
