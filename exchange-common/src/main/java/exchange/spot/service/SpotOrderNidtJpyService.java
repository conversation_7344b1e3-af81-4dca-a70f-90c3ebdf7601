package exchange.spot.service;

import exchange.spot.entity.SpotOrderNidtJpy;
import exchange.spot.model.SpotOrderNidtJpyRowMapper;
import exchange.spot.predicate.SpotOrderNidtJpyPredicate;
import org.springframework.stereotype.Service;

@Service
public class SpotOrderNidtJpyService extends SpotOrderService<SpotOrderNidtJpy, SpotOrderNidtJpyPredicate, SpotOrderNidtJpyRowMapper> {

  @Override
  public Class<SpotOrderNidtJpy> getEntityClass() {
    return SpotOrderNidtJpy.class;
  }

  @Override
  public Class<SpotOrderNidtJpyRowMapper> getRowMapperClass() {
    return SpotOrderNidtJpyRowMapper.class;
  }
}