package exchange.spot.service;

import org.springframework.stereotype.Service;

import exchange.common.service.CandlestickService;
import exchange.spot.entity.SpotCandlestickAdaJpy;
import exchange.spot.predicate.SpotCandlestickAdaJpyPredicate;

@Service
public class SpotCandlestickAdaJpyService extends CandlestickService<SpotCandlestickAdaJpy, SpotCandlestickAdaJpyPredicate> {

  @Override
  public Class<SpotCandlestickAdaJpy> getEntityClass() {
    return SpotCandlestickAdaJpy.class;
  }
}
