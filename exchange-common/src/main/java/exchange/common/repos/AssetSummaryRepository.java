package exchange.common.repos;

import java.util.Date;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import exchange.common.entity.AssetSummary;

/**
 * @author: jiangliu
 * @date: 2024/10/30
 */
public interface AssetSummaryRepository extends JpaRepository<AssetSummary,Long> {

	@Modifying
	@Query(nativeQuery = true, value = "DELETE from asset_summary WHERE target_at >= :before and target_at < :targetAt")
	@Transactional
	int deleteByTargetAtFromAndTargetAtTo(@Param("before") Date before, @Param("targetAt") Date targetAt);
	
}
