package exchange.common.repos;

import exchange.common.constant.KycStatus;
import exchange.common.entity.UserEkycStatusChangeHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface UserEkycStatusChangeHistoryRepository
    extends JpaRepository<UserEkycStatusChangeHistory, Long>,
        JpaSpecificationExecutor<UserEkycStatusChangeHistory> {

    List<UserEkycStatusChangeHistory> findByUserIdAndApplicantIdAndAfterStatus(Long userId, Long applicantId, KycStatus kycStatus);
}
