package exchange.common.repos;

import exchange.common.entity.OnetimeBankAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OneTimeBankAccountServiceRepository extends JpaRepository<OnetimeBankAccount,Long> {

  @Query(nativeQuery = true, value = "select id from user where kyc_status in ('ACCOUNT_OPENING_DONE','CORPORATE_PERSONAL_ACCOUNT_OPENING_DONE','DONE')  and id not in (select user_id from onetime_bank_account);")
  List<Long> findMigrationUser();
}
