package exchange.common.repos;

import exchange.common.entity.UserMailNoticesOff;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface UserMailNoticesOffRepository extends JpaRepository<UserMailNoticesOff, Long>,
    JpaSpecificationExecutor<UserMailNoticesOff> {

    List<UserMailNoticesOff> findByUserId(Long userId);

}
