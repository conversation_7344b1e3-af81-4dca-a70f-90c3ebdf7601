package exchange.common.repos;

import exchange.common.entity.GmoDeposit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface GmoServiceRepository extends JpaRepository<GmoDeposit,Long> {

  @Query(nativeQuery = true, value = "select max(item_key)  from gmo_deposit  limit 1")
  String gmoLatestItemKey();

  GmoDeposit findByFiatDepositId(Long id);
}
