package exchange.common.repos;

import exchange.common.entity.UserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface UserInfoRepository
    extends JpaRepository<UserInfo, Long>, JpaSpecificationExecutor<UserInfo> {

    UserInfo findFirstByUserId(Long userId);

    @Query(nativeQuery = true, value = "select count(*) from user_info inner join `user` on `user`.id = user_info.user_id where phone_number = :phoneNumber and `user`.user_status != 'LEFT'")
    int countPhoneNumber(@Param("phoneNumber") String phoneNumber);
}
