package exchange.common.repos;

import exchange.common.entity.ZipCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface ZipCodeRepository
        extends JpaRepository<ZipCode, Long>, JpaSpecificationExecutor<ZipCode> {

  ZipCode findByZipCode(String zipCode);

  List<ZipCode> findByZipCodeOrderById(String zipCode);
}
