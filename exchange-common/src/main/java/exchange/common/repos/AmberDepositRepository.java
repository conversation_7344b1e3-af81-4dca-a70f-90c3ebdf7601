package exchange.common.repos;

import exchange.common.entity.AmberDeposit;
import java.util.Date;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface AmberDepositRepository extends JpaRepository<AmberDeposit,Long> {

  @Query(nativeQuery = true, value = "select max(transaction_time)  from amber_deposit  limit 1")
  Date amberLatestTxTime();
}
