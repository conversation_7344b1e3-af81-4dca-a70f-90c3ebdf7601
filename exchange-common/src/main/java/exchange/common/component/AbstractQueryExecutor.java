package exchange.common.component;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Root;

import lombok.Getter;
import exchange.common.entity.AbstractEntity;

public abstract class AbstractQueryExecutor<E extends AbstractEntity> {

  @Getter
  private Class<E> entityClass;

  protected EntityManager entityManager;

  protected CriteriaBuilder criteriaBuilder;

  protected Root<E> root;

  protected void initialize(Class<E> entityClass, EntityManager entityManager) {
    this.entityClass = entityClass;
    this.entityManager = entityManager;
    criteriaBuilder = entityManager.getCriteriaBuilder();
    prepare();
  }

  protected abstract void prepare();
}
