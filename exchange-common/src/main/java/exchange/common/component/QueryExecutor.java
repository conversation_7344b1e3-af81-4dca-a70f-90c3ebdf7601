package exchange.common.component;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaQuery;

import exchange.common.entity.AbstractEntity;

public abstract class QueryExecutor<E extends AbstractEntity> extends AbstractQueryExecutor<E> {

  protected CriteriaQuery<E> criteriaQuery;

  public void execute(Class<E> domainClass, EntityManager entityManager) {
    initialize(domainClass, entityManager);
    query();
  }

  @Override
  protected void prepare() {
    criteriaQuery = criteriaBuilder.createQuery(getEntityClass());
    root = criteriaQuery.from(getEntityClass());
  }

  public abstract void query();
}
