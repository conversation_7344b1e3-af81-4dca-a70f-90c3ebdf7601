package exchange.common.component;

import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;

import exchange.common.util.CollectionUtil;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import exchange.common.entity.AbstractEntity;
import exchange.common.exception.CustomException;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class CustomTransactionManager {

  private static final CustomLogger log =
      new CustomLogger(CustomTransactionManager.class.getName());

  private final DataSourceManager dataSourceManager;

  public void execute(TransactionExecutor transactionExecutor) throws Exception {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();

    try {
      entityTransaction.begin();
      transactionExecutor.execute(entityManager);
      entityTransaction.commit();
    } catch (Exception e) {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      log.severe(getClass().getSimpleName(), e);
      throw wrapCustomException(e);
    } finally {
      entityManager.close();
    }
  }

  public <T> T execute(TransactionExecutorReturner<T> transactionExecutorReturner)
      throws Exception {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();
    T object;

    try {
      entityTransaction.begin();
      object = transactionExecutorReturner.execute(entityManager);
      entityTransaction.commit();
    } catch (Exception e) {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      log.severe(getClass().getSimpleName(), e);
      throw wrapCustomException(e);
    } finally {
      entityManager.close();
    }

    return object;
  }

  public <E extends AbstractEntity> void find(
      Class<E> entityClass, QueryExecutor<E> queryExecutor) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();

    try {
      entityTransaction.begin();
      queryExecutor.execute(entityClass, entityManager);
    } catch (Exception e) {
      Class<E> clazz = queryExecutor.getEntityClass();
      log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
      logCustomException(e);
    } finally {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      entityManager.clear();
      entityManager.close();
    }
  }

  public <E extends AbstractEntity, T> T find(
      Class<E> entityClass, QueryExecutorReturner<E, T> queryExecutorReturner) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();
    T object = null;

    try {
      entityTransaction.begin();
      object = queryExecutorReturner.execute(entityClass, entityManager);
    } catch (Exception e) {
      Class<E> clazz = queryExecutorReturner.getEntityClass();
      log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
      logCustomException(e);
    } finally {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      entityManager.clear();
      entityManager.close();
    }

    return object;
  }

  public <E extends AbstractEntity> long count(
      Class<E> entityClass, QueryExecutorCounter<E> queryExecutorCounter) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();
    Long count = null;

    try {
      entityTransaction.begin();
      count = queryExecutorCounter.execute(entityClass, entityManager);
    } catch (Exception e) {
      Class<E> clazz = queryExecutorCounter.getEntityClass();
      log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
      logCustomException(e);
    } finally {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      entityManager.clear();
      entityManager.close();
    }

    return count != null ? count : 0;
  }

  public <E extends AbstractEntity> Object[] sum(
      Class<E> entityClass, QueryExecutorSum<E> queryExecutorSum) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();
    Object[] objects = null;

    try {
      entityTransaction.begin();
      objects = queryExecutorSum.execute(entityClass, entityManager);
    } catch (Exception e) {
      Class<E> clazz = queryExecutorSum.getEntityClass();
      log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
      logCustomException(e);
    } finally {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      entityManager.clear();
      entityManager.close();
    }

    return objects;
  }

  public <E extends AbstractEntity> List<Object[]> sumList(
      Class<E> entityClass, QueryExecutorSumList<E> queryExecutorSumList) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();
    List<Object[]> objects = null;

    try {
      entityTransaction.begin();
      objects = queryExecutorSumList.execute(entityClass, entityManager);
    } catch (Exception e) {
      Class<E> clazz = queryExecutorSumList.getEntityClass();
      log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
      logCustomException(e);
    } finally {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      entityManager.clear();
      entityManager.close();
    }

    return objects;
  }

  public <E extends AbstractEntity> E save(E entity, EntityManager entityManager) throws Exception {
    return new EntityPersister<E>().save(entity, entityManager);
  }

  public <E extends AbstractEntity> E save(E entity) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();

    try {
      entityTransaction.begin();
      save(entity, entityManager);
      entityTransaction.commit();
    } catch (Exception e) {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      log.severe(entity == null ? "" : entity.getClass().getSimpleName(), e);
      logCustomException(e);
    } finally {
      entityManager.close();
    }

    return entity;
  }

  public <E extends AbstractEntity> List<E> batchSave(List<E> list) {
    if (CollectionUtil.isEmpty(list)) {
      return list;
    }
    EntityManager entityManager =
            dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();

    try {
      entityTransaction.begin();
      for (E entity : list) {
        save(entity, entityManager);
      }
      entityTransaction.commit();
    } catch (Exception e) {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      log.severe(CollectionUtil.isEmpty(list) ? "" : list.get(0).getClass().getSimpleName(), e);
      logCustomException(e);
    } finally {
      entityManager.close();
    }
    return list;
  }

  public <E extends AbstractEntity> void delete(E entity, EntityManager entityManager) {
    new EntityPersister<E>().delete(entity, entityManager);
  }

  public <E extends AbstractEntity> void delete(E entity) {
    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    EntityTransaction entityTransaction = entityManager.getTransaction();

    try {
      entityTransaction.begin();
      delete(entity, entityManager);
      entityTransaction.commit();
    } catch (Exception e) {
      if (entityTransaction.isActive()) {
        entityTransaction.rollback();
      }

      log.severe(entity == null ? "" : entity.getClass().getSimpleName(), e);
      logCustomException(e);
    } finally {
      entityManager.close();
    }
  }

  @Transactional(transactionManager = "masterTransactionManager")
  public int multiUpdate(String sql, MapSqlParameterSource mapSqlParameterSource) {
    NamedParameterJdbcTemplate namedParameterJdbcTemplate =
        new NamedParameterJdbcTemplate(dataSourceManager.getMasterJdbcTemplate());
    try {
      return namedParameterJdbcTemplate.update(sql, mapSqlParameterSource);
    } catch (Exception e) {
      log.severe(namedParameterJdbcTemplate.getClass().getSimpleName(), e);
      throw e;
    }
  }

  @Transactional(transactionManager = "masterTransactionManager")
  public void execute(String sql) {
    JdbcTemplate masterJdbcTemplate = dataSourceManager.getMasterJdbcTemplate();
    try {
      masterJdbcTemplate.execute(sql);
    } catch (Exception e) {
      log.severe(masterJdbcTemplate.getClass().getSimpleName(), e);
      throw e;
    }
  }

  private CustomException wrapCustomException(Exception e) {
    if (e instanceof CustomException) {
      return (CustomException) e;
    }
    return new CustomException(e);
  }

  private void logCustomException(Exception e) {
    if (!(e instanceof CustomException) || !((CustomException) e).hasDebugMessage()) {
      return;
    }
    log.severe(e.getClass().getName() + ": " + ((CustomException) e).getDebugMessage(), e);
  }
}
