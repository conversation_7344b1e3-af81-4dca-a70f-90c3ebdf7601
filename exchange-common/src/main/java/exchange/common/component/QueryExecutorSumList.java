package exchange.common.component;

import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaQuery;
import exchange.common.entity.AbstractEntity;

public abstract class QueryExecutorSumList<E extends AbstractEntity>
    extends AbstractQueryExecutor<E> {

  protected CriteriaQuery<Object[]> criteriaQuery;

  public List<Object[]> execute(Class<E> entityClass, EntityManager entityManager) {
    initialize(entityClass, entityManager);
    return query();
  }

  @Override
  protected void prepare() {
    criteriaQuery = criteriaBuilder.createQuery(Object[].class);
    root = criteriaQuery.from(getEntityClass());
  }

  public abstract List<Object[]> query();
}
