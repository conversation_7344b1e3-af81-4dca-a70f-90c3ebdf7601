package exchange.common.component;

import com.google.common.collect.Lists;
import exchange.common.config.DowJonesConfig;
import exchange.common.config.SpringConfig;
import exchange.common.constant.DowJonesSamRecordTypeEnum;
import exchange.common.constant.DowJonesUserRelationDataTypeEnum;
import exchange.common.dal.master.entity.dowjones.DowJonesSamCaseDO;
import exchange.common.dal.master.mapper.dowjones.DowJonesSamCaseMapper;
import exchange.common.exception.DowJonesAntisocialCheckException;
import exchange.common.http.cb.HttpMethod;
import exchange.common.model.request.dowjones.*;
import exchange.common.model.response.dowjones.*;
import exchange.common.service.dowjones.dto.DowJonesUserRelationDataInfoDTO;
import exchange.common.util.HttpUtil;
import exchange.common.util.IdUtil;
import exchange.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @author: wen.y
 * @date: 2024/12/6
 */
@Slf4j
@Component
public class DowJonesManager {
	@Resource
	private DowJonesConfig dowJonesConfig;

	@Resource
	private RedisClient redisClient;

	@Resource
	private SpringConfig springConfig;

	@Resource
	private DowJonesSamCaseMapper dowJonesSamCaseMapper;

	// ========================================== Transaction API ==========================================
	public DowJonesCaseTransactionGetRes getCaseTransaction(String tid, String caseId, String transactionId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseTransaction(caseId, transactionId), HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesCaseTransactionGetRes.class);
	}
	public DowJonesTransactionGetRes getTransaction(String tid, String transactionId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getTransaction(transactionId), HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesTransactionGetRes.class);
	}

	// ========================================== Screening and Monitoring API ==========================================
	public DowJonesCaseGetMatchesRes caseGetMatches(String tid, String caseId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseGetMatchesUrl(caseId), HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesCaseGetMatchesRes.class);
	}

	public DowJonesCaseDeleteAssociationRes caseDeleteAssociation(String tid, String caseId, String associationId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseDeleteAssociationUrl(caseId, associationId), HttpMethod.DELETE, null, null);
		DowJonesCaseDeleteAssociationRes caseDeleteAssociationRes = JsonUtil.decode(res, DowJonesCaseDeleteAssociationRes.class);
		log.info("[tid={}] DowJonesManager caseDeleteAssociation caseId={},associationId={}", tid, caseId, associationId);
		return caseDeleteAssociationRes;
	}

	public DowJonesCaseAddAssociationRes caseAddAssociation(String tid, String caseId, String associationId) throws Exception {
		DowJonesCaseAddAssociationReq req = new DowJonesCaseAddAssociationReq();
		req.getData().add(new DowJonesCaseAddAssociationReq.Data().setId(associationId));
		return caseAddAssociation(tid, caseId, req);
	}
	private DowJonesCaseAddAssociationRes caseAddAssociation(String tid, String caseId, DowJonesCaseAddAssociationReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseAddAssociationUrl(caseId), HttpMethod.POST, null, JsonUtil.encode(req));
		DowJonesCaseAddAssociationRes caseAddAssociationRes = JsonUtil.decode(res, DowJonesCaseAddAssociationRes.class);
		log.info("[tid={}] DowJonesManager caseAddAssociation caseId={}, req={}", tid, caseId, JsonUtil.encode(req));
		return caseAddAssociationRes;
	}

	public DowJonesAssociationGetRes getAssociation(String tid, String associationId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getAssociationGetUrl(associationId), HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesAssociationGetRes.class);
	}

	public DowJonesAssociationDeleteRes associationDelete(String tid, String associationId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getAssociationDeleteUrl(associationId), HttpMethod.DELETE, null, null);
		DowJonesAssociationDeleteRes associationDeleteRes = JsonUtil.decode(res, DowJonesAssociationDeleteRes.class);
		log.info("[tid={}] DowJonesManager associationDelete associationId={}", tid, associationId);
		return associationDeleteRes;
	}

	public DowJonesAssociationUpdateRes associationUpdate(String tid, String associationId, DowJonesUserRelationDataInfoDTO userRelationDataInfo) throws Exception {
		DowJonesUserRelationDataTypeEnum dowJonesUserRelationDataTypeEnum = DowJonesUserRelationDataTypeEnum.ofType(userRelationDataInfo.getUserRelationDataType());
		DowJonesSamRecordTypeEnum recordType = dowJonesUserRelationDataTypeEnum.getDowJonesSamRecordType();

		String externalIdSuffix = (null == userRelationDataInfo.getUserRelationDataId()) ? IdUtil.getUuid() : String.valueOf(userRelationDataInfo.getUserRelationDataId());
		String externalId = String.format("%s_%s_%s", springConfig.getEnvironment().name(), dowJonesUserRelationDataTypeEnum.name(), externalIdSuffix);
		String gender = null == userRelationDataInfo.getGender() ? null : switch (userRelationDataInfo.getGender()) {
			case 0 ->  "MALE";
			case 1 ->  "FEMALE";
			default ->  null;
		};
		Integer yearOfBirth = null;
		if (StringUtils.isNotBlank(userRelationDataInfo.getBirthday())) {
			char[] chars = userRelationDataInfo.getBirthday().toCharArray();
			String year = new String(Arrays.copyOfRange(chars, 0, 4));
			yearOfBirth = Integer.valueOf(year);
		}

		DowJonesAssociationUpdateReq.Name name = new DowJonesAssociationUpdateReq.Name();
		name.setNameType("SSN");
		if (DowJonesSamRecordTypeEnum.PERSON == recordType) {
			name.setSingleStringName(userRelationDataInfo.getLastName() + " " + userRelationDataInfo.getFirstName());
//			name.setFirstName(userRelationDataInfo.getFirstName());
//			name.setLastName(userRelationDataInfo.getLastName());
		} else {
			name.setSingleStringName(userRelationDataInfo.getName());
//			name.setEntityName(userRelationDataInfo.getName());
		}

		DowJonesAssociationUpdateReq req = new DowJonesAssociationUpdateReq();
		req.getData().getAttributes().setExternalId(externalId)
				.setGender(gender)
				.setRecordType(recordType.getType())
				.setYearOfBirth(yearOfBirth)
				.setNames(Lists.newArrayList(name))
		;

		return associationUpdate(tid, associationId, req);
	}

	private DowJonesAssociationUpdateRes associationUpdate(String tid, String associationId, DowJonesAssociationUpdateReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getAssociationUpdateUrl(associationId), HttpMethod.PATCH, null, JsonUtil.encode(req));
		DowJonesAssociationUpdateRes associationUpdateRes = JsonUtil.decode(res, DowJonesAssociationUpdateRes.class);
		log.info("[tid={}] DowJonesManager associationUpdateRes associationId={}", tid, associationId);
		return associationUpdateRes;
	}

	public DowJonesAssociationCreateRes associationCreate(String tid, DowJonesUserRelationDataInfoDTO userRelationDataInfo) throws Exception {
		DowJonesUserRelationDataTypeEnum dowJonesUserRelationDataTypeEnum = DowJonesUserRelationDataTypeEnum.ofType(userRelationDataInfo.getUserRelationDataType());
		DowJonesSamRecordTypeEnum recordType = dowJonesUserRelationDataTypeEnum.getDowJonesSamRecordType();

		String externalIdSuffix = (null == userRelationDataInfo.getUserRelationDataId()) ? IdUtil.getUuid() : String.valueOf(userRelationDataInfo.getUserRelationDataId());
		String externalId = String.format("%s_%s_%s", springConfig.getEnvironment().name(), dowJonesUserRelationDataTypeEnum.name(), externalIdSuffix);
		String gender = null == userRelationDataInfo.getGender() ? null : switch (userRelationDataInfo.getGender()) {
			case 0 ->  "MALE";
			case 1 ->  "FEMALE";
			default ->  null;
		};
		Integer yearOfBirth = null;
		if (StringUtils.isNotBlank(userRelationDataInfo.getBirthday())) {
			char[] chars = userRelationDataInfo.getBirthday().toCharArray();
			String year = new String(Arrays.copyOfRange(chars, 0, 4));
			yearOfBirth = Integer.valueOf(year);
		}

    	DowJonesAssociationCreateReq.Name name = new DowJonesAssociationCreateReq.Name();
		name.setNameType("SSN");
		if (DowJonesSamRecordTypeEnum.PERSON == recordType) {
			name.setSingleStringName(userRelationDataInfo.getLastName() + " " + userRelationDataInfo.getFirstName());
//			name.setFirstName(userRelationDataInfo.getFirstName());
//			name.setLastName(userRelationDataInfo.getLastName());
		} else {
			name.setSingleStringName(userRelationDataInfo.getName());
//			name.setEntityName(userRelationDataInfo.getName());
		}

		DowJonesAssociationCreateReq req = new DowJonesAssociationCreateReq();
		req.getData().getAttributes().setExternalId(externalId)
				.setGender(gender)
				.setRecordType(recordType.getType())
				.setYearOfBirth(yearOfBirth)
				.setNames(Lists.newArrayList(name))
				;

		return associationCreate(tid, req);
	}

	private DowJonesAssociationCreateRes associationCreate(String tid, DowJonesAssociationCreateReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getAssociationCreateUrl(), HttpMethod.POST, null, JsonUtil.encode(req));
		DowJonesAssociationCreateRes associationCreateRes = JsonUtil.decode(res, DowJonesAssociationCreateRes.class);
		log.info("[tid={}] DowJonesManager associationCreate associationId={}", tid, associationCreateRes.getAssociationId());
		return associationCreateRes;
	}

	public DowJonesCaseGetAssociationRes caseGetAssociation(String tid, String caseId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseGetAssociationUrl(caseId), HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesCaseGetAssociationRes.class);
	}

	public DowJonesCaseGetRes getCase(String tid, String caseId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseGetUrl(caseId), HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesCaseGetRes.class);
	}

	public DowJonesCaseUpdateRes updateCase(String tid, Long userId) throws Exception {
		DowJonesSamCaseDO caseDO = dowJonesSamCaseMapper.findOneByUserId(userId);
		if (null == caseDO) {
      		log.error("[tid={}] updateCase case not found. userId={}", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] updateCase case not found. userId=%s", tid, userId));
		}
		return updateCase(tid, userId, caseDO.getDowJonesSamCaseId());
	}

	public DowJonesCaseUpdateRes updateCase(String tid, Long userId, String caseId) throws Exception {
		String caseName = String.valueOf(userId);
		String externalId = String.format("%s_%s", springConfig.getEnvironment().name(), userId);
		DowJonesCaseUpdateReq req = new DowJonesCaseUpdateReq();
		req.getData().getAttributes().setCaseName(caseName)
				.setExternalId(externalId)
				.getOptions().setSearchType(dowJonesConfig.getSearchType().getType())
				.setFilterContentCategoryList(dowJonesConfig.getFilterContentCategory())
		;
		return updateCase(tid, caseId, req);
	}

	public DowJonesCaseCreateRes createCase(String tid, Long userId) throws Exception {
		String caseName = String.valueOf(userId);
		String externalId = String.format("%s_%s", springConfig.getEnvironment().name(), userId);
		DowJonesCaseCreateReq req = new DowJonesCaseCreateReq();
		req.getData().getAttributes().setCaseName(caseName)
				.setExternalId(externalId)
				.getOptions().setSearchType(dowJonesConfig.getSearchType().getType())
				.setFilterContentCategoryList(dowJonesConfig.getFilterContentCategory())
		;
		return createCase(tid, req);
	}

	private DowJonesCaseUpdateRes updateCase(String tid, String caseId, DowJonesCaseUpdateReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseUpdateUrl(caseId), HttpMethod.PATCH, null, JsonUtil.encode(req));
		DowJonesCaseUpdateRes caseUpdateRes = JsonUtil.decode(res, DowJonesCaseUpdateRes.class);
		log.info("[tid={}] DowJonesManager updateCase caseId={}", tid, caseId);
		return caseUpdateRes;
	}

	private DowJonesCaseCreateRes createCase(String tid, DowJonesCaseCreateReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseCreateUrl(), HttpMethod.POST, null, JsonUtil.encode(req));
		DowJonesCaseCreateRes caseCreateRes = JsonUtil.decode(res, DowJonesCaseCreateRes.class);
		log.info("[tid={}] DowJonesManager createCase caseId={}", tid, caseCreateRes.getCaseId());
		return caseCreateRes;
	}

	// ========================================== Risk & Compliance Search API ==========================================

	public boolean hasRiskForPerson(String tid,
									  String firstName,
									  String lastName,
									  String birthday,
									  Integer genderCode) {
		try {
			DowJonesRiskEntitiesSearchRes res = riskEntitiesSearchForPerson(tid, firstName, lastName, birthday, genderCode);
			return res.hasRisk();
		} catch (Exception e) {
			throw new DowJonesAntisocialCheckException("DowJonesManager hasRiskForPerson error");
		}
	}

	public boolean hasRiskForEntity(String tid, String fullName) {
		try {
			DowJonesRiskEntitiesSearchRes res = riskEntitiesSearchForEntity(tid, fullName);
			return res.hasRisk();
		} catch (Exception e) {
			throw new DowJonesAntisocialCheckException("DowJonesManager hasRiskForEntity error");
		}
	}

	/**
	 *
	 * @param tid
	 * @param firstName
	 * @param lastName
	 * @param birthday yyyyMMdd
	 * @param genderCode 0:male 1:female
	 * @return
	 * @throws Exception
	 */
	public DowJonesRiskEntitiesSearchRes riskEntitiesSearchForPerson(String tid,
																	 String firstName,
																	 String lastName,
																	 String birthday,
																	 Integer genderCode) throws Exception {
		String year = null;
		String month = null;
		String day = null;

		if (StringUtils.isNotBlank(birthday)) {
			char[] chars = birthday.toCharArray();
			year = new String(Arrays.copyOfRange(chars, 0, 4));
			month = new String(Arrays.copyOfRange(chars, 4, 6));
			day = new String(Arrays.copyOfRange(chars, 6, 8));
		}

		String gender = null == genderCode ? "unknown" : switch (genderCode) {
			case 0 ->  "male";
			case 1 ->  "female";
			default ->  "unknown";
		};

		DowJonesRiskEntitiesSearchReq req = new DowJonesRiskEntitiesSearchReq();
		req.getData().getAttributes().getFilterGroupAnd().getFilters()
			.setSearchKeyword(new DowJonesRiskEntitiesSearchReq.SearchKeyword()
					.setText(lastName + " " + firstName)
					.setScope(Lists.newArrayList("Name"))
					.setType(dowJonesConfig.getSearchType().getType().toUpperCase())
			)
        //			.setPersonName(new DowJonesRiskEntitiesSearchReq.PersonName()
        //					.setFirstName(firstName)
        //					.setLastName(lastName)
        //					.setSearchType(dowJonesConfig.getSearchType().getType())
        //			)
        .setDateOfBirth(
            new DowJonesRiskEntitiesSearchReq.DateOfBirth()
                .setDate(
                    new DowJonesRiskEntitiesSearchReq.Date()
                        .setYear(year)
                        .setMonth(month)
                        .setDay(day)))
        .setGender(gender)
        .setRecordTypes(Lists.newArrayList("Person"))
        .setContentSet(dowJonesConfig.getContentSet());

		return riskEntitiesSearch(tid, req);
	}

	public DowJonesRiskEntitiesSearchRes riskEntitiesSearchForEntity(String tid, String fullName) throws Exception {
		DowJonesRiskEntitiesSearchReq req = new DowJonesRiskEntitiesSearchReq();
		req.getData().getAttributes().getFilterGroupAnd().getFilters()
				.setSearchKeyword(new DowJonesRiskEntitiesSearchReq.SearchKeyword()
						.setText(fullName)
						.setScope(Lists.newArrayList("Name"))
						.setType(dowJonesConfig.getSearchType().getType().toUpperCase())
				)
//				.setEntityName(new DowJonesRiskEntitiesSearchReq.EntityName()
//						.setFullName(fullName)
//						.setSearchType(dowJonesConfig.getSearchType().getType())
//				)

				.setRecordTypes(Lists.newArrayList("Entity"))
				.setContentSet(dowJonesConfig.getContentSet())
		;

		return riskEntitiesSearch(tid, req);
	}

	private DowJonesRiskEntitiesSearchRes riskEntitiesSearch(String tid, DowJonesRiskEntitiesSearchReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getRiskEntitiesSearchUrl(), HttpMethod.POST, null, JsonUtil.encode(req));
		DowJonesRiskEntitiesSearchRes searchRes = JsonUtil.decode(res, DowJonesRiskEntitiesSearchRes.class);
		log.info("[tid={}] DowJonesManager riskEntitiesSearch screeningContext={}", tid, searchRes.getScreeningContext());
		return searchRes;
	}

	// ========================================== Auth ==========================================

	private DowJonesGetAuthZRes getAuthZ(String tid) throws Exception {
		DowJonesGetAuthZRes res = getAuthZCache();
		if (null != res) {
			return res;
		}
		synchronized (this) {
			res = getAuthZCache();
			if (null != res) {
				return res;
			}
			log.info("[tid={}]Dow Jones getAuthZ start", tid);
			DowJonesGetAuthIdRes getAuthIdRes = getAuthId(tid);

			try{
				res = getAuthZ(tid, new DowJonesGetAuthZReq()
					.setAssertion(getAuthIdRes.getAccessToken())
					.setClientId(dowJonesConfig.getClientId())
				);
				log.info("[tid={}]Dow Jones getAuthZ result={}", tid, JsonUtil.encode(res));
				setAuthZCache(res);
				return res;
			} catch (Exception e) {
				log.error("[tid={}]Dow Jones getAuthZ error", tid, e);
				throw e;
			}
		}
	}

	private DowJonesGetAuthIdRes getAuthId(String tid) throws Exception {
		DowJonesGetAuthIdRes res = getAuthIdCache();
		if (null != res) {
			return res;
		}
		synchronized (this) {
			res = getAuthIdCache();
			if (null != res) {
				return res;
			}
			log.info("[tid={}]Dow Jones getAuthId start", tid);
			DowJonesGetAuthNRes getAuthNRes = getAuthN(tid);

			try{
				res = getAuthId(tid, new DowJonesGetAuthIdReq()
					.setClientId(dowJonesConfig.getClientId())
					.setRefreshToken(getAuthNRes.getRefreshToken())
				);
				log.info("[tid={}]Dow Jones getAuthId result={}", tid, JsonUtil.encode(res));
				setAuthIdCache(res);
				return res;
			} catch (Exception e) {
				log.error("[tid={}]Dow Jones getAuthId error", tid, e);
				throw e;
			}
		}
	}

	private DowJonesGetAuthNRes getAuthN(String tid) throws Exception {
		DowJonesGetAuthNRes res = getAuthNCache();
		if (null != res) {
			return res;
		}
		synchronized (this) {
			res = getAuthNCache();
			if (null != res) {
				return res;
			}
			log.info("[tid={}]Dow Jones getAuthN start", tid);

			try{
				res = getAuthN(tid,
					new DowJonesGetAuthNReq()
						.setClientId(dowJonesConfig.getClientId())
						.setUsername(dowJonesConfig.getUsername())
						.setPassword(dowJonesConfig.getPassword())
						.setDevice(dowJonesConfig.getDevice())
					);

				log.info("[tid={}]Dow Jones getAuthN result={}", tid, JsonUtil.encode(res));
				setAuthNCache(res);
				return res;
			} catch (Exception e) {
				log.error("[tid={}]Dow Jones getAuthN error", tid, e);
				throw e;
			}
		}
	}

	private DowJonesGetAuthNRes getAuthN(String tid, DowJonesGetAuthNReq req) throws Exception {
		String res = request(tid, null, dowJonesConfig.getOAuth2V1TokenUrl(), HttpMethod.POST, null, JsonUtil.encode(req));
		return JsonUtil.decode(res, DowJonesGetAuthNRes.class);
	}

	private DowJonesGetAuthIdRes getAuthId(String tid, DowJonesGetAuthIdReq req) throws Exception {
		String res = request(tid, null, dowJonesConfig.getOAuth2V1TokenUrl(), HttpMethod.POST, null, JsonUtil.encode(req));
		return JsonUtil.decode(res, DowJonesGetAuthIdRes.class);
	}

	private DowJonesGetAuthZRes getAuthZ(String tid, DowJonesGetAuthZReq req) throws Exception {
		String res = request(tid, null, dowJonesConfig.getOAuth2V1TokenUrl(), HttpMethod.POST, null, JsonUtil.encode(req));
		return JsonUtil.decode(res, DowJonesGetAuthZRes.class);
	}

	// ========================================== Base ==========================================

	private String request(String tid, String token, String url, String method, Map<String, Object> params, String body) throws Exception {
		Map<String, String> headers = new HashMap<>();
		headers.put("Content-Type", "application/json");
		headers.put("Accept", "application/json");
		if (StringUtils.isNotBlank(token)) {
			headers.put("Authorization", String.format("Bearer %s", token));
		}

		try {
			String res =  HttpUtil.requestAsString(url, method, headers, params, body);
			log.info("[tid={}] Dow Jones Api Request success. url={},method={},headers={},params={},body={},res={}", tid, url, method, JsonUtil.encode(headers), JsonUtil.encode(params), body, res);
			return res;
		} catch (Exception e) {
			log.error("[tid={}] Dow Jones Api Request error. url={},method={},headers={},params={},body={}", tid, url, method, JsonUtil.encode(headers), JsonUtil.encode(params), body, e);
			throw  e;
		}
	}

	private DowJonesGetAuthZRes getAuthZCache() {
		return redisClient.getObject("dowjones:auth:z", DowJonesGetAuthZRes.class);
	}

	private void setAuthZCache(DowJonesGetAuthZRes res) {
		if (null == res) {
			return;
		}
		redisClient.setObject("dowjones:auth:z", res, res.getExpiresIn() - 120, TimeUnit.SECONDS);
	}

	private DowJonesGetAuthIdRes getAuthIdCache() {
		return redisClient.getObject("dowjones:auth:id", DowJonesGetAuthIdRes.class);
	}

	private void setAuthIdCache(DowJonesGetAuthIdRes res) {
		if (null == res) {
			return;
		}
		redisClient.setObject("dowjones:auth:id", res, res.getExpiresIn() - 120, TimeUnit.SECONDS);
	}

	private DowJonesGetAuthNRes getAuthNCache() {
		return redisClient.getObject("dowjones:auth:n", DowJonesGetAuthNRes.class);
	}

	private void setAuthNCache(DowJonesGetAuthNRes res) {
		if (null == res) {
			return;
		}
		redisClient.setObject("dowjones:auth:n", res);
	}

	public DowJonesAssociationDeleteRes caseDelete(String tid, String caseId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseDeleteUrl(caseId), HttpMethod.DELETE, null, null);
		DowJonesAssociationDeleteRes associationDeleteRes = JsonUtil.decode(res, DowJonesAssociationDeleteRes.class);
		log.info("[tid={}] DowJonesManager associationDelete caseId={}", tid, caseId);
		return associationDeleteRes;
	}


	public DowJonesCaseCreateRes createCaseGroup(String tid, String groupId) throws Exception {
		String caseName = String.valueOf(groupId);
		String externalId = String.format("%s_%s", springConfig.getEnvironment().name(), groupId);
		DowJonesCaseCreateReq req = new DowJonesCaseCreateReq();
		req.getData().getAttributes().setCaseName(caseName)
				.setExternalId(externalId)
				.getOptions().setSearchType(dowJonesConfig.getSearchType().getType())
				.setFilterContentCategoryList(dowJonesConfig.getFilterContentCategory())
		;
		return createCase(tid, req);
	}

	public DowJonesCaseAddAssociationRes createCaseBulkAssociation(String tid,String caseId, DowJonesBulkAssociationCaseCreateReq req) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseBulkAddAssociationsUrl(caseId), HttpMethod.POST, null,JsonUtil.encode(req));
		DowJonesCaseAddAssociationRes associationCreateRes = JsonUtil.decode(res, DowJonesCaseAddAssociationRes.class);
		return  associationCreateRes;
	}

	public DowJonesCaseAddAssociationRes getStatusBulkAssociation(String tid,String caseId, String tsId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getBulkTransactionStatusUrl(caseId,tsId), HttpMethod.GET, null, null);
		DowJonesCaseAddAssociationRes associationCreateRes = JsonUtil.decode(res, DowJonesCaseAddAssociationRes.class);
		return  associationCreateRes;
	}

	public List<DowJonesBulkAssociationCaseCreateReq.Association> createAssociation(List<DowJonesUserRelationDataInfoDTO> list) {
		List<DowJonesBulkAssociationCaseCreateReq.Association> createAssociations = new ArrayList<>();
		list.forEach(userRelationDataInfo -> {
			DowJonesUserRelationDataTypeEnum dowJonesUserRelationDataTypeEnum = DowJonesUserRelationDataTypeEnum.ofType(userRelationDataInfo.getUserRelationDataType());
			DowJonesSamRecordTypeEnum recordType = dowJonesUserRelationDataTypeEnum.getDowJonesSamRecordType();

			String externalIdSuffix = (null == userRelationDataInfo.getUserRelationDataId()) ? IdUtil.getUuid() : String.valueOf(userRelationDataInfo.getUserRelationDataId());
			String externalId = String.format("%s_%s_%s", springConfig.getEnvironment().name(), dowJonesUserRelationDataTypeEnum.name(), externalIdSuffix);
			String gender = null == userRelationDataInfo.getGender() ? null : switch (userRelationDataInfo.getGender()) {
				case 0 ->  "MALE";
				case 1 ->  "FEMALE";
				default ->  "OTHER";
			};
			Integer yearOfBirth = null;
			if (StringUtils.isNotBlank(userRelationDataInfo.getBirthday())) {
				char[] chars = userRelationDataInfo.getBirthday().toCharArray();
				String year = new String(Arrays.copyOfRange(chars, 0, 4));
				yearOfBirth = Integer.valueOf(year);
			}

			DowJonesBulkAssociationCaseCreateReq.Name name = getCreateName(userRelationDataInfo, recordType);
			DowJonesBulkAssociationCaseCreateReq.Association association = new DowJonesBulkAssociationCaseCreateReq.Association();
			association.setExternalId(externalId);
			association.setYearOfBirth(yearOfBirth);
			association.setGender(gender);
			association.setRecordType("PERSON");
			association.setNames(List.of(name));
			createAssociations.add(association);
		});
		return createAssociations;
	}

	private static DowJonesBulkAssociationCaseCreateReq.Name getCreateName(DowJonesUserRelationDataInfoDTO userRelationDataInfo, DowJonesSamRecordTypeEnum recordType) {
		DowJonesBulkAssociationCaseCreateReq.Name name = new DowJonesBulkAssociationCaseCreateReq.Name();
		name.setNameType("SSN");
		if (DowJonesSamRecordTypeEnum.PERSON == recordType) {
			name.setSingleStringName(userRelationDataInfo.getLastName() + " " + userRelationDataInfo.getFirstName());
		} else {
			name.setEntityName(userRelationDataInfo.getName());
		}
		return name;
	}

	public DowJonesAssociationGetAllRes associationSearch(String tid, String associationId, int limit, int offset) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getAssociationCreateUrl()+"?page[limit]=" + limit + "&page[offset]=" + offset, HttpMethod.GET, null, null);
		DowJonesAssociationGetAllRes associationUpdateRes = JsonUtil.decode(res, DowJonesAssociationGetAllRes.class);
		log.info("[tid={}] DowJonesManager associationSearch associationId={}", tid, associationId);
		return associationUpdateRes;
	}

	public DowJonesAssociationDeleteRes dowJonesAssociationDelete(String tid, String dowJonesSamAssociationId) throws Exception {
		log.info("[uuid={}] DowJonesManager dowJonesAssociationDelete dowJonesSamAssociationId={}", tid, dowJonesSamAssociationId);
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getAssociationDeleteUrl(dowJonesSamAssociationId), HttpMethod.DELETE, null, null);
		return JsonUtil.decode(res, DowJonesAssociationDeleteRes.class);
	}

	public DowJonesCaseGetAllResponse getByCase(String tid) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res =  request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseCreateUrl() +"?page[offset]=0&page[limit]=1000", HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesCaseGetAllResponse.class);
	}

	public DowJonesCaseGetMatchesRes caseGetMatchesPage(String tid, String caseId) throws Exception {
		DowJonesGetAuthZRes getAuthZRes = getAuthZ(tid);
		String res = request(tid, getAuthZRes.getAccessToken(), dowJonesConfig.getCaseGetMatchesUrl(caseId) + "?page[offset]=0&page[limit]=1000", HttpMethod.GET, null, null);
		return JsonUtil.decode(res, DowJonesCaseGetMatchesRes.class);
	}
}
