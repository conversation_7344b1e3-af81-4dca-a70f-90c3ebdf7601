package exchange.common.component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.Serializable;
import java.util.List;
import javax.annotation.PostConstruct;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.SSECustomerKey;
import exchange.common.config.AwsConfig;
import exchange.common.config.SpringConfig;
import exchange.common.util.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Component
public class S3Manager {

  @AllArgsConstructor
  public static class Bucket {

    @Getter @Setter private String name;
  }

  private final AwsConfig awsConfig;

  protected final SpringConfig springConfig;

  private AmazonS3 s3Client;
  
  @PostConstruct
  private void initialize() {
    s3Client =
        AmazonS3ClientBuilder.standard()
            .withCredentials(awsConfig.getCredentialsProvider())
            .withRegion(awsConfig.getRegion().getName())
            .build();
  }

  public void put(Bucket bucket, String path, byte[] bytes, String contentType) {
    ObjectMetadata objectMetadata = new ObjectMetadata();
    objectMetadata.setContentType(contentType);
    objectMetadata.setContentLength(bytes.length);
    s3Client.putObject(
        new PutObjectRequest(
            bucket.getName(), path, new ByteArrayInputStream(bytes), objectMetadata));
  }

  public void put(Bucket bucket, String path, byte[] bytes, String contentType, String seed) {
    ObjectMetadata objectMetadata = new ObjectMetadata();
    objectMetadata.setContentType(contentType);
    objectMetadata.setContentLength(bytes.length);
    s3Client.putObject(
        new PutObjectRequest(
                bucket.getName(), path, new ByteArrayInputStream(bytes), objectMetadata)
            .withSSECustomerKey(awsConfig.getSSECustomerKey(seed)));
  }
  
  public void putJson(Bucket bucket, String path, Serializable data) {
    put(bucket, path, JsonUtil.encode(data).getBytes(), MediaType.APPLICATION_JSON_VALUE);
  }

  public void putBytes(Bucket bucket, String path, byte[] bytes) {
    put(bucket, path, bytes, MediaType.TEXT_PLAIN_VALUE);
  }

  public void putBytes(Bucket bucket, String path, byte[] bytes, Long seed) {
    put(bucket, path, bytes, MediaType.TEXT_PLAIN_VALUE, String.valueOf(seed));
  }
  
  public void putFile(Bucket bucket, String path, File file) {
    s3Client.putObject(new PutObjectRequest(bucket.getName(), path, file));
  }

  public S3Object getObject(Bucket bucket, String key) {
    return s3Client.getObject(bucket.getName(), key);
  }

  public String getObjectAsString(Bucket bucket, String key) {
    return s3Client.getObjectAsString(bucket.getName(), key);
  }

  public S3ObjectInputStream getObjectContent(
      Bucket bucket, String key, SSECustomerKey sseCustomerKey) {
    GetObjectRequest getObjectRequest =
        new GetObjectRequest(bucket.getName(), key).withSSECustomerKey(sseCustomerKey);
    return s3Client.getObject(getObjectRequest).getObjectContent();
  }
  
  public S3ObjectInputStream getObjectContent(Bucket bucket, String key, Long seed) {
    return getObjectContent(bucket, key, awsConfig.getSSECustomerKey(String.valueOf(seed)));
  }
  
  public S3ObjectInputStream getObjectContentWithoutSeed(Bucket bucket, String key) {
    GetObjectRequest getObjectRequest =
        new GetObjectRequest(bucket.getName(), key);
    return s3Client.getObject(getObjectRequest).getObjectContent();
  }

  public List<S3ObjectSummary> listObjects(Bucket bucket, String prefix) {
    ListObjectsRequest listObjectsRequest =
        new ListObjectsRequest().withBucketName(bucket.getName()).withPrefix(prefix);
    ObjectListing list = s3Client.listObjects(listObjectsRequest);
    return list.getObjectSummaries();
  }

  public void deleteObject(Bucket bucket, String key) {
    s3Client.deleteObject(new DeleteObjectRequest(bucket.getName(), key));
  }
}
