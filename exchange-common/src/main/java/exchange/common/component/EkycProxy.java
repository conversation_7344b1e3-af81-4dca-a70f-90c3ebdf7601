package exchange.common.component;

import exchange.common.constant.ErrorCode;
import exchange.common.exception.CustomException;
import exchange.common.http.cb.HttpClient;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
public class EkycProxy {

  public static HttpClient.HttpResult reqApi(
      String api, List<String> headers, Map<String, String> params) throws IOException {
    HttpClient.HttpResult result = HttpClient.httpGet(api, headers, params);

    if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
      throw new IOException(
          "failed to req API:" + api + ". code:" + result.code + " msg: " + result.content);
    }
    return result;
  }

  public static String reqApi(
      String api, List<String> headers, String payload, Consumer<HttpClient.HttpResult> callback)
      throws IOException, CustomException {
    HttpClient.HttpResult result = HttpClient.httpPost(api, headers, payload);

    if (HttpURLConnection.HTTP_CLIENT_TIMEOUT == result.code
        || HttpURLConnection.HTTP_UNAVAILABLE == result.code) {
      log.warn("the status code({}) by api({})", result.code, api);
      throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_TIMEOUT);
    }

    if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
      throw new IOException(
          "failed to req API:" + api + ". code:" + result.code + " msg: " + result.content);
    }

    callback.accept(result);
    return result.content;
  }
}
