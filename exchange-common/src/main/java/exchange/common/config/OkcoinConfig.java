package exchange.common.config;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.TreeMap;
import org.apache.http.NameValuePair;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import com.amazonaws.HttpMethod;
import exchange.common.constant.TradeType;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.common.util.JsonUtil;
import exchange.common.util.SignatureUtil;
import exchange.common.util.SignatureUtil.Algorithm;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Configuration
@ConfigurationProperties(prefix = "dealing.okcoin")
@RequiredArgsConstructor
public class OkcoinConfig extends ExchangeConfig {

  @Getter @Setter private String passPhrase;

  /** Path */
  public String getPathOfV3Orderbook(String instrument_id) {
    return "/api/spot/v3/instruments/" + instrument_id + "/book";
  }

  public String getPathOfV3Order(TradeType tradeType) {
    return "/api/" + tradeType.toConnectedLowerCase() + "/v3/orders";
  }

  public String getPathOfV3OrderId(TradeType tradeType, Long orderId) {
    return "/api/" + tradeType.toConnectedLowerCase() + "/v3/orders/" + orderId;
  }

  public String getPathOfV3FilledOrders(TradeType tradeType) {
    return "/api/" + tradeType.toConnectedLowerCase() + "/v3/fills";
  }

  public String getPathOfV3OrderConfigs(TradeType tradeType) {
    return "/api/spot/v3/instruments";
  }

  public String getPathOfV3Assets(TradeType tradeType) {
    return "/api/" + tradeType.toConnectedLowerCase() + "/v3/accounts";
  }
  
  public String getPathOfV3MarketData(String instrument_id) {
    return "/api/spot/v3/instruments/" + instrument_id + "/candles";
  }

  /** Url */
  public String getUrlOfV3Orderbook(String instrument_id) {
    return getHost() + getPathOfV3Orderbook(instrument_id);
  }

  public String getUrlOfV3Order(TradeType tradeType) {
    return getHost() + getPathOfV3Order(tradeType);
  }

  public String getUrlOfV3OrderId(TradeType tradeType, Long orderId) {
    return getHost() + getPathOfV3OrderId(tradeType, orderId);
  }

  public String getUrlOfV3FilledOrders(TradeType tradeType) {
    return getHost() + getPathOfV3FilledOrders(tradeType);
  }

  public String getUrlOfV3OrderConfigs(TradeType tradeType) {
    return getHost() + getPathOfV3OrderConfigs(tradeType);
  }

  public String getUrlOfV3Assets(TradeType tradeType) {
    return getHost() + getPathOfV3Assets(tradeType);
  }
  
  public String getUrlOfV3MarketData(String instrument_id) {
    return getHost() + getPathOfV3MarketData(instrument_id);
  }

  /** createHeader */
  public String createSign(String secret, String seed) {
    return SignatureUtil.getBase64Hash(Algorithm.HMAC_SHA256, secret, seed);
  }

  private Map<String, String> createHeaderMap(
      String identifier, String method, String path, String body) {
    Map<String, String> headerMap = new HashMap<>();
    // nonce重複排除 redis lock
    Long baseNonce = Long.valueOf(getNonce(identifier, getApiKey()));
    String nonce = FormatUtil.format(new Date(baseNonce), FormatPattern.YYYY_MM_DDTHH_MM_SS_SSSZ);
    headerMap.put("OK-ACCESS-KEY", getApiKey());
    if (body == null) {
      headerMap.put("OK-ACCESS-SIGN", createSign(getSecret(), nonce + method + path));
    } else {
      headerMap.put("OK-ACCESS-SIGN", createSign(getSecret(), nonce + method + path + body));
    }
    headerMap.put("OK-ACCESS-TIMESTAMP", nonce);
    headerMap.put("OK-ACCESS-PASSPHRASE", getPassPhrase());
    return headerMap;
  }

  public Map<String, String> createHeaderMapGetOrDelete(
      String identifier, HttpMethod method, String path, List<NameValuePair> nameValuePairs) {
    if (!CollectionUtils.isEmpty(nameValuePairs)) {
      StringJoiner stringJoiner = new StringJoiner("&");
      nameValuePairs.forEach(
          nameValuePair ->
              stringJoiner.add(nameValuePair.getName() + "=" + nameValuePair.getValue()));
      path = path + "?" + stringJoiner.toString();
    }

    return createHeaderMap(identifier, method.toString(), path, null);
  }

  public Map<String, String> createHeaderMapPostOrPut(
      String identifier, HttpMethod method, String path, TreeMap<String, Object> parameterMap) {
    return createHeaderMap(identifier, method.toString(), path, JsonUtil.encode(parameterMap));
  }
}
