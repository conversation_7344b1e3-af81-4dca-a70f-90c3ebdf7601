package exchange.common.config;

import java.util.List;
import javax.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.Setter;

@ConfigurationProperties(prefix = "gmo")
@Configuration
@Getter
@Setter
public class GmoConfig {

    @PostConstruct
    public void init(){
        StringConfig.BASE_ENDPOINT=this.stgBaseEndpoint;
    }

    private String clientId;
    private String secret;
    private String stgBaseEndpoint;
    private String holderNameKana;
    private String redirectUri;
    private List<String> scope;

    private String authorizationToken;
    private String issueAccountUri;
    private String accountsUri;
    private Long refreshTokenInterval;
    private String depositTransactions;
    private String bulkTransferUri;
    private String bulkTransferRequestResultUri;
    private String bulkTransferStatusUri;
}
