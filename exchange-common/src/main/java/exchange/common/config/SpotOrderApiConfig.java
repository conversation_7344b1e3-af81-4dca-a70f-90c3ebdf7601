package exchange.common.config;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.TreeMap;
import org.apache.http.NameValuePair;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import exchange.common.constant.TradeType;
import exchange.common.entity.ApiInfo;
import exchange.common.util.JsonUtil;
import exchange.common.util.SignatureUtil;
import exchange.common.util.SignatureUtil.Algorithm;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

@Configuration
@ConfigurationProperties(prefix = "dealing.cb")
public class SpotOrderApiConfig extends ExchangeConfig {

  @Getter @Setter private String port;

  /** Path */
  public String getPathOfV1Order(TradeType tradeType) {
    return "/api/v1/" + tradeType.toConnectedLowerCase() + "/order";
  }

  public String getPathOfV1OrderOfHistory(TradeType tradeType) {
    return "/api/v1/" + tradeType.toConnectedLowerCase() + "/order/history";
  }

  /** Url */
  public String getUrlOfV1OrderMulti(TradeType tradeType) {
    return getHost() + ":" + getPort() + getPathOfV1Order(tradeType) + "/multi";
  }

  /** createHeader */
  public String createSignature(String secret, String seed) {
    return SignatureUtil.getHash(Algorithm.HMAC_SHA256, secret, seed);
  }

  private Map<String, String> createHeaderMap(String seed, ApiInfo orderUserApiInfo) {
    String nonce = Long.toString(new Date().getTime());
    Map<String, String> headerMap = new HashMap<>();
    headerMap.put("API-KEY", orderUserApiInfo.getApiKey());
    headerMap.put("NONCE", nonce);
    headerMap.put("SIGNATURE", createSignature(orderUserApiInfo.getSecret(), nonce + seed));
    return headerMap;
  }

   //実装時にコメントアウト & 検証する
   public Map<String, String> createHeaderMapGetOrDelete(String path, List<NameValuePair> nameValuePairs, ApiInfo marketMakerApiInfo) {
     if (!CollectionUtils.isEmpty(nameValuePairs)) {
       StringJoiner stringJoiner = new StringJoiner("&");
       nameValuePairs.forEach(
           nameValuePair ->
               stringJoiner.add(nameValuePair.getName() + "=" + nameValuePair.getValue()));
       path = path + "?" + stringJoiner.toString();
     }

     return createHeaderMap(path, marketMakerApiInfo);
   }

  public Map<String, String> createHeaderMapPostOrPut(
      TreeMap<String, Object> parameterMap, ApiInfo orderUserApiInfo) {
    return createHeaderMap(JsonUtil.encode(parameterMap), orderUserApiInfo);
  }
}
