package exchange.common.config;

import exchange.common.model.request.SNSRequestForm;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "sns")
public class SNSConfig {

  private String host;

  private String token;

  public Map<String, String> createHeaderMap() {
    return Map.of("token", token, "accept", MediaType.APPLICATION_JSON_VALUE, "Content-Type", MediaType.APPLICATION_JSON_VALUE);
  }

  public Map<String, Object> createEntityMap(SNSRequestForm snsRequestForm) {
    return Map.of("contacts", snsRequestForm.getContacts(), "text_message",
        snsRequestForm.getTextMessage());
  }
}
