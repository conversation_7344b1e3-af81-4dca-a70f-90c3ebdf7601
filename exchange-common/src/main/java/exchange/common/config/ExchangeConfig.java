package exchange.common.config;

import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import exchange.common.component.RedisManager;
import exchange.common.component.RedisManager.LockParams;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class ExchangeConfig {

  @Getter @Setter private String apiKey;

  @Getter @Setter private String secret;

  @Autowired private RedisManager redisManager;

  @Autowired private RedisTemplate<String, Date> dateRedisTemplate;

  @Getter @Setter private String host;

  private String getLockKey(String identifier, String apiKey) {
    return identifier + ":lock:" + apiKey;
  }

  private String getLatestNonceKey(String identifier, String apiKey) {
    return identifier + ":latestNonce:" + apiKey;
  }

  public String getNonce(String identifier, String apiKey) {
    try {
      return (String)
          redisManager.executeWithLock(
              getLockKey(identifier, apiKey),
              LockParams.DEFAULT,
              () -> {
                Date nonce = new Date();
                String latestNonceKey = getLatestNonceKey(identifier, apiKey);
                Date latestNonce = dateRedisTemplate.opsForValue().get(latestNonceKey);

                if (latestNonce != null && nonce.compareTo(latestNonce) < 1) {
                  nonce = new Date(latestNonce.getTime() + 1);
                }

                dateRedisTemplate.opsForValue().set(latestNonceKey, nonce);
                return Long.toString(nonce.getTime());
              });
    } catch (Exception e) {
      log.error(getClass().getName(), e);
      return Long.toString(new Date().getTime());
    }
  }
}
