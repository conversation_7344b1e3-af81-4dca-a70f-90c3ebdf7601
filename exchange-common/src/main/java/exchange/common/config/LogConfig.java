package exchange.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.Setter;

/**
 * ログ出力設定
 *
 * <AUTHOR>
 *
 */
@Configuration
@ConfigurationProperties(prefix = "common.log")
public class LogConfig {

  /**
   * コンソール(stdout/stderr) に出力するログの形式。<br>
   * logback-spring.xml で直接参照している。
   *
   * <p>
   * 以下の値を設定可能。もしくは logback-spring.xml で独自の Appender を作成して指定できる。
   * <ul>
   * <li>CONSOLE_DEFAULT - ローカル環境実行時のデフォルト設定。一般的な Spring Boot のログ形式で出力。</li>
   * <li>CONSOLE_JSON - AWS 環境でのデフォルト設定。json 形式で出力。</li>
   * </p>
   */
  @Getter
  @Setter
  @Value("${common.log.console.appender:#{null}}")
  private String consoleAppender;

  /**
   * リクエストログ出力時に詳細を出力するか。<br>
   * <p>デフォルト true。</p>
   * <p>true を指定するとリクエスト属性、リクエストヘッダ情報を追加で出力する。<br>
   *
   * <p>CloudWwtch Metrics で特定 API 呼び出しを監視するときに使用するので原則 true で運用する。
   */
  @Getter
  @Setter
  private boolean outputRequestDetails = true;
}
