package exchange.common.config;

import exchange.common.constant.DowJonesSearchTypeEnum;
import exchange.common.util.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/12/6
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "dowjones")
@Data
public class DowJonesConfig {

	private String baseAuthUrl;

	private String baseApiUrl;

	private String username;

	private String password;

	private String clientId;

	private DowJonesSearchTypeEnum searchType;

	private String device = "coinbook";

	private List<String> contentSet;

	private List<String> filterContentCategory;

	private Long count;

	@PostConstruct
	public void init() {
		if (StringUtils.isBlank(username)) {
			log.error("dowjones config username not exist");
			return;
		}
		if (StringUtils.isBlank(password)) {
			log.error("dowjones config password not exist");
			return;
		}
		if (StringUtils.isBlank(clientId)) {
			log.error("dowjones config clientId not exist");
			return;
		}
		log.info("dowjones config loaded. username={},clientId={},searchType={},contentSet={},filterContentCategory={}",
				username, clientId, searchType.name(), JsonUtil.encode(contentSet), JsonUtil.encode(filterContentCategory));
	}

	public String getOAuth2V1TokenUrl() {
		return String.format("%s/oauth2/v1/token", baseAuthUrl);
	}

	public String getRiskEntitiesSearchUrl() {
		return String.format("%s/riskentities/search", baseApiUrl);
	}

	public String getCaseCreateUrl() {
		return String.format("%s/risk-entity-screening-cases", baseApiUrl);
	}

	public String getCaseUpdateUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s", baseApiUrl, caseId);
	}

	public String getCaseGetUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s", baseApiUrl, caseId);
	}
	public String getCaseDeleteUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s", baseApiUrl, caseId);
	}

	public String getCaseGetMatchesUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s/matches", baseApiUrl, caseId);
	}

	public String getCaseGetAssociationUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s/risk-entity-screening-associations?page[limit]=1000", baseApiUrl, caseId);
	}

	public String getAssociationCreateUrl() {
		return String.format("%s/risk-entity-screening-associations", baseApiUrl);
	}

	public String getAssociationUpdateUrl(String associationId) {
		return String.format("%s/risk-entity-screening-associations/%s", baseApiUrl, associationId);
	}

	public String getAssociationDeleteUrl(String associationId) {
		return String.format("%s/risk-entity-screening-associations/%s", baseApiUrl, associationId);
	}

	public String getAssociationGetUrl(String associationId) {
		return String.format("%s/risk-entity-screening-associations/%s", baseApiUrl, associationId);
	}

	public String getCaseAddAssociationUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s/risk-entity-screening-associations", baseApiUrl, caseId);
	}

	public String getCaseDeleteAssociationUrl(String caseId, String associationId) {
		return String.format("%s/risk-entity-screening-cases/%s/risk-entity-screening-associations/%s", baseApiUrl, caseId, associationId);
	}

	public String getTransaction(String transactionId) {
		return String.format("%s/risk-entity-screening-transactions/%s", baseApiUrl, transactionId);
	}

	public String getCaseTransaction(String caseId, String transactionId) {
		return String.format("%s/risk-entity-screening-cases/%s/transactions/%s", baseApiUrl, caseId, transactionId);
	}

	public String getCaseBulkCreateUrl() {
		return String.format("%s/risk-entity-screening-cases/bulk-associations", baseApiUrl);
	}

	public String getCaseBulkAddAssociationsUrl(String caseId) {
		return String.format("%s/risk-entity-screening-cases/%s/bulk-associations", baseApiUrl, caseId);
	}

	public String getBulkTransactionStatusUrl(String caseId,String transactionId) {
		return String.format("%s/risk-entity-screening-cases/%s/bulk-associations/%s?details=true", baseApiUrl, caseId, transactionId);
	}
}
