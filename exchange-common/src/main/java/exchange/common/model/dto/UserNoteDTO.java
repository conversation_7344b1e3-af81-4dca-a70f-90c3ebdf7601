package exchange.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import exchange.common.constant.KycStatus;
import exchange.common.constant.UserNoteTypeEnum;
import java.util.Date;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Setter
@Getter
public class UserNoteDTO{

    private Long userId;
    private String note;
    private UserNoteTypeEnum noteType;
    private KycStatus currentKycStatus;
    private String operator;
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date createdAt;
}
