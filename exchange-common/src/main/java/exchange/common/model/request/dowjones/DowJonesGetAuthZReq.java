package exchange.common.model.request.dowjones;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: wen.y
 * @date: 2024/12/6
 */
@Accessors(chain = true)
@Data
public class DowJonesGetAuthZReq {

	@JsonProperty("assertion")
	private String assertion;

	@JsonProperty("client_id")
	private String clientId;

	@JsonProperty("grant_type")
	private String grantType = "urn:ietf:params:oauth:grant-type:jwt-bearer";

	@JsonProperty("scope")
	private String scope = "openid pib";
}
