package exchange.common.model.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class FiatWithdrawalInputForm {

    @Getter
    @Setter
    @NotNull
    private Long userId;

    @Getter
    @Setter
    @NotNull
    private BigDecimal amount;

    @Getter
    @Setter
    private BigDecimal fee;

    @Getter
    @Setter
    @NotNull
    private Long BankAccountId;

    @Getter
    @Setter
    @NotNull
    private String fiatWithdrawalStatus;

    @Getter
    @Setter
    private String comment;
}
