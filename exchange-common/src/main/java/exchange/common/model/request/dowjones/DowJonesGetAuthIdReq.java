package exchange.common.model.request.dowjones;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: wen.y
 * @date: 2024/12/6
 */
@Accessors(chain = true)
@Data
public class DowJonesGetAuthIdReq {

	@JsonProperty("client_id")
	private String clientId;

	@JsonProperty("grant_type")
	private String grantType = "refresh_token";

	@JsonProperty("refresh_token")
	private String refreshToken;

	@JsonProperty("scope")
	private String scope = "openid service_account_id";
}
