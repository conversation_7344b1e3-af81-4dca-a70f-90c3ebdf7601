package exchange.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ToString
public class SpotOrderForm {

  @Getter @Setter @NotNull private Long symbolId;

  @Getter
  @Setter
  @NotNull
  @Pattern(regexp = "SELL|BUY")
  private String orderSide;

  @Getter
  @Setter
  @NotNull
  // @Pattern(regexp = "MARKET|SIMPLE_MARKET|LIMIT")
  @Pattern(regexp = "MARKET|LIMIT")
  private String orderType;

  @Getter @Setter public BigDecimal price;

  @Getter
  @Setter
  @NotNull
  @DecimalMin(value = "0", inclusive = false)
  public BigDecimal amount;
}
