package exchange.common.model.request.dowjones;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@Accessors(chain = true)
@Data
public class DowJonesAssociationUpdateReq {
	@JsonProperty("data")
	private Data data = new Data();

	@Accessors(chain = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("type")
		private String type = "risk-entity-screening-associations";

		@JsonProperty("attributes")
		private Attributes attributes = new Attributes();
	}

	@Accessors(chain = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("external_id")
		private String externalId;

		@JsonProperty("gender")
		private String gender;

		@JsonProperty("record_type")
		private String recordType;

		@JsonProperty("names")
		private List<Name> names = new ArrayList<>();

		@JsonProperty("year_of_birth")
		private Integer yearOfBirth;

		@JsonProperty("identification_details")
		private IdentificationDetails identificationDetails;
	}

	@Accessors(chain = true)
	@lombok.Data
	public static class Name {
		@JsonProperty("prefix")
		private String prefix;

		@JsonProperty("suffix")
		private String suffix;

		@JsonProperty("entity_name")
		private String entityName;

		@JsonProperty("first_name")
		private String firstName;

		@JsonProperty("middle_name")
		private String middleName;

		@JsonProperty("last_name")
		private String lastName;

		@JsonProperty("single_string_name")
		private String singleStringName;

		@JsonProperty("name_type")
		private String nameType = "PRIMARY";
	}

	@Accessors(chain = true)
	@lombok.Data
	public static class IdentificationDetails {
		@JsonProperty("type")
		private String type = "abc";

		@JsonProperty("value")
		private String value = "abc";
	}
}
