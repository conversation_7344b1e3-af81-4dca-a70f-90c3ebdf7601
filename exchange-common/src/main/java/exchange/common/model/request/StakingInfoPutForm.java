package exchange.common.model.request;


import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class StakingInfoPutForm {

  @Getter
  @Setter
  private Long id;

  @Getter
  @Setter
  @NotNull
  private String currency;
  
  @Getter
  @Setter
  @NotNull
  private String period;
  
  @Getter
  @Setter
  @NotNull
  private BigDecimal cbFeeYearRate;
  
  @Getter
  @Setter
  @NotNull
  private BigDecimal cusYearRateDisplay;
  
  @Getter
  @Setter
  private BigDecimal yearRateFromChain;
  
  @Getter
  @Setter
  private BigDecimal totalAmount;
  
  @Getter
  @Setter
  @NotNull
  private BigDecimal minApplyAmount;
  
  @Getter
  @Setter
  @NotNull
  private boolean enabled;
  
  @Getter
  @Setter
  @NotNull
  private boolean continueEnabled;
  
  @Getter
  @Setter
  private Long applyDateFrom;
  
  @Getter
  @Setter
  private Long applyDateTo;
  
  @Getter
  @Setter
  @NotNull
  private BigDecimal minApplyUnit;
  
  @Getter
  @Setter
  @NotNull
  private boolean userLimitFlg;
  
  @Getter
  @Setter
  private String limitUsers;

}
