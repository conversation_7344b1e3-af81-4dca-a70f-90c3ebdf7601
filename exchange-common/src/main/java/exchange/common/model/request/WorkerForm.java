package exchange.common.model.request;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkerForm implements Serializable {

  private static final long serialVersionUID = 1269545530959359735L;

  @Getter
  @Setter
  @JsonProperty("method")
  private String method;

  @Getter
  @Setter
  @JsonProperty("params")
  private Map<String, Object> params = new HashMap<>();
}
