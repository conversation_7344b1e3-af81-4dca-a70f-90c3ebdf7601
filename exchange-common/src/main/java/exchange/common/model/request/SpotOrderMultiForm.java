package exchange.common.model.request;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
public class SpotOrderMultiForm {

  @Getter
  @Setter
  @NotNull
  private Long symbolId;

  @Getter
  @Setter
  private List<SpotOrderForm> orders = new ArrayList<>();

  @Getter
  @Setter
  private List<Long> cancelOrderIds = new ArrayList<>();
}
