package exchange.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class FiatDepositPostForm {
  @Getter
  @Setter
  @NotNull
  private Long userId;

  @Getter
  @Setter
  @NotNull
  private Long bankAccountId;

  @Getter
  @Setter
  @NotNull
  private BigDecimal amount;

  @Getter
  @Setter
  @NotNull
  private BigDecimal fee;

  @Getter
  @Setter
  @NotNull
  private String fiatDepositStatus;

  @Getter
  @Setter
  private String fiatDepositSubStatus;

  @Getter
  @Setter
  private String comment;

}
