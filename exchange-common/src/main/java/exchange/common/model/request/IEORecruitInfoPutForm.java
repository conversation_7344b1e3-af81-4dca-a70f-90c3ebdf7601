package exchange.common.model.request;

import java.io.File;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import org.springframework.web.multipart.MultipartFile;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
public class IEORecruitInfoPutForm extends IdForm {
  
  @NotNull
  private String deal;
  
  @NotNull
  private String branchNameTicker;
  
  @NotNull
  private String branchNameKana;
  
  @NotNull
  private String outline;
  
  @NotNull
  private String recruitDateFrom;
  
  @NotNull
  private String recruitDateTo;
  
  @NotNull
  private String raffleDate;
  
  @NotNull
  private String quotaDateFrom;
  
  @NotNull
  private String quotaDateTo;
  
  @NotNull
  private String lockupPeroid;
  
  @NotNull
  private BigDecimal saleAmount;
  
  @NotNull
  private BigDecimal unitPrice;

  @NotNull
  private BigDecimal feeRatio;

  @NotNull
  private BigDecimal shareAmount;

  @NotNull
  private BigDecimal applySharesMax;

  @NotNull
  private BigDecimal applySharesMin;

  @NotNull
  private File disclosureInfo;
}
