package exchange.common.model.request.dowjones;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@Accessors(chain = true)
@Data
public class DowJonesCaseCreateReq {

	@JsonProperty("data")
	private Data data = new Data();

	@Accessors(chain = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("type")
		private String type = "risk-entity-screening-cases";

		@JsonProperty("attributes")
		private Attributes attributes = new Attributes();
	}

	@Accessors(chain = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("case_name")
		private String caseName;

		@JsonProperty("external_id")
		private String externalId;

		@JsonProperty("owner_id")
		private String ownerId = "SYS";

		@JsonProperty("options")
		private Options options = new Options();
	}

	@Accessors(chain = true)
	@lombok.Data
	public static class Options {
		@JsonProperty("search_type")
		private String searchType;

		@JsonProperty("filter_content_category")
		private List<String> filterContentCategoryList;
	}

}
