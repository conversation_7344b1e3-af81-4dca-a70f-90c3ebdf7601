package exchange.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ToString
@Getter
@Setter
public class PosOrderForm {
  @NotNull
  private Long symbolId;

  @NotNull
  @Pattern(regexp = "SELL|BUY")
  private String orderSide;

  @Pattern(regexp = "MARKET")
  private String orderType;

  public BigDecimal price;

  public BigDecimal mmPrice;

  @NotNull
  @DecimalMin(value = "0", inclusive = false)
  public BigDecimal amount;
}
