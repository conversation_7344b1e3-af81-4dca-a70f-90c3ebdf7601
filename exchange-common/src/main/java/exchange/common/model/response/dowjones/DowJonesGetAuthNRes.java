package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: wen.y
 * @date: 2024/12/6
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesGetAuthNRes {

	@JsonProperty("access_token")
	private String accessToken;

	@JsonProperty("id_token")
	private String idToken;

	@JsonProperty("refresh_token")
	private String refreshToken;

	@JsonProperty("token_type")
	private String tokenType;

}
