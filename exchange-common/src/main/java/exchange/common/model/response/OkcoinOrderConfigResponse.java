package exchange.common.model.response;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OkcoinOrderConfigResponse implements Serializable {

  private static final long serialVersionUID = -2073932866670468538L;

  @Getter @Setter private String base_currency;

  @Getter @Setter private String instrument_id;

  // 最小注文数量
  @Getter @Setter private String min_size;

  @Getter @Setter private String quote_currency;

  // 注文数量の最小単位
  @Getter @Setter private String size_increment;

  // 呼値(価格の最小単位)
  @Getter @Setter private String tick_size;

  /**
   * response sample [ { "base_currency":"BTC", "instrument_id":"BTC-JPY", "min_size":"0.001",
   * "quote_currency":"JPY", "size_increment":"0.00000001", "tick_size":"0.1" }, {
   * "base_currency":"ETH", "instrument_id":"ETH-JPY", "min_size":"0.001", "quote_currency":"JPY",
   * "size_increment":"0.000001", "tick_size":"0.01" } ]
   */
}
