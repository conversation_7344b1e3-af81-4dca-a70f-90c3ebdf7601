package exchange.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.ReportLabel;
import exchange.common.constant.ReportLabel.HeaderTrade;
import exchange.common.constant.SygnaConstants;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.pos.entity.PosTrade;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@JsonPropertyOrder({
    "id",
    "userId",
    "orderId",
    "currencyPair",
    "amount",
    "orderSide",
    "orderType",
    "tradeAction",
    "orderChannel",
    "price",
    "fee",
    "assetAmount",
    "createdAt",
    "updatedAt",
    "tradeType"
})
public class PosTradeReportData implements Serializable {

  private static final long serialVersionUID = -2885403527974338834L;

  private Long id;

  private Long userId;

  private Long orderId;

  private String currencyPair;

  private BigDecimal amount;

  private String orderSide;

  private String orderType;

  private String tradeAction;

  private String orderChannel = OrderChannel.UNKNOWN.name();

  private BigDecimal price;

  private BigDecimal fee;

  private BigDecimal assetAmount;

  private String createdAt;

  private String updatedAt;

  private String tradeType;

    public PosTradeReportData setProperties(PosTrade posTrade, CurrencyPair currencyPair) {
    this.setId(posTrade.getId());
    this.setUserId(posTrade.getUserId());
    this.setOrderId(posTrade.getOrderId());
    this.setCurrencyPair(currencyPair.getName());
    this.setAmount(currencyPair.getPosScaledAmount(posTrade.getAmount()));
    this.setOrderSide(ReportLabel.OrderSide.valueOfName(posTrade.getOrderSide().name()).getLabel());
    this.setOrderType(ReportLabel.OrderType.valueOfName(posTrade.getOrderType().name()).getLabel());
    this.setTradeAction(ReportLabel.TradeAction.valueOfName(posTrade.getTradeAction().name()).getLabel());
    this.setOrderChannel(ReportLabel.OrderChannel.valueOfName(posTrade.getOrderChannel().name()).getLabel());
    this.setPrice(currencyPair.getScaledPrice(posTrade.getPrice()));
    this.setFee(currencyPair.getScaledPrice(posTrade.getFee()));
    this.setAssetAmount(currencyPair.getPosScaledAmount(posTrade.getAssetAmount()));
    this.setCreatedAt(FormatUtil.formatJst(posTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    this.setUpdatedAt(FormatUtil.formatJst(posTrade.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    this.setTradeType(SygnaConstants.POS_TRADE_TYPE_NAME);  // 販売所　固定
    return this;
  }

  public static String getReportHeader() {
    return HeaderTrade.ID.getLabel()
        + ","
        + HeaderTrade.USER_ID.getLabel()
        + ","
        + HeaderTrade.ORDER_ID.getLabel()
        + ","
        + HeaderTrade.CURRENCY_PAIR.getLabel()
        + ","
        + HeaderTrade.AMOUNT.getLabel()
        + ","
        + HeaderTrade.ORDER_SIDE.getLabel()
        + ","
        + HeaderTrade.ORDER_TYPE.getLabel()
        + ","
        + HeaderTrade.TRADE_ACTION.getLabel()
        + ","
        + HeaderTrade.ORDER_CHANNEL.getLabel()
        + ","
        + HeaderTrade.PRICE.getLabel()
        + ","
        + HeaderTrade.FEE.getLabel()
        + ","
        + HeaderTrade.ASSET_AMOUNT.getLabel()
        + ","
        + HeaderTrade.CREATED_AT.getLabel()
        + ","
        + HeaderTrade.UPDATED_AT.getLabel()
        + ","
        + HeaderTrade.TRADE_TYPE.getLabel();
  }
}
