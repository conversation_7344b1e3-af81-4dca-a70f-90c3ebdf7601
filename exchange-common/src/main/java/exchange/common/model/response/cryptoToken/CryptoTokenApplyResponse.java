package exchange.common.model.response.cryptoToken;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.CryptoToken;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import exchange.common.constant.CryptoTokenApplyStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CryptoTokenApplyResponse implements Serializable {

	@Serial
	private static final long serialVersionUID = 3885817213715806834L;

	private Long userId;

	private CryptoToken cryptoToken;

	private CryptoTokenApplyStatus applyStatus;

	@JsonFormat(shape = JsonFormat.Shape.NUMBER)
	private Date applyTime;

	@JsonFormat(shape = JsonFormat.Shape.NUMBER)
	private Date approvedTime;
}
