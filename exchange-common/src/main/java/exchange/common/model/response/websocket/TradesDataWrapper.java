package exchange.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.model.response.TradesData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TradesDataWrapper extends DataWrapper implements Serializable {
  @Serial
  private static final long serialVersionUID = 7676077234313495798L;

  private TradesData tradesData;

  @Override
  public String getChecksum() {
    long timestamp = tradesData.getTimestamp(); // Back up timestamp
    tradesData.setTimestamp(0L); // Exclude timestamp for checksum
    String checksum = calculateChecksum(tradesData);
    tradesData.setTimestamp(timestamp); // Set timestamp back
    return checksum;
  }

  @Override
  public String getCacheKey() {
    return "websocket:" + this.getClass().getSimpleName() + ":symbol." + this.tradesData.getSymbolId();
  }
  public TradesData unwrap(){
    return tradesData;
  }

  public TradesData unwrapToEmpty(){
    TradesData data = new TradesData();
    data.setSymbolId(tradesData.getSymbolId());
    return data;
  }
}
