package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpotOrderCalculateData implements Serializable {

  private static final long serialVersionUID = -5287301713288427755L;

  @Getter @Setter private BigDecimal sellAveragePrice;

  @Getter @Setter private BigDecimal buyAveragePrice;

  @Getter @Setter private BigDecimal sellAssetAmount;

  @Getter @Setter private BigDecimal buyAssetAmount;

  @Getter @Setter private BigDecimal sellAmount;

  @Getter @Setter private BigDecimal buyAmount;
}
