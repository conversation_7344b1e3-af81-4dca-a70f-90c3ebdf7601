package exchange.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.*;
import exchange.spot.entity.SpotTrade;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpotTradeDataWrapper implements Serializable {
  @Serial
  private static final long serialVersionUID = -2449129621393893070L;

  private Long id;
  private Long symbolId;
  private Long userId;
  private OrderSide orderSide;
  private BigDecimal price;
  private BigDecimal amount;
  private TradeAction tradeAction;
  private Long orderId;
  private BigDecimal fee;
  private OrderType orderType;
  private OrderChannel orderChannel;
  private BigDecimal jpyConversion;
  private Long targetOrderId;
  private Long targetUserId;
  private BigDecimal assetAmount;
  private Date createdAt;
  private Date updatedAt;
  public SpotTradeDataWrapper(SpotTrade trade){
    this.id = trade.getId();
    this.symbolId = trade.getSymbolId();
    this.userId = trade.getUserId();
    this.orderSide = trade.getOrderSide();
    this.price = trade.getPrice();
    this.amount = trade.getAmount();
    this.tradeAction = trade.getTradeAction();
    this.orderId = trade.getOrderId();
    this.fee = trade.getFee();
    this.orderType = trade.getOrderType();
    this.orderChannel = trade.getOrderChannel();
    this.jpyConversion = trade.getJpyConversion();
    this.targetOrderId = trade.getTargetOrderId();
    this.targetUserId = trade.getTargetUserId();
    this.assetAmount = trade.getAssetAmount();
    this.createdAt = trade.getCreatedAt();
    this.updatedAt = trade.getUpdatedAt();
  }

  public SpotTrade unwrap(){
    SpotTrade spotTrade = new SpotTrade(){};
    spotTrade.setId(id);
    spotTrade.setSymbolId(symbolId);
    spotTrade.setUserId(userId);
    spotTrade.setOrderSide(orderSide);
    spotTrade.setPrice(price);
    spotTrade.setAmount(amount);
    spotTrade.setTradeAction(tradeAction);
    spotTrade.setOrderId(orderId);
    spotTrade.setFee(fee);
    spotTrade.setOrderType(orderType);
    spotTrade.setOrderChannel(orderChannel);
    spotTrade.setJpyConversion(jpyConversion);
    spotTrade.setTargetOrderId(targetOrderId);
    spotTrade.setTargetUserId(targetUserId);
    spotTrade.setAssetAmount(assetAmount);
    spotTrade.setCreatedAt(createdAt);
    spotTrade.setUpdatedAt(updatedAt);
    return spotTrade;
  }
}
