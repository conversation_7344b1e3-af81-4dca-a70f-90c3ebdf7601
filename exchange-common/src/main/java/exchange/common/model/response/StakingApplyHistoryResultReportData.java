package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Currency;
import exchange.common.constant.ReportLabel;
import exchange.common.constant.ReportLabel.HeaderStakingApplyInfo;
import exchange.common.constant.StakingStatus;
import exchange.common.entity.StakingApplyDetail;
import exchange.common.entity.StakingInfo;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
  "id",
  "userId",
  "currency",
  "period",
  "cusYearRateDisplay",
  "applyDate",
  "stakingDatePlan",
  "applyAmount",
  "yearRate",
  "confirmedRewards",
  "rewardAccumulate",
  "stakingStatus",
  "cancelApplyDate",
  "autoContinue",
  "expirationDate",
  "cancelDate",
  "amountToAccountDate",
  "createdAt",
  "updatedAt"
})
public class StakingApplyHistoryResultReportData implements Serializable {

  private static final long serialVersionUID = -823879922881347574L;
  
  @Getter @Setter private Long id;
  
//  @Getter @Setter private Long relationId;
//
//  @Getter @Setter private Long autoContinueNum;
  
  @Getter @Setter private Long userId;
  
  @Getter @Setter private String currency;
  
  @Getter @Setter private String period;

  @Getter @Setter private String cusYearRateDisplay;
  
  @Getter @Setter private String applyDate;

  @Getter @Setter private String stakingDatePlan;
  
  @Getter @Setter private String applyAmount;

  @Getter @Setter private String yearRate;
  
  @Getter @Setter private String confirmedRewards;

  @Getter @Setter private String rewardAccumulate;

  @Getter @Setter private String stakingStatus;
  
 @Getter @Setter private String cancelApplyDate;
 
 @Getter @Setter private String autoContinue;
 
 @Getter @Setter private String expirationDate;
  
  @Getter @Setter private String cancelDate;

  @Getter @Setter private String amountToAccountDate;
  
  @Getter @Setter private String createdAt;

  @Getter @Setter private String updatedAt;
  
  public StakingApplyHistoryResultReportData setProperties(StakingApplyDetail stakingApplyDetail) {
    BigDecimal rewards = stakingApplyDetail.getRewardAccumulate();
    BigDecimal confirmedRewards = BigDecimal.ZERO;
    BigDecimal unconfirmedRewards = BigDecimal.ZERO;
    BigDecimal foundingRewards = BigDecimal.ZERO;
    switch(stakingApplyDetail.getStakingStatus()) {
      case EXPIRED, EXPIRED_WAIT_FUND -> {
        foundingRewards = stakingApplyDetail.getRewardAccumulate();
      }
      case EXPIRED_FUNDED -> {
        if(stakingApplyDetail.getAmountToAccountDate() != null) {
          confirmedRewards = stakingApplyDetail.getRewardAccumulate();
        }
      }
      default ->{
        unconfirmedRewards = stakingApplyDetail.getRewardAccumulate();
      }
    }
    NumberFormat numberFormat = NumberFormat.getNumberInstance();
    StakingInfo stakingInfo = stakingApplyDetail.getStakingInfo();
    this.setId(stakingApplyDetail.getId());
//    this.setRelationId(stakingApplyDetail.getRelationId());
//    this.setAutoContinueNum(stakingApplyDetail.getAutoContinueNum());
    this.setUserId(stakingApplyDetail.getUserId());
    this.setCurrency(stakingApplyDetail.getCurrency().getName());
    this.setPeriod(stakingInfo.getPeriod());
    this.setCusYearRateDisplay(stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString() + "%");
    this.setApplyDate(FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    this.setStakingDatePlan(FormatUtil.formatJst(stakingApplyDetail.getStakingDatePlan(), FormatPattern.YYYY_MM_DD));
    this.setApplyAmount(this.toStrigForReport(stakingApplyDetail.getCurrency(), stakingApplyDetail.getApplyAmount(), numberFormat));
    
    if(ObjectUtils.isNotEmpty(stakingApplyDetail.getRelateDetails())) {
      StakingApplyDetail latestDetail = 
          stakingApplyDetail.getRelateDetails().stream().
          max((x, y) -> x.getAutoContinueNum().compareTo(y.getAutoContinueNum())).get();
      
      Date maxAmountToAccountDate = stakingApplyDetail.getAmountToAccountDate();
      if (maxAmountToAccountDate != null) {
        List<StakingApplyDetail> amountToAccountNotNullDetail = 
            stakingApplyDetail.getRelateDetails().stream().
            filter(
                s -> s.getAmountToAccountDate() != null
              ).collect(Collectors.toList());
        if (amountToAccountNotNullDetail != null && amountToAccountNotNullDetail.size() > 0) {
          StakingApplyDetail maxAmountToAccountDetail = amountToAccountNotNullDetail.stream().
              max((x, y) -> x.getAmountToAccountDate().compareTo(y.getAmountToAccountDate())).get();
          maxAmountToAccountDate = maxAmountToAccountDetail.getAmountToAccountDate();
        }
        
      }
      this.setYearRate( latestDetail.getYearRate().stripTrailingZeros().toPlainString() + "%");
      this.setStakingStatus(ReportLabel.StakingStatus.valueOfName(latestDetail.getStakingStatus().name()).getLabel());
      this.setCancelApplyDate(FormatUtil.formatJst(latestDetail.getCancelApplyDate(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
      this.setCancelDate(FormatUtil.formatJst(latestDetail.getCancelDate(), FormatPattern.YYYY_MM_DD_SLASH));
      this.setAutoContinue(latestDetail.isAutoContinue()?"要":"否");
      this.setExpirationDate(FormatUtil.formatJst(latestDetail.getExpirationDate(), FormatPattern.YYYY_MM_DD));
      this.setAmountToAccountDate(FormatUtil.formatJst(maxAmountToAccountDate,
          FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
//      this.setAmountToAccount(latestDetail.getAmountToAccount());
//      this.setAutoContinueNum(latestDetail.getAutoContinueNum());
//      boolean canCancelVal = false;
//      if (StakingStatus.APPLING.equals(latestDetail.getStakingStatus()) ||
//          StakingStatus.APPLIED.equals(latestDetail.getStakingStatus())) {
//        Long currentTime = System.currentTimeMillis();
//        if (ObjectUtils.isEmpty(latestDetail.getCancelDisableDateFrom()) 
//            || ObjectUtils.isEmpty(latestDetail.getCancelDisableDateTo()) 
//            || currentTime.compareTo(latestDetail.getCancelDisableDateFrom().getTime()) < 0
//            || currentTime.compareTo(latestDetail.getCancelDisableDateTo().getTime()) > 0) {
//          canCancelVal = true;
//        }
//        
//      }
//      this.setCanCancel(canCancelVal);
      rewards = stakingApplyDetail.getRelateDetails().stream().map(StakingApplyDetail::getRewardAccumulate)
          .reduce(rewards,BigDecimal::add);
      confirmedRewards = stakingApplyDetail.getRelateDetails().stream()
          .filter((detail) -> detail.getStakingStatus().equals(StakingStatus.EXPIRED_FUNDED) 
              && detail.getAmountToAccountDate() != null)
          .map(StakingApplyDetail::getRewardAccumulate)
          .reduce(confirmedRewards,BigDecimal::add);
      unconfirmedRewards = latestDetail.getRewardAccumulate();
      foundingRewards = stakingApplyDetail.getRelateDetails().stream()
          .filter((detail) -> detail.getStakingStatus().equals(StakingStatus.EXPIRED) 
              || detail.getStakingStatus().equals(StakingStatus.EXPIRED_WAIT_FUND))
          .map(StakingApplyDetail::getRewardAccumulate)
          .reduce(foundingRewards,BigDecimal::add);
      BigDecimal rewardAccumulate = unconfirmedRewards.add(foundingRewards);
      this.setRewardAccumulate(this.toStrigForReport(stakingApplyDetail.getCurrency(), rewardAccumulate, numberFormat));
      this.setConfirmedRewards(this.toStrigForReport(stakingApplyDetail.getCurrency(), confirmedRewards, numberFormat));
//      this.setHasRelateDetails(true);
      this.setCreatedAt(
          FormatUtil.formatJst(latestDetail.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
      this.setUpdatedAt(
          FormatUtil.formatJst(latestDetail.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

    } else {
      this.setYearRate(stakingApplyDetail.getYearRate().stripTrailingZeros().toPlainString() + "%");
      this.setConfirmedRewards(this.toStrigForReport(stakingApplyDetail.getCurrency(), confirmedRewards, numberFormat));
      this.setRewardAccumulate(this.toStrigForReport(stakingApplyDetail.getCurrency(), unconfirmedRewards.add(foundingRewards), numberFormat));
      this.setStakingStatus(ReportLabel.StakingStatus.valueOfName(stakingApplyDetail.getStakingStatus().name()).getLabel());
      this.setCancelApplyDate(FormatUtil.formatJst(stakingApplyDetail.getCancelApplyDate(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
      this.setAutoContinue(stakingApplyDetail.isAutoContinue()?"要":"否");
      this.setExpirationDate(FormatUtil.formatJst(stakingApplyDetail.getExpirationDate(), FormatPattern.YYYY_MM_DD));
      this.setCancelDate(FormatUtil.formatJst(stakingApplyDetail.getCancelDate(), FormatPattern.YYYY_MM_DD));
      if (stakingApplyDetail.getAmountToAccountDate() != null) {
        this.setAmountToAccountDate(FormatUtil.formatJst(stakingApplyDetail.getAmountToAccountDate(),
            FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

      }
//      if (stakingApplyDetail.getAmountToAccount() != null) {
//        this.setAmountToAccount(this.toStrigForReport(Currency.ADA,
//            stakingApplyDetail.getAmountToAccount(), numberFormat));
//
//      }
      this.setCreatedAt(
          FormatUtil.formatJst(stakingApplyDetail.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
      this.setUpdatedAt(
          FormatUtil.formatJst(stakingApplyDetail.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
      
    }
    return this;
  }

  private String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
    // stripTrailingZeros() 末尾0除去
    // toPlainString() 指数表記にならないようにString変換
    String strValue =
        currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
    // 整数部3桁区切り
    int decimalPointIndex = strValue.indexOf(".");
    if (decimalPointIndex < 0) {
      // 小数点なし
      return numberFormat.format(Long.valueOf(strValue));
    } else {
      // 小数点あり
      String seisu = strValue.substring(0, decimalPointIndex);
      return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
    }
  }


  public static String getReportHeader() {
    return  HeaderStakingApplyInfo.NO.getLabel()
        + ","
        + HeaderStakingApplyInfo.USERID.getLabel()
        + ","
        + HeaderStakingApplyInfo.CURRENCY.getLabel()
        + ","
        + HeaderStakingApplyInfo.PERIOD.getLabel()
        + ","
        + HeaderStakingApplyInfo.CUS_YEAR_RATE_DISPLAY.getLabel()
        + ","
        + HeaderStakingApplyInfo.APPLY_DATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.STAKING_DATE_PLAN.getLabel()
        + ","
        + HeaderStakingApplyInfo.APPLY_AMOUNT.getLabel()
        + ","
        + HeaderStakingApplyInfo.YEAR_RATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.CONFIRMED_REWARDS.getLabel()
        + ","
        + HeaderStakingApplyInfo.REWARD_ACCUMULATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.STAKING_STATUS.getLabel()
        + ","
        + HeaderStakingApplyInfo.CANCEL_APPLY_DATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.AUTO_CONTINUE.getLabel()
        + ","
        + HeaderStakingApplyInfo.EXPIRATION_DATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.CANCEL_DATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.AMOUNT_TO_ACCOUNTDATE.getLabel()
        + ","
        + HeaderStakingApplyInfo.CREATED_AT.getLabel()
        + ","
        + HeaderStakingApplyInfo.UPDATED_AT.getLabel();
  }
}