package exchange.common.model.response;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.TmsStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UneconomicalCryptoDepositUserResponse implements Serializable {
  private static final long serialVersionUID = 4236129338432034583L;

  @Getter
  @Setter
  private Long id;

  @Getter
  @Setter
  private Long userId;

  @Getter
  @Setter
  private String email;

  @Getter
  @Setter
  private Integer depositCount;

  @Getter
  @Setter
  private TmsStatus tmsStatus;

  @Getter
  @Setter
  private String targetAt;

  @Getter
  @Setter
  private String createdAt;
}
