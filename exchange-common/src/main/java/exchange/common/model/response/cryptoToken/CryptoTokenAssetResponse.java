package exchange.common.model.response.cryptoToken;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.CryptoToken;
import exchange.common.constant.Currency;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CryptoTokenAssetResponse implements Serializable {

	@Serial
	private static final long serialVersionUID = -7680248572939011341L;

	private Long userId;

	private CryptoToken cryptoToken;

	private BigDecimal onhandAmount;
}
