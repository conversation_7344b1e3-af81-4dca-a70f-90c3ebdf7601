package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DowJonesAssociationGetAllRes {
    @JsonProperty("data")
    private List<AssociationData> data;
    @JsonProperty("links")
    private PaginationLinks links;
    @JsonProperty("meta")
    private MetaInfo meta;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AssociationData {
        @JsonProperty("attributes")
        private Attributes attributes;
        @JsonProperty("type")
        private String type;
        @JsonProperty("id")
        private String id;
        @JsonProperty("links")
        private DataLinks links;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Attributes {
        @JsonProperty("gender")
        private String gender; // "FEMALE"

        @JsonProperty("record_type")
        private String recordType; // "PERSON"
        @JsonProperty("names")
        private List<NameInfo> names;

        @JsonProperty("identification_details")
        private IdentificationDetails identificationDetails;

        @JsonProperty("year_of_birth")
        private Integer yearOfBirth;

        @JsonProperty("external_id")
        private String externalId;

        @JsonProperty("timestamp")
        private String timestamp; // ISO格式日期
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NameInfo {
        @JsonProperty("name_type")
        private String nameType; // "SSN"

        @JsonProperty("single_string_name")
        private String singleStringName; // "上田テスト 桃佳"
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdentificationDetails {

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataLinks {
        @JsonProperty("self")
        private String self;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaginationLinks {
        @JsonProperty("last")
        private String last;
        @JsonProperty("next")
        private String next;
        @JsonProperty("first")
        private String first;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MetaInfo {
        @JsonProperty("count")
        private Integer count;
        @JsonProperty("offset")
        private OffsetInfo offset;

        @JsonProperty("total_count")
        private Integer totalCount;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OffsetInfo {
        @JsonProperty("last")
        private String last;
        @JsonProperty("next")
        private String next;
        @JsonProperty("first")
        private String first;
    }
}
