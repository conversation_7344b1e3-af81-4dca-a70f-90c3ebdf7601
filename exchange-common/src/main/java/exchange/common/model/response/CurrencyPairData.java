package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrencyPairData implements Serializable {

  private static final long serialVersionUID = 1L;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private TradeType tradeType;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private CurrencyPair currencyPair;

  @Getter
  @Setter
  private BigDecimal minOrderAmount;

  @Getter
  @Setter
  private BigDecimal maxOrderAmount;

  @Getter
  @Setter
  private BigDecimal limitPriceRangeRate;

  @Getter
  @Setter
  private BigDecimal marketPriceRangeRate;

  @Getter
  @Setter
  private BigDecimal marketAmountRangeRate;

  @Getter
  @Setter
  private BigDecimal makerTradeFeePercent;

  @Getter
  @Setter
  private BigDecimal takerTradeFeePercent;

  @Getter
  @Setter
  private boolean tradable = true;

  @Getter
  @Setter
  private boolean enabled = false;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date circuitBreakUpdatedAt;

  @Getter
  @Setter
  private BigDecimal circuitBreakPercent;

  @Getter
  @Setter
  private Long circuitBreakCheckTimespan;

  @Getter
  @Setter
  private Long circuitBreakStopTimespan;

  @Getter
  @Setter
  private BigDecimal simpleMarketSpreadPercent;

  @Getter
  @Setter
  private BigDecimal simpleMarketFeePercent;

  @Getter
  @Setter
  private BigDecimal spikePercent;

  @Getter
  @Setter
  private Integer spikeMinutes;

  @Getter
  @Setter
  private Integer spikeCount;

  @Getter
  @Setter
  private Integer spoofingCheckerSpanHours;

  @Getter
  @Setter
  private BigDecimal canceledOrdersPercentThreshold;

  @Getter
  @Setter
  private Integer washTradingCheckSpanHours;

  @Getter
  @Setter
  private BigDecimal washTradingPercentThreshold;

  @Getter
  @Setter
  private Integer highValueTraderCheckSpanHours;

  @Getter
  @Setter
  private Integer sameIpCheckSpanHours;

  @Getter
  @Setter
  private Integer sameIpThreshold;

  @Getter
  @Setter
  private BigDecimal highValueTraderSpotOrderLimitAmountThreshold;

  @Getter
  @Setter
  private BigDecimal highValueTraderSpotTradeMarketAmountThreshold;

  @Getter
  @Setter
  private Integer highValueTraderCountThreshold;

  @Getter
  @Setter
  private String baseIconUrl;

  @Getter
  @Setter
  private String quoteIconUrl;

  public CurrencyPairData(CurrencyPairConfig currencyPairConfig, String baseCurrencyIconUrl,
      String quoteCurrencyIconUrl) {
    this.tradeType = currencyPairConfig.getTradeType();
    this.currencyPair = currencyPairConfig.getCurrencyPair();
    this.minOrderAmount = currencyPairConfig.getMinOrderAmount();
    this.maxOrderAmount = currencyPairConfig.getMaxOrderAmount();
    this.limitPriceRangeRate = currencyPairConfig.getLimitPriceRangeRate();
    this.marketPriceRangeRate = currencyPairConfig.getMarketPriceRangeRate();
    this.marketAmountRangeRate = currencyPairConfig.getMarketAmountRangeRate();
    this.makerTradeFeePercent = currencyPairConfig.getMakerTradeFeePercent();
    this.takerTradeFeePercent = currencyPairConfig.getTakerTradeFeePercent();
    this.tradable = currencyPairConfig.isTradable();
    this.enabled = currencyPairConfig.isEnabled();
    this.circuitBreakUpdatedAt = currencyPairConfig.getCircuitBreakUpdatedAt();
    this.circuitBreakPercent = currencyPairConfig.getCircuitBreakPercent();
    this.circuitBreakCheckTimespan = currencyPairConfig.getCircuitBreakCheckTimespan();
    this.circuitBreakStopTimespan = currencyPairConfig.getCircuitBreakStopTimespan();
    this.simpleMarketSpreadPercent = currencyPairConfig.getSimpleMarketSpreadPercent();
    this.simpleMarketFeePercent = currencyPairConfig.getSimpleMarketFeePercent();
    this.spikePercent = currencyPairConfig.getSpikePercent();
    this.spikeMinutes = currencyPairConfig.getSpikeMinutes();
    this.spikeCount = currencyPairConfig.getSpikeCount();
    this.spoofingCheckerSpanHours = currencyPairConfig.getSpoofingCheckerSpanHours();
    this.canceledOrdersPercentThreshold = currencyPairConfig.getCanceledOrdersPercentThreshold();
    this.washTradingCheckSpanHours = currencyPairConfig.getWashTradingCheckSpanHours();
    this.washTradingPercentThreshold = currencyPairConfig.getWashTradingPercentThreshold();
    this.highValueTraderCheckSpanHours = currencyPairConfig.getHighValueTraderCheckSpanHours();
    this.sameIpCheckSpanHours = currencyPairConfig.getSameIpCheckSpanHours();
    this.sameIpThreshold = currencyPairConfig.getSameIpThreshold();
    this.highValueTraderSpotOrderLimitAmountThreshold =
        currencyPairConfig.getHighValueTraderSpotOrderLimitAmountThreshold();
    this.highValueTraderSpotTradeMarketAmountThreshold =
        currencyPairConfig.getHighValueTraderSpotTradeMarketAmountThreshold();
    this.highValueTraderCountThreshold = currencyPairConfig.getHighValueTraderCountThreshold();

    this.baseIconUrl = baseCurrencyIconUrl;
    this.quoteIconUrl = quoteCurrencyIconUrl;
  }
}
