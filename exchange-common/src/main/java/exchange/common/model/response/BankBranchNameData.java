package exchange.common.model.response;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankBranchNameData implements Serializable {

  private static final long serialVersionUID = 332350155679199387L;

  @Getter
  @Setter
  private Long bankId;

  @Getter
  @Setter
  private Integer branchCode;

  @Getter
  @Setter
  private String branchName;

}
