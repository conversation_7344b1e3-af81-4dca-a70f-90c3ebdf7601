package exchange.common.model.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.Country;
import exchange.common.constant.KycStatus;
import exchange.common.constant.MfaType;
import exchange.common.constant.UserStatus;
import exchange.common.entity.User;
import exchange.common.entity.UserLoginInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class UserConditionData implements Serializable {

  private static final long serialVersionUID = 332350155679199387L;

  @Getter @Setter private Long userId;

  @Getter @Setter private String authority;

  @Getter @Setter private Integer level;

  @Getter @Setter private String email;

  @Getter @Setter private String antiPhishingCode;

  @Getter @Setter private boolean profileRegistered;

  @Getter @Setter private KycStatus kycStatus;

  @Getter @Setter private UserStatus userStatus;

  @Getter @Setter private String smsPhoneNumber;

  @Getter @Setter private Country country;

  @Getter @Setter private boolean insider;

  @Getter @Setter private List<MfaType> mfaTypeList = new ArrayList<>();

  @Getter @Setter private List<UserMailnoticesResponse> userMailnoticesResponseList;

  @AllArgsConstructor
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class UserHistoryElement implements Serializable {
    private static final long serialVersionUID = -6724221163430559710L;

    @Getter @Setter private Long id;

    @Getter @Setter private Long userId;

    @Getter @Setter private String ip;

    @Getter @Setter private boolean inJapan;

    @Getter @Setter private Long createdAt;
  }

  public void addMfaType(MfaType mfaType) {
    this.mfaTypeList.add(mfaType);
  }

  public void setUser(User user) {
    this.setEmail(user.getEmail());
    this.setUserId(user.getId());
    this.setLevel(user.getLevel());
    this.setKycStatus(user.getKycStatus());
    this.setUserStatus(user.getUserStatus());
    this.setInsider(user.isInsider());
    if (!StringUtils.isEmpty(user.getAntiPhishingCode())) {
      StringBuilder sb = new StringBuilder();
      sb.append(user.getAntiPhishingCode());
      sb.replace(3, user.getAntiPhishingCode().length(), "*********");
      this.setAntiPhishingCode(sb.toString());
    }
  }

  @Getter @Setter private List<UserHistoryElement> histories = new ArrayList<>();

  public void userHistories(List<UserLoginInfo> userLoginInfos) {
    userLoginInfos.forEach(
        userLoginInfo ->
            histories.add(
                new UserHistoryElement(
                    userLoginInfo.getId(),
                    userLoginInfo.getUserId(),
                    userLoginInfo.getIpAddress(),
                    userLoginInfo.isInJapan(),
                    userLoginInfo.getCreatedAt().getTime())));
  }
}
