package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Currency;
import exchange.common.constant.ReportLabel.HeaderStakingSummary;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
  "no",
  "targetAt",
  "currency",
  "jpyConversion",
  "stakingLockAmount",
  "cancelAmount",
  "reward",
  "cbReward"
})
public class StakingSummaryReportData implements Serializable {

  private static final long serialVersionUID = -709424307772900620L;

  @Getter @Setter private Long no;

  @Getter @Setter private String targetAt;

  @Getter @Setter private String currency;

  @Getter @Setter private String jpyConversion = "0";

  @Getter @Setter private String stakingLockAmount = "0";

  @Getter @Setter private String cancelAmount = "0";

  @Getter @Setter private String reward = "0";

  @Getter @Setter private String cbReward = "0";


  public StakingSummaryReportData setProperties(Object[] exchangeSummary, int count) {

    this.setNo(Long.valueOf(count));

    this.setTargetAt(String.valueOf(exchangeSummary[0]));

    this.setCurrency(String.valueOf(exchangeSummary[1]));

    NumberFormat numberFormat = NumberFormat.getNumberInstance();

    this.setJpyConversion(
        toStrigForReport(Currency.JPY, new BigDecimal(String.valueOf(exchangeSummary[2])), numberFormat));

    this.setStakingLockAmount(
        toStrigForReport(
            Currency.valueOfName(String.valueOf(exchangeSummary[1])), new BigDecimal(String.valueOf(exchangeSummary[3])), numberFormat));
    
    this.setCancelAmount(
        toStrigForReport(Currency.valueOfName(String.valueOf(exchangeSummary[1])), new BigDecimal(String.valueOf(exchangeSummary[4])), numberFormat));

    this.setReward(
        toStrigForReport(
            Currency.valueOfName(String.valueOf(exchangeSummary[1])), new BigDecimal(String.valueOf(exchangeSummary[5])), numberFormat));
    this.setCbReward(
        toStrigForReport(Currency.valueOfName(String.valueOf(exchangeSummary[1])), new BigDecimal(String.valueOf(exchangeSummary[6])), numberFormat));

    return this;
  }

  private String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
    // stripTrailingZeros() 末尾0除去
    // toPlainString() 指数表記にならないようにString変換
    String strValue =
        currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
    // 整数部3桁区切り
    int decimalPointIndex = strValue.indexOf(".");
    if (decimalPointIndex < 0) {
      // 小数点なし
      return numberFormat.format(Long.valueOf(strValue));
    } else {
      // 小数点あり
      String seisu = strValue.substring(0, decimalPointIndex);
      String returnVal = numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
      if ("-0".equals(seisu)) {
        returnVal = "-" + returnVal;
      }
      return returnVal;
    }
  }

  public static String getReportHeader() {
    return HeaderStakingSummary.NO.getLabel()
        + ","
        + HeaderStakingSummary.TARGET_AT.getLabel()
        + ","
        + HeaderStakingSummary.CURRENCY.getLabel()
        + ","
        + HeaderStakingSummary.JPY_CONVERSION.getLabel()
        + ","
        + HeaderStakingSummary.STAKING_LOCK_AMOUNT.getLabel()
        + ","
        + HeaderStakingSummary.CANCEL_AMOUNT.getLabel()
        + ","
        + HeaderStakingSummary.REWARD.getLabel()
        + ","
        + HeaderStakingSummary.CB_REWARD.getLabel();
  }
}
