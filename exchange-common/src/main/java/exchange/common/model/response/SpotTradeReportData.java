package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.ReportLabel;
import exchange.common.constant.ReportLabel.HeaderTrade;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.spot.entity.SpotTrade;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
  "id",
  "userId",
  "orderId",
  "currencyPair",
  "amount",
  "orderSide",
  "orderType",
  "tradeAction",
  "orderChannel",
  "price",
  "fee",
  "assetAmount",
  "createdAt",
  "updatedAt"
})
public class SpotTradeReportData implements Serializable {

  private static final long serialVersionUID = 3398270753840100159L;

  @Getter @Setter private Long id;

  @Getter @Setter private Long userId;

  @Getter @Setter private Long orderId;

  @Getter @Setter private String currencyPair;

  @Getter @Setter private String amount;

  @Getter @Setter private String orderSide;

  @Getter @Setter private String orderType;

  @Getter @Setter private String tradeAction;

  @Getter @Setter private String orderChannel = OrderChannel.UNKNOWN.name();

  @Getter @Setter private String price;

  @Getter @Setter private String fee;

  @Getter @Setter private String assetAmount;

  @Getter @Setter private String createdAt;

  @Getter @Setter private String updatedAt;

  public SpotTradeReportData setProperties(SpotTrade spotTrade, CurrencyPair currencyPair) {
	NumberFormat numberFormat = NumberFormat.getNumberInstance();
    this.setId(spotTrade.getId());
    this.setUserId(spotTrade.getUserId());
    this.setOrderId(spotTrade.getOrderId());
    this.setCurrencyPair(currencyPair.getName());
    this.setAmount(toStrigForReport(currencyPair.getBaseCurrency(), spotTrade.getAmount(), numberFormat));
    this.setOrderSide(
        ReportLabel.OrderSide.valueOfName(spotTrade.getOrderSide().name()).getLabel());
    this.setOrderType(
        ReportLabel.OrderType.valueOfName(spotTrade.getOrderType().name()).getLabel());
    this.setTradeAction(
        ReportLabel.TradeAction.valueOfName(spotTrade.getTradeAction().name()).getLabel());
    this.setOrderChannel(
        ReportLabel.OrderChannel.valueOfName(spotTrade.getOrderChannel().name()).getLabel());
    this.setPrice(toStrigForReport(currencyPair.getBaseCurrency(), spotTrade.getPrice(), numberFormat));
    this.setFee(toStrigForReport(currencyPair.getBaseCurrency(), spotTrade.getFee(), numberFormat));
    this.setAssetAmount(toStrigForReport(currencyPair.getQuoteCurrency(), spotTrade.getAssetAmount(), numberFormat));
    this.setCreatedAt(
        FormatUtil.formatJst(spotTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    this.setUpdatedAt(
        FormatUtil.formatJst(spotTrade.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    return this;
  }
  
  private String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
    // stripTrailingZeros() 末尾0除去
    // toPlainString() 指数表記にならないようにString変換
    String strValue =
        currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
    // 整数部3桁区切り
    int decimalPointIndex = strValue.indexOf(".");
    if (decimalPointIndex < 0) {
      // 小数点なし
      return numberFormat.format(Long.valueOf(strValue));
    } else {
      // 小数点あり
      String seisu = strValue.substring(0, decimalPointIndex);
      return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
    }
  }
  
  public static String getReportHeader() {
    return HeaderTrade.ID.getLabel()
        + ","
        + HeaderTrade.USER_ID.getLabel()
        + ","
        + HeaderTrade.ORDER_ID.getLabel()
        + ","
        + HeaderTrade.CURRENCY_PAIR.getLabel()
        + ","
        + HeaderTrade.AMOUNT.getLabel()
        + ","
        + HeaderTrade.ORDER_SIDE.getLabel()
        + ","
        + HeaderTrade.ORDER_TYPE.getLabel()
        + ","
        + HeaderTrade.TRADE_ACTION.getLabel()
        + ","
        + HeaderTrade.ORDER_CHANNEL.getLabel()
        + ","
        + HeaderTrade.PRICE.getLabel()
        + ","
        + HeaderTrade.FEE.getLabel()
        + ","
        + HeaderTrade.ASSET_AMOUNT.getLabel()
        + ","
        + HeaderTrade.CREATED_AT.getLabel()
        + ","
        + HeaderTrade.UPDATED_AT.getLabel();
  }
}
