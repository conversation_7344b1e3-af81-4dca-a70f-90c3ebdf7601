package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import exchange.common.constant.Currency;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({ "userId", "year", "currency", "startAmount", "spotTradeBuyAmount", "spotTradeBuyAmountJpy",
    "spotTradeSellAmount", "spotTradeSellAmountJpy", "spotTradeFee", "depositAmount", "withDrawalAmount",
    "withDrawalAndTransactionFee", "endAmount" })
public class YearlyReportData implements Serializable {

  private static final long serialVersionUID = 7678346609672126007L;

  @Getter
  @Setter
  private Long userId;

  @Getter
  @Setter
  private Long year;

  @Getter
  @Setter
  private Currency currency;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal startAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal endAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal spotTradeBuyAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal spotTradeBuyAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal spotTradeSellAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal spotTradeSellAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal spotTradeFee = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal depositAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal withDrawalAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Builder.Default
  private BigDecimal withDrawalAndTransactionFee = BigDecimal.ZERO;

}