package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesCaseGetRes {
	@JsonProperty("data")
	private Data data;


	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("type")
		private String type;

		@JsonProperty("id")
		private String id;

		@JsonProperty("attributes")
		private Attributes attributes;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("case_name")
		private String caseName;

		@JsonProperty("external_id")
		private String externalId;

		@JsonProperty("owner_id")
		private String ownerId;

		@JsonProperty("has_alerts")
		private Boolean hasAlerts;

		@JsonProperty("timestamp")
		private Date timestamp;

		@JsonProperty("revision")
		private Integer revision;

		@JsonProperty("is_case_valid")
		private Boolean isCaseValid;

		@JsonProperty("options")
		private Options options;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Options {
		@JsonProperty("search_type")
		private String searchType;

		@JsonProperty("filter_content_category")
		private List<String> filterContentCategoryList;
	}

	public String getCaseId() {
		if (null != data) {
			return this.getData().getId();
		}

		return null;
	}

	public Boolean getHasAlerts() {
		if (null != data && null != data.getAttributes()) {
			return this.getData().getAttributes().getHasAlerts();
		}
		return null;
	}
}
