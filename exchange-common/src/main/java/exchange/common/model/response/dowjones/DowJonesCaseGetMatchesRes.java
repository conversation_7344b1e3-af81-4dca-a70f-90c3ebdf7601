package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import exchange.common.util.CollectionUtil;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesCaseGetMatchesRes {

	@JsonProperty("data")
	private List<Data> data;

	@JsonProperty("meta")
	private Meta meta;

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("id")
		private String id;

		@JsonProperty("type")
		private String type;

		@JsonProperty("attributes")
		private Attributes attributes;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("external_id")
		private String externalId;

		@JsonProperty("match_count")
		private Long matchCount;

		@JsonProperty("has_alerts")
		private Boolean hasAlerts;

		@JsonProperty("matches")
		private List<Matches> matches;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Matches {
		@JsonProperty("has_alerts")
		private Boolean hasAlerts;

		@JsonProperty("is_match_valid")
		private Boolean isMatchValid;

		@JsonProperty("match_invalid_reason")
		private String matchInvalidReason;

		@JsonProperty("match_name")
		private String matchName;

		@JsonProperty("subscription_name")
		private String subscriptionName;

		@JsonProperty("match_type")
		private String matchType;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Meta {
		@JsonProperty("count")
		private Long count;

		@JsonProperty("total_count")
		private Long totalCount;

		@JsonProperty("offset")
		private Offset offset;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Offset {
		@JsonProperty("last")
		private Long last;

		@JsonProperty("first")
		private Long first;
	}

	public Boolean getHasAlerts() {
		if (CollectionUtil.isEmpty(data)) {
			return Boolean.FALSE;
		}
		for (Data item : data) {
			if (checkHasAlerts(item.getAttributes())) {
				return Boolean.TRUE;
			}
		}
		return Boolean.FALSE;
	}

	private Boolean checkHasAlerts(Attributes attributes) {
		if (null == attributes || null == attributes.getHasAlerts() || !attributes.getHasAlerts() || CollectionUtil.isEmpty(attributes.getMatches())) {
			return Boolean.FALSE;
		}

		for (Matches matches : attributes.getMatches()) {
			if (null != matches.getIsMatchValid() && matches.getIsMatchValid() && null != matches.getHasAlerts() && matches.getHasAlerts()) {
				return Boolean.TRUE;
			}
		}

		return Boolean.FALSE;
	}

	public Boolean getHasAlerts(Data data) {
		if (Objects.isNull(data)) {
			return Boolean.FALSE;
		} else {
			if (checkHasAlerts(data.getAttributes())) {
				return Boolean.TRUE;
			}
		}
		return Boolean.FALSE;
	}
}
