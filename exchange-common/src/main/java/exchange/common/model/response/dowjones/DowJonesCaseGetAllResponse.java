package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DowJonesCaseGetAllResponse {
    @JsonProperty("data")
    private List<CaseData> data;
    @JsonProperty("links")
    private Links links;
    @JsonProperty("meta")
    private Meta meta;

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CaseData {
        @JsonProperty("attributes")
        private Attributes attributes;
        @JsonProperty("type")
        private String type;
        @JsonProperty("id")
        private String id;
        @JsonProperty("links")
        private CaseLinks links;

    }
    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Attributes {

        @JsonProperty("options")
        private Options options;

        @JsonProperty("case_name")
        private String caseName;

        @JsonProperty("external_id")
        private String externalId;

        @JsonProperty("owner_id")
        private String ownerId;

        @JsonProperty("has_alerts")
        private boolean hasAlerts;

        private String timestamp;
        private int revision;

        @JsonProperty("is_case_valid")
        private boolean isCaseValid;

    }

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Options {
        @JsonProperty("search_type")
        private String searchType;
    }

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CaseLinks {
        @JsonProperty("self")
        private String self;

    }

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        @JsonProperty("last")
        private String last;
        @JsonProperty("first")
        private String first;

    }

    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Meta {
        @JsonProperty("count")
        private int count;
        @JsonProperty("offset")
        private Offset offset;
        @JsonProperty("totalCount")
        private int totalCount;

        // getters and setters
    }
    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Offset {
        @JsonProperty("last")
        private String last;
        @JsonProperty("first")
        private String first;
    }
}


