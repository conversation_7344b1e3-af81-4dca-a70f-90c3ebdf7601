package exchange.common.model.response;

import java.io.Serializable;
import java.util.ArrayList;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnalysesResultsResponse implements Serializable  {

  private static final long serialVersionUID = 9074131502785957172L;
  
  @Getter 
  @Setter
  private Integer total_pages;
  
  @Getter 
  @Setter
  private ArrayList<AnalysesResultResponse> items;

}
