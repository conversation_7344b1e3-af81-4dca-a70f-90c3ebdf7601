package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TradesData implements Serializable {

  @AllArgsConstructor
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Trade implements Serializable {

    private static final long serialVersionUID = 8334944822526509054L;

    @Getter
    @Setter
    private long id;

    @Getter
    @Setter
    private String orderSide;

    @Getter
    @Setter
    private BigDecimal price;

    @Getter
    @Setter
    private BigDecimal amount;

    @Getter
    @Setter
    private long tradedAt;
  }

  private static final long serialVersionUID = 1186282977317285165L;

  @Getter
  @Setter
  private Long symbolId;

  @Getter
  @Setter
  private List<Trade> trades = new CopyOnWriteArrayList<>();

  @Getter
  @Setter
  private long timestamp = new Date().getTime();

  public TradesData(Long symbolId) {
    this.symbolId = symbolId;
  }
}
