package exchange.common.model.response;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransferNameData implements Serializable {

  private static final long serialVersionUID = 7727347798881039331L;

  @Getter
  @Setter
  private String firstname;

  @Getter
  @Setter
  private String lastname;

}
