package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesCaseGetAssociationRes {

	@JsonProperty("data")
	private List<Data> data;

	@JsonProperty("meta")
	private Meta meta;

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("id")
		private String id;

		@JsonProperty("type")
		private String type;

		@JsonProperty("attributes")
		private Attributes attributes;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("has_alerts")
		private Boolean hasAlerts;

		@JsonProperty("association")
		private Association association;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Association {
		@JsonProperty("external_id")
		private String externalId;

		@JsonProperty("gender")
		private String gender;

		@JsonProperty("record_type")
		private String recordType;

		@JsonProperty("names")
		private List<Name> names;

		@JsonProperty("year_of_birth")
		private Integer yearOfBirth;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Name {
		@JsonProperty("prefix")
		private String prefix;

		@JsonProperty("suffix")
		private String suffix;

		@JsonProperty("entity_name")
		private String entityName;

		@JsonProperty("first_name")
		private String firstName;

		@JsonProperty("middle_name")
		private String middleName;

		@JsonProperty("last_name")
		private String lastName;

		@JsonProperty("single_string_name")
		private String singleStringName;

		@JsonProperty("name_type")
		private String nameType = "PRIMARY";
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Meta {
		@JsonProperty("count")
		private Long count;

		@JsonProperty("total_count")
		private Long totalCount;

		@JsonProperty("offset")
		private Offset offset;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Offset {
		@JsonProperty("last")
		private Long last;

		@JsonProperty("first")
		private Long first;
	}
}
