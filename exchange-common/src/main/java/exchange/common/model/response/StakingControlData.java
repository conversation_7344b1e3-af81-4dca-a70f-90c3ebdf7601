package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonFormat;
import exchange.common.constant.Currency;
import exchange.common.constant.StakingControlDealings;
import exchange.common.constant.StakingControlOperationStatus;
import exchange.common.constant.StakingControlOperations;
import exchange.common.entity.StakingControl;
import exchange.common.entity.StakingOperationRecord;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class StakingControlData implements Serializable {

  private static final long serialVersionUID = -2442773807283492959L;
  
  @Getter @Setter private Long id;
    
  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private Currency currency;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private String operationDate;
  
  @Getter
  @Setter
  private BigDecimal stakeAmountAccumulatePlan;
  
  @Getter
  @Setter
  private BigDecimal unstakeAmountPlan;
  
  @Getter
  @Setter
  private BigDecimal rewardAdvance;
  
  @Getter
  @Setter
  private BigDecimal rewardGet;
  
  @Getter
  @Setter
  private BigDecimal fee;
  
  @Getter
  @Setter
  private Boolean isStakingDate;
  
  @Getter @Setter private Long operationId;
  
  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private StakingControlOperations operation;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private StakingControlOperationStatus operationStatus;
  
  @Getter
  @Setter
  private BigDecimal stakingPoolAmount;
  
  @Getter
  @Setter
  private BigDecimal cancelPrepareAmount;
  
  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private StakingControlDealings stakingControlDealing;
  
  @Getter
  @Setter
  private Boolean canStakingControlDeal;
  
  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date updatedAt;

  public StakingControlData setProperties(StakingControl stakingControlDetails, StakingOperationRecord stakingOperationRecord) {
    this.setId(stakingControlDetails.getId());
    this.setOperationDate(FormatUtil.formatJst(stakingControlDetails.getOperationDate(), FormatPattern.YYYY_MM_DD_SLASH));
    this.setIsStakingDate(stakingControlDetails.isStakeRunDateFlg());
    this.setCurrency(stakingControlDetails.getCurrency());
    this.setStakeAmountAccumulatePlan(stakingControlDetails.getStakeAmountAccumulatePlan());
    this.setUnstakeAmountPlan(stakingControlDetails.getUnstakeAmountPlan());
    BigDecimal expirationNotContinueReward =
        stakingControlDetails.getExpirationNotContinueReward() != null
            ? stakingControlDetails.getExpirationNotContinueReward()
            : new BigDecimal(0);
    BigDecimal expirationContinueReward =
        stakingControlDetails.getExpirationContinueReward() != null
            ? stakingControlDetails.getExpirationContinueReward()
            : new BigDecimal(0);
    BigDecimal rewardAdvanceVal = expirationNotContinueReward.add(expirationContinueReward);
    this.setRewardAdvance(rewardAdvanceVal);
    if (stakingOperationRecord != null) {
      if (StakingControlOperations.REWARD.equals(stakingControlDetails.getOperation())) {
        this.setRewardGet(stakingOperationRecord.getAmount());
        // ETHの場合、成功になると、自動取得の報酬をプラスして、画面に表示する
        if (Currency.ETH.equals(stakingControlDetails.getCurrency()) && StakingControlOperationStatus.SUCCESS.equals(stakingControlDetails.getOperationStatus())) {
          BigDecimal autoGetRewardAmount = new BigDecimal(0);
          if (stakingOperationRecord.getAutoRewardAmount() != null) {
            autoGetRewardAmount = stakingOperationRecord.getAutoRewardAmount();
          }
          
          this.setRewardGet(stakingOperationRecord.getAmount().add(autoGetRewardAmount));
        }
      }
      this.setFee(stakingOperationRecord.getGasFee());
    }
    // this.setOperation(stakingControlDetails.getOperation());
    this.setOperationStatus(stakingControlDetails.getOperationStatus());
    this.setStakingPoolAmount(stakingControlDetails.getStakingPoolAmount());
    this.setCancelPrepareAmount(stakingControlDetails.getCancelPrepareAmount());
    this.setStakingControlDealing(stakingControlDetails.getStakingControlDealing());
    this.setOperationId(stakingControlDetails.getOperationId());
    this.setUpdatedAt(stakingControlDetails.getUpdatedAt());
    this.setCanStakingControlDeal(false);
    Date systemTime = new Date();
    String today = FormatUtil.formatJst(systemTime, FormatPattern.YYYY_MM_DD_SLASH);
    Date currentDate = FormatUtil.parseJst(today, FormatPattern.YYYY_MM_DD_SLASH);
    // 今日15:50前に処理できる
    long dealTime = currentDate.getTime() + 1000*3600*15 + 1000*60*50;
    if (this.getOperationDate().equals(today)) {
      if (dealTime >= systemTime.getTime()) {
        this.setCanStakingControlDeal(true);

      }
    }
    if (Currency.ETH.equals(stakingControlDetails.getCurrency()) && this.getCanStakingControlDeal() && StakingControlDealings.NONE.equals(this.getStakingControlDealing())) {
      if (this.getStakingPoolAmount().compareTo(new BigDecimal(32)) >= 0 || this.getStakingPoolAmount().compareTo(new BigDecimal(0)) < 0) {
        this.setOperation(StakingControlOperations.WAITTING);
      } else {
        this.setOperation(stakingControlDetails.getOperation());
      }
      
    } else {
      this.setOperation(stakingControlDetails.getOperation());
    }
    return this;
  }
}
