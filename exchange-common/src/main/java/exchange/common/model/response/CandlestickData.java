package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import exchange.common.entity.Candlestick;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CandlestickData implements Serializable {

  @AllArgsConstructor
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class CandlestickElement implements Serializable {

    private static final long serialVersionUID = -8337830627809305460L;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal open;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal high;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal low;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal close;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal volume;

    @Getter
    @Setter
    private long time;
  }

  private static final long serialVersionUID = -5338217329580567524L;

  @Getter
  @Setter
  private Long symbolId;

  @Getter
  @Setter
  private List<CandlestickElement> candlesticks = new ArrayList<>();

  @Getter
  @Setter
  private long timestamp = new Date().getTime();

  public CandlestickData(Long symbolId, List<Candlestick> candlesticks) {
    this.symbolId = symbolId;
    candlesticks.forEach(candlestick -> this.candlesticks.add(new CandlestickElement(candlestick.getOpen(), candlestick.getHigh(), candlestick.getLow(), candlestick.getClose(), candlestick.getVolume(), candlestick.getTargetAt().getTime())));
  }
}
