package exchange.common.model.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserInfoCorporateAgent;
import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.entity.UserInfoCorporateRepresentative;
import exchange.common.model.dto.UserAgreementDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class UserInfoCoporateData implements Serializable {

  private static final long serialVersionUID = 332350155679199387L;

  @Getter
  @Setter
  private UserInfoCorporate corporate;
  
  @Getter
  @Setter
  private UserInfoCorporateRepresentative representative;

  
  @Getter
  @Setter
  private List<UserInfoCorporateOwner> owners = new ArrayList<>();
  
  @Getter
  @Setter
  private UserInfoCorporateAgent agent;

  @Getter
  @Setter
  private List<UserAgreementDTO> userAgreements;
  
}
