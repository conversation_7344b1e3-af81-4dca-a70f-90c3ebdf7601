package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Currency;
import exchange.common.constant.ReportLabel;
import exchange.common.constant.ReportLabel.HeaderIEOApply;
import exchange.common.entity.IEODetails;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
  "no",
  "id",
  "userId",
  "deal",
  "unitPrice",
  "applyAmount",
  "applyAmountStr",
  "applyPrice",
  "ieoDetailStatus",
  "ieoChannel",
  "operation",
  "createdAt",
  "updatedAt"
})
public class IEOApplyReportData implements Serializable {

  private static final long serialVersionUID = -823879922881347574L;
  
  @Getter @Setter private Long no;

  @Getter @Setter private Long id;

  @Getter @Setter private Long userId;

  @Getter @Setter private String deal;

  @Getter @Setter private String unitPrice;
  
  @Getter @Setter private String applyAmount;
  
  @Getter @Setter private String applyAmountStr;
  
  @Getter @Setter private String applyPrice;

  @Getter @Setter private String ieoDetailStatus;

  @Getter @Setter private String ieoChannel;

  @Getter @Setter private String operation;

  @Getter @Setter private String createdAt;

  @Getter @Setter private String updatedAt;
  
  public IEOApplyReportData setProperties(IEODetails ieoDetails, Long index) {
    NumberFormat numberFormat = NumberFormat.getNumberInstance();
    
    this.setId(ieoDetails.getId());
    this.setUserId(ieoDetails.getUserId());
    this.setNo(index);
    this.setDeal(ieoDetails.getIeoRecruitInfo().getDeal());
    this.setUnitPrice(toStrigForReport(Currency.JPY, ieoDetails.getIeoRecruitInfo().getUnitPrice(), numberFormat));
    //this.setApplyAmount(ieoDetails.getApplyAmount());
    BigDecimal shareAmount = ieoDetails.getIeoRecruitInfo().getShareAmount();
    BigDecimal amountForApply = ieoDetails.getApplyAmount().multiply(shareAmount);
    this.setApplyAmount(toStrigForReport(Currency.JPY, ieoDetails.getApplyAmount(), numberFormat));
    this.setApplyAmountStr(toStrigForReport(Currency.JPY, ieoDetails.getApplyAmount(), numberFormat) + "口(" + toStrigForReport(Currency.JPY, amountForApply, numberFormat) + ")");
    BigDecimal applyPriceWithoutFee = amountForApply.multiply(ieoDetails.getIeoRecruitInfo().getUnitPrice());
    BigDecimal fee = ieoDetails.getIeoRecruitInfo().getFeeRatio().multiply(applyPriceWithoutFee);
    this.setApplyPrice(toStrigForReport(Currency.JPY, applyPriceWithoutFee.add(fee), numberFormat));
    if(ieoDetails.getIeoDetailStatus() == null) {
      this.setIeoDetailStatus("-");
    } else {
      this.setIeoDetailStatus(ReportLabel.IeoDetailStatus.valueOfName(ieoDetails.getIeoDetailStatus().name()).getLabel());
    }
    this.setIeoChannel(ieoDetails.getIeoChannel().name());
    if(ieoDetails.getOperation() != null) {
      this.setOperation(ReportLabel.Operation.valueOfName(ieoDetails.getOperation().name()).getLabel());
    }
    this.setCreatedAt(
        FormatUtil.formatJst(ieoDetails.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    this.setUpdatedAt(
        FormatUtil.formatJst(ieoDetails.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

    return this;
  }

  private String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
    // stripTrailingZeros() 末尾0除去
    // toPlainString() 指数表記にならないようにString変換
    String strValue =
        currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
    // 整数部3桁区切り
    int decimalPointIndex = strValue.indexOf(".");
    if (decimalPointIndex < 0) {
      // 小数点なし
      return numberFormat.format(Long.valueOf(strValue));
    } else {
      // 小数点あり
      String seisu = strValue.substring(0, decimalPointIndex);
      return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
    }
  }


  public static String getReportHeader() {
    return  HeaderIEOApply.NO.getLabel()
        + ","
        + HeaderIEOApply.ID.getLabel()
        + ","
        + HeaderIEOApply.USER_ID.getLabel()
        + ","
        + HeaderIEOApply.DEAL.getLabel()
        + ","
        + HeaderIEOApply.UNIT_PRICE.getLabel()
        + ","
        + HeaderIEOApply.APPLY_AMOUNT.getLabel()
        + ","
        + HeaderIEOApply.APPLY_AMOUNT_STR.getLabel()
        + ","
        + HeaderIEOApply.APPLY_PRICE.getLabel()
        + ","
        + HeaderIEOApply.IEO_DETAIL_STATUS.getLabel()
        + ","
        + HeaderIEOApply.IEO_CHANNEL.getLabel()
        + ","
        + HeaderIEOApply.OPERATION.getLabel()
        + ","
        + HeaderIEOApply.CREATED_AT.getLabel()
        + ","
        + HeaderIEOApply.UPDATED_AT.getLabel();
  }
}
