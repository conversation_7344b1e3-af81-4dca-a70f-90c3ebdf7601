package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.entity.Deposit;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaypayDepositData implements Serializable {

  private static final long serialVersionUID = 332350155679199387L;

  @AllArgsConstructor
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class DepositElement implements Serializable {
    private static final long serialVersionUID = -6724221163430559710L;

    @Getter
    @Setter
    private Long id;

    @Getter
    @Setter
    private Currency currency;

    @Getter
    @Setter
    private Long depositAccountId;

    @Getter
    @Setter
    private BigDecimal amount;


    @Getter
    @Setter
    private BigDecimal fee;

    @Getter
    @Setter
    private String transactionId;

    @Getter
    @Setter
    private DepositStatus depositStatus;

    @Getter
    @Setter
    private String date;

  }

  @Getter
  @Setter
  private List<DepositElement> depositList = new ArrayList<>();


  public void depositList(List<Deposit> depositList) {
    depositList.forEach(v -> this.depositList
        .add(new DepositElement(v.getId(), v.getCurrency(), v.getDepositAccountId(), v.getAmount(),
            v.getFee(), v.getTransactionId(), v.getDepositStatus(), v.getFormattedCreatedAt())));
  }

}
