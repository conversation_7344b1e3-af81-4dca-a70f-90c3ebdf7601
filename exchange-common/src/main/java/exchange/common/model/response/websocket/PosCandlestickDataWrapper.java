package exchange.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.entity.Candlestick;
import exchange.common.model.response.CandlestickData;
import exchange.common.model.response.PosCandlestickData;
import exchange.pos.entity.PosCandlestick;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PosCandlestickDataWrapper extends DataWrapper implements Serializable {

  @Serial
  private static final long serialVersionUID = -75526195289321121L;

  private String candlestickType;

  private Long symbolId;

  private BigDecimal open;

  private BigDecimal high;

  private BigDecimal low;

  private BigDecimal close;

  private BigDecimal volume;

  private long time;

  private Date targetAt;

  public PosCandlestickDataWrapper(PosCandlestick candlestick) {
    this.symbolId = candlestick.getSymbolId();
    this.candlestickType = candlestick.getCandlestickType().getName();
    this.open = candlestick.getOpen();
    this.high = candlestick.getHigh();
    this.low = candlestick.getLow();
    this.close = candlestick.getClose();
    this.volume = candlestick.getVolume();
    this.time = candlestick.getTargetAt().getTime();
  }

  public PosCandlestickData unwrap() {
    PosCandlestickData data = new PosCandlestickData();
    data.setSymbolId(symbolId);
    data.setTimestamp(new Date().getTime());
    data.setCandlesticks(List.of(new CandlestickData.CandlestickElement(open, high, low, close, volume, time)));
    return data;
  }

  public PosCandlestickData unwrapToEmpty() {
    PosCandlestickData data = new PosCandlestickData();
    data.setSymbolId(symbolId);
    data.setTimestamp(new Date().getTime());
    data.setCandlesticks(new ArrayList<>());
    return data;
  }

  @Override
  public String getChecksum() {
    PosCandlestickData data = this.unwrap();
    data.setTimestamp(0L); // Exclude timestamp for checksum
    return super.calculateChecksum(data);
  }

  @Override
  public String getCacheKey() {
    return "websocket:" + this.getClass().getSimpleName() + ":symbol." + this.symbolId + ":" + this.candlestickType + ":" + this.time;
  }
}

