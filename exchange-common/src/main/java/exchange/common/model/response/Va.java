
package exchange.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class Va {
  @JsonAlias("vaId")
  private String vaId;

  @JsonAlias("vaBranchCode")
  private String vaBranchCode;

  @<PERSON>sonAlias("vaBranchNameKana")
  private String vaBranchNameKana;

  @JsonAlias("vaAccountNumber")
  private String vaAccountNumber;

  public Va vaId(String vaId) {
    this.vaId = vaId;
    return this;
  }




  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Va {\n");

    sb.append("    vaId: ").append(toIndentedString(vaId)).append("\n");
    sb.append("    vaBranchCode: ").append(toIndentedString(vaBranchCode)).append("\n");
    sb.append("    vaBranchNameKana: ").append(toIndentedString(vaBranchNameKana)).append("\n");
    sb.append("    vaAccountNumber: ").append(toIndentedString(vaAccountNumber)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

