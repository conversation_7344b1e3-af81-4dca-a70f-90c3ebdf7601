package exchange.common.model.response;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OkcoinAssetResponse implements Serializable {

  private static final long serialVersionUID = -2073932866473467528L;

  // 利用中ロック資産
  @Getter @Setter private String hold;

  @Getter @Setter private String currency;

  @Getter @Setter private String balance;

  // balance - hold
  @Getter @Setter private String available;
}
