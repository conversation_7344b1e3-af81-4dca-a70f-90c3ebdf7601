
package exchange.common.model.response;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonAlias;
import exchange.common.entity.IEODetails;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class IEODetailsResponse {

  @JsonAlias("ieoRecruitInfoId")
  private Long ieoRecruitInfoId;

  @JsonAlias("ieoDetailsList")
  private List<IEODetails> ieoDetailsList = new ArrayList<>();

}