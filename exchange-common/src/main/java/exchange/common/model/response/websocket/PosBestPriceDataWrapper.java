package exchange.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.pos.model.PosBestPriceData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PosBestPriceDataWrapper extends DataWrapper implements Serializable {

  @Serial
  private static final long serialVersionUID = -986736058848090554L;

  private PosBestPriceData posBestPriceData;

  @Override
  public String getChecksum() {
    long timestamp = posBestPriceData.getTimestamp(); // Back up timestamp
    posBestPriceData.setTimestamp(0L); // Exclude timestamp for checksum
    String checksum = calculateChecksum(posBestPriceData);
    posBestPriceData.setTimestamp(timestamp); // Set timestamp back
    return checksum;
  }

  @Override
  public String getCacheKey() {
    return "websocket:" + this.getClass().getSimpleName() + ":symbol." + this.posBestPriceData.getSymbolId();
  }
}

