package exchange.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpotCoverOrderApiResponse implements Serializable {

  private static final long serialVersionUID = -8848347005372795872L;

  private Long id;
  private Long symbolId;
  private Long userId;
  private String orderSide;
  private String orderType;
  private BigDecimal price;
  private BigDecimal averagePrice;
  private BigDecimal amount;
  private BigDecimal remainingAmount;
  private String orderStatus;
  private String orderOperator;
  private String orderChannel;
  private Date createdAt;
  private Date updatedAt;
  private BigDecimal fee;
}
