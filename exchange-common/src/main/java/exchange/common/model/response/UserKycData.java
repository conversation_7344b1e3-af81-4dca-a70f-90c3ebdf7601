package exchange.common.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import exchange.common.entity.UserKycSub;
import exchange.common.model.dto.UserNoteDTO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserInfoCorporateAgent;
import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.entity.UserInfoCorporateRepresentative;
import exchange.common.entity.UserKyc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class UserKycData implements Serializable {

  @AllArgsConstructor
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class KycImage implements Serializable {

    private static final long serialVersionUID = 7566295349115748983L;

    @Getter @Setter String name;

    @Getter @Setter String imageData;
  }

  private static final long serialVersionUID = 4696045618996815635L;

  @Getter @Setter private User user;

  @Getter @Setter private UserKyc userKyc;

  @Getter @Setter private UserInfo userInfo;

  @Getter @Setter private UserInfoCorporate userInfoCorporate;

  @Getter @Setter private UserInfoCorporateRepresentative representative;

  @Getter @Setter private UserInfoCorporateAgent agent;

  @Getter @Setter private List<UserInfoCorporateOwner> owners;

  @Getter @Setter private List<KycImage> kycImages = new ArrayList<>();

  @Getter @Setter private List<UserKycSub> kycSubStatuses = new ArrayList<>();

  @Getter
  @Setter
  @JsonProperty("isLatestKycData")
  private boolean isLatestKycData;

  @Getter @Setter private List<UserNoteDTO> userNotes = new ArrayList<>();
  
  @Getter @Setter private String email;
  
  @Getter @Setter private boolean flag = true;
}
