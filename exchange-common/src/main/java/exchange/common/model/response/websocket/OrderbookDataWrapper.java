package exchange.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.model.response.OrderbookData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderbookDataWrapper extends DataWrapper implements Serializable {
  @Serial
  private static final long serialVersionUID = 6639192590026995112L;

  private OrderbookData orderbookData;

  @Override
  public String getChecksum() {
    long timestamp = orderbookData.getTimestamp(); // Back up timestamp
    orderbookData.setTimestamp(0L); // Exclude timestamp for checksum
    String checksum = calculateChecksum(orderbookData);
    orderbookData.setTimestamp(timestamp); // Set timestamp back
    return checksum;
  }

  @Override
  public String getCacheKey() {
    return "websocket:" + this.getClass().getSimpleName() + ":symbol." + this.orderbookData.getSymbolId();
  }

  public OrderbookData unwrap(){
    return orderbookData;
  }

  public OrderbookData unwrapToEmpty(){
    orderbookData.setAsks(new ArrayList<>());
    orderbookData.setBids(new ArrayList<>());
    return orderbookData;
  }
}
