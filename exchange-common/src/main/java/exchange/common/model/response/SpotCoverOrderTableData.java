package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.serializer.BigDecimalSerializer;
import exchange.spot.entity.SpotCoverOrder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class SpotCoverOrderTableData implements Serializable {

  private static final long serialVersionUID = -2442773807173492959L;

  @Getter @Setter private String tableKey;

  @Getter @Setter private Long id;

  @Getter @Setter private Long symbolId;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private CurrencyPair currencyPair;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private OrderSide orderSide;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private OrderType orderType;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal price;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal averagePrice;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal amount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal filledAmount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal remainingAmount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal fee;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  @Getter @Setter private Long exchangeOrderId;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date createdAt;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date updatedAt;

  // カバー注文に紐づくコピー注文約定履歴の約定数量サマリ
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal copyTradeSellAmount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal copyTradeBuyAmount;

  // カバー注文に紐づくコピー注文約定履歴の平均約定価格
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal copyTradeAveragePrice;

  // コピー注文約定手数料サマリ（マリー済み分含む）
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal copyTradeFeeSum;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal priceDiff;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal profit;

  public SpotCoverOrderTableData setProperties(
      CurrencyPair currencyPair, SpotCoverOrder spotCoverOrder) {
    // for vue.js v-data-table
    this.setTableKey(spotCoverOrder.getSymbolId() + "_" + spotCoverOrder.getId());

    this.setId(spotCoverOrder.getId());
    this.setSymbolId(spotCoverOrder.getSymbolId());
    this.setCurrencyPair(currencyPair);
    this.setOrderSide(spotCoverOrder.getOrderSide());
    this.setOrderType(spotCoverOrder.getOrderType());
    this.setPrice(spotCoverOrder.getPrice());
    // 損益計算に使用するため桁数処理しない
    this.setAveragePrice(spotCoverOrder.getAveragePrice());
    this.setAmount(spotCoverOrder.getAmount());
    this.setRemainingAmount(spotCoverOrder.getRemainingAmount());
    this.setFilledAmount(spotCoverOrder.getAmount().subtract(spotCoverOrder.getRemainingAmount()));
    this.setFee(spotCoverOrder.getFee());
    this.setExchange(spotCoverOrder.getExchange());
    this.setExchangeOrderId(spotCoverOrder.getExchangeOrderId());
    this.setCreatedAt(spotCoverOrder.getCreatedAt());
    this.setUpdatedAt(spotCoverOrder.getUpdatedAt());

    return this;
  }
}
