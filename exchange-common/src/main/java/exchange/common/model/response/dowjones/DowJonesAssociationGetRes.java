package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesAssociationGetRes {
	@JsonProperty("data")
	private Data data;

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("id")
		private String id;

		@JsonProperty("type")
		private String type;

		@JsonProperty("attributes")
		private Attributes attributes;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("external_id")
		private String externalId;

		@JsonProperty("gender")
		private String gender;

		@JsonProperty("record_type")
		private String recordType;

		@JsonProperty("names")
		private List<Name> names;

		@JsonProperty("year_of_birth")
		private Integer yearOfBirth;

		@JsonProperty("is_deceased")
		private Boolean isDeceased;

		@JsonProperty("timestamp")
		private Date timestamp;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Name {
		@JsonProperty("prefix")
		private String prefix;

		@JsonProperty("suffix")
		private String suffix;

		@JsonProperty("entity_name")
		private String entityName;

		@JsonProperty("first_name")
		private String firstName;

		@JsonProperty("middle_name")
		private String middleName;

		@JsonProperty("last_name")
		private String lastName;

		@JsonProperty("name_type")
		private String nameType = "PRIMARY";
	}

	public String getAssociationId() {
		if (null != this.getData()) {
			return this.getData().getId();
		}
		return null;
	}
}
