package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Currency;
import exchange.common.constant.ReportLabel;
import exchange.common.constant.ReportLabel.HeaderIEOApplyUserMode;
import exchange.common.entity.IEOUserElectInfo;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
  "no",
  "userId",
  "deal",
  "unitPrice",
  "hasChangeElectAmount",
  "totalApplyAmount",
  "totalApplyAmountStr",
  "totalApplyPrice",
  "totalElectTargetAmount",
  "totalElectTargetAmountStr",
  "totalElectTargetPrice",
  "excludeElectTargetAmount",
  "excludeElectTargetAmountStr",
  "excludeElectTargetPrice",
  "applyLimitAmount",
  "applyLimitAmountStr",
  "incomeFinancialAssetsLimitPriceStr",
  "createdAt",
  "updatedAt"
})
public class IEOUserElectReportData implements Serializable {

  private static final long serialVersionUID = -823879922881347574L;
  
  @Getter @Setter private Long no;

  @Getter @Setter private Long userId;

  @Getter @Setter private String deal;

  @Getter @Setter private String unitPrice;
  
  @Getter @Setter private String hasChangeElectAmount;
  
  @Getter @Setter private String totalApplyAmount;
  
  @Getter @Setter private String totalApplyAmountStr;
  
  @Getter @Setter private String totalApplyPrice;
  
  @Getter @Setter private String totalElectTargetAmount;
  
  @Getter @Setter private String totalElectTargetAmountStr;
  
  @Getter @Setter private String totalElectTargetPrice;
  
  @Getter @Setter private String excludeElectTargetAmount;
  
  @Getter @Setter private String excludeElectTargetAmountStr;
  
  @Getter @Setter private String excludeElectTargetPrice;
  
  @Getter @Setter private String applyLimitAmount;
  
  @Getter @Setter private String applyLimitAmountStr;
  
  @Getter @Setter private String incomeFinancialAssetsLimitPriceStr;

  @Getter @Setter private String createdAt;

  @Getter @Setter private String updatedAt;
  
  public IEOUserElectReportData setProperties(IEOUserElectInfo ieoUserElectInfo, Long index) {
    NumberFormat numberFormat = NumberFormat.getNumberInstance();
    this.setUserId(ieoUserElectInfo.getUserId());
    this.setNo(index);
    this.setDeal(ieoUserElectInfo.getIeoRecruitInfo().getDeal());
    this.setUnitPrice(toStrigForReport(Currency.JPY, ieoUserElectInfo.getIeoRecruitInfo().getUnitPrice(), numberFormat));
    this.setHasChangeElectAmount(ReportLabel.IEOHasOrHasNotChanged
        .valueOfName(ieoUserElectInfo.getHasChangeElectAmount().getName()).getLabel());
    
    BigDecimal shareAmount = ieoUserElectInfo.getIeoRecruitInfo().getShareAmount();
    // 1口価格
    BigDecimal oneIEOPrice = ieoUserElectInfo.getIeoRecruitInfo().getUnitPrice()
        .multiply(ieoUserElectInfo.getIeoRecruitInfo().getShareAmount()).multiply(new BigDecimal(1)
            .add(ieoUserElectInfo.getIeoRecruitInfo().getFeeRatio()));
    BigDecimal amountForApply = ieoUserElectInfo.getTotalApplyAmount().multiply(shareAmount);
    // 合計申込口数
    this.setTotalApplyAmount(toStrigForReport(Currency.JPY, ieoUserElectInfo.getTotalApplyAmount(), numberFormat));
    // 合計申込口数（数量）
    this.setTotalApplyAmountStr(
        toStrigForReport(Currency.JPY, ieoUserElectInfo.getTotalApplyAmount(), numberFormat) + "口("
            + toStrigForReport(Currency.JPY, amountForApply, numberFormat) + ")");
    BigDecimal applyPrice = ieoUserElectInfo.getTotalApplyAmount().multiply(oneIEOPrice).setScale(0, RoundingMode.DOWN);
    // 合計申込金額（円）
    this.setTotalApplyPrice(toStrigForReport(Currency.JPY, applyPrice, numberFormat));
    
    if (ieoUserElectInfo.getTotalElectTargetAmount() != null) {
      BigDecimal amountForTotalElectTarget = ieoUserElectInfo.getTotalElectTargetAmount().multiply(shareAmount);
      // 合計抽選対象口数
      this.setTotalElectTargetAmount(
          ieoUserElectInfo.getTotalElectTargetAmount() == null ? "0"
              : toStrigForReport(Currency.JPY, ieoUserElectInfo.getTotalElectTargetAmount(), numberFormat));
      // 合計抽選対象口数（数量）
      this.setTotalElectTargetAmountStr(
          toStrigForReport(Currency.JPY, ieoUserElectInfo.getTotalElectTargetAmount(), numberFormat) + "口("
              + toStrigForReport(Currency.JPY, amountForTotalElectTarget, numberFormat) + ")");
      BigDecimal priceForTotalElectTarget = ieoUserElectInfo.getTotalElectTargetAmount().multiply(oneIEOPrice).setScale(0, RoundingMode.DOWN);
      // 合計抽選対象金額（円）
      this.setTotalElectTargetPrice(toStrigForReport(Currency.JPY, priceForTotalElectTarget, numberFormat));
      
      BigDecimal excludeElectTargetAmount = ieoUserElectInfo.getTotalApplyAmount()
          .subtract(ieoUserElectInfo.getTotalElectTargetAmount()).setScale(0, RoundingMode.DOWN);
      // 抽選除外口数
      this.setExcludeElectTargetAmount(toStrigForReport(Currency.JPY, excludeElectTargetAmount, numberFormat));
      // 抽選除外口数（数量）
      this.setExcludeElectTargetAmountStr(
          toStrigForReport(Currency.JPY, excludeElectTargetAmount, numberFormat) + "口("
              + toStrigForReport(Currency.JPY, excludeElectTargetAmount.multiply(shareAmount).setScale(0, RoundingMode.DOWN), numberFormat) + ")");
      BigDecimal priceForExcludeElectTarget = excludeElectTargetAmount.multiply(oneIEOPrice).setScale(0, RoundingMode.DOWN);
      // 抽選除外金額（円）
      this.setExcludeElectTargetPrice(toStrigForReport(Currency.JPY, priceForExcludeElectTarget, numberFormat));
    }
    
    if (ieoUserElectInfo.getApplyLimitAmount() != null) {
      // 申込上限口数
      this.setApplyLimitAmount(toStrigForReport(Currency.JPY, ieoUserElectInfo.getApplyLimitAmount(), numberFormat));
      // 申込上限口数（数量）
      this.setApplyLimitAmountStr(
          toStrigForReport(Currency.JPY, ieoUserElectInfo.getApplyLimitAmount(), numberFormat) + "口("
              + toStrigForReport(Currency.JPY,
            		  ieoUserElectInfo.getApplyLimitAmount().multiply(shareAmount).setScale(0, RoundingMode.DOWN),
                  numberFormat)
              + ")");

    }

    if (ieoUserElectInfo.getIncomeFinancialAssetsLimitPrice() != null) {
      BigDecimal incomeFinancialAssetsLimitPrice = ieoUserElectInfo
          .getIncomeFinancialAssetsLimitPrice().divide(new BigDecimal(10000), RoundingMode.DOWN);
      if (incomeFinancialAssetsLimitPrice.compareTo(new BigDecimal(10000)) == 0) {
        this.setIncomeFinancialAssetsLimitPriceStr("1億円");
      } else {
        this.setIncomeFinancialAssetsLimitPriceStr(
            toStrigForReport(Currency.JPY, incomeFinancialAssetsLimitPrice, numberFormat) + "万円");
      }
    }
   
    this.setCreatedAt(
        FormatUtil.formatJst(ieoUserElectInfo.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
    this.setUpdatedAt(
        FormatUtil.formatJst(ieoUserElectInfo.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

    return this;
  }

  private String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
    // stripTrailingZeros() 末尾0除去
    // toPlainString() 指数表記にならないようにString変換
    String strValue =
        currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
    // 整数部3桁区切り
    int decimalPointIndex = strValue.indexOf(".");
    if (decimalPointIndex < 0) {
      // 小数点なし
      return numberFormat.format(Long.valueOf(strValue));
    } else {
      // 小数点あり
      String seisu = strValue.substring(0, decimalPointIndex);
      return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
    }
  }


  public static String getReportHeader() {
    return  HeaderIEOApplyUserMode.NO.getLabel()
        + ","
        + HeaderIEOApplyUserMode.USER_ID.getLabel()
        + ","
        + HeaderIEOApplyUserMode.DEAL.getLabel()
        + ","
        + HeaderIEOApplyUserMode.UNIT_PRICE.getLabel()
        + ","
        + HeaderIEOApplyUserMode.HAS_CHANGE_ELECT_AMOUNT.getLabel()
        + ","
        + HeaderIEOApplyUserMode.TOTAL_APPLY_AMOUNT.getLabel()
        + ","
        + HeaderIEOApplyUserMode.TOTAL_APPLY_AMOUNT_STR.getLabel()
        + ","
        + HeaderIEOApplyUserMode.TOTAL_APPLY_PRICE.getLabel()
        + ","
        + HeaderIEOApplyUserMode.TOTAL_ELECT_TARGET_AMOUNT.getLabel()
        + ","
        + HeaderIEOApplyUserMode.TOTAL_ELECT_TARGET_AMOUNT_STR.getLabel()
        + ","
        + HeaderIEOApplyUserMode.TOTAL_ELECT_TARGET_PRICE.getLabel()
        + ","
        + HeaderIEOApplyUserMode.EXCLUDE_ELECT_TARGET_AMOUNT.getLabel()
        + ","
        + HeaderIEOApplyUserMode.EXCLUDE_ELECT_TARGET_AMOUNT_STR.getLabel()
        + ","
        + HeaderIEOApplyUserMode.EXCLUDE_ELECT_TARGET_PRICE.getLabel()
        + ","
        + HeaderIEOApplyUserMode.APPLY_LIMIT_AMOUNT.getLabel()
        + ","
        + HeaderIEOApplyUserMode.APPLY_LIMIT_AMOUNT_STR.getLabel()
        + ","
        + HeaderIEOApplyUserMode.INCOME_FINANCIAL_ASSETS_LIMIT_PRICE_STR.getLabel()
        + ","
        + HeaderIEOApplyUserMode.CREATED_AT.getLabel()
        + ","
        + HeaderIEOApplyUserMode.UPDATED_AT.getLabel();
  }
}
