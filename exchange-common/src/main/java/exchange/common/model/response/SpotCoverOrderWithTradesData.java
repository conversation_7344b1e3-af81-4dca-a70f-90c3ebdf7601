package exchange.common.model.response;

import java.io.Serializable;
import java.util.List;
import exchange.spot.entity.SpotCopyTrade;
import exchange.spot.entity.SpotCoverOrder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

// SpotCoverOrder + カバー対象SpotCopyTradeのkeyリスト

@NoArgsConstructor
public class SpotCoverOrderWithTradesData implements Serializable {

  private static final long serialVersionUID = -2442773875453492959L;

  @Getter @Setter private SpotCoverOrder spotCoverOrder;

  @Getter @Setter private List<SpotCopyTrade> spotCopyTrades;

  public SpotCoverOrderWithTradesData setProperties(
      SpotCoverOrder spotCoverOrder, List<SpotCopyTrade> spotCopyTrades) {

    this.setSpotCoverOrder(spotCoverOrder);
    this.setSpotCopyTrades(spotCopyTrades);

    return this;
  }
}
