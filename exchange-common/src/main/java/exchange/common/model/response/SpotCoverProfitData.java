package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.Exchange;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class SpotCoverProfitData implements Serializable {

  private static final long serialVersionUID = -2442773807173474159L;

  @Getter @Setter private String tableKey;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  @Getter @Setter private Long symbolId;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private CurrencyPair currencyPair;

  // カバー注文数量サマリ
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal amountSum;

  // カバー約定数量サマリ
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal filledAmountSum;

  // 損益 = カバー済みコピー注文の約定総額とカバー注文の約定総額の差分(表示用に桁数処理する)
  // 部分約定の場合、assetAmountの桁数処理をカバーできないため概算となる
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal profit;

  // カバー手数料サマリ
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal fee;

  // コピー注文約定数量サマリ（マリー済み分含む）
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal copyTradeAmountSum;

  // コピー注文約定手数料サマリ（マリー済み分含む）
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal copyTradeFeeSum;

  // カバー損益 - カバー手数料 - コピー注文約定手数料
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal profitWithFee;

  public SpotCoverProfitData setProperties(
      Exchange exchange,
      Long symbolId,
      CurrencyPair currencyPair,
      BigDecimal amountSum,
      BigDecimal filledAmountSum,
      BigDecimal profit,
      BigDecimal fee,
      BigDecimal copyTradeAmountSum,
      BigDecimal copyTradeFeeSum) {

    // for vue.js v-data-table
    this.setTableKey(exchange + "_" + symbolId);

    this.setExchange(exchange);
    this.setSymbolId(symbolId);
    this.setCurrencyPair(currencyPair);
    this.setAmountSum(amountSum);
    this.setFilledAmountSum(filledAmountSum);
    this.setProfit(profit);
    this.setFee(fee);
    this.setCopyTradeAmountSum(copyTradeAmountSum);
    this.setCopyTradeFeeSum(copyTradeFeeSum);
    this.setProfitWithFee(profit.subtract(fee).subtract(copyTradeFeeSum));

    return this;
  }
}
