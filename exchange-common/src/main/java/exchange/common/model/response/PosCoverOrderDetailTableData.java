package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.serializer.BigDecimalSerializer;
import exchange.pos.entity.PosCoverOrder;
import exchange.pos.entity.PosCustomizeOrder;
import exchange.pos.entity.PosOrder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class PosCoverOrderDetailTableData implements Serializable {

  private static final long serialVersionUID = -2442773807173492959L;

  @Getter @Setter private String tableKey;

  @Getter @Setter private Long id;

  @Getter @Setter private Long symbolId;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private CurrencyPair currencyPair;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private OrderSide orderSide;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private OrderType orderType;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal price;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal averagePrice;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal usdtPrice;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal amount;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal autoAmount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal filledAmountAuto;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal filledAmountManual;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal orderingAmount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal remainingAmount;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal fee;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal averagePriceManual;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal orderPrice;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  @Getter @Setter private String exchangeOrderId;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date createdAt;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date updatedAt;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal customizeAmount;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal customizeOrderPrice;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal customizeOrderingAmount;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal customizeFilledAmount;
  
  @Getter
  @Setter
  private OrderStatus customizeOrderStatus;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal customizeFee;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal averagePriceCustomize;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal amountManual;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal remainingAmountManual;
  
  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal feeManual;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal priceDiff;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal profit;
  
  @Getter
  @Setter
  private OrderStatus orderStatus;

  public PosCoverOrderDetailTableData setProperties(
      CurrencyPair currencyPair, PosCoverOrder posCoverOrder, PosOrder posOrder) {
    // for vue.js v-data-table
    this.setTableKey(posCoverOrder.getSymbolId() + "_" + posCoverOrder.getId());

    this.setId(posCoverOrder.getId());
    this.setSymbolId(posCoverOrder.getSymbolId());
    this.setCurrencyPair(currencyPair);
    this.setOrderSide(posCoverOrder.getOrderSide());
    this.setOrderType(posCoverOrder.getOrderType());
    this.setPrice(posCoverOrder.getPrice());
    this.setOrderStatus(posCoverOrder.getOrderStatus());
    // 損益計算に使用するため桁数処理しない
    this.setAveragePrice(posCoverOrder.getAveragePrice());
    this.setUsdtPrice(posCoverOrder.getUsdtPrice());
    this.setOrderPrice(posOrder.getPrice());
    if (CurrencyPair.NIDT_JPY.equals(currencyPair)) {
      this.setAutoAmount(posCoverOrder.getAmount());
      this.setAmount(posOrder.getAmount());
      if (!(OrderStatus.CANCELED_UNFILLED.equals(posCoverOrder.getOrderStatus()) ||
          OrderStatus.CANCELED_PARTIALLY_FILLED.equals(posCoverOrder.getOrderStatus()))) {
        // 注文中
        this.setOrderingAmount(posCoverOrder.getRemainingAmount());
      } else {
        this.setOrderingAmount(BigDecimal.ZERO);
      }
      // 未約定数量
      this.setRemainingAmount(posCoverOrder.getRemainingAmountManualNidt());
      // カスタマイズ情報
      PosCustomizeOrder posCustomizeOrder = posCoverOrder.getPosCustomizeOrder();
      if (posCustomizeOrder != null) {
        this.setCustomizeAmount(posCustomizeOrder.getAmount());
        this.setCustomizeOrderPrice(posCustomizeOrder.getPrice());
        if (!(OrderStatus.CANCELED_UNFILLED.equals(posCustomizeOrder.getOrderStatus())
            || OrderStatus.CANCELED_PARTIALLY_FILLED.equals(posCustomizeOrder.getOrderStatus()))) {
          // 注文中
          this.setCustomizeOrderingAmount(posCustomizeOrder.getRemainingAmount());
        } else {
          this.setCustomizeOrderingAmount(BigDecimal.ZERO);
        }
        this.setCustomizeFilledAmount(posCustomizeOrder.getAmount().subtract(posCustomizeOrder.getRemainingAmount()));
        this.setCustomizeOrderStatus(posCustomizeOrder.getOrderStatus());
        this.setAveragePriceCustomize(posCustomizeOrder.getAveragePrice());
        this.setCustomizeFee(posCustomizeOrder.getFee());
      } else {
        this.setCustomizeAmount(posCoverOrder.getAmountManualNidt());
      }
      this.setAmountManual(posCoverOrder.getAmountManualNidt());
      this.setRemainingAmountManual(posCoverOrder.getRemainingAmountManualNidt());
      this.setAveragePriceManual(posCoverOrder.getAveragePriceManual());
      this.setFeeManual(posCoverOrder.getFeeManualNidt());
    } else {
      this.setAutoAmount(posCoverOrder.getAmount());
      this.setAmount(posCoverOrder.getAmount());
      // 未約定数量
      this.setRemainingAmount(posCoverOrder.getRemainingAmount());
    }

    this.setFilledAmountAuto(posCoverOrder.getAmount().subtract(posCoverOrder.getRemainingAmount()));
    this.setFee(posCoverOrder.getFee());
    this.setExchange(posCoverOrder.getExchange());
    this.setExchangeOrderId(posCoverOrder.getExchangeOrderId());
    this.setCreatedAt(posCoverOrder.getCreatedAt());
    this.setUpdatedAt(posCoverOrder.getUpdatedAt());

    return this;
  }
}
