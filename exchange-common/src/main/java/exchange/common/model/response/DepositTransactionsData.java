package exchange.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties
public class DepositTransactionsData implements Serializable {

  private String raId;

  private String raBranchCode;

  private String raBranchNameKana;

  private String raAccountNumber;

  private String raHolderName;

  private String dateFrom;

  private String dateTo;

  private String baseDate;

  private String baseTime;

  private String hasNext;

  private String nextItemKey;

  private String count;

  private String errorCode;

  private String errorMessage;

  private List<VaTransactionsData> vaTransactions;
}
