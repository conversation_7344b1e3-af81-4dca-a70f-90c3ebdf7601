package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import exchange.common.constant.KycData;
import lombok.Data;

/**
 * @author: wen.y
 * @date: 2024/12/6
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesRiskEntitiesSearchRes {

	@JsonProperty("meta")
	private Meta meta;

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Meta {
		@JsonProperty("count")
		private Long count;

		@JsonProperty("first")
		private Long first;

		@JsonProperty("last")
		private Long last;

		@JsonProperty("next")
		private Long next;

		@JsonProperty("total_count")
		private Long totalCount;

		@JsonProperty("screening_context")
		private String screeningContext;
	}

	public boolean hasRisk() {
		if (null != this.getMeta() && null != this.getMeta().getTotalCount() && this.getMeta().getTotalCount().compareTo(0L) > 0) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	public KycData getKycFlag() {
		KycData kycData = KycData.NONE;
		if (null != this.getMeta() && null != this.getMeta().getTotalCount() && this.getMeta().getTotalCount().compareTo(0L) > 0) {
			kycData = KycData.EXISTS;
		}
		return kycData;
	}

	public String getScreeningContext() {
		if (null == this.getMeta()) {
			return null;
		}
		return this.getMeta().getScreeningContext();
	}

}
