package exchange.common.model.response;

import java.io.Serializable;
import java.math.RoundingMode;
import java.text.NumberFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Authority;
import exchange.common.constant.ReportLabel.HeaderAsset;
import exchange.common.entity.Asset;
import exchange.common.entity.UserInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"userId", "userName", "email", "currency", "onhandAmount", "lockedAmount"})
public class AssetReportData implements Serializable {

  private static final long serialVersionUID = 6502972389068401244L;

  @Getter @Setter private Long userId;

  @Getter @Setter private String userName = "";

  @Getter @Setter private String email;

  @Getter @Setter private String currency;

  @Getter @Setter private String onhandAmount;

  @Getter @Setter private String lockedAmount;

  public AssetReportData setProperties(Asset asset) {
    this.setUserId(asset.getUser().getId());

    String authority = asset.getUser().getAuthorities().get(0).getAuthority();
    if (Authority.PERSONAL.name().equals(authority)) {
      UserInfo userInfo = asset.getUser().getUserInfo();
      if (userInfo != null) {
        this.setUserName(userInfo.getLastName() + " " + userInfo.getFirstName());
      }
    } else if (Authority.CORPORATE.name().equals(authority)) {
      if (asset.getUser().getUserInfoCorporate() != null) {
        this.setUserName(asset.getUser().getUserInfoCorporate().getName());
      }
    }

    this.setEmail(asset.getUser().getEmail());
    this.setCurrency(asset.getCurrency().getName());

    NumberFormat numberFormat = NumberFormat.getNumberInstance();

    String onhandAmountStr =
        asset
            .getCurrency()
            .getScaledAmount(asset.getOnhandAmount(), RoundingMode.FLOOR)
            .stripTrailingZeros()
            .toPlainString();
    if (onhandAmountStr.indexOf(".") < 0) {
      this.setOnhandAmount(numberFormat.format(Long.valueOf(onhandAmountStr)));
    } else {
      // 整数部3桁区切り
      String onhandAmountInt = onhandAmountStr.substring(0, onhandAmountStr.indexOf("."));
      this.setOnhandAmount(
          numberFormat.format(Long.valueOf(onhandAmountInt))
              + onhandAmountStr.substring(onhandAmountStr.indexOf(".")));
    }

    String lockedAmountStr =
        asset
            .getCurrency()
            .getScaledAmount(asset.getLockedAmount(), RoundingMode.FLOOR)
            .stripTrailingZeros()
            .toPlainString();
    if (lockedAmountStr.indexOf(".") < 0) {
      this.setLockedAmount(numberFormat.format(Long.valueOf(lockedAmountStr)));
    } else {
      // 整数部3桁区切り
      String lockedAmountInt = lockedAmountStr.substring(0, lockedAmountStr.indexOf("."));
      this.setLockedAmount(
          numberFormat.format(Long.valueOf(lockedAmountInt))
              + lockedAmountStr.substring(lockedAmountStr.indexOf(".")));
    }

    return this;
  }

  public static String getReportHeader() {
    return HeaderAsset.USER_ID.getLabel()
        + ","
        + HeaderAsset.USER_NAME.getLabel()
        + ","
        + HeaderAsset.EMAIL.getLabel()
        + ","
        + HeaderAsset.CURRENCY.getLabel()
        + ","
        + HeaderAsset.ONHAND_AMOUNT.getLabel()
        + ","
        + HeaderAsset.LOCKED_AMOUNT.getLabel();
  }
}
