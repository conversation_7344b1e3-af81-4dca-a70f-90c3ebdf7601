package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonFormat;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.Exchange;
import exchange.common.constant.TradeType;
import exchange.common.entity.CoverOrderConfig;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class CoverOrderConfigData implements Serializable {

  private static final long serialVersionUID = -2442773813573492959L;

  @Getter @Setter private Long id;

  @Getter @Setter private Long symbolId;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private CurrencyPair currencyPair;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  @Getter @Setter private BigDecimal rangePricePercent;

  @Getter @Setter private BigDecimal minOrderAmount;

  @Getter @Setter private boolean enabled = false;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date createdAt;

  @Getter
  @Setter
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  @Temporal(TemporalType.TIMESTAMP)
  private Date updatedAt;

  @Getter
  @Setter
  @Enumerated(EnumType.STRING)
  private TradeType tradeType;

  @Getter
  @Setter
  private BigDecimal orderAmountPercent;

  public CoverOrderConfigData setProperties(
      CurrencyPair currencyPair, CoverOrderConfig coverOrderConfig) {
    this.setId(coverOrderConfig.getId());
    this.setSymbolId(coverOrderConfig.getSymbolId());
    this.setCurrencyPair(currencyPair);
    this.setExchange(coverOrderConfig.getExchange());
    this.setRangePricePercent(coverOrderConfig.getRangePricePercent());
    this.setMinOrderAmount(coverOrderConfig.getMinOrderAmount());
    this.setEnabled(coverOrderConfig.isEnabled());
    this.setCreatedAt(coverOrderConfig.getCreatedAt());
    this.setUpdatedAt(coverOrderConfig.getUpdatedAt());
    this.setTradeType(coverOrderConfig.getTradeType());
    this.setOrderAmountPercent(coverOrderConfig.getOrderAmountPercent());
    return this;
  }
}
