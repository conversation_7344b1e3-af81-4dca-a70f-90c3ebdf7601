package exchange.common.constant;

import java.math.BigDecimal;
import java.math.RoundingMode;
import exchange.common.util.StringUtil;
import lombok.Getter;

// ▼運用
// 有効な通貨ペア一覧を取得する場合はcurrencyPairConfig(enabled=true)を使用すること

// ▼リスト
// currencyPairConfigの無効(enabled=false)通貨ペアもここで保持すること。ここのみに存在する分は問題なし
// xxx_JPYは必須 ※assetSummaryService.findJpyConversionFromCandlestickなど、円価変換で必須のため

// ▼設定値
// base通貨、quote通貨、数量(base)の小数点以下桁数、価格(quote)の小数点以下桁数、手数料・資産の小数点以下桁数

public enum CurrencyPair {
  // crypto
  // JPY basis
  ADA_JPY(Currency.ADA, Currency.JPY, 0, 3, 0,1),
  NIDT_JPY(Currency.NIDT, Currency.JPY, 0, 3, 0,0),
  BTC_JPY(Currency.BTC, Currency.JPY, 4, 0, 0,4),
  ETH_JPY(Currency.ETH, Currency.JPY, 4, 0, 0,4),
  XRP_JPY(Currency.XRP, Currency.JPY, 3, 3, 0,0);

  public static CurrencyPair valueOf(Currency baseCurrency, Currency quoteCurrency) {
    for (CurrencyPair currencyPair : values()) {
      if (currencyPair.getBaseCurrency() == baseCurrency
          && currencyPair.getQuoteCurrency() == quoteCurrency) {
        return currencyPair;
      }
    }

    return null;
  }

  public static CurrencyPair valueOfName(String name) {
    for (CurrencyPair currencyPair : values()) {
      if (currencyPair.name().equals(name)) {
        return currencyPair;
      }
    }

    return null;
  }

  public static CurrencyPair valueOfNameWithoutUnderscore(String name) {
    for (CurrencyPair currencyPair : values()) {
      if (currencyPair.getNameWithoutUnderscore().equals(name)) {
        return currencyPair;
      }
    }

    return null;
  }

  @Getter private final Currency baseCurrency;

  @Getter private final Currency quoteCurrency;

  @Getter private final int basePrecision;

  @Getter private final int quotePrecision;

  @Getter private final int assetPrecision;

  @Getter private final int posBasePrecision;

  CurrencyPair(
      Currency baseCurrency,
      Currency quoteCurrency,
      int basePrecision,
      int quotePrecision,
      int assetPrecision,
      int posBasePrecision) {
    this.baseCurrency = baseCurrency;
    this.quoteCurrency = quoteCurrency;
    this.basePrecision = basePrecision;
    this.quotePrecision = quotePrecision;
    this.assetPrecision = assetPrecision;
    this.posBasePrecision = posBasePrecision;
  }

  public String getName() {
    return name();
  }

  public String toLowerCase() {
    return name().toLowerCase();
  }

  public String toUpperCamelCase() {
    return StringUtil.toUpperCamelCase(name());
  }

  public String getNameWithoutUnderscore() {
    return name().replaceAll("_", "");
  }

  public String getNameWithHyphen() {
    return baseCurrency.name() + "-" + quoteCurrency.name();
  }

  public BigDecimal getScaledPrice(BigDecimal price, int precision, RoundingMode roundingMode) {
    return price != null ? price.setScale(precision, roundingMode) : BigDecimal.ZERO;
  }

  public BigDecimal getScaledPrice(BigDecimal price, RoundingMode roundingMode) {
    return price != null ? price.setScale(quotePrecision, roundingMode) : BigDecimal.ZERO;
  }

  public BigDecimal getScaledPrice(BigDecimal price) {
    return price != null ? getScaledPrice(price, RoundingMode.HALF_UP) : BigDecimal.ZERO;
  }

  public BigDecimal getScaledAmount(BigDecimal amount, int precision, RoundingMode roundingMode) {
    return amount.setScale(precision, roundingMode);
  }

  public BigDecimal getScaledAmount(BigDecimal amount, RoundingMode roundingMode) {
    return amount.setScale(basePrecision, roundingMode);
  }

  public BigDecimal getPosScaledAmount(BigDecimal amount, RoundingMode roundingMode){
    return amount.setScale(posBasePrecision,roundingMode);
  }

  public BigDecimal getScaledAmount(BigDecimal amount) {
    return getScaledAmount(amount, RoundingMode.HALF_UP);
  }

  public BigDecimal getPosScaledAmount(BigDecimal amount) {
     return getPosScaledAmount(amount, RoundingMode.HALF_UP);
  }

  // fee(手数料), onhandAmount(Asset)のスケール
  public BigDecimal getScaledAsset(BigDecimal assetAmount, RoundingMode roundingMode) {
    return assetAmount.setScale(assetPrecision, roundingMode);
  }

  public Currency getTargetCurrency(OrderSide orderSide) {
    return orderSide.isSell() ? baseCurrency : quoteCurrency;
  }
}
