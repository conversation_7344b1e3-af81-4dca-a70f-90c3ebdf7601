package exchange.common.constant;

public enum FiatDepositStatus {
  DONE, // 完了
  UNKNOWN_DEPOSIT,
  UNKNOWN_DEPOSIT_INFORMED,
  UNKNOWN_DEPOSIT_WAITING_FOR_CHANGE,
  REJECTED;

	public static FiatDepositStatus valueOfName(String name) {
		for (FiatDepositStatus withdrawalStatus : values()) {
	      if (withdrawalStatus.name().equals(name)) {
	        return withdrawalStatus;
	      }
	    }
		return null;
	}
}