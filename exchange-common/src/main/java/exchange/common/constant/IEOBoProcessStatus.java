package exchange.common.constant;

public enum IEOBoProcessStatus {
  INIT, // 申込開始の状態
  APPLY_START, // 申込開始
  APPLY_END, // 申込終了
  APPLY_CONFIRMED, // 申込確認
  RAFFLE_PROCESSING, // 抽選中
  RAFFLE, // 抽選
  APPROVE_PROCESSING, // 承認中
  APPROVED, // 承認
  MAIL_PROCESSING; // メール送信中

  public static IEOBoProcessStatus valueOfName(String name) {
    for (IEOBoProcessStatus ieoBoProcessStatus : values()) {
      if (ieoBoProcessStatus.name().equals(name)) {
        return ieoBoProcessStatus;
      }
    }

    return null;
  }
}