package exchange.common.constant;

public enum StakingStatus {
  APPLING, // 申請中
  WAIT_STAKING, // ステーキング依頼中
  APPLIED, // ステーキング中
  EXPIRED, // 期間満了
  EXPIRED_WAIT_FUND, // 期満返金待
  EXPIRED_FUNDED, // 期満返金済
  CANCEL_APPLY, // 解除申請中
  CANCEL_WAIT_FUND, // 解除返金待
  CANCEL_FUNDED; // 解除返金済

  public static StakingStatus valueOfName(String name) {
    for (StakingStatus stakingStatus : values()) {
      if (stakingStatus.name().equals(name)) {
        return stakingStatus;
      }
    }

    return null;
  }
}
