package exchange.common.constant;

import com.baomidou.mybatisplus.annotation.IEnum;

public enum KycMailStatus implements IEnum<String> {
  NONE, // メール送信なし
  MAIL_PREPARING, // 準備中
  MAIL_SENT, // 送信済み
  MAIL_FAILED; // 送信エラー

  public static KycMailStatus valueOfName(String name) {
    for (KycMailStatus kycMailStatus : values()) {
      if (kycMailStatus.name().equals(name)) {
        return kycMailStatus;
      }
    }

    return null;
  }

  @Override
  public String getValue() {
    return this.name();
  }
}
