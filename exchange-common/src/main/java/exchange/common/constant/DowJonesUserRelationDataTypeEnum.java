package exchange.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wen.y
 * @date: 2024/12/13
 */
@Getter
@AllArgsConstructor
public enum DowJonesUserRelationDataTypeEnum {
	USER("USER", "個人", DowJonesSamRecordTypeEnum.PERSON),
	CORPORATE("CORPORATE", "法人", DowJonesSamRecordTypeEnum.ENTITY),
	REPRESENTATIVE("REPRESENTATIVE", "代表者", DowJonesSamRecordTypeEnum.PERSON),
	AGENT("AGENT", "取引担当者", DowJonesSamRecordTypeEnum.PERSON),
	OWNER("OWNER", "UBO", DowJonesSamRecordTypeEnum.PERSON),
	;
	private final String type;
	private final String desc;
	private final DowJonesSamRecordTypeEnum dowJonesSamRecordType;

	private static final Map<String, DowJonesUserRelationDataTypeEnum> typeMap = Stream.of(values()).collect(Collectors.toMap(DowJonesUserRelationDataTypeEnum::getType, Function.identity()));

	public static DowJonesUserRelationDataTypeEnum ofType(String type) {
		return typeMap.get(type);
	}
}
