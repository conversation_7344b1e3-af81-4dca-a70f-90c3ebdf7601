package exchange.common.constant;

import lombok.Getter;

// 【運用】 エラーコード編集時はclientのServerErrorCodeとServerErrorMessageの編集も行う
// ServerErrorMessageも全件登録必須
// エラーコード一覧ドキュメントを更新する
// コード番号昇順に記載する(重複チェックする)

public enum ErrorCode {
  COMMON_ERROR_NOT_FOUND(10000),
  COMMON_ERROR_SYSTEM_ERROR(10001),

  COMMON_ERROR_LOCK(10002),

  COMMON_ERROR_CURRENCY_PAIR_IS_DISABLED(10003),
  COMMON_ERROR_INVALID_DATE_STRING(10004),
  COMMON_ERROR_IN_MAINTENANCE(10005),

  AUTHENTICATION_ERROR_API_KEY_NOT_FOUND(20001),
  AUTHENTICATION_ERROR_INVALID_API_KEY(20002),
  AUTHENTICATION_ERROR_NONCE_NOT_FOUND(20003),
  AUTHENTICATION_ERROR_INVALID_NONCE(20004),
  AUTHENTICATION_ERROR_SIGNATURE_NOT_FOUND(20005),
  AUTHENTICATION_ERROR_INVALID_SIGNATURE(20006),
  AUTHENTICATION_ERROR_INVALID_AUTHORITY(20007),
  AUTHENTICATION_ERROR_INVALID_USER(20008),
  AUTHENTICATION_ERROR_LOGIN_AUTH_CONSISTENCY(20009),
  AUTHENTICATION_ERROR_TOO_MANY_REQUESTS(20010),
  AUTHENTICATION_ERROR_ACCOUNT_LOCKED(20011),
  REQUEST_ERROR_AUTHORITY_MASTER_NOT_EDIT(20012),
  AUTHENTICATION_ERROR_ACCOUNT_STOPPED(20013),
  SEARCH_OVER_FIFTY_THOUSAND(20014),
  DOWNLOAD_OVER_FIFTY_THOUSAND(20015),

  REQUEST_ERROR_OLD_PASSWORD(30001),
  REQUEST_ERROR_API_INFO_IS_NULL(30002),
  REQUEST_ERROR_BOLLINGER_BAND_CONFIG_IS_NULL(30003),
  REQUEST_ERROR_BUDGET_IS_NULL(30004),
  REQUEST_ERROR_RSI_CONFIG_IS_NULL(30005),
  REQUEST_ERROR_ARBITRAGER_CONFIG_IS_NULL(30006),
  REQUEST_ERROR_AUTO_CYCLE_ORDER_CONFIG_IS_NULL(30007),
  REQUEST_ERROR_INVALID_CURRENCY_PAIR(30008),
  REQUEST_ERROR_INVALID_ID(30009),
  REQUEST_ERROR_INVALID_USER(30010),
  REQUEST_ERROR_EVENT_GUEST_IS_NULL(30011),
  REQUEST_ERROR_EVENT_IS_NULL(30012),
  REQUEST_ERROR_EVENT_GUEST_IS_NOT_NULL(30013),
  REQUEST_ERROR_GUEST_IS_NULL(30014),
  REQUEST_ERROR_EMAIL_IS_REGISTERED(30015),
  REQUEST_ERROR_UNAUTHORIZED(30016),
  REQUEST_ERROR_FORBIDDEN(30017),
  REQUEST_ERROR_USER_SMS_EXIST(30018),
  REQUEST_ERROR_USER_SMS_IS_NULL(30019),
  REQUEST_ERROR_INVALID_USER_SMS_CODE(30020),
  REQUEST_ERROR_INVALID_TRADE_TYPE(30021),
  REQUEST_ERROR_INVALID_ORDER_TYPE(30022),
  REQUEST_ERROR_UNAUTHORIZED_TWO_FACTOR(30023),
  REQUEST_ERROR_USER_INFO_NOT_FOUND(30024),
  REQUEST_ERROR_USER_PHISHING_AUTH_CODE_NOT_FOUND(30025),
  REQUEST_ERROR_USER_PHISHING_CODE_NOT_FOUND(30026),
  REQUEST_ERROR_USER_PHISHING_AUTH_CODE_EXPIRED(30027),
  REQUEST_ERROR_USER_PHISHING_OLD_CODE(30028),
  REQUEST_ERROR_USER_PHISHING_CODE_INVALID(30029),
  REQUEST_ERROR_USER_PASSWORD_CODE_INVALID(30030),
  REQUEST_ERROR_USER_PASSWORD_CODE_EXPIRED(30031),
  REQUEST_ERROR_USER_EMAIL_CODE_INVALID(30032),
  REQUEST_ERROR_USER_EMAIL_CODE_EXPIRED(30033),
  REQUEST_ERROR_USER_SMS_CODE_INVALID(30034),
  REQUEST_ERROR_USER_SMS_CODE_EXPIRED(30035),
  REQUEST_ERROR_USER_OLD_EMAIL(30036),
  REQUEST_ERROR_USER_NEW_EMAIL_CONFIRM(30037),
  REQUEST_ERROR_USER_INFO_REGISTERED(30038),
  REQUEST_ERROR_USER_REGISTER(30039),
  REQUEST_ERROR_USER_REGISTER_CODE_INVALID(30040),
  REQUEST_ERROR_FILE_UPLOAD(30042),
  REQUEST_ERROR_USER_GOOGLE_AUTH_CODE_INVALID(30043),
  REQUEST_ERROR_USER_EMAIL_AUTH_CODE_INVALID(30044),
  REQUEST_ERROR_USER_EMAIL_AUTH_CODE_EXPIRED(30045),
  REQUEST_ERROR_NEWS_NOT_FOUND(30046),
  REQUEST_ERROR_USER_EDIT(30047),
  REQUEST_ERROR_USER_EDIT_CORPORATE(30048),
  REQUEST_ERROR_USER_EDIT_CORPORATE_AGENT(30049),
  REQUEST_ERROR_USER_EDIT_CORPORATE_OWNER(30050),
  REQUEST_ERROR_USER_EDIT_CORPORATE_REPRESENTATIVE(30051),
  REQUEST_ERROR_WITHDRAWAL_ACCOUNT_NOT_FOUND(30052),
  REQUEST_ERROR_TRADINGVIEW_TEMPLATE_IS_NULL(30053),
  REQUEST_ERROR_UNAUTHORIZED_ACCOUNT_LOCKED(30054),
  REQUEST_ERROR_INVALID_AMOUNT(30055),
  REQUEST_ERROR_GET_ASSET_API_IS_NOT_AVAILABLE(30056),
  REQUEST_ERROR_GET_ORDER_API_IS_NOT_AVAILABLE(30057),
  REQUEST_ERROR_POST_ORDER_API_IS_NOT_AVAILABLE(30058),
  REQUEST_ERROR_DELETE_ORDER_API_IS_NOT_AVAILABLE(30059),
  REQUEST_ERROR_GET_TRADE_API_IS_NOT_AVAILABLE(30060),
  REQUEST_ERROR_LOGIN_INFO_IS_NULL(30061),
  REQUEST_ERROR_ACCESS_TOKEN_IS_NULL(30062),
  REQUEST_ERROR_DEPOSIT_ACCOUNT_NOT_FOUND(30063),
  REQUEST_ERROR_INVALID_CURRENCY(30064),
  REQUEST_ERROR_ORDER_IS_CONTROLLED(30065),
  REQUEST_ERROR_ASSET_SUMMARY_NOT_EXIST(30067),
  REQUEST_ERROR_ASSET_NOT_ENOUGH(30068),
  REQUEST_ERROR_AMOUNT_NOT_POSITIVE(30069),
  REQUEST_ERROR_SAME_ONE_EXIST(30070),
  REQUEST_ERROR_INVALID_MFA_CODE(30071),
  REQUEST_ERROR_MFA_CODE_IS_EXPIRED(30072),
  REQUEST_ERROR_NOTSETUP_PHONE_NUMBER(30075),
  REQUEST_ERROR_INVALID_KYC_STATUS_TRANSITION(30076),
  REQUEST_ERROR_SPECIFY_DIFFERENT_EMAIL(30077),
  REQUEST_ERROR_SPECIFY_DIFFERENT_PASSWORD(30078),
  REQUEST_ERROR_SEND_REPEATED_CONTACT(30079),
  REQUEST_ERROR_INVALID_NAME(30080),
  REQUEST_ERROR_INVALID_EMAIL(30081),
  REQUEST_ERROR_INVALID_SUBJECT(30082),
  REQUEST_ERROR_INVALID_CONTANT(30083),
  REQUEST_ERROR_USER_INFO_CORPORATE_NOT_FOUND(30084),
  REQUEST_ERROR_USER_KYC_NOT_FOUND(30085),
  REQUEST_ERROR_PASSWORD_NOT_FOUND(30086),
  REQUEST_ERROR_INVALID_COUNTRY_NAME(30087),
  REQUEST_ERROR_VALIDATE_USER_ANTI_PHISHING(30088),
  REQUEST_ERROR_SPECIFY_DIFFERENT_ANTI_PHISHING(30089),
  REQUEST_ERROR_ANTI_PHISHING_NOT_FOUND(30090),
  REQUEST_ERROR_INVALID_KYC_UPLOAD_FILE(30091),
  REQUEST_ERROR_CODE_SENDING_FREQUENCY_LIMIT(30092),
  REQUEST_ERROR_ADMIN_USER_REGISTER_AUTHORITY_NOT_FOUND(30093),
  REQUEST_ERROR_MAIL_SEND(30094),
  REQUEST_ERROR_USER_EXIST(30096),
  REQUEST_ERROR_USER_MFA_EXIST(30097),
  REQUEST_ERROR_USER_MFA_NOT_FOUND(30098),
  REQUEST_ERROR_INVALID_RECAPTCHA_TOKEN(30099),
  REQUEST_ERROR_INVALID_API_INFO(30100),
  REQUEST_ERROR_EXCEED_MAXIMUM_UPLOAD_SIZE(30101),
  REQUEST_ERROR_USER_MFA_UPPER_LIMIT(30102),
  REQUEST_ERROR_RESET_PASSWORD_IS_WAITING_SET_PWD(30200),
  REQUEST_ERROR_PAST_ASSET_SUMMARY_NOT_EXIST(30201),
  REQUEST_ERROR_INVALID_CRYPTO_TOKEN(30202),
  REQUEST_ERROR_CRYPTO_TOKEN_NOT_FOUND(30203),
  REQUEST_ERROR_REQ_QUICK_FAIL(30204),
  REQUEST_ERROR_CRYPTO_TOKEN_APPLY_TERMINATED(30205),
  REQUEST_ERROR_CRYPTO_TOKEN_CANNOT_REPEAT_APPLY(30206),
  SQS_ERROR_MESSAGE_BODY_DATA_NOT_FOUND(40001),
  SQS_ERROR_INVALID_MESSAGE_BODY_DATA(40011),
  ORDER_ERROR_INVALID_ORDER_STATUS(50001),
  ORDER_ERROR_HALT(50002),
  ORDER_ERROR_ORDERBOOK_NOT_FOUND(50003),
  ORDER_ERROR_AMOUNT_OUT_OF_RANGE(50004),
  ORDER_ERROR_PRICE_NOT_FOUND(50005),
  ORDER_ERROR_OPPOSITE_ORDER_NOT_FOUND(50006),
  ORDER_ERROR_UNAVAILABLE_MARKET_ORDER(50007),
  ORDER_ERROR_PRICE_OUT_OF_RANGE(50008),
  ORDER_ERROR_ORDER_NOT_FOUND(50009),
  ORDER_ERROR_INVALID_BROKER(50010),
  ORDER_ERROR_ORDER_IS_INACTIVE(50011),
  ORDER_ERROR_INVALID_USER(50012),
  ORDER_ERROR_POSITION_IS_CLOSED(50013),
  ORDER_ERROR_INVALID_TRADE_TYPE(50014),
  ORDER_ERROR_USER_IS_NOT_ACTIVE(50015),
  REQUEST_ERROR_USER_NOT_FOUND(50016),
  ORDER_ERROR_INVALID_SYMBOLID(50017),
  ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND(50018),
  ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND(50019),
  ORDER_ERROR_AMOUNT_OUT_OF_MINMAX(50020),
  ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY(50021),
  ORDER_ERROR_SPOT_ORDER_NOT_FOUND(50022),
  ORDER_ERROR_INVALID_AMOUNT(50023),
  ORDER_ERROR_AMOUNT_EXCEED_ASSET(50024),
  ORDER_ERROR_INVALID_FEE(50025),
  ORDER_ERROR_INVALID_PRICE(50026),
  ORDER_ERROR_INVALID_ASSET(50027),
  ORDER_ERROR_INVALID_TARGET_ORDER_ID(50028),
  CLIENT_ASSERTION_ERROR_CREATE_FAILED(50029),
  ORDER_ERROR_ASSET_NOT_FOUND(50030),
  ORDER_ERROR_CIRCUIT_BREAKER(50031),
  ORDER_ERROR_INVALID_SPREAD(50032),
  ORDER_ERROR_SECURITY_LEVEL(50033),
  REQUEST_ERROR_INVALID_USER_STATUS(50034),
  ORDER_ERROR_ACTIVE_AMOUNT_OVER_MAX(50326),
  REQUEST_ERROR_COPY_ORDER_CONFIG_NOT_FOUND(51000),
  DEALING_ERROR_ORDERBOOK_NOT_FOUND(51001),
  DEALING_ERROR_INVALID_COPY_SIZE(51002),
  DEALING_ERROR_COPY_ORDER_FAILED(51003),
  REQUEST_ERROR_MARKETMAKER_USER_NOT_FOUND(51004),
  DEALING_ERROR_COVER_ORDER_FAILED(51005),
  REQUEST_ERROR_COVER_ORDER_CONFIG_NOT_FOUND(51006),
  DEALING_ERROR_OKCOIN_ORDER_CONFIG_NOT_FOUND(51007),
  DEALING_ERROR_COVER_ORDER_UPDATE_FAILED(51008),
  DEALING_ERROR_COVER_ORDER_NOT_FOUND(51009),
  DEALING_ERROR_INVALID_PRICE(51010),
  DEALING_ERROR_INVALID_AMOUNT(51011),
  DEALING_ERROR_COVER_FILLED_ORDER_NOT_FOUND(51012),
  DEALING_ERROR_COVER_ORDER_TOO_OLD(51013),
  DEALING_ERROR_NO_COPY_ORDER(51014),
  DEALING_ERROR_LESS_THAN_MIN_COVER_SIZE(51015),
  DEALING_ERROR_BEST_PRICE_CHECK(51016),
  DEALING_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND(51017),
  DEALING_ERROR_CURRENCY_PAIR_NOT_FOUND(51018),
  ORDER_ERROR_ORDER_IS_INACTIVE_POS_NIDT(51019),
  ORDER_ERROR_ORDER_IS_INACTIVE_POS(51020),

  REQUEST_ERROR_MAIL_NOT_FOUND(60001),
  REQUEST_ERROR_MAIL_TEMPLATE_NOT_FOUND(60002),
  REQUEST_ERROR_MAIL_SENT_ALREADY(60003),
  REQUEST_ERROR_MAIL_NOREPLY_NOT_FOUND(60004),
  SYSTEM_ERROR_REDIS_CONNECTION_FACTORY_IS_NULL(60005),
  SYSTEM_ERROR_ENTITY_MANAGER_FACTORY_IS_NULL(60006),
  SYSTEM_ERROR_ORDERBOOK_IS_NULL(60007),
  SYSTEM_ERROR_ORDERBOOK_ASK_IS_EMPTY(60008),
  SYSTEM_ERROR_ORDERBOOK_BID_IS_EMPTY(60009),

  REQUEST_ERROR_USER_INFO_FIRST_NAME(70001),
  REQUEST_ERROR_USER_INFO_FIRST_KANA(70002),
  REQUEST_ERROR_USER_INFO_LAST_NAME(70003),
  REQUEST_ERROR_USER_INFO_LAST_KANA(70004),
  REQUEST_ERROR_USER_INFO_ZIP_CODE(70005),
  REQUEST_ERROR_USER_INFO_PREFECTURE(70006),
  REQUEST_ERROR_USER_INFO_CITY(70007),
  REQUEST_ERROR_USER_INFO_ADDRESS(70008),
  REQUEST_ERROR_USER_INFO_BUILDING(70009),
  REQUEST_ERROR_USER_INFO_OCCUPATION(70011),
  REQUEST_ERROR_USER_INFO_INDUSTRY(70124),
  REQUEST_ERROR_USER_INFO_WORKPLACE(70012),
  REQUEST_ERROR_USER_INFO_POSITION(70013),
  REQUEST_ERROR_USER_INFO_INCOME(70014),
  REQUEST_ERROR_USER_INFO_FINANCIAL_ASSETS(70015),
  REQUEST_ERROR_USER_INFO_PURPOSE(70016),
  REQUEST_ERROR_USER_INFO_INVESTMENT_PURPOSE(70018),
  REQUEST_ERROR_USER_INFO_CRYPTO_EXPERIENCE(70019),
  REQUEST_ERROR_USER_INFO_STOCKS_EXPERIENCE(70020),
  REQUEST_ERROR_USER_INFO_FUND_EXPERIENCE(70021),
  REQUEST_ERROR_USER_INFO_APPLICATION_HISTORY(70022),
  REQUEST_ERROR_USER_INFO_FOREIGN_PEPS(70023),
  REQUEST_ERROR_USER_INFO_NATIONALITY(70024),

  REQUEST_ERROR_USER_INFO_CORPORATE_NAME(70025),
  REQUEST_ERROR_USER_INFO_CORPORATE_NAME_KANA(70026),
  REQUEST_ERROR_USER_INFO_CORPORATE_ESTABLISHED_YEAR(70027),
  REQUEST_ERROR_USER_INFO_CORPORATE_ESTABLISHED_MONTH(70028),
  REQUEST_ERROR_USER_INFO_CORPORATE_ESTABLISHED_DAY(70029),
  REQUEST_ERROR_USER_INFO_CORPORATE_ZIP_CODE(70030),
  REQUEST_ERROR_USER_INFO_CORPORATE_CITY(70031),
  REQUEST_ERROR_USER_INFO_CORPORATE_ADDRESS(70032),
  REQUEST_ERROR_USER_INFO_CORPORATE_BUILDING(70033),
  REQUEST_ERROR_USER_INFO_CORPORATE_BIRTHDAY(70034),
  REQUEST_ERROR_USER_INFO_CORPORATE_PHONE_NUMBER(70035),
  REQUEST_ERROR_USER_INFO_CORPORATE_BUSINESS_CONTENT(70038),
  REQUEST_ERROR_USER_INFO_CORPORATE_SALES(70039),
  REQUEST_ERROR_USER_INFO_CORPORATE_FINANCIAL_ASSETS(70040),
  REQUEST_ERROR_USER_INFO_CORPORATE_PURPOSE(70041),
  REQUEST_ERROR_USER_INFO_CORPORATE_STOCKS_EXPERIENCE(70042),
  REQUEST_ERROR_USER_INFO_CORPORATE_FUND_EXPERIENCE(70043),
  REQUEST_ERROR_USER_INFO_CORPORATE_APPLICATION_HISTORY(70044),
  REQUEST_ERROR_USER_INFO_CORPORATE_FOREIGN_PEPS(70045),
  REQUEST_ERROR_USER_INFO_CORPORATE_NATIONALITY(70046),

  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_FIRST_NAME(70047),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_FIRST_KANA(70048),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_LAST_NAME(70049),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_LAST_KANA(70050),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_POSITION(70051),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_NATIONALITY(70052),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_ZIP_CODE(70053),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_PREFECTURE(70054),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_CITY(70055),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_ADDRESS(70056),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_BUILDING(70057),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_BIRTHDAY(70058),

  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_FIRST_NAME(70059),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_FIRST_KANA(70060),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_LAST_NAME(70061),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_LAST_KANA(70062),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_POSITION(70063),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_NATIONALITY(70064),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_ZIP_CODE(70065),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_PREFECTURE(70066),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_CITY(70067),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_ADDRESS(70068),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_BUILDING(70069),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_FOREIGN_PEPS(70070),

  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_FIRST_KANA(70071),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_LAST_NAME(70072),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_LAST_KANA(70073),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_POSITION(70074),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_NATIONALITY(70075),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_ZIP_CODE(70076),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_PREFECTURE(70077),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_CITY(70078),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_ADDRESS(70079),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_BUILDING(70080),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_FOREIGN_PEPS(70081),

  REQUEST_ERROR_USER_INFO_BIRTHDAY(70082),
  REQUEST_ERROR_USER_INFO_GENDER(70083),
  REQUEST_ERROR_MULTIPART_FILE_IS_NULL(70084),
  REQUEST_ERROR_UNKNOWN_EXTENTION(70085),
  REQUEST_ERROR_USER_IS_NULL(70086),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_GENDER(70087),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_FIRST_NAME(70088),
  REQUEST_ERROR_USER_INFO_CORPORATE_PREFECTURE(70089),
  REQUEST_ERROR_USER_INFO_CORPORATE_CRYPTO_EXPERIENCE(70090),
  REQUEST_ERROR_USER_INFO_INCORRECT_AUTHORITY(70091),
  REQUEST_ERROR_USER_INFO_CORPORATE_INCORRECT_AUTHORITY(70092),
  REQUEST_ERROR_USER_INFO_CORPORATE_OFFICE_PHONE_NUMBER(70093),
  REQUEST_ERROR_SYMBOL_IS_NULL(70094),
  REQUEST_ERROR_USER_INFO_CHANGE_TWO_FACTOR(70095),
  REQUEST_ERROR_USER_CHANGE_TWO_FACTOR(70096),
  REQUEST_ERROR_VALIDATE_USER_EMAIL(70097),
  REQUEST_ERROR_USER_CHANGE_EXPIRED(70098),
  REQUEST_ERROR_INVALID_USER_PASSWORD(70099),
  REQUEST_ERROR_BANK_ACCOUNT_NOT_FOUND(70100),
  REQUEST_ERROR_AMOUNT_OUT_OF_RANGE(70101),
  REQUEST_ERROR_INVALID_PHONE_NUMBER(70102),
  REQUEST_ERROR_INVALID_PERSONAL_PHONE_NUMBER(70126),
  REQUEST_ERROR_INVALID_MFA_TYPE(70103),
  REQUEST_ERROR_USER_INFO_APPLICATION_HISTORY_OTHER(70104),
  REQUEST_ERROR_USER_INFO_CORPORATE_APPLICATION_HISTORY_OTHER(70105),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_BIRTHDAY(70106),
  REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_BIRTHDAY(70107),
  REQUEST_ERROR_BANK_ACCOUNT_NUMBER(70108),
  REQUEST_ERROR_BANK_ACCOUNT_NAME_IS_NULL(70130),
  REQUEST_ERROR_BANK_ACCOUNT_NAME_KANA(70131),
  REQUEST_ERROR_BANK_ACCOUNT_IS_ALREADY_REGISTERD(70109),
  REQUEST_ERROR_USER_INFO_IS_NULL(70110),
  REQUEST_ERROR_COUNTRY_CODE_IS_NULL(70111),
  REQUEST_ERROR_USER_IS_NOT_TRADABLE(70112),
  REQUEST_ERROR_USER_IS_NOT_WITHDRAWABLE(70113),
  REQUEST_ERROR_AMOUNT_OUT_OF_RANGE_USER_TODAY(70125),
  REQUEST_ERROR_IS_NOT_WITHDRAWABLE(70127),

  REQUEST_ERROR_PASSWORD_PATTERN(70114),
  REQUEST_ERROR_NEW_PASSWORD_MISMATCH(70115),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_COUNTRY(70116),
  REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_PHONE_NUMBER(70117),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_COUNTRY(70118),
  REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_PHONE_NUMBER(70119),
  REQUEST_ERROR_USER_INFO_COUNTRY(70120),

  REQUEST_ERROR_USER_INFO_CORPORATE_ACCOUNTING_MONTH(70121),

  REQUEST_ERROR_JWT_TOKEN_INVALIDATE(70122),

  REQUEST_ERROR_USER_INFO_ADDRESS_NOT_VALIDATE(70123),
  REQUEST_ERROR_ADMIN_WITHDRAWAL_CANNOT_APPROVED_SAME_USER(80001),
  REQUEST_ERROR_ADMIN_FIAT_WITHDRAWAL_CANNOT_APPROVED_SAME_USER(80002),

  ONE_TIME_BANK_ACCOUNT_EXISTS(80003),
  REQUEST_ERROR_INVALID_TOKEN(80004),
  REQUEST_ERROR_EXPIRE_TOKEN(80005),
  REQUEST_ERROR_ZIPCODE(85000),
  REQUEST_ERROR_ZIPCODE_NOTNULL(85001),
  REGISTER_TOKEN_NOT_EXPIRE(85002),
  REQUEST_ERROR_EKYC_REQUEST_ERROR(85003),
  REQUEST_ERROR_NOTICES_ID_NOT_FOUND(85004),
  REQUEST_ERROR_EKYC_ALREADY_COMPLETED(85005),
  REQUEST_ERROR_EKYC_APPLY_FREQUENT(85006),
  REQUEST_ERROR_EKYC_REQUEST_TIMEOUT(85007),
  REQUEST_ERROR_EKYC_URL_ISSUE_FAILED(85008),
  REQUEST_ERROR_EKYC_INVALID_STATUS(85009),
  REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS(85010),
  REQUEST_ERROR_BANK_NOT_EXISTS(85011),
  REQUEST_ERROR_OVER_SALE_AMOUNT(85012),
  REQUEST_ERROR_EXCLUSION_LOCK(85013),
  REQUEST_ERROR_INVALID_TMS_STATUS(85014),
  REQUEST_ERROR_IEO_RECRUIT_INFO_NOT_FOUND(90001),
  REQUEST_ERROR_IEO_RECRUIT_STATUS_ERROR(90002),
  REQUEST_ERROR_IEO_AMOUNT_OUT_OF_RANGE(90003),
  REQUEST_ERROR_IEO_JPY_AMOUNT_OUT_OF_RANGE(90004),
  REQUEST_ERROR_STAKING_INFO_NOT_FOUND(90005),
  REQUEST_ERROR_STAKING_AMOUNT_MORE_THAN_MAX(90006),
  REQUEST_ERROR_STAKING_AMOUNT_OUT_OF_RANGE(90007),
  REQUEST_ERROR_STAKING_ASSET_OUT_OF_RANGE(90008),
  REQUEST_ERROR_STAKING_DATE_OUT_OF_RANGE(90009),
  REQUEST_ERROR_STAKING_STATUS_ERROR(90010),
  REQUEST_ERROR_ADDRESS_IS_OWNERADDRESS(90011),
  REQUEST_ERROR_STAKING_OUT_OF_DEAL_TIME(90012),
  REQUEST_ERROR_OTC_DEPOSIT_ACCOUNT_NOT_FOUND(90013),

  REQUEST_ERROR_MASTER_USER_ID_NOT_FOUND(12000),
  REQUEST_ERROR_USER_ID_NOT_FOUND(12001),
  REQUEST_ERROR_AMOUNT_NOT_FOUND(12002),
  REQUEST_ERROR_CURRENCY_NOT_FOUND(12003),
  REQUEST_ERROR_MASTER_USER_NOT_FOUND(12004),
  REQUEST_ERROR_DEPOSIT_ACCOUNT_USER_NOT_FOUND(12005),
  REQUEST_ERROR_YEARLY_REPORT_DOWNLOAD_FAILED(12006),
  REQUEST_ERROR_YEARLY_REPORT_CREATE_FAILED(12007),
  REQUEST_ERROR_ADDRESS_WRITE(12008),
  POS_ORDER_ERROR_PRICE_NOT_FOUND(12009),
  LOCK_KEY(12010);

  public static ErrorCode valueOfCode(int code) {
    for (ErrorCode errorCode : values()) {
      if (errorCode.code == code) {
        return errorCode;
      }
    }

    return null;
  }

  @Getter private final int code;

  @Getter private final String keyword;

  ErrorCode(int code, String keyword) {
    this.code = code;
    this.keyword = keyword;
  }

  ErrorCode(int code) {
    this(code, null);
  }
}
