package exchange.common.constant;

import com.baomidou.mybatisplus.annotation.IEnum;

public enum KycType implements IEnum<String> {
  NONE, // 本人確認未申請
  UPLOAD; // ファイルアップロードを使用した従来型Kyc

  public static KycType valueOfName(String name) {
    for (KycType kycType : values()) {
      if (kycType.name().equals(name)) {
        return kycType;
      }
    }
    return null;
  }

  @Override
  public String getValue() {
    return this.name();
  }
}
