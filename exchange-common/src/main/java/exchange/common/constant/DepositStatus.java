package exchange.common.constant;

public enum DepositStatus {
      CONFIRMED, TRANSACTION_ERROR, SYSTEM_ERROR,UNKNOWN_DEPOSIT,
	  // 送付人情報取得待ち
	  FOUND,
	  // 送付人情報取得済
	  SENDER_INFO_RECIVED,
	  // AML情報取得済
	  AML_INFO_RECIVED,
	  // AML1次審査中
	  FIRST_AML_EXAMING,
	  // AML2次審査待ち
	  WAITING_SECOND_AML_EXAM,
	  // AML2次審査中
	  SECOND_AML_EXAMING,
	  // 追加資料依頼
	  WAITING_ADD_INFO,
	  // 追加資料依頼対応中
	  ADD_INFO_CHECKING,
	  // 取扱不可
	  INABILITY,
      // 承認
      APPROVED,
	  // 入金完了
	  DONE,
	  // 却下(未返金)
	  REJECTED,
	  // 却下(返金済)
	  REJECTED_FUNDED,
      // 着金通知差戻
	  SYGNA_TRANSFER_REJECTED,
	  // メール通知済
	  MAIL_SENDED,
      // 通知情報不一致
      SYGNA_INFO_NOT_MATCHED,
      // Sygna txhash待ち
      SYGNA_TXHASH_WAITTING;
}
