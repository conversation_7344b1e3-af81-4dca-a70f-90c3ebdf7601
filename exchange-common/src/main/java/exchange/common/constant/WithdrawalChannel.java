package exchange.common.constant;

public enum WithdrawalChannel {
  BANK,
  WALLET,
  SALES,
  SYSTEM_OPERATION,
  UNKNOWN;

  public static WithdrawalChannel valueOfName(String name) {
    for (WithdrawalChannel withdrawalChannel : values()) {
      if (withdrawalChannel.name().equals(name)) {
        return withdrawalChannel;
      }
    }
    return null;
  }

  public String getName() {
    return name();
  }
}
