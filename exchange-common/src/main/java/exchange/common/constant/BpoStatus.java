package exchange.common.constant;

public enum BpoStatus {
    applying, // 申請中(※)、
    accepted, // 確認待ち、
    processing, // 確認中、
    completed; // 確認完了

    public static BpoStatus from(String status) {
        if (status != null) {
            if (status.equalsIgnoreCase(BpoStatus.applying.name())) {
                return BpoStatus.applying;
            }
            if (status.equalsIgnoreCase(BpoStatus.accepted.name())) {
                return BpoStatus.accepted;
            }
            if (status.equalsIgnoreCase(BpoStatus.processing.name())) {
                return BpoStatus.processing;
            }
            if (status.equalsIgnoreCase(BpoStatus.completed.name())) {
                return BpoStatus.completed;
            }
        }
        return null;
    }
}
