package exchange.common.constant;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wen.y
 * @date: 2024/12/18
 */
@Getter
@AllArgsConstructor
public enum DowJonesSearchTypeEnum implements IEnum<String> {
	BROAD("Broad"),
	NEAR("Near"),
	PRECISE("Precise"),
	;
	private final String type;

	private static final Map<String, DowJonesSearchTypeEnum> typeMap = Stream.of(values()).collect(Collectors.toMap(DowJonesSearchTypeEnum::getType, Function.identity()));

	public static DowJonesSearchTypeEnum ofType(String type) {
		return typeMap.get(type);
	}

	@Override
	public String getValue() {
		return this.getType();
	}
}
