package exchange.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Getter
@AllArgsConstructor
public enum UserTypeEnum {
	PERSONAL(1, "個人"),
	CORPORATE(2, "法人"),
	;
	private final Integer type;
	private final String desc;

	private static final Map<Integer, UserTypeEnum> typeMap = Stream.of(values()).collect(Collectors.toMap(UserTypeEnum::getType, Function.identity()));

	public static UserTypeEnum ofType(Integer type) {
		return typeMap.get(type);
	}
}
