package exchange.common.constant;

public enum TradeType {
  SPOT,
  POS,
  CFD;

  public static TradeType valueOfName(String name) {
    for (TradeType tradeType : values()) {
      if (tradeType.name().equals(name)) {
        return tradeType;
      }
    }

    return null;
  }

  public static TradeType valueOfLowerCase(String nameOfLowerCase) {
    for (TradeType tradeType : values()) {
      if (tradeType.toLowerCase().equals(nameOfLowerCase)) {
        return tradeType;
      }
    }

    return null;
  }

  public String getName() {
    return name();
  }

  public String toLowerCase() {
    return name().toLowerCase();
  }

  public String toConnectedLowerCase() {
    return name().toLowerCase().replaceAll("_", "");
  }

  public String toCamelCase() {
    StringBuilder stringBuilder = new StringBuilder();

    for (String part : name().split("_")) {
      stringBuilder.append(part.charAt(0) + part.toLowerCase().substring(1));
    }

    return stringBuilder.toString();
  }

  public String toLowerCamelCase() {
    StringBuilder stringBuilder = new StringBuilder();

    for (String part : name().split("_")) {
      stringBuilder.append(stringBuilder.length() == 0 ? part.toLowerCase() : part.charAt(0) + part.toLowerCase().substring(1));
    }

    return stringBuilder.toString();
  }

  public boolean isSpot() {
    return this == SPOT;
  }

  public boolean isCfd() {
    return this == CFD;
  }
}
