package exchange.common.constant;

public enum MailNoreplyType {
  ACCOUNT_CREATED,
  PASSWORD_COMPLETE,
  USER_EKYC_STARTED,
  USER_KYC_STATUS_CHANGED_EKYC_SUCCESS,
  EKYC_BPO_FAILED_ERROR,
  INFORMATION_REQUIRED,   // 不備あり（却下）
  ACCOUNT_OPENING_DONE,
  DONE,                   // 承認
  REJECTED,               // 謝絶
  ACCOUNT_CLOSED,         // 口座解約
  ACCOUNT_DUPLICATION,    // 口座重複
  CORPORATE_PERSONAL_ACCOUNT_OPENING_DONE,  // 法人口座開設完了
  DEPOSIT_DONE,
  FIAT_WITHDRAWAL_APPLY,
  FIAT_WITHDRAWAL_DONE,
  IEO_APPLY_APPROVED,
  IEO_APPLY_CANCELED,
  IEO_ELECT_APPROVED,
  WIT<PERSON><PERSON><PERSON>L_DONE,
  WITHD<PERSON>WAL_REJECTED,
  WIT<PERSON>RAWAL_FAILED,
  DEPOSIT_DETECTED,
  DEPOSIT_R<PERSON>OGNITION_CODE,

  MFA_CODE,
  SMS_CODE,

  ORDER,
  DELETE_ORDER,
  TRADE,
  EXPIRED_ORDER,

  STAKING_APPLY_CONFIRM,
  STAKING_CANCEL_APPLY,
  STAKING_EXPIRATION_FUNDED,
  STAKING_CANCEL_FUNDED,
  STAKING_EXPIRATED,
  STAKING_AUTOCONTINUE,

  POS_ORDER,
  POS_TRADE,
  POS_FAILED_ORDER,
  TRANSFER_DONE,
  TRANSFER_RECEIVED_DONE,

  BALANCE_NOTIFY,

  CRYPTO_TOKEN_APPLY_CREATE,

  CRYPTO_TOKEN_APPLY_REPORT,
}
