package exchange.common.constant;

public enum WithdrawalPurpose {
  DEPOSIT, // 預入金
  COMMISSION_ADJUSTMENT, // 手数料調整金
  PAYMENT, // 出金
  CAMPAIGN, // キャンペーン
  RETURN_WITHDRAWAL, // 出金返金
  REVERSE_DEPOSIT, // 入金組戻
  ABANDON_BALANCE, // 残高放棄
  CORRECT_EXECUTION, // 約定訂正
  CORRECT_COMMISSION, // 手数料訂正
  ELECTRONIC_MONEY_WITHDRAW, // 電マネ出金
  ACCOUNT_REBALANCE, // 運用口座調整金
  SYSTEM, // システム対応
  OTHER, // その他
  OTHER1, // その他1
  OTHER2, // その他2
  OTHER3, // その他3
}
