package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Currency;
import exchange.common.entity.StakingInfo;
import exchange.common.entity.StakingInfo_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.StakingInfoPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
@Service
@Slf4j
public class StakingInfoService extends EntityService<StakingInfo, StakingInfoPredicate> {

  private final UserService userService;

  @Override
  public Class<StakingInfo> getEntityClass() {
    return StakingInfo.class;
  }

  public PageData<StakingInfo> findByConditionPageData(Long id, Currency currency, String period,
      Long totalAmountFrom, Long totalAmountTo, Long minApplyAmountFrom,
      Long minApplyAmountTo, Boolean enabled, Long applyDateFrom, Long applyDateTo,
      Long createdAtFrom, Long createdAtTo, Long updatedAtFrom, Long updatedAtTo, Integer number, Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(entityManager, criteriaBuilder, criteriaQuery, root,
                    getPredicatesOfFindByCondition(criteriaBuilder, root, id, currency, period,
                        totalAmountFrom, totalAmountTo, minApplyAmountFrom, minApplyAmountTo, enabled, applyDateFrom, applyDateTo,
                        createdAtFrom, createdAtTo, updatedAtFrom, updatedAtTo, null, null));
              }
            });

    return new PageData<StakingInfo>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<StakingInfo, List<StakingInfo>>() {
              @Override
              public List<StakingInfo> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(criteriaBuilder, root, id, currency, period,
                        totalAmountFrom, totalAmountTo, minApplyAmountFrom, minApplyAmountTo, enabled, applyDateFrom, applyDateTo,
                        createdAtFrom, createdAtTo, updatedAtFrom, updatedAtTo, null, null);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(StakingInfo_.id)));
              }
            }));
  }
  
  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<StakingInfo> root, Long id, Currency currency, String period, Long totalAmountFrom,
      Long totalAmountTo, Long minApplyAmountFrom, Long minApplyAmountTo, Boolean enabled,
      Long applyDateFrom, Long applyDateTo, Long createdAtFrom, Long createdAtTo,
      Long updatedAtFrom, Long updatedAtTo, Long yearRateFrom, Long yearRateTo) {
    
    List<Predicate> predicates = new ArrayList<>();

    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    }

    if (currency != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
    }
    
    if (!StringUtils.isEmpty(period)) {
      predicates.add(predicate.equalPeriod(criteriaBuilder, root, period));
    }
    
    if (totalAmountFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToTotalAmount(criteriaBuilder, root, totalAmountFrom));
    }
    
    if (totalAmountTo != null) {
      predicates.add(predicate.lessThanOrEqualToTotalAmount(criteriaBuilder, root, totalAmountTo));
    }
    
    if (enabled != null) {
      predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
    }
    
    if (minApplyAmountFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToMinApplyAmount(criteriaBuilder, root, minApplyAmountFrom));
    }

    if (minApplyAmountTo != null) {
      predicates.add(predicate.lessThanOrEqualToMinApplyAmount(criteriaBuilder, root, minApplyAmountTo));
    }

    if (applyDateFrom != null) {
      predicates
          .add(predicate.greaterThanOrEqualToApplyDateFrom(criteriaBuilder, root, new Date(applyDateFrom)));
    }

    if (applyDateTo != null) {
      predicates.add(predicate.lessThanApplyDateFrom(criteriaBuilder, root, new Date(applyDateTo)));
    }
    
    if (createdAtFrom != null) {
      predicates
          .add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(createdAtFrom)));
    }

    if (createdAtTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
    }
    
    if (updatedAtFrom != null) {
      predicates
          .add(predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, new Date(updatedAtFrom)));
    }

    if (updatedAtTo != null) {
      predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updatedAtTo)));
    }
    
    if (yearRateFrom != null) {
      predicates
          .add(predicate.greaterThanOrEqualToCBYearRateFrom(criteriaBuilder, root, yearRateFrom));
    }

    if (yearRateTo != null) {
      predicates.add(predicate.lessThanOrEqualToCBYearRateTo(criteriaBuilder, root, yearRateTo));
    }


    return predicates;
  }

  public List<StakingInfo> findByCondition(Long id, Currency currency, String period,
      Long totalAmountFrom, Long totalAmountTo, Long minApplyAmountFrom,
      Long minApplyAmountTo, Boolean enabled, Long applyDateFrom, Long applyDateTo,
      Long createdAtFrom, Long createdAtTo, Long updatedAtFrom, Long updatedAtTo, Long cbYearRateFrom,
      Long cbYearRateTo) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingInfo, List<StakingInfo>>() {
          @Override
          public List<StakingInfo> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root, id,
                currency, period, totalAmountFrom, totalAmountTo, minApplyAmountFrom,
                minApplyAmountTo, enabled, applyDateFrom, applyDateTo, createdAtFrom, createdAtTo,
                updatedAtFrom, updatedAtTo, cbYearRateFrom, cbYearRateTo);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(StakingInfo_.id)));
          }
        });

  }

}