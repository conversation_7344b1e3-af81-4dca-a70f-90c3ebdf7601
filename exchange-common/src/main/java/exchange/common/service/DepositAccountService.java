package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Currency;
import exchange.common.entity.DepositAccount;
import exchange.common.entity.DepositAccount_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.DepositAccountPredicate;

@Service
public class DepositAccountService extends EntityService<DepositAccount, DepositAccountPredicate> {

  @Override
  public Class<DepositAccount> getEntityClass() {
    return DepositAccount.class;
  }

  @Override
  protected void fetch(Root<DepositAccount> root) {
    super.fetch(root);
    root.fetch(DepositAccount_.user, JoinType.LEFT);
  }

  public List<DepositAccount> findByCondition(Long userId, String currency, Long dateFrom,
      Long dateTo, Integer number, Integer size) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<DepositAccount, List<DepositAccount>>() {
          @Override
          public List<DepositAccount> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            if (currency != null) {
              predicates
                  .add(predicate.equalCurrency(criteriaBuilder, root, Currency.valueOf(currency)));
            }

            if (dateFrom != null) {
              predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root,
                  new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            predicates.add(predicate.isEnabled(criteriaBuilder, root, true));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<DepositAccount> findByAddress(String address, String currency) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<DepositAccount, List<DepositAccount>>() {
          @Override
          public List<DepositAccount> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (address != null) {
              predicates.add(predicate.equalAddress(criteriaBuilder, root, address));
            }

            if (currency != null) {
              predicates
                  .add(predicate.equalCurrency(criteriaBuilder, root, Currency.valueOf(currency)));
            }
            predicates.add(predicate.isEnabled(criteriaBuilder, root, true));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public DepositAccount findByAddressTransfer(String address) {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<>() {
      @Override
      public DepositAccount query() {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalAddress(criteriaBuilder, root, address));
        return getSingleResult(entityManager, criteriaQuery, root, predicates);
      }
    });
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<DepositAccount> root, Long userId, Currency currency, String address, String destinationTag) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }
    if (currency != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
    }
    if (address != null) {
      predicates.add(predicate.likeAddress(criteriaBuilder, root, address));
    }
    if (destinationTag != null) {
      predicates.add(predicate.likeDestinationTag(criteriaBuilder, root, destinationTag));
    }

    return predicates;
  }

  public PageData<DepositAccount> findByConditionPageData(Long userId, Currency currency,
      String address, String destinationTag, Integer number, Integer size) {
    long count = customTransactionManager.count(getEntityClass(),
        new QueryExecutorCounter<DepositAccount>() {
          @Override
          public Long query() {
            return count(entityManager, criteriaBuilder, criteriaQuery, root,
                getPredicatesOfFindByCondition(criteriaBuilder, root, userId, currency, address, destinationTag));
          }
        });
    return new PageData<DepositAccount>(number, size, count, customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<DepositAccount, List<DepositAccount>>() {
          @Override
          public List<DepositAccount> query() {
            return getResultList(entityManager, criteriaQuery, root,
                getPredicatesOfFindByCondition(criteriaBuilder, root, userId, currency, address, destinationTag),
                number, size, criteriaBuilder.desc(root.get(DepositAccount_.id)));
          }
        }));
  }

  public DepositAccount findByUserIdAndCurrency(Long userId, Currency currency) {
     return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<DepositAccount, DepositAccount>() {
          @Override
          public DepositAccount query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
            predicates.add(predicate.isEnabled(criteriaBuilder, root, true));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<DepositAccount> findByConditionForBO(Long userId, String currency) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<DepositAccount, List<DepositAccount>>() {
          @Override
          public List<DepositAccount> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            if (currency != null) {
              predicates
                  .add(predicate.equalCurrency(criteriaBuilder, root, Currency.valueOf(currency)));
            }

            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<DepositAccount> findListByUpdateAt(Date updatedAt) {
      return customTransactionManager.find(getEntityClass(),
          new QueryExecutorReturner<DepositAccount, List<DepositAccount>>() {
              @Override
              public List<DepositAccount> query() {
                  List<Predicate> predicates = new ArrayList<>();
                  if (null != updatedAt) {
                      predicates.add(predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, updatedAt));
                  }
                  return getResultList(entityManager, criteriaQuery, root, predicates);
              }
          }
      );
  }
}
