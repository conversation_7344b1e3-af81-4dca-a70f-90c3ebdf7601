package exchange.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.CustomLogger;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Currency;
import exchange.common.entity.FiatDeposit;
import exchange.common.entity.PaypayDeposit;
import exchange.common.entity.PaypayDeposit_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.PaypayDepositPredicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class PaypayDepositService extends EntityService<PaypayDeposit, PaypayDepositPredicate> {

  private static final CustomLogger log = new CustomLogger(PaypayDepositService.class.getName());

  private final FiatDepositService fiatDepositService;
  private final AssetService assetService;

  @Override
  public Class<PaypayDeposit> getEntityClass() {
    return PaypayDeposit.class;
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<PaypayDeposit> root, Boolean noFiatDepositId, Long fiatDepositId, Long dateFrom,
      Long dateTo, Integer number, Integer size) {
    List<Predicate> predicates = new ArrayList<>();

    if (noFiatDepositId) {
      predicates.add(predicate.isNullOfFiatDepositId(criteriaBuilder, root));
    }

    if (fiatDepositId != null) {
      predicates.add(predicate.equalFiatDepositId(criteriaBuilder, root, fiatDepositId));
    }

    if (dateFrom != null) {
      predicates
          .add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
    }

    if (dateTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
    }

    return predicates;
  }

  public PageData<PaypayDeposit> findByConditionPageData(Boolean noFiatDepositId,
      Long fiatDepositId, Long dateFrom, Long dateTo, Integer number, Integer size) {
    long count = customTransactionManager.count(getEntityClass(), new QueryExecutorCounter<>() {
      @Override
      public Long query() {
        return count(entityManager, criteriaBuilder, criteriaQuery, root,
            getPredicatesOfFindByCondition(criteriaBuilder, root, noFiatDepositId, fiatDepositId,
                dateFrom, dateTo, number, size));
      }
    });

    return new PageData<PaypayDeposit>(number, size, count, customTransactionManager
        .find(getEntityClass(), new QueryExecutorReturner<PaypayDeposit, List<PaypayDeposit>>() {
          @Override
          public List<PaypayDeposit> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                noFiatDepositId, fiatDepositId, dateFrom, dateTo, number, size);
            return getResultList(entityManager, criteriaQuery, root, predicates, number, size,
                criteriaBuilder.desc(root.get(PaypayDeposit_.id)));
          }
        }));
  }


  public List<PaypayDeposit> findAllByCondition(Boolean noFiatDepositId, Long fiatDepositId,
      Long dateFrom, Long dateTo) {
    List<PaypayDeposit> dep = customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PaypayDeposit, List<PaypayDeposit>>() {
          @Override
          public List<PaypayDeposit> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                noFiatDepositId, fiatDepositId, dateFrom, dateTo, 0, Integer.MAX_VALUE);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(PaypayDeposit_.id)));
          }
        });
    return dep;
  }

  public List<PaypayDeposit> findByCondition(Boolean noFiatDepositId, Long fiatDepositId,
      Long dateFrom, Long dateTo, Integer number, Integer size) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PaypayDeposit, List<PaypayDeposit>>() {
          @Override
          public List<PaypayDeposit> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (noFiatDepositId) {
              predicates.add(predicate.isNullOfFiatDepositId(criteriaBuilder, root));
            }

            if (fiatDepositId != null) {
              predicates.add(predicate.equalFiatDepositId(criteriaBuilder, root, fiatDepositId));
            }

            if (dateFrom != null) {
              predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root,
                  new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            return getResultList(entityManager, criteriaQuery, root, predicates, number, size,
                criteriaBuilder.desc(root.get(PaypayDeposit_.id)));
          }
        });
  }

  public PaypayDeposit findOneByTranId(String tranId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PaypayDeposit, PaypayDeposit>() {
          @Override
          public PaypayDeposit query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalTranId(criteriaBuilder, root, tranId));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public PaypayDeposit findOneByFiatDepositId(Long fiatDepositId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PaypayDeposit, PaypayDeposit>() {
          @Override
          public PaypayDeposit query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalFiatDepositId(criteriaBuilder, root, fiatDepositId));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public void applyNew(PaypayDeposit paypayDeposit, FiatDeposit fiatDeposit) throws Exception {

    if (paypayDeposit == null) {
      return;
    }

    // 入金情報を作成できない場合、PayPay銀行の入金のみ記録
    if (fiatDeposit.getUserId() == null) {
      customTransactionManager.execute(entityManager -> {
        try {
          save(paypayDeposit, entityManager);
          return;
        } catch (Exception e) {
          log.severe(getClass().getName(), e);
        }
      });

    } else {
      customTransactionManager.execute(entityManager -> {
        try {
          FiatDeposit newFiatDeposit = fiatDepositService.save(fiatDeposit, entityManager);
          assetService.update(fiatDeposit.getUserId(), Currency.JPY, fiatDeposit.getAmount(),
              BigDecimal.ZERO, entityManager);
          paypayDeposit.setFiatDepositId(newFiatDeposit.getId());
          save(paypayDeposit, entityManager);
          return;
        } catch (Exception e) {
          log.severe(getClass().getName(), e);
        }
      });
    }
  }

  public void applyDeposit(PaypayDeposit paypayDeposit, FiatDeposit fiatDeposit) throws Exception {
    customTransactionManager.execute(entityManager -> {
      try {
        FiatDeposit newFiatDeposit = fiatDepositService.save(fiatDeposit, entityManager);
        assetService.update(fiatDeposit.getUserId(), Currency.JPY, fiatDeposit.getAmount(),
            BigDecimal.ZERO, entityManager);
        paypayDeposit.setFiatDepositId(newFiatDeposit.getId());
        save(paypayDeposit, entityManager);
        return;
      } catch (Exception e) {
        log.severe(getClass().getName(), e);
      }
    });
  }
}
