package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.IEOFiles;
import exchange.common.entity.IEOFiles_;
import exchange.common.predicate.IEOFilesPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class IEOFilesService extends EntityService<IEOFiles, IEOFilesPredicate> {

  @Override
  public Class<IEOFiles> getEntityClass() {
    return IEOFiles.class;
  }


  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<IEOFiles> root, String fileType, Long ieoRecuritId, String year, String month) {
    List<Predicate> predicates = new ArrayList<>();

    if (fileType != null) {
      predicates.add(predicate.equalFileType(criteriaBuilder, root, fileType));
    }

    if (ieoRecuritId != null) {
      predicates.add(predicate.equalIeoRecruitId(criteriaBuilder, root, ieoRecuritId));
    }

    if (year != null) {
      predicates.add(predicate.equalYear(criteriaBuilder, root, year));
    }

    if (month != null) {
      predicates.add(predicate.equalMonth(criteriaBuilder, root, month));
    }

    return predicates;
  }

  public List<IEOFiles> findByCondition(String fileType, Long ieoRecuritId, String year, String month) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<IEOFiles, List<IEOFiles>>() {
          @Override
          public List<IEOFiles> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByCondition(criteriaBuilder, root, fileType, ieoRecuritId, year, month);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(IEOFiles_.id)));
          }
        });
  }
}