package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Service;

import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.MailTemplate;
import exchange.common.entity.MailTemplate_;
import exchange.common.entity.Mail_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.MailTemplatePredicate;

@Service
public class MailTemplateService extends EntityService<MailTemplate, MailTemplatePredicate> {

  @Override
  public Class<MailTemplate> getEntityClass() {
    return MailTemplate.class;
  }
  
  public List<MailTemplate> findByCondition() {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<MailTemplate, List<MailTemplate>>() {
      @Override
      public List<MailTemplate> query() {
        List<Predicate> predicates = new ArrayList<>();
        return getResultList(entityManager, criteriaQuery, root, predicates, ViewVariables.DEFAULT_NUMBER, ViewVariables.DEFAULT_SIZE, criteriaBuilder.desc(root.get(MailTemplate_.createdAt)));
      }
    });
  }
  
  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder, Root<MailTemplate> root, Long dateFrom, Long dateTo, Boolean enabled, Integer number, Integer size) {
	List<Predicate> predicates = new ArrayList<>();
	
    if (dateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
    }

    if (dateTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
    }
    
    return predicates;
  }
  
  public PageData<MailTemplate> findByConditionPageData(Long dateFrom, Long dateTo, Integer number, Integer size) {
	long count = customTransactionManager.count(getEntityClass(), new QueryExecutorCounter<>() {
	  @Override
	  public Long query() {
	    return count(entityManager, criteriaBuilder, criteriaQuery, root,getPredicatesOfFindByCondition(criteriaBuilder, root, dateFrom, dateTo, true, number, size));
	  }
	});

	return new PageData<MailTemplate>(number, size, count,customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<MailTemplate, List<MailTemplate>>() {
      @Override
      public List<MailTemplate> query() {
        List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root, dateFrom, dateTo, true, number, size);
        return getResultList(entityManager, criteriaQuery, root, predicates, number, size,
            criteriaBuilder.desc(root.get(Mail_.id)));
      }
    }));
  }  
}
