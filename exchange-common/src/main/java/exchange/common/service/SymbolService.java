package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.Symbol;
import exchange.common.entity.Symbol_;
import exchange.common.predicate.SymbolPredicate;

@Service
public class SymbolService extends EntityService<Symbol, SymbolPredicate> {

  @Override
  public Class<Symbol> getEntityClass() {
    return Symbol.class;
  }

  public List<Symbol> findByCondition(TradeType tradeType) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<Symbol, List<Symbol>>() {
          @Override
          public List<Symbol> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<Symbol> findAllByCondition(TradeType tradeType, EntityManager entityManager) {
    return new QueryExecutorReturner<Symbol, List<Symbol>>() {
      @Override
      public List<Symbol> query() {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
        return getResultList(entityManager, criteriaQuery, root, predicates);
      }
    }.execute(getEntityClass(), entityManager);
  }

  public List<Symbol> findAllListByCondition(TradeType tradeType, CurrencyPair currencyPair) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<Symbol, List<Symbol>>() {
          @Override
          public List<Symbol> query() {
            List<Predicate> predicates = new ArrayList<>();
            if (tradeType != null) {
              predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
            }
            if (currencyPair != null) {
              predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
            }
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(Symbol_.id)));
          }
        });
  }

  public Symbol findByCondition(TradeType tradeType, CurrencyPair currencyPair) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<Symbol, Symbol>() {
          @Override
          public Symbol query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
            predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public Symbol findByConditionWithCache(TradeType tradeType, CurrencyPair currencyPair) {
    Symbol entity = null;
    try {
      entity = redisTemplate.getValue(this.getCacheKey(tradeType, currencyPair));
    } catch (Exception e) {
    }

    if (entity == null) {
      entity =
          customTransactionManager.find(
              getEntityClass(),
              new QueryExecutorReturner<>() {
                @Override
                public Symbol query() {
                  List<Predicate> predicates = new ArrayList<>();
                  predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
                  predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
                  return getSingleResult(entityManager, criteriaQuery, root, predicates);
                }
              });
    }

    if(entity != null) {
      super.redisTemplate.setValue(this.getCacheKey(tradeType, currencyPair), entity);
    }
    return entity;
  }

  public Symbol findByCondition(
      TradeType tradeType, CurrencyPair currencyPair, EntityManager entityManager) {
    return new QueryExecutorReturner<Symbol, Symbol>() {
      @Override
      public Symbol query() {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
        predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
        return getSingleResult(entityManager, criteriaQuery, root, predicates);
      }
    }.execute(getEntityClass(), entityManager);
  }

  protected String getCacheKey(TradeType tradeType, CurrencyPair currencyPair) {
    return getClass().getSimpleName() + ":" + tradeType.getName()+":"+currencyPair.getName();
  }
}
