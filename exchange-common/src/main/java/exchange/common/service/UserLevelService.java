package exchange.common.service;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import exchange.common.entity.User;
import exchange.common.model.response.UserLevelData;


@RequiredArgsConstructor
@Service
public class UserLevelService {

  public static UserLevelData set(User user) {
	UserLevelData uld = new UserLevelData();	
	uld.setLevel(user.getLevel());
    return uld;
  }
}
