package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.WashTrader;
import exchange.common.entity.WashTrader_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.WashTraderPredicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class WashTraderService extends EntityService<WashTrader, WashTraderPredicate> {

  @Override
  public Class<WashTrader> getEntityClass() {
    return WashTrader.class;
  }

  @Override
  protected void fetch(Root<WashTrader> root) {
    super.fetch(root);
    root.fetch(WashTrader_.user, JoinType.LEFT);
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<WashTrader> root,
      Long dateFrom,
      Long dateTo,
      Integer number,
      Integer size) {
    List<Predicate> predicates = new ArrayList<>();

    if (dateFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
    }

    if (dateTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
    }

    return predicates;
  }

  public PageData<WashTrader> findByConditionPageData(
      Long dateFrom,
      Long dateTo,
      Integer number,
      Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        dateFrom,
                        dateTo,
                        number,
                        size));
              }
            });

    return new PageData<WashTrader>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<WashTrader, List<WashTrader>>() {
              @Override
              public List<WashTrader> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        dateFrom,
                        dateTo,
                        number,
                        size);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.asc(root.get(WashTrader_.id)));
              }
            }));
  }

  public List<WashTrader> findAllByCondition(
     Long dateFrom, Long dateTo) {
    List<WashTrader> washTrader =
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<WashTrader, List<WashTrader>>() {

              @Override
              public List<WashTrader> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        dateFrom,
                        dateTo,
                        0,
                        Integer.MAX_VALUE);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.asc(root.get(WashTrader_.id)));
              }
            });
    return washTrader;
  }

  public List<WashTrader> findByCondition(
      Long dateFrom,
      Long dateTo,
      Integer number,
      Integer size) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<WashTrader, List<WashTrader>>() {
          @Override
          public List<WashTrader> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToCreatedAt(
                      criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(WashTrader_.id)));
          }
        });
  }
}
