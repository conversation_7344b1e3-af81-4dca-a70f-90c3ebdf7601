package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.criteria.Predicate;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.SygnaHubManager;
import exchange.common.entity.SygnaCurrency;
import exchange.common.exception.SygnaException;
import exchange.common.model.response.SygnaHubResponse;
import exchange.common.predicate.SygnaCurrencyPredicate;
import exchange.common.sygna.SygnaHubClient.CurrenciesRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class SygnaCurrencyService extends EntityService<SygnaCurrency, SygnaCurrencyPredicate> {
  
  private final SygnaHubManager sygnaHubManager;

  @Override
  public Class<SygnaCurrency> getEntityClass() {
    return SygnaCurrency.class;
  }

  public SygnaCurrency findOneByCurrencyName(String currencyName) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<SygnaCurrency, SygnaCurrency>() {
          @Override
          public SygnaCurrency query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalCurrencyName(criteriaBuilder, root, currencyName));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  public String getCurrencyId(String symbol) {
    String currencyId = null;
    SygnaCurrency sygnaCurrency = findOneByCurrencyName(symbol);
    if(ObjectUtils.isEmpty(sygnaCurrency)) {
      List<SygnaCurrency> AllSygnaCurrency = findAll();
      try {
        SygnaHubResponse<List<CurrenciesRes>> sygnaCurrenciesRes = sygnaHubManager.getCurrencies();
        if(ObjectUtils.isNotEmpty(sygnaCurrenciesRes.getData())) {
          CurrenciesRes existCurrencyRes = sygnaCurrenciesRes.getData().stream()
            .filter(currencyRes -> currencyRes.getCurrencySymbol().equals(symbol)).findFirst().orElse(null);
          if(ObjectUtils.isNotEmpty(existCurrencyRes)) {
            // テーブルに登録していない通貨をSygna側から取得して、登録
            sygnaCurrenciesRes.getData().stream()
              .filter(currencyRes -> !AllSygnaCurrency.stream().map(sygnaCurrencyExist -> sygnaCurrencyExist.getCurrencyId()).collect(Collectors.joining())
                  .contains(currencyRes.getCurrencyId())).forEach(sygnaCurrencyNotExist -> {
                    SygnaCurrency sygnaCurrencyForInsert = new SygnaCurrency();
                    sygnaCurrencyForInsert.setCurrencyId(sygnaCurrencyNotExist.getCurrencyId());
                    sygnaCurrencyForInsert.setCurrencyName(sygnaCurrencyNotExist.getCurrencySymbol());
                    save(sygnaCurrencyForInsert);
                  });
            SygnaCurrency updatedSygnaCurrency = findOneByCurrencyName(symbol);
            if(ObjectUtils.isNotEmpty(sygnaCurrency)) {
              currencyId = updatedSygnaCurrency.getCurrencyId();
            }
          }
        }
      } catch (Exception e) {
        log.error("Sygna Hubから通貨情報の取得処理エラー。" + e.getMessage());
      }
    } else {
      currencyId = sygnaCurrency.getCurrencyId();
    }
    return currencyId;
  }
}
