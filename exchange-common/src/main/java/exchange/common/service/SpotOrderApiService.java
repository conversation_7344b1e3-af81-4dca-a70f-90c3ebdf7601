package exchange.common.service;

import exchange.common.model.response.SpotCoverOrdersApiResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import exchange.common.config.SpotOrderApiConfig;
import exchange.common.constant.TradeType;
import exchange.common.entity.ApiInfo;
import exchange.common.http.HttpManager;
import exchange.common.model.request.SpotOrderForm;
import exchange.common.model.request.SpotOrderMultiForm;
import exchange.common.model.response.SpotOrdersApiResponse;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class SpotOrderApiService {

  @Autowired
  private HttpManager<SpotOrdersApiResponse> ordersHttpManager;
  @Autowired
  private HttpManager<SpotCoverOrdersApiResponse> spotCoverOrdersHttpManager;

  private final SpotOrderApiConfig spotOrderApiConfig;

  private TreeMap<String, Object> createSpotParameterMap(SpotOrderForm form) {
    TreeMap<String, Object> parameterMap = new TreeMap<>();
    parameterMap.put("symbolId", form.getSymbolId());
    parameterMap.put("orderSide", form.getOrderSide());
    parameterMap.put("orderType", form.getOrderType());
    parameterMap.put("price", form.getPrice());
    parameterMap.put("amount", form.getAmount());
    return parameterMap;
  }

  public SpotOrdersApiResponse postSpotOrderMulti(
      SpotOrderMultiForm multiForm, ApiInfo orderUserApiInfo) throws Exception {
    List<TreeMap<String, Object>> spotOrders = new ArrayList<>();

    // if (CollectionUtils.isEmpty(multiForm.getOrders())) {
    // throw new CustomException(ErrorCode.DEALING_ERROR_NO_COPY_ORDER);
    // }

    multiForm.getOrders().forEach(order -> spotOrders.add(createSpotParameterMap(order)));

    TreeMap<String, Object> parameterMap = new TreeMap<>();
    parameterMap.put("symbolId", multiForm.getSymbolId());
    parameterMap.put("orders", spotOrders);
    parameterMap.put("cancelOrderIds", multiForm.getCancelOrderIds());

    return ordersHttpManager.doPostJson(
        spotOrderApiConfig.getUrlOfV1OrderMulti(TradeType.SPOT),
        parameterMap,
        spotOrderApiConfig.createHeaderMapPostOrPut(parameterMap, orderUserApiInfo),
        null,
        SpotOrdersApiResponse.class);
  }

  public SpotCoverOrdersApiResponse getSpotOrderApiResponse(String host,String orderId,Long symbolId, ApiInfo orderUserApiInfo)
      throws Exception {
    List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();
    nameValuePairs.add(new BasicNameValuePair("symbolId",String.valueOf(symbolId)));
    nameValuePairs.add(new BasicNameValuePair("id", orderId));
    return spotCoverOrdersHttpManager.doGet(
        host + spotOrderApiConfig.getPathOfV1OrderOfHistory(TradeType.SPOT),
        nameValuePairs,
        spotOrderApiConfig.createHeaderMapGetOrDelete(
            spotOrderApiConfig.getPathOfV1OrderOfHistory(TradeType.SPOT),
            nameValuePairs,
            orderUserApiInfo),
        null,
        SpotCoverOrdersApiResponse.class);
  }
}
