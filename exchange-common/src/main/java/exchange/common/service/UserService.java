package exchange.common.service;

import java.util.*;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import com.google.common.collect.Lists;
import exchange.common.constant.AntisocialStatus;
import exchange.common.util.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.KycStatus;
import exchange.common.constant.UserStatus;
import exchange.common.entity.User;
import exchange.common.entity.UserInfoCorporate_;
import exchange.common.entity.UserKyc;
import exchange.common.entity.User_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.UserPredicate;
import lombok.RequiredArgsConstructor;

import static exchange.common.entity.AbstractEntity_.id;
import static exchange.common.entity.AbstractEntity_.updatedAt;

@RequiredArgsConstructor
@Service
public class UserService extends EntityService<User, UserPredicate> implements UserDetailsService {

  @Override
  public Class<User> getEntityClass() {
    return User.class;
  }

  @Override
  public User loadUserByUsername(String username) throws UsernameNotFoundException {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, User>() {
          @Override
          public User query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalEmail(criteriaBuilder, root, username));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  /** use this method for update AuthenticationPrincipal together. */
  public User saveWithAuthenticationPrincipal(User user) {
    user = super.save(user);
    SecurityContextHolder.getContext()
        .setAuthentication(
            new UsernamePasswordAuthenticationToken(
                user, user.getPassword(), user.getAuthorities()));
    return user;
  }
  public User saveWithAuthenticationPrincipal(User user, EntityManager entityManager) throws Exception{
    user = super.save(user,entityManager);
    SecurityContextHolder.getContext()
        .setAuthentication(
            new UsernamePasswordAuthenticationToken(
                user, user.getPassword(), user.getAuthorities()));
    return user;
  }

    public User save(User user) {
        return super.save(user);
    }

  @Override
  protected void fetch(Root<User> root) {
    super.fetch(root);
    root.fetch(User_.authorities, JoinType.LEFT);
    root.fetch(User_.userInfo, JoinType.LEFT);
    root.fetch(User_.userInfoCorporate, JoinType.LEFT)
        .fetch(UserInfoCorporate_.agent, JoinType.LEFT);
    root.fetch(User_.userInfoCorporate, JoinType.LEFT)
        .fetch(UserInfoCorporate_.representative, JoinType.LEFT);
    root.fetch(User_.userKyc, JoinType.LEFT);
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<User> root,
      Long id,
      Long idFrom,
      Long idTo,
      String email,
      String firstName,
      String lastName,
      String firstKana,
      String lastKana,
      UserStatus userStatus,
      Long affiliateInfoId,
      String uuid,
      Boolean insideAccountFlg,
      KycStatus kycStatus,
      Long dateFrom,
      Long dateTo,
      String corporateName,
      String corporateKana) {
    List<Predicate> predicates = new ArrayList<>();

    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    } else {
      if (idFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
      }

      if (idTo != null) {
        predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
      }

      if (!StringUtils.isEmpty(email)) {
        predicates.add(predicate.likeEmail(criteriaBuilder, root, email));
      }

      if (!StringUtils.isEmpty(firstName)) {
        predicates.add(predicate.likeFirstName(criteriaBuilder, root, firstName));
      }

      if (!StringUtils.isEmpty(lastName)) {
        predicates.add(predicate.likeLastName(criteriaBuilder, root, lastName));
      }

      if (!StringUtils.isEmpty(firstKana)) {
        predicates.add(predicate.likeFirstKana(criteriaBuilder, root, firstKana));
      }

      if (!StringUtils.isEmpty(lastKana)) {
        predicates.add(predicate.likeLastKana(criteriaBuilder, root, lastKana));
      }

      if (userStatus != null) {
        predicates.add(predicate.equalUserStatus(criteriaBuilder, root, userStatus));
      }

      if (affiliateInfoId != null) {
        predicates.add(predicate.equalAffiliateInfoId(criteriaBuilder, root, affiliateInfoId));
      }

      if (!StringUtils.isEmpty(uuid)) {
        predicates.add(predicate.equalUuid(criteriaBuilder, root, uuid));
      }

      if (insideAccountFlg != null) {
          predicates.add(predicate.isInsideAccountFlg(criteriaBuilder, root, insideAccountFlg));
        }

      if (kycStatus != null) {
        predicates.add(predicate.equalKycStatus(criteriaBuilder, root, kycStatus));
      }

      if (dateFrom != null) {
        predicates.add(
            predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
      }

      if (dateTo != null) {
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
      }

      if (!StringUtils.isEmpty(corporateName)) {
        predicates.add(predicate.likeCorporateName(criteriaBuilder, root, corporateName));
      }

      if (!StringUtils.isEmpty(corporateKana)) {
        predicates.add(predicate.likeCorporateKana(criteriaBuilder, root, corporateKana));
      }
    }

    return predicates;
  }
  /**
   *  ユーザー情報の検索条件の作成‌（制裁チェックを追加）‌
   */
  private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<User> root,
            Long id,
            Long idFrom,
            Long idTo,
            String email,
            String firstName,
            String lastName,
            String firstKana,
            String lastKana,
            UserStatus userStatus,
            Long affiliateInfoId,
            String uuid,
            Boolean insideAccountFlg,
            KycStatus kycStatus,
            Long dateFrom,
            Long dateTo,
            String corporateName,
            String corporateKana,
            AntisocialStatus antisocialStatus) {
        List<Predicate> predicates = new ArrayList<>();

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }

            if (idTo != null) {
                predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
            }

            if (!StringUtils.isEmpty(email)) {
                predicates.add(predicate.likeEmail(criteriaBuilder, root, email));
            }

            if (!StringUtils.isEmpty(firstName)) {
                predicates.add(predicate.likeFirstName(criteriaBuilder, root, firstName));
            }

            if (!StringUtils.isEmpty(lastName)) {
                predicates.add(predicate.likeLastName(criteriaBuilder, root, lastName));
            }

            if (!StringUtils.isEmpty(firstKana)) {
                predicates.add(predicate.likeFirstKana(criteriaBuilder, root, firstKana));
            }

            if (!StringUtils.isEmpty(lastKana)) {
                predicates.add(predicate.likeLastKana(criteriaBuilder, root, lastKana));
            }

            if (userStatus != null) {
                predicates.add(predicate.equalUserStatus(criteriaBuilder, root, userStatus));
            }

            if (affiliateInfoId != null) {
                predicates.add(predicate.equalAffiliateInfoId(criteriaBuilder, root, affiliateInfoId));
            }

            if (!StringUtils.isEmpty(uuid)) {
                predicates.add(predicate.equalUuid(criteriaBuilder, root, uuid));
            }

            if (insideAccountFlg != null) {
                predicates.add(predicate.isInsideAccountFlg(criteriaBuilder, root, insideAccountFlg));
            }

            if (kycStatus != null) {
                predicates.add(predicate.equalKycStatus(criteriaBuilder, root, kycStatus));
            }

            if (dateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
                predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            if (!StringUtils.isEmpty(corporateName)) {
                predicates.add(predicate.likeCorporateName(criteriaBuilder, root, corporateName));
            }

            if (!StringUtils.isEmpty(corporateKana)) {
                predicates.add(predicate.likeCorporateKana(criteriaBuilder, root, corporateKana));
            }

            if (antisocialStatus != null) {
                predicates.add(predicate.equalAntisocialStatus(criteriaBuilder, root, antisocialStatus));
            }
        }

        return predicates;
  }

  public PageData<User> findByCondition(
      Long id,
      Long idFrom,
      Long idTo,
      String email,
      String firstName,
      String lastName,
      String firstKana,
      String lastKana,
      UserStatus userStatus,
      Long affiliateInfoId,
      String uuid,
      Boolean insideAccountFlg,
      KycStatus kycStatus,
      Long dateFrom,
      Long dateTo,
      String corporateName,
      String corporateKana,
      Integer number,
      Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        id,
                        idFrom,
                        idTo,
                        email,
                        firstName,
                        lastName,
                        firstKana,
                        lastKana,
                        userStatus,
                        affiliateInfoId,
                        uuid,
                        insideAccountFlg,
                        kycStatus,
                        dateFrom,
                        dateTo,
                        corporateName,
                        corporateKana));
              }
            });

    return new PageData<User>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<User, List<User>>() {
              @Override
              public List<User> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        id,
                        idFrom,
                        idTo,
                        email,
                        firstName,
                        lastName,
                        firstKana,
                        lastKana,
                        userStatus,
                        affiliateInfoId,
                        uuid,
                        insideAccountFlg,
                        kycStatus,
                        dateFrom,
                        dateTo,
                        corporateName,
                        corporateKana);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(updatedAt)));
              }
            }));
  }

    /**
     *  ユーザー情報の検索（制裁チェックを追加）‌
     */
    public PageData<User> findByCondition(
            Long id,
            Long idFrom,
            Long idTo,
            String email,
            String firstName,
            String lastName,
            String firstKana,
            String lastKana,
            UserStatus userStatus,
            Long affiliateInfoId,
            String uuid,
            Boolean insideAccountFlg,
            KycStatus kycStatus,
            Long dateFrom,
            Long dateTo,
            String corporateName,
            String corporateKana,
            AntisocialStatus antisocialStatus,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                idFrom,
                                                idTo,
                                                email,
                                                firstName,
                                                lastName,
                                                firstKana,
                                                lastKana,
                                                userStatus,
                                                affiliateInfoId,
                                                uuid,
                                                insideAccountFlg,
                                                kycStatus,
                                                dateFrom,
                                                dateTo,
                                                corporateName,
                                                corporateKana,
                                                antisocialStatus));
                            }
                        });

        return new PageData<User>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<User, List<User>>() {
                            @Override
                            public List<User> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                idFrom,
                                                idTo,
                                                email,
                                                firstName,
                                                lastName,
                                                firstKana,
                                                lastKana,
                                                userStatus,
                                                affiliateInfoId,
                                                uuid,
                                                insideAccountFlg,
                                                kycStatus,
                                                dateFrom,
                                                dateTo,
                                                corporateName,
                                                corporateKana,
                                                antisocialStatus);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(updatedAt)));
                            }
                        }));
    }

  public User findByCondition(String email, KycStatus kycStatus) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, User>() {
          @Override
          public User query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
            predicates.add(predicate.equalKycStatus(criteriaBuilder, root, kycStatus));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public User findByEmailAndStatus(String email, UserStatus userStatus) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, User>() {
          @Override
          public User query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
            predicates.add(predicate.equalUserStatus(criteriaBuilder, root, userStatus));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public User findByEmailAndNotStatus(String email, UserStatus userStatus) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, User>() {
          @Override
          public User query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
            predicates.add(predicate.notEqualUserStatus(criteriaBuilder, root, userStatus));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public User findByEmail(String email) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, User>() {
          @Override
          public User query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<User> findByEnabled() {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalEnabled(criteriaBuilder, root, true));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public User findByUserKyc(UserKyc userKyc) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, User>() {
          @Override
          public User query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalId(criteriaBuilder, root, userKyc.getUserId()));
            predicates.add(predicate.equalUserKycId(criteriaBuilder, root, userKyc.getId()));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<User> findByIdOrName(String query) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.likeFirstName(criteriaBuilder, root, query));
            predicates.add(predicate.likeLastName(criteriaBuilder, root, query));
            predicates.add(predicate.equalId(criteriaBuilder, root, Long.parseLong(query)));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public User findById(Long id) {
	    return customTransactionManager.find(
	        getEntityClass(),
	        new QueryExecutorReturner<User, User>() {
	          @Override
	          public User query() {
	            List<Predicate> predicates = new ArrayList<>();
	            predicates.add(predicate.equalId(criteriaBuilder, root, id));
	            return getSingleResult(entityManager, criteriaQuery, root, predicates);
	          }
	        });
	  }

  public List<User> findByUpdatedAt(Long updatedAtFrom, Long updatedAtTo) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(
                predicate.greaterThanOrEqualToUpdatedAt(
                    criteriaBuilder, root, new Date(updatedAtFrom)));
            predicates.add(
                predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updatedAtTo)));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<User> findByTradeUncapped(boolean isTradeUncapped) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.isTradeUncapped(criteriaBuilder, root, isTradeUncapped));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<User> findByInsideAccountFlg(boolean isInsideAccountFlg) {
	    return customTransactionManager.find(
	        getEntityClass(),
	        new QueryExecutorReturner<User, List<User>>() {
	          @Override
	          public List<User> query() {
	            List<Predicate> predicates = new ArrayList<>();
	            predicates.add(predicate.isInsideAccountFlg(criteriaBuilder, root, isInsideAccountFlg));
	            return getResultList(entityManager, criteriaQuery, root, predicates);
	          }
	        });
	  }

  public List<User> findByStatus(UserStatus userStatus) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserStatus(criteriaBuilder, root, userStatus));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<User> findByKycStatus(KycStatus[] kycStatus) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.inKycStatus(criteriaBuilder, root, kycStatus));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

    public List<User> findByKycStatusAndUpdateAt(KycStatus[] kycStatus, Date updatedAt) {
        return customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<User, List<User>>() {
                @Override
                public List<User> query() {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(predicate.inKycStatus(criteriaBuilder, root, kycStatus));
                    if (null != updatedAt) {
                        predicates.add(predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, updatedAt));
                    }
                    return getResultList(entityManager, criteriaQuery, root, predicates);
                }
            }
        );
    }

    public List<User> findByKycStatusAndIds(KycStatus[] kycStatus, Set<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<User, List<User>>() {
              @Override
              public List<User> query() {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(predicate.inKycStatus(criteriaBuilder, root, kycStatus));
                predicates.add(predicate.inId(root, Lists.newArrayList(ids)));
                return getResultList(entityManager, criteriaQuery, root, predicates);
              }
            }
        );
    }

  public List<User> findByCreatedAt(Long createdAtTo) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
          @Override
          public List<User> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(
                predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<User> findByUserAll() {
    return customTransactionManager.find(
            getEntityClass(),
        new QueryExecutorReturner<User, List<User>>() {
            @Override
            public List<User> query() {
                List<Predicate> predicates = new ArrayList<>();
                Predicate userInfo = criteriaBuilder.or(
                        criteriaBuilder.isNotNull(root.get(User_.USER_INFO)),
                        criteriaBuilder.isNotNull(root.get(User_.USER_INFO_CORPORATE_ID))
                );
                predicates.add(criteriaBuilder.equal(root.get(User_.caseId), 0));
                predicates.add(userInfo);
                return getResultList(entityManager, criteriaQuery, root, predicates);
            }
        }
    );
  }

  public List<User> findByUserStatusAndIds(List<Long> userIds, Long caseId) {
        return customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<User, List<User>>() {
                @Override
                public List<User> query() {
                    UserStatus[] userStatuses = new UserStatus[] {
                            UserStatus.REVIEWING,
                            UserStatus.LEFT
                    };
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(predicate.notInId(root, userIds.toArray(Long[]::new)));
                    predicates.add(predicate.equalCaseId(criteriaBuilder, root, caseId));
                    predicates.add(predicate.notInUserStatus(criteriaBuilder, root,userStatuses));
                    return getResultList(entityManager, criteriaQuery, root, predicates);
                }
            }
    );
  }
}
