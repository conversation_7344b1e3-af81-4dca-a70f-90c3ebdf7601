package exchange.common.service;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.Predicate;

import org.springframework.stereotype.Service;

import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.UserAgreementType;
import exchange.common.entity.UserAgreementFile;
import exchange.common.entity.UserAgreementFile_;
import exchange.common.predicate.UserAgreementFilePredicate;

@Service
public class UserAgreementFileService extends EntityService<UserAgreementFile, UserAgreementFilePredicate> {

  @Override
  public Class<UserAgreementFile> getEntityClass() {
    return UserAgreementFile.class;
  }

  public UserAgreementFile findByCondition(UserAgreementType userAgreementType, String version) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<UserAgreementFile, UserAgreementFile>() {
          @Override
          public UserAgreementFile query() {
            List<Predicate> predicates = new ArrayList<>();
            if (userAgreementType != null) {
              predicates.add(predicate.equalUserAgreementType(criteriaBuilder, root, userAgreementType));
            }
            if (version != null) {
              predicates.add(predicate.equalVersion(criteriaBuilder, root, version));
            }
            return getSingleResult(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(UserAgreementFile_.id)));
          }
        });
  }

  public List<UserAgreementFile> findAll() {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<UserAgreementFile, List<UserAgreementFile>>() {
      @Override
      public List<UserAgreementFile> query() {
        List<Predicate> predicates = new ArrayList<>();
        return getResultList(entityManager, criteriaQuery, root, predicates, criteriaBuilder.asc(root.get(UserAgreementFile_.id)));
      }
    });
  }

  public List<UserAgreementFile> findByAgreementType(UserAgreementType userAgreementType) {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<UserAgreementFile, List<UserAgreementFile>>() {
      @Override
      public List<UserAgreementFile> query() {
        List<Predicate> predicates = new ArrayList<>();
        if (userAgreementType != null) {
          predicates.add(predicate.equalUserAgreementType(criteriaBuilder, root, userAgreementType));
        }
        return getResultList(entityManager, criteriaQuery, root, predicates, criteriaBuilder.asc(root.get(UserAgreementFile_.id)));
      }
    });
  }


}
