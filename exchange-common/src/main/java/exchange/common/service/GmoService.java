package exchange.common.service;

import exchange.common.config.SpringConfig;
import exchange.common.model.response.Account;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatus.Series;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.thymeleaf.util.StringUtils;
import com.google.gson.Gson;
import exchange.common.auth.AuthTypeEnum;
import exchange.common.auth.AuthUtil;
import exchange.common.auth.StateFactory;
import exchange.common.auth.common.StateVerificationException;
import exchange.common.auth.model.TokenResponse;
import exchange.common.component.RedisManager;
import exchange.common.config.GmoAuthorizationTokenConfiguration;
import exchange.common.config.GmoConfig;
import exchange.common.constant.CommonConstants;
import exchange.common.entity.AppConfiguration;
import exchange.common.exception.GmoRequestException;
import exchange.common.model.request.BulkTransfer;
import exchange.common.model.request.BulkTransferRequest;
import exchange.common.model.request.BulkTransferResultRequest;
import exchange.common.model.request.VaIssueRequest;
import exchange.common.model.response.AccountsResponse;
import exchange.common.model.response.BulkTransferRequestResponse;
import exchange.common.model.response.BulkTransferStatusResponse;
import exchange.common.model.response.VaIssueResponse;
import exchange.common.repos.AppConfigurationRepository;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GmoService {


    private String GMO_LOG_PREFIX="[GMO]";
    @Value("${coin.cus.host:@null}")
    private String cusHost;
    @Autowired
    GmoConfig gmoConfig;
    @Value("${coin.cus.host-external:@null}")
    String hostExternal;

    @Autowired
    SpringConfig springConfig;

    @Autowired
    AppConfigurationRepository appConfigurationRepository;

    @Autowired
    RestTemplate restOperations;

    @Autowired
    RedisManager redisManager;

    @PostConstruct
    public void init() {
      SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
      factory.setOutputStreaming(false);
      restOperations.setRequestFactory(factory);
      ResponseErrorHandler responseErrorHandler = new ResponseErrorHandler() {
        @Override
        public boolean hasError(ClientHttpResponse response) throws IOException {
          return (response.getStatusCode().series() == Series.CLIENT_ERROR || response.getStatusCode().series() == Series.SERVER_ERROR);
        }

        @Override
        public void handleError(ClientHttpResponse response) throws IOException{
          log.error("{} response handleError errorCode :{}",GMO_LOG_PREFIX,response.getStatusCode());
          log.error("{} response handleError statusText :{}",GMO_LOG_PREFIX,response.getStatusText());
          log.error("{} response handleError rawStatusCode :{}",GMO_LOG_PREFIX,response.getRawStatusCode());
        }
      };
      MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
      mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_HTML, MediaType.TEXT_PLAIN));
      restOperations.getMessageConverters().add(mappingJackson2HttpMessageConverter);
      restOperations.setErrorHandler(responseErrorHandler);
    }
    public String callBack(String code,String state,String error,String description) throws GmoRequestException {
        Gson gson = new Gson();
        AppConfiguration appConfiguration = appConfigurationRepository.findFirstByKey(CommonConstants.authorizationTokenKey);
        GmoAuthorizationTokenConfiguration tokenConfiguration = gson.fromJson(appConfiguration.getValue(), GmoAuthorizationTokenConfiguration.class);
        String redirectFullUri=hostExternal+gmoConfig.getRedirectUri();
        StateFactory stateFactory = new StateFactory(tokenConfiguration.getHashKey());

        try {
            //check if state match with hashKey and sessionId;
            stateFactory.validateState(tokenConfiguration.getSessionId(),state);
        }catch (StateVerificationException e){
            log.info("{} hashKey:{} sessionId:{} not match with state:{}",GMO_LOG_PREFIX,tokenConfiguration.getHashKey(),tokenConfiguration.getSessionId(),state);
            throw new GmoRequestException("hashKey and sessionId not match with state");
        }
        String authorization = AuthUtil.getAuthorization(AuthTypeEnum.CLIENT_SECRET_BASIC, gmoConfig.getClientId(), gmoConfig.getSecret());

        //build header
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        httpHeaders.add("Authorization",CommonConstants.GMO_TOKEN_PRE_FIX+authorization);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add(CommonConstants.GRANT_TYPE,CommonConstants.AUTHORIZATION_CODE);
        map.add(CommonConstants.AUTHORIZATION_CODE_PRE_FIX,code);
        map.add(CommonConstants.REDIRECT_URI,redirectFullUri);
        map.add(CommonConstants.CLIENT_ID,gmoConfig.getClientId());
        map.add(CommonConstants.CLIENT_SECRET,gmoConfig.getSecret());

        HttpEntity<MultiValueMap<String, String>> request =
                new HttpEntity<>(map,httpHeaders);

        URI uri = UriComponentsBuilder.fromHttpUrl(gmoConfig.getStgBaseEndpoint()+gmoConfig.getAuthorizationToken()).build().toUri();

        log.info( "{}request access token API entity's header :{}", GMO_LOG_PREFIX,request.getHeaders().toSingleValueMap());

        log.info( "{}request access token API entity's body :{}", GMO_LOG_PREFIX, request.getBody().toSingleValueMap());

        log.info("{}request {}  for authorization token ",GMO_LOG_PREFIX, uri.getPath());
        TokenResponse response = this.restOperations.postForObject(uri,request,TokenResponse.class);
        if(response==null){
            log.warn("{}gmo authorization response is null ,please contact developer",GMO_LOG_PREFIX);
            throw new GmoRequestException("gmo authorization response is null ,please contact developer");
        }
        //todo remove sensitive log
        log.info("{}authorization token response:{}",GMO_LOG_PREFIX,response.toString());

        tokenConfiguration.setAccessToken(response.getAccessToken());
        tokenConfiguration.setRefreshToken(response.getRefreshToken());
        long current = System.currentTimeMillis();
        Long expireAt = current+Long.parseLong(response.getExpiresIn())*1000;
        tokenConfiguration.setExpiresIn(response.getExpiresIn());
        tokenConfiguration.setExpireAt(expireAt);
        tokenConfiguration.setScope(response.getScope());
        tokenConfiguration.setTokenType(response.getTokenType());
        tokenConfiguration.setState(state);
        String tokenStr = gson.toJson(tokenConfiguration);
        appConfiguration.setValue(tokenStr);
        log.info("gmo authorization config str value:{}",tokenStr);
        appConfigurationRepository.save(appConfiguration);
        return "GMO oauth flow finished , cache token successfully";
    }

    /**
     *
     * @return
     * @throws GmoRequestException
     * @description 保有する全ての口座情報一覧を照会します
     */
    public String accounts(String accessToken) throws GmoRequestException {

        String raId = redisManager.get(CommonConstants.GMO_CORPORATE_KEY);
        if(StringUtils.isEmpty(raId)){
            //build header
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(CommonConstants.GMO_HEADER_TOKEN_KEY,accessToken);

            HttpEntity<MultiValueMap<String, String>> request =
                    new HttpEntity<>(null,httpHeaders);

            URI uri = UriComponentsBuilder.fromHttpUrl(gmoConfig.getStgBaseEndpoint()+gmoConfig.getAccountsUri()).build().toUri();

            log.info( "{}request accounts API entity's header :{}", GMO_LOG_PREFIX,request.getHeaders().toSingleValueMap());

            log.info("{}request accounts API {} for accounts",GMO_LOG_PREFIX, uri.getPath());

            ResponseEntity<AccountsResponse> res = this.restOperations.exchange(uri,HttpMethod.GET,request,AccountsResponse.class);
            if(res.getStatusCode() != HttpStatus.OK ){
                log.error("request:{} exception host:https://{},errorCode: {}, errorMessage: {}", GMO_LOG_PREFIX, uri.getHost() + uri.getPath(), res.getBody().getErrorCode(), res.getBody().getErrorMessage());
                throw new GmoRequestException();
            }
            log.info("{} account response: {}",GMO_LOG_PREFIX,res);
            String accountNumber = "";
            if(springConfig.isStg()){
                accountNumber = CommonConstants.GMO_STG_ACCOUNT_NUMBER;
            } else if (springConfig.isPrd()){
                accountNumber = CommonConstants.GMO_PRD_ACCOUNT_NUMBER;
            } else {
                accountNumber = CommonConstants.GMO_STG_ACCOUNT_NUMBER;
            }
            List<Account> accounts = Objects.requireNonNull(res.getBody()).getAccounts();
            for (Account account : accounts) {
                if(accountNumber.equals(account.getAccountNumber())){
                  raId = account.getAccountId();
                }
            }
            if(StringUtils.isEmpty(raId)){
               log.error("no accountId was found based on the oral setting accountNumber: {}", accountNumber);
               throw new GmoRequestException();
            }
            log.info(" {} account first raId: {},accountNumber:{}",GMO_LOG_PREFIX, raId,accountNumber);
            redisManager.set(CommonConstants.GMO_CORPORATE_KEY, raId);
        }
        return raId;
    }


    public TokenResponse refreshToken() throws GmoRequestException {

        GmoAuthorizationTokenConfiguration appConfig = getGmoAuthConfig();

        String authorization = AuthUtil.getAuthorization(AuthTypeEnum.CLIENT_SECRET_BASIC, gmoConfig.getClientId(), gmoConfig.getSecret());
        //build header
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        httpHeaders.add("Authorization",CommonConstants.GMO_TOKEN_PRE_FIX+authorization);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add(CommonConstants.GRANT_TYPE,"refresh_token");
        map.add(CommonConstants.REFRESH_TOKEN,appConfig.getRefreshToken());
        map.add(CommonConstants.CLIENT_ID,gmoConfig.getClientId());
        map.add(CommonConstants.CLIENT_SECRET,gmoConfig.getSecret());


        HttpEntity<MultiValueMap<String, String>> request =
                new HttpEntity<>(map,httpHeaders);

        //the path of get token by code is the same with refresh token path
        URI uri = UriComponentsBuilder.fromHttpUrl(gmoConfig.getStgBaseEndpoint()+gmoConfig.getAuthorizationToken()).build().toUri();

        log.info( "{}request refresh token API entity's header :{}", GMO_LOG_PREFIX,request.getHeaders().toSingleValueMap());

        log.info( "{}request refresh token API entity's body :{}", GMO_LOG_PREFIX, request.getBody().toSingleValueMap());

        log.info("{}request {}  for authorization refresh token ",GMO_LOG_PREFIX, uri.getPath());

        TokenResponse response = this.restOperations.postForObject(uri,request,TokenResponse.class);

        if(response == null){
            throw new GmoRequestException("refresh token fail,the response is null");
        }

        updateAppConfiguration(response);

        return response;
    }

    public GmoAuthorizationTokenConfiguration getGmoAuthConfig() {
        AppConfiguration configuration = appConfigurationRepository.findFirstByKey(CommonConstants.authorizationTokenKey);
        Gson gson = new Gson();
        return gson.fromJson(configuration.getValue(), GmoAuthorizationTokenConfiguration.class);
    }

    /**
     * @param varTypeCode 1=期限型 2=継続型
     * @param "issueRequestCount" 1回のリクエストで1,000口座まで発行可能
     * @param "raId"      入金先の口座を識別するID ，科目は以下のいずれかのみ受け付けます 01 = 普通存款（利息） ・ 02 = 普通存款（用于结算）
     */
    public VaIssueResponse issueAccount(String varTypeCode) throws GmoRequestException {

        String accessToken;

        GmoAuthorizationTokenConfiguration authConfig = this.getGmoAuthConfig();

        Long expireAt = authConfig.getExpireAt();

        accessToken = authConfig.getAccessToken();

        Long current = System.currentTimeMillis();
        if(current>expireAt||(current<expireAt&&(expireAt-current<gmoConfig.getRefreshTokenInterval()))){
            log.info("access token has expired,call refresh token manually");
            TokenResponse response = this.refreshToken();
            accessToken = response.getAccessToken();
        }
        String raId = this.accounts(accessToken);

        log.info("{} issue Account parameter raId :{}",GMO_LOG_PREFIX,raId);
        //build header
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.add("Accept-Charset", "UTF-8");
        httpHeaders.add(CommonConstants.GMO_HEADER_TOKEN_KEY, accessToken);

        //build body
        VaIssueRequest vaIssueRequest = VaIssueRequest.builder().vaTypeCode(varTypeCode).issueRequestCount("1").raId(raId).build();

        HttpEntity<VaIssueRequest> request =
                new HttpEntity<>(vaIssueRequest,httpHeaders);

        URI uri = UriComponentsBuilder.fromHttpUrl(gmoConfig.getStgBaseEndpoint()+gmoConfig.getIssueAccountUri()).build().toUri();

        log.info("{}request issue account API entity's header :{}", GMO_LOG_PREFIX,request.getHeaders().toSingleValueMap());

        log.info("{}request issue account API entity's body :{}", GMO_LOG_PREFIX, request.getBody().toString());

        log.info("{}request {}  for issue account ",GMO_LOG_PREFIX, uri.getPath());

        try {
            ResponseEntity<VaIssueResponse> res = this.restOperations.postForEntity(uri,request,VaIssueResponse.class);
            log.info("{}return response res: {}",GMO_LOG_PREFIX,res);
            if(res.getStatusCode() == HttpStatus.OK || res.getStatusCode() == HttpStatus.CREATED) {
              log.info("{} issue response detail is {}",GMO_LOG_PREFIX,res.getBody());
              return res.getBody();
            } else {
               log.error("request:{} exception {host: https://{},errorCode: {},errorMessage: {}}", GMO_LOG_PREFIX, uri.getHost() + uri.getPath(), Objects.requireNonNull(res.getBody()).getErrorCode(), res.getBody().getErrorMessage());
               return null;
            }
        } catch (Exception e) {
            log.error("request gmo server error message {}",e.getMessage());
        }
        return null;
    }

    public void updateAppConfiguration(TokenResponse response){
        AppConfiguration appConfiguration = appConfigurationRepository.findFirstByKey(CommonConstants.authorizationTokenKey);
        Gson gson = new Gson();
        GmoAuthorizationTokenConfiguration gmoTokenConfig = gson.fromJson(appConfiguration.getValue(), GmoAuthorizationTokenConfiguration.class);
        long current = System.currentTimeMillis();
        Long expireAt = current+Long.parseLong(response.getExpiresIn())*1000;
        gmoTokenConfig.setExpireAt(expireAt);
        gmoTokenConfig.setAccessToken(response.getAccessToken());
        gmoTokenConfig.setRefreshToken(response.getRefreshToken());
        gmoTokenConfig.setExpiresIn(response.getExpiresIn());
        gmoTokenConfig.setScope(response.getScope());
        gmoTokenConfig.setTokenType(response.getTokenType());
        String value = gson.toJson(gmoTokenConfig);
        appConfiguration.setValue(value);
        appConfigurationRepository.save(appConfiguration);
    }

    /**
     * 振込限度額を取得
     * @return
     * @throws GmoRequestException
     */
    public String getTransferLimitAmount() throws GmoRequestException {

      String accessToken = getAccessToken();

      String transferLimitAmount = "";

      // build header
      HttpHeaders httpHeaders = new HttpHeaders();
      httpHeaders.add(CommonConstants.GMO_HEADER_TOKEN_KEY, accessToken);

      HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(null, httpHeaders);

      URI uri = UriComponentsBuilder
          .fromHttpUrl(gmoConfig.getStgBaseEndpoint() + gmoConfig.getAccountsUri()).build().toUri();

      log.info("{}request 振込限度額 API entity's header :{}", GMO_LOG_PREFIX,
          request.getHeaders().toSingleValueMap());

      log.info("{}request API {} for 振込限度額", GMO_LOG_PREFIX, uri.getPath());

      ResponseEntity<AccountsResponse> res =
          this.restOperations.exchange(uri, HttpMethod.GET, request, AccountsResponse.class);
      if (res.getStatusCode() == HttpStatus.UNAUTHORIZED) {
        log.warn(
            "[GMO]call GMO accounts API still UNAUTHORIZED after refresh token,please manual intervention");
        throw new GmoRequestException();
      }
      transferLimitAmount = res.getBody().getAccounts().get(0).getTransferLimitAmount();

      log.info("{} 振込限度額 is {}",GMO_LOG_PREFIX, transferLimitAmount);
      return transferLimitAmount;
    }

    /**
     * AccessTokenを取得
     * 
     * @return
     * @throws GmoRequestException
     */
    private String getAccessToken() throws GmoRequestException {
      GmoAuthorizationTokenConfiguration authConfig = this.getGmoAuthConfig();

      Long expireAt = authConfig.getExpireAt();

      String accessToken = authConfig.getAccessToken();

      Long current = System.currentTimeMillis();
      if (current > expireAt
          || (current < expireAt && (expireAt - current < gmoConfig.getRefreshTokenInterval()))) {
        log.info("access token has expired,call refresh token manually");
        TokenResponse response = this.refreshToken();
        accessToken = response.getAccessToken();
      }

      return accessToken;
    }
    
    /**
     * 総合振込・総合振込予約を行うための依頼をします
     * @param totalAmount 合計金額
     * @param bulkTransferList API用総合振込明細情報
     * @return
     * @throws GmoRequestException 異常
     */
    public ResponseEntity<BulkTransferRequestResponse> bulkTransfer(BigDecimal totalAmount, List<BulkTransfer> bulkTransferList)
        throws GmoRequestException {

      String accessToken = getAccessToken();

      String raId = this.accounts(accessToken);

      log.info("{} 総合振込依頼 parameter raId :{}", GMO_LOG_PREFIX, raId);
      // build header
      HttpHeaders httpHeaders = new HttpHeaders();
      httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
      httpHeaders.add("charset", "UTF-8");
      httpHeaders.add(CommonConstants.GMO_HEADER_TOKEN_KEY, accessToken);

      // 振込依頼日は今日を指定
      String transferDesignatedDate = FormatUtil.formatJst(new Date(), FormatPattern.YYYY_MM_DD);
      // build body
      BulkTransferRequest bulkTransferRequest =
          BulkTransferRequest.builder().accountId(raId).transferDesignatedDate(transferDesignatedDate)
              .transferDateHolidayCode("1").totalCount(String.valueOf(bulkTransferList.size())).
              totalAmount(totalAmount.stripTrailingZeros().toPlainString()).bulkTransfers(bulkTransferList).build();

      HttpEntity<BulkTransferRequest> request = new HttpEntity<>(bulkTransferRequest, httpHeaders);

      URI uri = UriComponentsBuilder
          .fromHttpUrl(gmoConfig.getStgBaseEndpoint() + gmoConfig.getBulkTransferUri()).build()
          .toUri();

      log.info("{}総合振込依頼 API entity's header :{}", GMO_LOG_PREFIX,
          request.getHeaders().toSingleValueMap());

      log.info("{}総合振込依頼 API entity's body :{}", GMO_LOG_PREFIX,
          request.getBody().toString());

      log.info("{}request {}  for 総合振込依頼 ", GMO_LOG_PREFIX, uri.getPath());

      ResponseEntity<BulkTransferRequestResponse> res = null;
      try {
        res = this.restOperations.postForEntity(uri, request, BulkTransferRequestResponse.class);
        log.info("{} 総合振込依頼 detail is {}", GMO_LOG_PREFIX, res.getBody().toString());
      }catch(HttpClientErrorException e) {
        log.error(e.getMessage());
        throw new GmoRequestException();
      }
      
      return res;
    }
    
    /**
     * 総合振込依頼結果照会(1:完了　２：未完了　8：期限切)
     * @param applyNo 受付番号
     * @return 結果コード 
     * @throws GmoRequestException
     */
    public String getBulktransferRequestResult(String applyNo) throws GmoRequestException {

      String accessToken = getAccessToken();

      // build header
      HttpHeaders httpHeaders = new HttpHeaders();
      httpHeaders.add(CommonConstants.GMO_HEADER_TOKEN_KEY, accessToken);

      String raId = this.accounts(accessToken);

      HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(null, httpHeaders);

      URI uri = UriComponentsBuilder
          .fromHttpUrl(gmoConfig.getStgBaseEndpoint() + gmoConfig.getBulkTransferRequestResultUri()
              + "?accountId=" + raId + "&applyNo=" + applyNo)
          .build().toUri();

      log.info("{}総合振込依頼結果照会 API entity's header :{}", GMO_LOG_PREFIX,
          request.getHeaders().toSingleValueMap());

      log.info("{}request API {} for 総合振込依頼結果照会", GMO_LOG_PREFIX, uri.getPath());

      ResponseEntity<BulkTransferRequestResponse> res = this.restOperations.exchange(uri,
          HttpMethod.GET, request, BulkTransferRequestResponse.class);

      log.info("{} 総合振込依頼結果照会 response detail is {}", GMO_LOG_PREFIX, res.getBody().toString());

      return res.getBody().getResultCode();
    }

    /**
     * ：総合振込状況照会 (2:申請中、3:差戻、4:取下げ、5:期限切れ、8:承認取消/予約取消、 11:予約中、12:手続中、13:リトライ中、
     * 20:手続済、30:不能・組戻あり、40:手続不成立)
     * @param applyNo 受付番号
     * @return 振込ステータス
     * 
     * @throws GmoRequestException
     * 
     */
    public BulkTransferStatusResponse getBulktransferStatus(String applyNo) throws GmoRequestException {

      String accessToken = getAccessToken();

      // build header
      HttpHeaders httpHeaders = new HttpHeaders();
      httpHeaders.add(CommonConstants.GMO_HEADER_TOKEN_KEY, accessToken);

      String raId = this.accounts(accessToken);

      HttpEntity<BulkTransferResultRequest> request = new HttpEntity<>(null, httpHeaders);

      URI uri = UriComponentsBuilder
          .fromHttpUrl(
              gmoConfig.getStgBaseEndpoint() + gmoConfig.getBulkTransferStatusUri() + "?accountId="
                  + raId + "&queryKeyClass=1" + "&detailInfoNecessity=true" + "&applyNo=" + applyNo)
          .build().toUri();

      log.info("{}総合振込状況照会 API entity's header :{}", GMO_LOG_PREFIX,
          request.getHeaders().toSingleValueMap());

      log.info("{}request API {} for 総合振込状況照会", GMO_LOG_PREFIX, uri.getPath());

      ResponseEntity<BulkTransferStatusResponse> res = this.restOperations.exchange(uri,
          HttpMethod.GET, request, BulkTransferStatusResponse.class);

      log.info("{} 総合振込状況照会 response detail is {}", GMO_LOG_PREFIX,
          res.getBody().toString());

       return res.getBody();

    }
}
