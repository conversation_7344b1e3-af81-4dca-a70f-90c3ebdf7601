package exchange.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.entity.CoverOrder;
import exchange.common.entity.CoverOrder_;
import exchange.common.entity.Symbol;
import exchange.common.model.response.PageData;
import exchange.common.predicate.CoverOrderPredicate;

public abstract class CoverOrderService<E extends CoverOrder, P extends CoverOrderPredicate<E>>
    extends EntityService<E, P> implements ApplicationContextAware {

  private static ApplicationContext APPLICATION_CONTEXT;

  @SuppressWarnings("unchecked")
  public static <E extends CoverOrder, P extends CoverOrderPredicate<E>>
      CoverOrderService<E, P> getBean(Exchange exchange, Symbol symbol) {
    return (CoverOrderService<E, P>)
        APPLICATION_CONTEXT.getBean(
            symbol.getTradeType().toLowerCamelCase()
                + "CoverOrder"
                + exchange.toUpperCamelCase()
                + symbol.getCurrencyPair().toUpperCamelCase()
                + "Service");
  }

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    APPLICATION_CONTEXT = applicationContext;
  }

  /*
   * 【運用】predicates作成メソッドは共通化して使用する
   * インデックス順でaddする
   */

  // インデックス: symbol_id, order_type, order_side
  protected List<Predicate> createPredicates(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderSide orderSide) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

    if (orderType != null) {
      predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
    } else if (!ArrayUtils.isEmpty(orderTypes)) {
      predicates.add(predicate.inOrderType(root, orderTypes));
    } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
      predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
    }

    if (orderSide != null) {
      predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
    }

    return predicates;
  }

  private List<Predicate> createPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderSide orderSide,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      Exchange exchange,
      Long exchangeOrderId,
      BigDecimal greaterThanRemainingAmount,
      BigDecimal lessThanRemainingAmount) {
    List<Predicate> predicates =
        createPredicates(
            criteriaBuilder, root, symbolId, orderType, orderTypes, exceptOrderTypes, orderSide);

    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    } else {
      if (idFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
      }

      if (idTo != null) {
        predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
      }

      if (dateFrom != null) {
        predicates.add(
            predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
      }

      if (dateTo != null) {
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
      }

      if (exchange != null) {
        predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
      }

      if (exchangeOrderId != null) {
        predicates.add(predicate.equalExchangeOrderId(criteriaBuilder, root, exchangeOrderId));
      }
      if (greaterThanRemainingAmount != null) {
        predicates.add(predicate.greaterThanOrEqualToRemainingAmount(criteriaBuilder,root, greaterThanRemainingAmount));
      }
      if (lessThanRemainingAmount != null) {
        predicates.add(predicate.lessThanRemainingAmount(criteriaBuilder,root, lessThanRemainingAmount));
      }
    }

    return predicates;
  }

  public List<E> findByCondition(
      Long symbolId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      OrderType orderType,
      OrderSide orderSide,
      Exchange exchange,
      Long exchangeOrderId,
      BigDecimal greaterThanRemainingAmount,
      BigDecimal lessThanRemainingAmount,
      Integer number,
      Integer size,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {

            List<Predicate> predicates =
                createPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    symbolId,
                    orderType,
                    null,
                    null,
                    orderSide,
                    id,
                    idFrom,
                    idTo,
                    dateFrom,
                    dateTo,
                    exchange,
                    exchangeOrderId,
                    greaterThanRemainingAmount,
                    lessThanRemainingAmount);

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                number,
                size,
                isAscending
                    ? criteriaBuilder.asc(root.get(CoverOrder_.id))
                    : criteriaBuilder.desc(root.get(CoverOrder_.id)));
          }
        });
  }

  public List<E> findByCondition(Long symbolId, Integer number, Integer size) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                number,
                size,
                criteriaBuilder.asc(root.get(CoverOrder_.id)));
          }
        });
  }

  public List<E> findUpdatable(Long symbolId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
            predicates.add(
                predicate.greaterThanRemainingAmount(criteriaBuilder, root, BigDecimal.ZERO));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(CoverOrder_.id)));
          }
        });
  }

  public PageData<E> findByConditionPageData(
      Long symbolId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      OrderType orderType,
      OrderSide orderSide,
      Exchange exchange,
      Long exchangeOrderId,
      BigDecimal greaterThanRemainingAmount,
      BigDecimal lessThanRemainingAmount,
      Integer number,
      Integer size) {

    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        orderType,
                        null,
                        null,
                        orderSide,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        exchange,
                        exchangeOrderId,
                        greaterThanRemainingAmount,
                        lessThanRemainingAmount));
              }
            });

    return new PageData<E>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, List<E>>() {
              @Override
              public List<E> query() {
                List<Predicate> predicates =
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        orderType,
                        null,
                        null,
                        orderSide,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        exchange,
                        exchangeOrderId,
                        greaterThanRemainingAmount,
                        lessThanRemainingAmount);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(CoverOrder_.id)));
              }
            }));
  }

  public E add(
      Long symbolId,
      OrderSide orderSide,
      OrderType orderType,
      BigDecimal price,
      BigDecimal amount,
      BigDecimal fee,
      Exchange exchange,
      Long exchangeOrderId) {
    E coverOrder = newEntity();
    coverOrder.setProperties(
        symbolId, orderSide, orderType, price, amount, fee, exchange, exchangeOrderId);
    return save(coverOrder);
  }
}
