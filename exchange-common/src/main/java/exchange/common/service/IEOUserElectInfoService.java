package exchange.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.DataSourceManager;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.IEOHasOrHasNotChanged;
import exchange.common.constant.IEOMailStatus;
import exchange.common.entity.IEOUserElectInfo;
import exchange.common.entity.IEOUserElectInfo_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.IEOUserElectInfoPredicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class IEOUserElectInfoService extends EntityService<IEOUserElectInfo, IEOUserElectInfoPredicate> {

  private final DataSourceManager dataSourceManager;

  @Override
  public Class<IEOUserElectInfo> getEntityClass() {
    return IEOUserElectInfo.class;
  }

  @Override
  protected void fetch(Root<IEOUserElectInfo> root) {
    super.fetch(root);
    root.fetch(IEOUserElectInfo_.user, JoinType.INNER);
  }

  /**
   * ユーザーモードの場合、一覧を取得
   * 
   * @param ieoRecruitInfoId
   * @param userIds
   * @param excludeUserIds
   * @param ieoMailStatus
   * @param valueOfName
   * @param valueOfName2
   * @param dateFrom
   * @param dateTo
   * @param number
   * @param size
   * @return
   */
  public PageData<IEOUserElectInfo> findByConditionUserModePageData(Long ieoRecruitInfoId,
      List<Long> userIds, List<Long> excludeUserIds,
      IEOHasOrHasNotChanged hasChanged, IEOMailStatus ieoMailStatus, Long dateFrom, Long dateTo, Integer number, Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(entityManager, criteriaBuilder, criteriaQuery, root,
                    getPredicatesOfFindByCondition(criteriaBuilder, root, ieoRecruitInfoId, userIds, excludeUserIds,
                        hasChanged, ieoMailStatus, dateFrom, dateTo));
              }
            });

    return new PageData<IEOUserElectInfo>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<IEOUserElectInfo, List<IEOUserElectInfo>>() {
              @Override
              public List<IEOUserElectInfo> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(criteriaBuilder, root, ieoRecruitInfoId, userIds, excludeUserIds,
                        hasChanged, ieoMailStatus, dateFrom, dateTo);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(IEOUserElectInfo_.id)));
              }
            }));
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<IEOUserElectInfo> root, Long ieoRecruitInfoId, List<Long> userIds,
      List<Long> excludeUserIds, 
      IEOHasOrHasNotChanged hasChanged, IEOMailStatus ieoMailStatus, Long dateFrom, Long dateTo) {
List<Predicate> predicates = new ArrayList<>();
    
    if (ieoRecruitInfoId != null) {
      predicates.add(predicate.equalIeoRecruitInfoId(criteriaBuilder, root, ieoRecruitInfoId));
    }

    if (!CollectionUtils.isEmpty(userIds)) {
      predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
    }
    
    if (!CollectionUtils.isEmpty(excludeUserIds)) {
      predicates.add(predicate.notInUserIds(criteriaBuilder, root, excludeUserIds));
    }
    
    if (hasChanged != null) {
      predicates.add(predicate.equalHasChangeElectAmount(criteriaBuilder, root, hasChanged));
    }
    
    if (ieoMailStatus != null) {
        predicates.add(predicate.equalIEOMailStatus(criteriaBuilder, root, ieoMailStatus));
      }

    if (dateFrom != null) {
      predicates
          .add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
    }

    if (dateTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
    }

    return predicates;
  }
  
  /**
   * 合計の抽選対象数を取得
   * @param userId
   * @param id
   * @return
   */
public BigDecimal getSumElectTargetAmount(Long id) {
    
    BigDecimal sumUserApplyAmount = new BigDecimal(0);
    String sql = "SELECT sum(total_elect_target_amount) FROM `ieo_user_elect_info` where ieo_recruit_info_id = :id";
    
    EntityManager entityManager =
            dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    try {
    	Query query = entityManager.createNativeQuery(sql);
        query.setParameter("id", id);
        @SuppressWarnings("unchecked")
        List<BigDecimal> list = query.getResultList();
        if(list != null && list.size() > 0) {
          if(list.get(0) != null) {
            sumUserApplyAmount = list.get(0);
          }
        }
    }finally {
        entityManager.clear();
        entityManager.close();
      }
    return sumUserApplyAmount;
  }

/**
 * 合計の当選数を取得
 * @param userId
 * @param id
 * @return
 */
public BigDecimal getSumElectWinAmount(Long id, Long userId) {
  
  BigDecimal sumUserApplyAmount = new BigDecimal(0);
  String sql = "SELECT sum(total_elect_win_amount) FROM `ieo_user_elect_info` where ieo_recruit_info_id = :id and user_id <> :uid";
  
  EntityManager entityManager =
          dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
  try {
	  Query query = entityManager.createNativeQuery(sql);
	  query.setParameter("id", id);
	  query.setParameter("uid", userId);
	  @SuppressWarnings("unchecked")
	  List<BigDecimal> list = query.getResultList();
	  if(list != null && list.size() > 0) {
	    if(list.get(0) != null) {
	      sumUserApplyAmount = list.get(0);
	    }
	  }
  }finally {
      entityManager.clear();
      entityManager.close();
    }
  return sumUserApplyAmount;
}

  /**
   * 抽選対象情報を取得
   * 
   * @param id
   * @return
   */
  public List<IEOUserElectInfo> findByCondition(Long ieoRecruitInfoId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<IEOUserElectInfo, List<IEOUserElectInfo>>() {
          @Override
          public List<IEOUserElectInfo> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (ieoRecruitInfoId != null) {
              predicates.add(predicate.equalIeoRecruitInfoId(criteriaBuilder, root, ieoRecruitInfoId));
            }
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(IEOUserElectInfo_.id)));
          }
        });
  }

  /**
   * ダウンロードCSV・ユーザーモードを取得
   * @param ieoRecruitInfoId
   * @param userIds
   * @param excludeUserIds
   * @param valueOfName
   * @param dateFrom
   * @param dateTo
   * @return
   */
  public List<IEOUserElectInfo> findByCondition(Long ieoRecruitInfoId, List<Long> userIds,
      List<Long> excludeUserIds, IEOHasOrHasNotChanged hasChanged, IEOMailStatus ieoMailStatus, Long dateFrom, Long dateTo) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<IEOUserElectInfo, List<IEOUserElectInfo>>() {
          @Override
          public List<IEOUserElectInfo> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                ieoRecruitInfoId, userIds, excludeUserIds, hasChanged, ieoMailStatus, dateFrom, dateTo);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(IEOUserElectInfo_.id)));
          }
        });
  }
  
}