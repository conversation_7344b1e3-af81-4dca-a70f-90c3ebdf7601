package exchange.common.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.CustomRedisTemplate;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.TradeType;
import exchange.common.entity.Candlestick;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.model.response.OrderbookData;
import exchange.common.model.response.OrderbookData.Orderbook;
import exchange.common.websocket.RedisPublisher;
import exchange.pos.model.PosBestPriceData;
import exchange.pos.service.PosCandlestickService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class OrderbookService {

  private static String getCacheKey(Symbol symbol) {
    return "orderbook:" + symbol.getName();
  }

  private static String getCacheKey(Exchange exchange, Symbol symbol) {
    return "orderbook:" + exchange + ":" + symbol.getName();
  }

  private static String getPosCacheKey(Symbol symbol){
    return ("best_price:" + Exchange.AMBER + ":" + symbol.getName()).toLowerCase();
  }

  private final CustomRedisTemplate<OrderbookData> orderbookDataRedisTemplate;

  private final CustomRedisTemplate<PosBestPriceData> bestPriceRedisTemplate;

  private final SymbolService symbolService;

  private final RedisPublisher redisPublisher;
  
  private final CurrencyPairConfigService currencyPairConfigService;

  public OrderbookData get(Symbol symbol) {
    return orderbookDataRedisTemplate.getValue(getCacheKey(symbol));
  }
   public PosBestPriceData getPos(Symbol symbol) {
    return bestPriceRedisTemplate.getValue(getPosCacheKey(symbol));
  }

  public OrderbookData get(Exchange exchange, Symbol symbol) {
    return orderbookDataRedisTemplate.getValue(getCacheKey(exchange, symbol));
  }

  public void set(Symbol symbol, OrderbookData orderbook) {
    orderbookDataRedisTemplate.setUnexpireValue(getCacheKey(symbol), orderbook);
    // Pub to websocket through redis
    redisPublisher.publish(orderbook);
  }

  public void set(Exchange exchange, Symbol symbol, OrderbookData orderbookData) {
    orderbookDataRedisTemplate.setUnexpireValue(getCacheKey(exchange, symbol), orderbookData);
  }

  public BigDecimal getBestBid(Symbol symbol) throws Exception {
    OrderbookData orderbookData = get(symbol);

    if (orderbookData == null) {
      throw new CustomException(ErrorCode.SYSTEM_ERROR_ORDERBOOK_IS_NULL);
    }

    return orderbookData.getBestBid();
  }

  private final PosCandlestickService posCandlestickService;
 
  public BigDecimal getBestBidOfQuoteJpy(Currency baseCurrency) throws Exception {
//    if (baseCurrency == Currency.JPY) {
//      return BigDecimal.ONE;
//    }
//    if (baseCurrency == Currency.ETH) {
//      // ETH SYMBOL ID:7
//      Candlestick latest = posCandlestickService.findOneByCondition(7l, CandlestickType.P1D, null,
//          null, false);
//      if(latest == null) {
//        return BigDecimal.ZERO;
//      }
//      return latest.getClose();
//    }
//
//    OrderbookData orderbookData =
//        get(
//            symbolService.findByCondition(
//                TradeType.SPOT, CurrencyPair.valueOf(baseCurrency, Currency.JPY)));
//    if (orderbookData == null) {
//      // 板が空の場合はエラーとせず円価0換算
//      return BigDecimal.ZERO;
//    }
//    return orderbookData.getBestBid();

    if (baseCurrency == Currency.JPY) {
      return BigDecimal.ONE;
    }
    OrderbookData orderbookData = null;

    // UNYO-758 NIDTの場合、CBから取得、以外の場合、whaleFinから取得
    if (Currency.NIDT.equals(baseCurrency)) {
      orderbookData = get(symbolService.findByCondition(TradeType.SPOT,
          CurrencyPair.valueOf(baseCurrency, Currency.JPY)));
      if (orderbookData == null) {
        // 板が空の場合はエラーとせず円価0換算
        return BigDecimal.ZERO;
      }
      return orderbookData.getBestBid();
    } else {
      List<CurrencyPairConfig> currencyPairConfig = currencyPairConfigService
          .findAllByCondition(TradeType.POS, CurrencyPair.valueOf(baseCurrency, Currency.JPY), true);
      if (CollectionUtils.isEmpty(currencyPairConfig)) {
        if (baseCurrency == Currency.ETH) {
          // ETH SYMBOL ID:7
          Candlestick latest =
              posCandlestickService.findOneByCondition(7l, CandlestickType.P1D, null, null, false, false);
          if (latest == null) {
            return BigDecimal.ZERO;
          }
          return latest.getClose();
        } else {
          Symbol symbol = symbolService.findByCondition(TradeType.SPOT,
              CurrencyPair.valueOf(baseCurrency, Currency.JPY));
          if (symbol != null) {
            orderbookData = get(symbol);
            if (orderbookData == null) {
              // 板が空の場合はエラーとせず円価0換算
              return BigDecimal.ZERO;
            }
            return orderbookData.getBestBid();
          } else {
            return BigDecimal.ZERO;
          }
          
        }
      } else {
        PosBestPriceData posBestPriceData = getPos(symbolService.findByCondition(TradeType.POS,
            CurrencyPair.valueOf(baseCurrency, Currency.JPY)));
        if (posBestPriceData == null) {
          return BigDecimal.ZERO;
        }
        return posBestPriceData.getBestBid();
      }

    }

  }

  /** 桁数処理後の同一価格でサマリ・ソート */
  public OrderbookData sumOrderbook(
      Symbol symbol, List<Orderbook> asksBefore, List<Orderbook> bidsBefore) {
    CopyOnWriteArrayList<Orderbook> asks = new CopyOnWriteArrayList<>();
    CopyOnWriteArrayList<Orderbook> bids = new CopyOnWriteArrayList<>();

    // 桁数処理してサマリ
    // ※桁数処理後で0以下は対象から除外
    addOrderbook(symbol, asks, asksBefore, OrderSide.SELL);
    addOrderbook(symbol, bids, bidsBefore, OrderSide.BUY);

    // asks(売り板)昇順ソート, bids(買い板)降順ソート ※worker.OrderbookMakerと同じ
    // 除外後に空の場合はsortもスキップ
    if (!CollectionUtils.isEmpty(asks)) {
      asks.sort((o1, o2) -> o1.getPrice().compareTo(o2.getPrice()));
    }

    if (!CollectionUtils.isEmpty(bids)) {
      bids.sort((o1, o2) -> o2.getPrice().compareTo(o1.getPrice()));
    }

    return new OrderbookData(symbol.getId(), asks, bids);
  }

  private void addOrderbook(
      Symbol symbol,
      List<Orderbook> newOrderbooks,
      List<Orderbook> oldOrderbooks,
      OrderSide orderSide) {
    // 価格昇順ソート
    oldOrderbooks.sort((o1, o2) -> o1.getPrice().compareTo(o2.getPrice()));

    // 価格昇順で桁数処理後の同一価格をサマリ
    for (Orderbook oldOrderbook : oldOrderbooks) {
      // 桁数処理
      // 価格：asks売り板切り上げ、bids買い板切り捨て
      // ※切り上げは単純切り上げ (例：小数点以下0桁とするとき、120.1=> 121, 120.01も121となる)
      BigDecimal oldOrderbookPriceScaled =
          (orderSide == OrderSide.SELL)
              ? symbol.getCurrencyPair().getScaledPrice(oldOrderbook.getPrice(), RoundingMode.UP)
              : symbol.getCurrencyPair().getScaledPrice(oldOrderbook.getPrice(), RoundingMode.DOWN);
      BigDecimal oldOrderbookAmountScaled =
          symbol.getCurrencyPair().getScaledAmount(oldOrderbook.getAmount(), RoundingMode.DOWN);

      // 桁数処理後で0以下は注文対象から除外
      if (oldOrderbookPriceScaled.compareTo(BigDecimal.ZERO) <= 0
          || oldOrderbookAmountScaled.compareTo(BigDecimal.ZERO) <= 0) {
        continue;
      }

      if (!CollectionUtils.isEmpty(newOrderbooks)) {
        // oldOrderbooksは価格昇順ソート済み。newOrderbooksからリストの最後(最も高い価格)を取得
        Orderbook orderbook = newOrderbooks.get(newOrderbooks.size() - 1);

        if (orderbook.getPrice().compareTo(oldOrderbookPriceScaled) == 0) {
          // 同一価格の板を合算
          // 複数のコピー元外部板の場合も、同一価格は単純合算する
          orderbook.setAmount(orderbook.getAmount().add(oldOrderbookAmountScaled));
        } else {
          newOrderbooks.add(new Orderbook(oldOrderbookPriceScaled, oldOrderbookAmountScaled));
        }
      } else {
        newOrderbooks.add(new Orderbook(oldOrderbookPriceScaled, oldOrderbookAmountScaled));
      }
    }
  }

  public boolean isMinusSpread(OrderbookData orderbookData) {
    // Asks:売り板 => 昇順sort済み => ベストプライス = 最安値
    // Bids:買い板 => 降順sort済み => ベストプライス = 最高値
    // ベストBid > ベストAskならマイナススプレッド
    if (orderbookData
            .getBids()
            .get(0)
            .getPrice()
            .compareTo(orderbookData.getAsks().get(0).getPrice())
        > 0) {
      return true;
    }
    return false;
  }
}
