package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Currency;
import exchange.common.entity.StakingPool;
import exchange.common.predicate.StakingPoolPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class StakingPoolService extends EntityService<StakingPool, StakingPoolPredicate> {

  @Override
  public Class<StakingPool> getEntityClass() {
    return StakingPool.class;
  }

  public StakingPool findByCondition(Currency currency) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingPool, StakingPool>() {
          @Override
          public StakingPool query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
}
