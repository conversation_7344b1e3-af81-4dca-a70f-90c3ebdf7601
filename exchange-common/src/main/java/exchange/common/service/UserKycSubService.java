package exchange.common.service;

import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.UserKycSub;
import exchange.common.entity.UserKycSub_;
import exchange.common.predicate.UserKycSubPredicate;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;

@Service
public class UserKycSubService extends EntityService<UserKycSub, UserKycSubPredicate> {

  @Override
  public Class<UserKycSub> getEntityClass() {
    return UserKycSub.class;
  }

  public List<UserKycSub> findByKycId(Long kycId) {
    return
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<>() {
              @Override
              public List<UserKycSub> query() {
                final var predicates = new ArrayList<Predicate>();

                if (kycId != null) {
                  predicates.add(predicate.equalKycId(criteriaBuilder, root, kycId));
                }

                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.asc(root.get(UserKycSub_.id)));
              }
            });
  }
}
