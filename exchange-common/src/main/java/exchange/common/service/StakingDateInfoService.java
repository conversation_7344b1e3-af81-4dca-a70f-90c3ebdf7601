package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.config.WalletConfig;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.entity.StakingDateInfo;
import exchange.common.exception.CustomException;
import exchange.common.predicate.StakingDateInfoPredicate;
import exchange.common.repos.StakingDateInfoRepository;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class StakingDateInfoService extends EntityService<StakingDateInfo, StakingDateInfoPredicate> {
  
  private final StakingDateInfoRepository stakingDateInfoRepository;
  private final WalletConfig walletConfig;

  @Override
  public Class<StakingDateInfo> getEntityClass() {
    return StakingDateInfo.class;
  }

  @Override
  protected void fetch(Root<StakingDateInfo> root) {
    super.fetch(root);
  }
  
  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<StakingDateInfo> root, String currency, Date stakingDate) {
    List<Predicate> predicates = new ArrayList<>();
    
    if (Currency.valueOfName(currency) != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, Currency.valueOfName(currency)));
    }
    
    if (stakingDate != null) {
      predicates.add(predicate.equalStakingDate(criteriaBuilder, root, stakingDate));
    }
    
    return predicates;
  }
  
  public List<StakingDateInfo> findByCondition(String currency, Date stakingDate) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingDateInfo, List<StakingDateInfo>>() {
          @Override
          public List<StakingDateInfo> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByCondition(criteriaBuilder, root, 
                    currency, 
                    stakingDate);
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  // stake実施日
  public Date getStakingDate(Currency currency) throws CustomException {
    long stakeDate = System.currentTimeMillis();
    long current = System.currentTimeMillis();
    
    if(Currency.ETH.equals(currency)) {
      String currentDateString = FormatUtil.formatJst(new Date(), FormatPattern.YYYYMMDD);
      Date currentDate = FormatUtil.parseJst(currentDateString, FormatPattern.YYYYMMDD);
      long todayFrom = currentDate.getTime();
      if(todayFrom + 1000*3600*walletConfig.getStakingPrepareTime() <= current) {
        stakeDate = todayFrom + 1000*3600*24;
      } else {
        stakeDate = todayFrom;
      }
    } else {
      Date latestStakeDate = stakingDateInfoRepository.latestStakeDate(currency.name());
      if(latestStakeDate == null) {
        Date latestStakeDateOld = stakingDateInfoRepository.latestStakeDateOld(currency.name());
        if(latestStakeDateOld != null) {
          String stakeDateOldString = FormatUtil.formatJst(latestStakeDateOld, FormatPattern.YYYYMMDD);
          Date stakeDateOldDate = FormatUtil.parseJst(stakeDateOldString, FormatPattern.YYYYMMDD);
          long stakeDateOldLong = stakeDateOldDate.getTime();
          while(stakeDateOldLong < current) {
            stakeDateOldLong = stakeDateOldLong + 1000*3600*24*5;
          }
          long stakeDateAdd = stakeDateOldLong - 1000*3600*24;
          if(stakeDateAdd + 1000*3600*walletConfig.getStakingPrepareTime() <= current) {
            stakeDate = stakeDateAdd + 1000*3600*24*5;
          } else {
            stakeDate = stakeDateAdd;
          }
        } else {
          throw new CustomException(ErrorCode.REQUEST_ERROR_STAKING_STATUS_ERROR);
        }
      } else {
        String stakeDateString = FormatUtil.formatJst(latestStakeDate, FormatPattern.YYYYMMDD);
        Date stakeDateDate = FormatUtil.parseJst(stakeDateString, FormatPattern.YYYYMMDD);
        long stakeDateLong = stakeDateDate.getTime();
        long stakeDateAdd = stakeDateLong - 1000 * 3600 * 24;
        if(stakeDateAdd + 1000*3600*walletConfig.getStakingPrepareTime() <= current) {
          stakeDate = stakeDateAdd + 1000*3600*24*5;
        } else {
          stakeDate = stakeDateAdd;
        }
      }
    }
    return new Date(stakeDate);
  }
  
}
