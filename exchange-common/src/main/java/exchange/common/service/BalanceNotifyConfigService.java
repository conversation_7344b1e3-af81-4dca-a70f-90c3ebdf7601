package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Exchange;
import exchange.common.entity.BalanceNotifyConfig;
import exchange.common.entity.BalanceNotifyConfig_;
import exchange.common.predicate.BalanceNotifyConfigPredicate;

@Service
public class BalanceNotifyConfigService extends EntityService<BalanceNotifyConfig, BalanceNotifyConfigPredicate> {

  @Override
  public Class<BalanceNotifyConfig> getEntityClass() {
    return BalanceNotifyConfig.class;
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder, Root<BalanceNotifyConfig> root, String currency, Long userId, Exchange exchange, Boolean enabled) {
    List<Predicate> predicates = new ArrayList<>();

    if (currency != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
    }
    
    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }
    
    if (exchange != null) {
      predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
    }
    if (enabled != null) {
      predicates.add(predicate.equalEnabled(criteriaBuilder, root, enabled));
    }
    return predicates;
  }

  public List<BalanceNotifyConfig> findAllByCondition(String currency, Long userId,  Exchange exchange, Boolean enabled) {
    List<BalanceNotifyConfig> balanceNotifyConfig =
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<BalanceNotifyConfig, List<BalanceNotifyConfig>>() {

              @Override
              public List<BalanceNotifyConfig> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(criteriaBuilder, root, currency, userId, exchange, enabled);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.asc(root.get(BalanceNotifyConfig_.id)));
              }
            });

    return balanceNotifyConfig;
  }
}
