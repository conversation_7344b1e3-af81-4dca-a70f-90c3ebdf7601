package exchange.common.service.dowjones;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import exchange.common.component.DowJonesManager;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.RedisClient;
import exchange.common.config.DowJonesConfig;
import exchange.common.constant.DowJonesUserRelationDataTypeEnum;
import exchange.common.constant.UserTypeEnum;
import exchange.common.dal.master.entity.dowjones.DowJonesSamCaseDO;
import exchange.common.dal.master.entity.user.*;
import exchange.common.dal.master.mapper.dowjones.DowJonesSamCaseMapper;
import exchange.common.dal.master.mapper.user.*;
import exchange.common.entity.dowjones.DowJonesSamCase;
import exchange.common.exception.DowJonesAntisocialCheckException;
import exchange.common.model.response.dowjones.DowJonesCaseCreateRes;
import exchange.common.model.response.dowjones.DowJonesCaseGetMatchesRes;
import exchange.common.predicate.dowjones.DowJonesSamCasePredicate;
import exchange.common.service.EntityService;
import exchange.common.service.dowjones.dto.DowJonesUserRelationDataInfoDTO;
import exchange.common.util.IdUtil;
import exchange.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DowJonesSamCaseService extends EntityService<DowJonesSamCase, DowJonesSamCasePredicate> {

	private final RedisClient redisClient;
	private final UserMapper userMapper;
	private final UserInfoMapper userInfoMapper;
	private final UserInfoCorporateMapper userInfoCorporateMapper;
	private final UserInfoCorporateRepresentativeMapper userInfoCorporateRepresentativeMapper;
	private final UserInfoCorporateAgentMapper userInfoCorporateAgentMapper;
	private final UserInfoCorporateOwnerMapper userInfoCorporateOwnerMapper;
	private final DowJonesManager dowJonesManager;
	private final DowJonesSamCaseMapper dowJonesSamCaseMapper;
	private final DowJonesSamAssociationService dowJonesSamAssociationService;
	private final DowJonesConfig dowJonesConfig;

	private String getCaseUpdateKey(Long userId) {
		return String.format("dowjones:case:update:%s", userId);
	}

	@Override
	public Class<DowJonesSamCase> getEntityClass() {
		return DowJonesSamCase.class;
	}

	public void asyncUpdateCase(Long userId) {
		asyncUpdateCase(IdUtil.getUuid(), userId);
	}
	public void asyncUpdateCase(String tid, Long userId) {
		try {
			log.info("[tid={}] asyncUpdateCase start. userId={}", tid, userId);
			this.checkRiskOrUpdateCase(tid, userId);
			log.info("[tid={}] asyncUpdateCase end. userId={}", tid, userId);
		} catch (Exception e) {
			log.warn("[tid={}] asyncUpdateCase error. userId={}", tid, userId, e);
		}
	}

	public Boolean checkRiskOrUpdateCase(String tid, Long userId) throws Exception {
		String lockKey = getCaseUpdateKey(userId);
		try {
			if (!redisClient.tryLock(lockKey)) {
				log.info("[tid={}] checkRiskOrUpdateCase get redis lock failed. userId={}", tid, userId);
				return null;
			}
			// process case
			DowJonesSamCaseDO caseDO = findOrUpdateCase(tid, userId);

			// build UserRelationDataInfoList
			List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = buildUserRelationDataInfoList(tid, userId);

			// process association
			boolean flag = dowJonesSamAssociationService.batchUpdateOrDelete(tid, caseDO.getId(), caseDO.getDowJonesSamCaseId(), userRelationDataInfoDTOList);
			if (flag) {
				dowJonesSamCaseMapper.update(null, new LambdaUpdateWrapper<DowJonesSamCaseDO>()
						.eq(DowJonesSamCaseDO::getId, caseDO.getId())
						.set(DowJonesSamCaseDO::getUserInfoSyncTime, new Date()));

				return null;
			}

			return checkRisk(tid, userId);
		} finally {
			redisClient.unlock(lockKey);
		}
	}

	public Boolean checkRisk(String tid, Long userId) throws Exception {
		DowJonesSamCaseDO caseDO = dowJonesSamCaseMapper.findOneByUserId(userId);
		if (null == caseDO) {
			return null;
		}
		// check risk
//		DowJonesCaseGetRes dowJonesCaseGetRes = dowJonesManager.getCase(tid, caseDO.getDowJonesSamCaseId());
		DowJonesCaseGetMatchesRes dowJonesCaseGetMatchesRes = dowJonesManager.caseGetMatches(tid, caseDO.getDowJonesSamCaseId());

		// update case
		Date now = new Date();

		DowJonesSamCaseDO caseUpdateDO = new DowJonesSamCaseDO();
		caseUpdateDO.setId(caseDO.getId());
		caseUpdateDO.setHasRisk(dowJonesCaseGetMatchesRes.getHasAlerts());
		caseUpdateDO.setUpdatedAt(now);
		dowJonesSamCaseMapper.updateById(caseUpdateDO);
		return dowJonesCaseGetMatchesRes.getHasAlerts();
	}

	private DowJonesSamCaseDO findOrUpdateCase(String tid, Long userId) throws Exception {
		DowJonesSamCaseDO caseDO = dowJonesSamCaseMapper.findOneByUserId(userId);
		if (null != caseDO) {
			if (!dowJonesConfig.getSearchType().getType().equalsIgnoreCase(caseDO.getSearchType())) {
				dowJonesManager.updateCase(tid, userId, caseDO.getDowJonesSamCaseId());
				dowJonesSamCaseMapper.updateSearchTypeById(caseDO.getId(), dowJonesConfig.getSearchType().getType());
			}
			return caseDO;
		}

		DowJonesCaseCreateRes dowJonesCaseCreateRes = dowJonesManager.createCase(tid, userId);

		if (null == dowJonesCaseCreateRes || null == dowJonesCaseCreateRes.getData() || null == dowJonesCaseCreateRes.getData().getAttributes()) {
			log.error("[tid={}] findOrCreateCase dow jones api result error. userId={},dowJonesCaseCreateRes={}", tid, userId, JsonUtil.encode(dowJonesCaseCreateRes));
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] dow jones api result error.", tid));
		}
		DowJonesCaseCreateRes.Attributes attributes = dowJonesCaseCreateRes.getData().getAttributes();

		UserDO userDO = userMapper.selectById(userId);
		if (null == userDO) {
			log.error("[tid={}] findOrCreateCase userId={} not exist", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s not exist", tid, userId));
		}

		Integer userType = (null != userDO.getUserInfoId()) ? UserTypeEnum.PERSONAL.getType() : UserTypeEnum.CORPORATE.getType();
		Date now = new Date();

		caseDO = new DowJonesSamCaseDO();
		caseDO.setDowJonesSamCaseId(dowJonesCaseCreateRes.getCaseId());
		caseDO.setDowJonesSamCaseName(attributes.getCaseName());
		caseDO.setDowJonesSamExternalId(attributes.getExternalId());
		caseDO.setUserId(userId);
		caseDO.setUserType(userType);
		caseDO.setSearchType(dowJonesConfig.getSearchType().getType());
		caseDO.setEnabled(Boolean.TRUE);
		caseDO.setCreatedAt(now);
		caseDO.setUpdatedAt(now);
		dowJonesSamCaseMapper.insert(caseDO);

		return caseDO;
	}

	private List<DowJonesUserRelationDataInfoDTO> buildUserRelationDataInfoList(String tid, Long userId) throws Exception {
		UserDO userDO = userMapper.selectById(userId);
		if (null == userDO) {
			log.error("[tid={}] buildUserRelationDataInfoList userId={} not exist", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s not exist", tid, userId));
		}
		if (null == userDO.getUserInfoId() && null == userDO.getUserInfoCorporateId()) {
			log.error("[tid={}] buildUserRelationDataInfoList userId={} data error", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s data error", tid, userId));
		}
		UserTypeEnum userType = (null != userDO.getUserInfoId()) ? UserTypeEnum.PERSONAL : UserTypeEnum.CORPORATE;

		// 個人ユーザー
		if (userType == UserTypeEnum.PERSONAL) {
			UserInfoDO userInfoDO = userInfoMapper.selectById(userDO.getUserInfoId());
			if (null == userInfoDO) {
				log.error("[tid={}] buildUserRelationDataInfoList userId={},userInfoId={} not exist", tid, userId, userDO.getUserInfoId());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,userInfoId=%s not exist", tid, userId, userDO.getUserInfoId()));
			}
			List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = Lists.newArrayList();
			// 個人
			userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
					.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.USER.getType())
					.setUserRelationDataId(userInfoDO.getId())
					.setFirstName(userInfoDO.getFirstName())
					.setLastName(userInfoDO.getLastName())
					.setBirthday(userInfoDO.getBirthday())
					.setGender(userInfoDO.getGender())
			);
			return userRelationDataInfoDTOList;
		}

		// 法人ユーザー
		UserInfoCorporateDO userInfoCorporateDO = userInfoCorporateMapper.selectById(userDO.getUserInfoCorporateId());
		if (null == userInfoCorporateDO) {
			log.error("[tid={}] buildUserRelationDataInfoList userId={},userInfoCorporateId={} not exist", tid, userId, userDO.getUserInfoCorporateId());
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,userInfoCorporateId=%s not exist", tid, userId, userDO.getUserInfoCorporateId()));
		}

		List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = Lists.newArrayList();
		// 法人
		userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
				.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.CORPORATE.getType())
				.setUserRelationDataId(userInfoCorporateDO.getId())
				.setName(userInfoCorporateDO.getName())
		);
		if (null != userInfoCorporateDO.getRepresentativeId()) {
			UserInfoCorporateRepresentativeDO userInfoCorporateRepresentativeDO = userInfoCorporateRepresentativeMapper.selectById(userInfoCorporateDO.getRepresentativeId());
			if (null == userInfoCorporateRepresentativeDO) {
				log.error("[tid={}] buildUserRelationDataInfoList hasRisk userId={},representativeId={} not exist", tid, userId, userInfoCorporateDO.getRepresentativeId());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,representativeId=%s not exist", tid, userId, userInfoCorporateDO.getRepresentativeId()));
			} else {
				// 代表者
				userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.REPRESENTATIVE.getType())
						.setUserRelationDataId(userInfoCorporateRepresentativeDO.getId())
						.setFirstName(userInfoCorporateRepresentativeDO.getFirstName())
						.setLastName(userInfoCorporateRepresentativeDO.getLastName())
						.setBirthday(userInfoCorporateRepresentativeDO.getBirthday())
						.setGender(userInfoCorporateRepresentativeDO.getGender())
				);
			}
		}

		if (null != userInfoCorporateDO.getAgentId()) {
			UserInfoCorporateAgentDO userInfoCorporateAgentDO = userInfoCorporateAgentMapper.selectById(userInfoCorporateDO.getAgentId());
			if (null == userInfoCorporateAgentDO) {
				log.error("[tid={}] buildUserRelationDataInfoList userId={},agentId={} not exist", tid, userId, userInfoCorporateDO.getAgentId());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,agentId=%s not exist", tid, userId, userInfoCorporateDO.getAgentId()));
			} else {
				// 取引担当者
				userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.AGENT.getType())
						.setUserRelationDataId(userInfoCorporateAgentDO.getId())
						.setFirstName(userInfoCorporateAgentDO.getFirstName())
						.setLastName(userInfoCorporateAgentDO.getLastName())
						.setBirthday(userInfoCorporateAgentDO.getBirthday())
				);
			}
		}

		if (StringUtils.isNotBlank(userInfoCorporateDO.getOwnerIds())) {
			String[] ownerIdStrArray = userInfoCorporateDO.getOwnerIds().split(",");
			Set<Long> ownerIds = Stream.of(ownerIdStrArray).map(Long::valueOf).collect(Collectors.toSet());
			List<UserInfoCorporateOwnerDO> userInfoCorporateOwnerDOList = userInfoCorporateOwnerMapper.selectBatchIds(ownerIds);
			if (CollectionUtils.isEmpty(userInfoCorporateOwnerDOList)) {
				log.error("[tid={}] buildUserRelationDataInfoList userId={},ownerIds={} not exist", tid, userId, userInfoCorporateDO.getOwnerIds());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,ownerIds=%s not exist", tid, userId, userInfoCorporateDO.getOwnerIds()));
			} else if (userInfoCorporateOwnerDOList.size() != ownerIdStrArray.length) {
				log.error("[tid={}] buildUserRelationDataInfoList userId={},ownerIds={} section data not exist", tid, userId, userInfoCorporateDO.getOwnerIds());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,ownerIds=%s section data not exist", tid, userId, userInfoCorporateDO.getOwnerIds()));
			} else {
				userRelationDataInfoDTOList.addAll(userInfoCorporateOwnerDOList.stream().map(owner -> new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.OWNER.getType())
						.setUserRelationDataId(owner.getId())
						.setFirstName(owner.getFirstName())
						.setLastName(owner.getLastName())
						.setBirthday(owner.getBirthday())
				).toList());
			}
		}
		return userRelationDataInfoDTOList;
	}

		/**
	 * Find or create Dow Jones SAM case
	 *
	 * @param tid Transaction ID
	 * @param userType User type
	 * @return DowJonesSamCase instance
	 * @throws Exception Processing exception
	 */
	public DowJonesSamCase findByDowJonesSamCase(String tid, UserTypeEnum userType) throws Exception {
		log.info("[tid={}] Starting to find Dow Jones SAM case, userType={}", tid, userType);

		try {
			List<DowJonesSamCase> dowJonesSamCaseList = findExistingInactiveCases(tid, userType);

			if (CollectionUtils.isEmpty(dowJonesSamCaseList)) {
				log.info("[tid={}] No existing case found, creating new case, userType={}", tid, userType);
				return createNewSamCase(tid, userType);
			} else {
				DowJonesSamCase existingCase = dowJonesSamCaseList.get(0);
				log.info("[tid={}] Found existing case, caseId={}, userType={}, associationCount={}",
						tid, existingCase.getDowJonesSamCaseId(), userType, existingCase.getAssociationOccupyCount());
				return existingCase;
			}
		} catch (Exception e) {
			log.error("[tid={}] Failed to find or create Dow Jones SAM case, userType={}, error={}",
					tid, userType, e.getMessage(), e);
			throw e;
		}
	}

	/**
	 * Find existing inactive cases
	 */
	private List<DowJonesSamCase> findExistingInactiveCases(String tid, UserTypeEnum userType) {
		log.debug("[tid={}] Querying existing inactive cases, userType={}", tid, userType);

		List<DowJonesSamCase> dowJonesSamCaseList = customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<>() {
			@Override
			public List<DowJonesSamCase> query() {
				List<Predicate> predicates = new ArrayList<>();
				predicates.add(predicate.equalUserType(criteriaBuilder, root, userType));
				predicates.add(predicate.equalActive(criteriaBuilder, root, Boolean.FALSE));
				return getResultList(entityManager, criteriaQuery, root, predicates);
			}
		});

		log.debug("[tid={}] Found {} existing inactive cases, userType={}",
				tid, dowJonesSamCaseList != null ? dowJonesSamCaseList.size() : 0, userType);

		return dowJonesSamCaseList;
	}

	/**
	 * Create new SAM case
	 */
	private DowJonesSamCase createNewSamCase(String tid, UserTypeEnum userType) throws Exception {
		log.info("[tid={}] Starting to create new Dow Jones SAM case, userType={}", tid, userType);

		try {
			String groupName = generateCaseGroupName(tid, userType);
			log.debug("[tid={}] Generated case group name: {}, userType={}", tid, groupName, userType);

			DowJonesCaseCreateRes dowJonesCaseCreateRes = dowJonesManager.createCaseGroup(tid, groupName);

			if (isInvalidApiResponse(dowJonesCaseCreateRes)) {
				String errorMsg = String.format("[tid=%s] Dow Jones API returned invalid response, userType=%s", tid, userType);
				log.error("{}, response={}", errorMsg, JsonUtil.encode(dowJonesCaseCreateRes));
				throw new DowJonesAntisocialCheckException(errorMsg);
			}

			DowJonesSamCase dowJonesSamCaseBo = getDowJonesSamCase(userType, dowJonesCaseCreateRes);
			DowJonesSamCase savedCase = this.save(dowJonesSamCaseBo);

			log.info("[tid={}] Successfully created new Dow Jones SAM case, caseId={}, userType={}, groupName={}",
					tid, savedCase.getDowJonesSamCaseId(), userType, groupName);

			return savedCase;
		} catch (Exception e) {
			log.error("[tid={}] Failed to create new Dow Jones SAM case, userType={}, error={}",
					tid, userType, e.getMessage(), e);
			throw e;
		}
	}

	/**
	 * Generate case group name
	 */
	private String generateCaseGroupName(String tid, UserTypeEnum userType) {
		String userTypeCode = userType.name().toLowerCase();
		long timestamp = System.currentTimeMillis();
		String timestampSuffix = String.valueOf(timestamp % 100000);
		String tidHash = String.valueOf(Math.abs(tid.hashCode()) % 1000);

		return String.format("group_%s_%s_%s", userTypeCode, timestampSuffix, tidHash);
	}

	/**
	 * Check if API response is invalid
	 */
	private boolean isInvalidApiResponse(DowJonesCaseCreateRes response) {
		return response == null ||
			   response.getData() == null ||
			   response.getData().getAttributes() == null;
	}

	private DowJonesSamCase getDowJonesSamCase(UserTypeEnum userType, DowJonesCaseCreateRes dowJonesCaseCreateRes) {
		DowJonesCaseCreateRes.Attributes attributes = dowJonesCaseCreateRes.getData().getAttributes();
		Date now = new Date();
		DowJonesSamCase dowJonesSamCase = new DowJonesSamCase();
		dowJonesSamCase.setDowJonesSamCaseId(dowJonesCaseCreateRes.getCaseId());
		dowJonesSamCase.setDowJonesSamCaseName(attributes.getCaseName());
		dowJonesSamCase.setDowJonesSamExternalId(attributes.getExternalId());
		dowJonesSamCase.setUserId(0L);
		dowJonesSamCase.setUserType(userType.getType());
		dowJonesSamCase.setSearchType(dowJonesConfig.getSearchType().getType());
		dowJonesSamCase.setEnabled(Boolean.TRUE);
		dowJonesSamCase.setActive(Boolean.FALSE);
		dowJonesSamCase.setAssociationOccupyCount(0L);
		dowJonesSamCase.setCreatedAt(now);
		dowJonesSamCase.setUpdatedAt(now);
		return dowJonesSamCase;
	}
	public DowJonesSamCase findByDowJonesSamCase (String caseId) {
		return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<>() {
			@Override
			public DowJonesSamCase query() {
				List<Predicate> predicates = new ArrayList<>();
				predicates.add(predicate.equalDowJonesSamCaseId(criteriaBuilder,root,caseId));
				predicates.add(predicate.equalActive(criteriaBuilder,root,Boolean.FALSE));
				return getSingleResult(entityManager, criteriaQuery, root, predicates);
			}
		});
	}

	public List<DowJonesSamCase> findDowJonesSamCaseList(){
		return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<>() {
			@Override
			public List<DowJonesSamCase> query() {
				List<Predicate> predicates = new ArrayList<>();
				predicates.add(predicate.equalUserType(criteriaBuilder, root, UserTypeEnum.PERSONAL));
				predicates.add(predicate.greaterThanUserCount(criteriaBuilder, root,0L));
				return getResultList(entityManager, criteriaQuery, root, predicates);
			}
		});
	}
}
