package exchange.common.service.dowjones;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import exchange.common.component.DowJonesManager;
import exchange.common.config.DowJonesConfig;
import exchange.common.constant.*;
import exchange.common.dal.master.bo.user.AntiSocialCheckUserBO;
import exchange.common.dal.master.entity.dowjones.DowJonesAntisocialCheckDO;
import exchange.common.dal.master.entity.dowjones.DowJonesSamCaseDO;
import exchange.common.dal.master.entity.user.*;
import exchange.common.dal.master.mapper.dowjones.DowJonesAntisocialCheckMapper;
import exchange.common.dal.master.mapper.dowjones.DowJonesSamCaseMapper;
import exchange.common.dal.master.mapper.user.*;
import exchange.common.entity.Deposit;
import exchange.common.entity.User;
import exchange.common.entity.Withdrawal;
import exchange.common.entity.dowjones.DowJonesAntisocialCheck;
import exchange.common.entity.dowjones.DowJonesBulkAssociation;
import exchange.common.entity.dowjones.DowJonesSamCase;
import exchange.common.exception.DowJonesAntisocialCheckException;
import exchange.common.model.request.dowjones.DowJonesBulkAssociationCaseCreateReq;
import exchange.common.model.response.dowjones.*;
import exchange.common.predicate.dowjones.DowJonesAntisocialCheckPredicate;
import exchange.common.service.*;
import exchange.common.service.dowjones.dto.DowJonesUserRelationDataInfoDTO;
import exchange.common.util.DateUnit;
import exchange.common.util.IdUtil;
import exchange.common.util.JsonUtil;
import exchange.common.util.SystemUtil;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DowJonesAntisocialCheckService extends EntityService<DowJonesAntisocialCheck, DowJonesAntisocialCheckPredicate> {
	private final UserMapper userMapper;
	private final UserInfoMapper userInfoMapper;
	private final UserInfoCorporateMapper userInfoCorporateMapper;
	private final UserInfoCorporateRepresentativeMapper userInfoCorporateRepresentativeMapper;
	private final UserInfoCorporateAgentMapper userInfoCorporateAgentMapper;
	private final UserInfoCorporateOwnerMapper userInfoCorporateOwnerMapper;
	private final DowJonesAntisocialCheckMapper dowJonesAntisocialCheckMapper;
	private final DowJonesManager dowJonesManager;
	private final DowJonesSamCaseService dowJonesSamCaseService;
	private final DowJonesSamCaseMapper dowJonesSamCaseMapper;
	private final WithdrawalService withdrawalService;
	private final DepositService depositService;
	private final DowJonesBulkAssociationService dowJonesBulkAssociationService;
	private final UserService userService;
	private final DowJonesConfig dowJonesConfig;

	@Override
	public Class<DowJonesAntisocialCheck> getEntityClass() {
		return DowJonesAntisocialCheck.class;
	}

	/**
	 * Refer to：{@link AntiSocialCheckService#sanctionScreening(boolean, Long)}
	 * @param id
	 * @return
	 */
	public Boolean checkRiskByDepositId(Long id) {
		String tid = IdUtil.getUuid();
		AntiSocialCheckBusinessTypeEnum businessType = AntiSocialCheckBusinessTypeEnum.CRYPTO_TOKEN_DEPOSIT;
		String businessId = String.valueOf(id);
		Boolean hasRisk = false;

		Deposit depoist = depositService.findOne(id);
		if("お客様本人以外".equals(depoist.getOwnertype())) {
			if("個人".equals(depoist.getRecipienttype())) {
				hasRisk = hasRiskForPersonal(tid, businessType, businessId, depoist.getFirst_name_english(), depoist.getLast_name_english(), null, null);
			} else if("法人".equals(depoist.getRecipienttype())) {
				hasRisk = hasRiskForCorporate(tid, businessType, businessId, depoist.getLegalname_english());
			}
		} else {
			String ownUserRecipientType = depoist.getUser().getAuthorities().get(0).getAuthority();
			if("PERSONAL".equals(ownUserRecipientType)) {
				hasRisk = hasRisk(tid, businessType, businessId, DowJonesApiTypeEnum.RCS, depoist.getUserId());
			} else {
				hasRisk = hasRisk(tid, businessType, businessId, DowJonesApiTypeEnum.SAM, depoist.getUserId());
			}
		}
		return hasRisk;
	}

	/**
	 * Refer to：{@link AntiSocialCheckService#sanctionScreening(boolean, Long)}
	 * @param id
	 * @return
	 */
	public Boolean checkRiskByWithdrawalId(Long id) {
		String tid = IdUtil.getUuid();
		AntiSocialCheckBusinessTypeEnum businessType = AntiSocialCheckBusinessTypeEnum.CRYPTO_TOKEN_WITHDRAW;
		String businessId = String.valueOf(id);
		Boolean hasRisk = false;

		Withdrawal withdrawal = withdrawalService.findOne(id);
		if("お客様本人以外".equals(withdrawal.getWithdrawalAccount().getOwnertype())) {
			if("個人".equals(withdrawal.getWithdrawalAccount().getRecipienttype())) {
				hasRisk = hasRiskForPersonal(tid, businessType, businessId, withdrawal.getWithdrawalAccount().getFirst_name_english(), withdrawal.getWithdrawalAccount().getLast_name_english(), null, null);
			} else if("法人".equals(withdrawal.getWithdrawalAccount().getRecipienttype())) {
				hasRisk = hasRiskForCorporate(tid, businessType, businessId, withdrawal.getWithdrawalAccount().getLegalname_english());
			}
		} else {
			String ownUserRecipientType = withdrawal.getUser().getAuthorities().get(0).getAuthority();
			if("PERSONAL".equals(ownUserRecipientType)) {
				hasRisk = hasRisk(tid, businessType, businessId, DowJonesApiTypeEnum.RCS, withdrawal.getUserId());
			} else {
				hasRisk = hasRisk(tid, businessType, businessId, DowJonesApiTypeEnum.SAM, withdrawal.getUserId());
			}
		}
		return hasRisk;
	}

	public Boolean hasRisk(String tid, AntiSocialCheckBusinessTypeEnum businessType, String businessId, DowJonesApiTypeEnum dowJonesApiType, Long userId) {
		return hasRisk(tid, businessType, businessId, dowJonesApiType, userId, null);
	}

	public Boolean hasRisk(String tid, AntiSocialCheckBusinessTypeEnum businessType, String businessId, DowJonesApiTypeEnum dowJonesApiType, Long userId, Integer timeoutSecond) {
		if (StringUtils.isBlank(tid)) {
			tid = IdUtil.getUuid();
		}

		UserDO userDO = userMapper.selectById(userId);
		if (null == userDO) {
			log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={} not exist", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s not exist", tid, userId));
		}
		if (null == userDO.getUserInfoId() && null == userDO.getUserInfoCorporateId()) {
			log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={} data error", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s data error", tid, userId));
		}
		UserTypeEnum userType = UserTypeEnum.PERSONAL;
		if (null == userDO.getUserInfoId()) {
			userType = UserTypeEnum.CORPORATE;
		}

		// 個人ユーザー
		if (userType == UserTypeEnum.PERSONAL) {
			UserInfoDO userInfoDO = userInfoMapper.selectById(userDO.getUserInfoId());
			if (null == userInfoDO) {
				log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={},userInfoId={} not exist", tid, userId, userDO.getUserInfoId());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,userInfoId=%s not exist", tid, userId, userDO.getUserInfoId()));
			}
			List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = Lists.newArrayList();
			// 個人
			userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
					.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.USER.getType())
					.setUserRelationDataId(userInfoDO.getId())
					.setFirstName(userInfoDO.getFirstName())
					.setLastName(userInfoDO.getLastName())
					.setBirthday(userInfoDO.getBirthday())
					.setGender(userInfoDO.getGender())
			);
			return hashRisk(tid, businessType, businessId, dowJonesApiType, userId, userType, userRelationDataInfoDTOList, timeoutSecond);
		}

		// 法人ユーザー
		UserInfoCorporateDO userInfoCorporateDO = userInfoCorporateMapper.selectById(userDO.getUserInfoCorporateId());
		if (null == userInfoCorporateDO) {
			log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={},userInfoCorporateId={} not exist", tid, userId, userDO.getUserInfoCorporateId());
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,userInfoCorporateId=%s not exist", tid, userId, userDO.getUserInfoCorporateId()));
		}

		List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = Lists.newArrayList();
		// 法人
		userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
				.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.CORPORATE.getType())
				.setUserRelationDataId(userInfoCorporateDO.getId())
				.setName(userInfoCorporateDO.getName())
		);

		if (null != userInfoCorporateDO.getRepresentativeId()) {
			UserInfoCorporateRepresentativeDO userInfoCorporateRepresentativeDO = userInfoCorporateRepresentativeMapper.selectById(userInfoCorporateDO.getRepresentativeId());
			if (null == userInfoCorporateRepresentativeDO) {
				log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={},representativeId={} not exist", tid, userId, userInfoCorporateDO.getRepresentativeId());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,representativeId=%s not exist", tid, userId, userInfoCorporateDO.getRepresentativeId()));
			} else {
				// 代表者
				userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.REPRESENTATIVE.getType())
						.setUserRelationDataId(userInfoCorporateRepresentativeDO.getId())
						.setFirstName(userInfoCorporateRepresentativeDO.getFirstName())
						.setLastName(userInfoCorporateRepresentativeDO.getLastName())
						.setBirthday(userInfoCorporateRepresentativeDO.getBirthday())
						.setGender(userInfoCorporateRepresentativeDO.getGender())
				);
			}
		}

		if (null != userInfoCorporateDO.getAgentId()) {
			UserInfoCorporateAgentDO userInfoCorporateAgentDO = userInfoCorporateAgentMapper.selectById(userInfoCorporateDO.getAgentId());
			if (null == userInfoCorporateAgentDO) {
				log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={},agentId={} not exist", tid, userId, userInfoCorporateDO.getAgentId());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,agentId=%s not exist", tid, userId, userInfoCorporateDO.getAgentId()));
			} else {
				// 取引担当者
				userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.AGENT.getType())
						.setUserRelationDataId(userInfoCorporateAgentDO.getId())
						.setFirstName(userInfoCorporateAgentDO.getFirstName())
						.setLastName(userInfoCorporateAgentDO.getLastName())
						.setBirthday(userInfoCorporateAgentDO.getBirthday())
				);
			}
		}

		if (StringUtils.isNotBlank(userInfoCorporateDO.getOwnerIds())) {
			String[] ownerIdStrArray = userInfoCorporateDO.getOwnerIds().split(",");
			Set<Long> ownerIds = Stream.of(ownerIdStrArray).map(Long::valueOf).collect(Collectors.toSet());
			List<UserInfoCorporateOwnerDO> userInfoCorporateOwnerDOList = userInfoCorporateOwnerMapper.selectBatchIds(ownerIds);
			if (CollectionUtils.isEmpty(userInfoCorporateOwnerDOList)) {
				log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={},ownerIds={} not exist", tid, userId, userInfoCorporateDO.getOwnerIds());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,ownerIds=%s not exist", tid, userId, userInfoCorporateDO.getOwnerIds()));
			} else if (userInfoCorporateOwnerDOList.size() != ownerIdStrArray.length) {
				log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userId={},ownerIds={} section data not exist", tid, userId, userInfoCorporateDO.getOwnerIds());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s,ownerIds=%s section data not exist", tid, userId, userInfoCorporateDO.getOwnerIds()));
			} else {
				userRelationDataInfoDTOList.addAll(userInfoCorporateOwnerDOList.stream().map(owner -> new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.OWNER.getType())
						.setUserRelationDataId(owner.getId())
						.setFirstName(owner.getFirstName())
						.setLastName(owner.getLastName())
						.setBirthday(owner.getBirthday())
				).toList());
			}
		}

		return hashRisk(tid, businessType, businessId, dowJonesApiType, userId, userType, userRelationDataInfoDTOList, timeoutSecond);
	}

	public Boolean hasRiskForPersonal(String tid,
									  AntiSocialCheckBusinessTypeEnum businessType,
									  String businessId,
									  String firstName,
									  String lastName,
									  String birthday,
									  Integer gender) {
		return hasRisk(tid, businessType, businessId, UserTypeEnum.PERSONAL, firstName, lastName, birthday, gender);
	}

	public Boolean hasRiskForCorporate(String tid,
									   AntiSocialCheckBusinessTypeEnum businessType,
									   String businessId,
									   String name) {

		return hasRisk(tid, businessType, businessId, UserTypeEnum.CORPORATE, name, null, null, null);
	}

	public Boolean hasRisk(String tid,
						   AntiSocialCheckBusinessTypeEnum businessType,
						   String businessId,
						   UserTypeEnum userType,
						   String firstName,
						   String lastName,
						   String birthday,
						   Integer gender) {
		if (StringUtils.isBlank(tid)) {
			tid = IdUtil.getUuid();
		}

    	List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = Lists.newArrayList();
		if (UserTypeEnum.PERSONAL == userType) {
			userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
					.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.USER.getType())
					.setFirstName(firstName)
					.setLastName(lastName)
					.setBirthday(birthday)
					.setGender(gender)
			);
		} else {
			userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
					.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.CORPORATE.getType())
					.setName(firstName)
			);
		}

		return hashRisk(tid, businessType, businessId, DowJonesApiTypeEnum.RCS, null, userType, userRelationDataInfoDTOList, null);
	}

	private Boolean hashRisk(String tid,
							 AntiSocialCheckBusinessTypeEnum businessType,
							 String businessId,
							 DowJonesApiTypeEnum dowJonesApiType,
							 Long userId,
							 UserTypeEnum userType,
							 List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList,
							 Integer timeoutSecond) {
		if (CollectionUtils.isEmpty(userRelationDataInfoDTOList)) {
			log.error("[tid={}] DowJonesAntisocialCheckService hasRisk userRelationDataInfoList cannot empty. userId={}", tid, userId);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userId=%s userRelationDataInfoList cannot empty", tid, userId));
		}

		Long userInfoId = userRelationDataInfoDTOList.stream().filter(item -> DowJonesUserRelationDataTypeEnum.USER.getType().equals(item.getUserRelationDataType()))
				.map(DowJonesUserRelationDataInfoDTO::getUserRelationDataId).filter(Objects::nonNull).findFirst().orElse(null);
		Long userInfoCorporateId = userRelationDataInfoDTOList.stream().filter(item -> DowJonesUserRelationDataTypeEnum.CORPORATE.getType().equals(item.getUserRelationDataType()))
				.map(DowJonesUserRelationDataInfoDTO::getUserRelationDataId).filter(Objects::nonNull).findFirst().orElse(null);

		AntisocialCheckContext context = new AntisocialCheckContext()
				.setTraceId(tid)
				.setUserId(userId)
				.setUserType(userType)
				.setUserRelationDataInfoDTOList(userRelationDataInfoDTOList);

		if (null == timeoutSecond && null != businessType.getDefaultTimeoutSecond()) {
			timeoutSecond = businessType.getDefaultTimeoutSecond();
		}

		Date startTime = new Date();
		String errorReason = null;
		String errorMsg = null;
		String errorStackTrace = null;
		try {
			if (dowJonesApiType == DowJonesApiTypeEnum.RCS) {
				riskAndComplianceSearch(context);
			} else {
				screeningAndMonitoringMatch(context, timeoutSecond);
			}
			return context.getHasRisk();
		} catch (Exception e) {
			errorMsg = e.getMessage();
			errorStackTrace = ExceptionUtils.getStackTrace(e);
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] DowJonesAntisocialCheckService error", tid), e);
		} finally{
			Date endTime = new Date();

			dowJonesAntisocialCheckMapper.insert(new DowJonesAntisocialCheckDO()
				.setUserId(userId)
				.setUserType(userType.getType())
				.setApiType(dowJonesApiType.getType())
				.setBusinessType(businessType.getType())
				.setBusinessId(businessId)
				.setUserInfoId(userInfoId)
				.setUserInfoCorporateId(userInfoCorporateId)
				.setUserRelationDataInfoList(userRelationDataInfoDTOList)
				.setCaseId(context.getCaseId())
				.setHasRisk(context.getHasRisk())
				.setStartTime(startTime)
				.setEndTime(endTime)
				.setDurationMillis(endTime.getTime() - startTime.getTime())
				.setTraceId(tid)
				.setErrorReason(errorReason)
				.setErrorMsg(errorMsg)
				.setErrorStackTrace(errorStackTrace)
				.setCreatedAt(endTime)
				.setUpdatedAt(endTime)
			);
		}
	}

	private void riskAndComplianceSearch(AntisocialCheckContext context) {
		String tid = context.getTraceId();
		List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = context.getUserRelationDataInfoDTOList();

		for (DowJonesUserRelationDataInfoDTO userRelationDataInfo : userRelationDataInfoDTOList) {
			DowJonesUserRelationDataTypeEnum dowJonesUserRelationDataTypeEnum = DowJonesUserRelationDataTypeEnum.ofType(userRelationDataInfo.getUserRelationDataType());
			if (null == dowJonesUserRelationDataTypeEnum) {
				log.error("[tid={}] DowJonesAntisocialCheckService riskAndComplianceSearch userRelationDataType={} not support", tid, userRelationDataInfo.getUserRelationDataType());
				throw new DowJonesAntisocialCheckException(String.format("[tid=%s] userRelationDataType=%s not support", tid, userRelationDataInfo.getUserRelationDataType()));
			}
			boolean hasRisk;

			if (DowJonesUserRelationDataTypeEnum.CORPORATE.getType().equals(userRelationDataInfo.getUserRelationDataType())) {
				hasRisk = dowJonesManager.hasRiskForEntity(tid, userRelationDataInfo.getName());
			} else {
				hasRisk = dowJonesManager.hasRiskForPerson(tid, userRelationDataInfo.getFirstName(),
						userRelationDataInfo.getLastName(), userRelationDataInfo.getBirthday(), userRelationDataInfo.getGender());
			}

			if (hasRisk) {
				context.setHasRisk(Boolean.TRUE);
				return;
			}
 		}

		context.setHasRisk(Boolean.FALSE);
	}

	private void screeningAndMonitoringMatch(AntisocialCheckContext context, Integer timeoutSecond) throws Exception {
		Boolean flag = dowJonesSamCaseService.checkRiskOrUpdateCase(context.getTraceId(), context.getUserId());
		DowJonesSamCaseDO caseDO = dowJonesSamCaseMapper.findOneByUserId(context.getUserId());

		context.setCaseId(caseDO.getId());
		if (null != flag) {
			context.setHasRisk(flag);
			return;
		}

		if (null != timeoutSecond && timeoutSecond > 0) {
			SystemUtil.trySleep(DateUnit.SECOND.getMillis() * timeoutSecond);
		}

		// 結果が得られなかったことを異常扱いするか、無リスク扱いするかはビジネス層が決める。
		Boolean hasRisk = dowJonesSamCaseService.checkRisk(context.getTraceId(), context.getUserId());
		context.setHasRisk(hasRisk);
	}

	@Accessors(chain = true)
	@Data
	public static class AntisocialCheckContext {
		private String traceId;
		private Long userId;
		private UserTypeEnum userType;
		private List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList;
		private Boolean hasRisk;
		private Long caseId;
	}


	public void processAntiSocialCheckInBatches(String tid, List<List<AntiSocialCheckUserBO>> groupedLists, String caseId) throws Exception {
		log.info("[tid={}] Starting anti-social check batch processing. caseId={}, total batch groups={}",
				tid, caseId, groupedLists != null ? groupedLists.size() : 0);

		if (CollectionUtils.isEmpty(groupedLists)) {
			log.debug("[tid={}] No grouped lists to process", tid);
			return;
		}

		try {
			BatchProcessingContext context = initializeProcessingContext(tid, caseId);

			for (List<AntiSocialCheckUserBO> groupedList : groupedLists) {
				processGroupedList(tid, groupedList, context);
			}

			log.info("[tid={}] Completed anti-social check batch processing successfully", tid);
		} catch (Exception e) {
			log.error("[tid={}] Error processing anti-social check batches. caseId={}", tid, caseId, e);
			throw e;
		}
	}

	/**
	 * Initialize batch processing context
	 */
	private BatchProcessingContext initializeProcessingContext(String tid, String caseId) throws Exception {
		DowJonesSamCase currentCase = null;
		if (StringUtils.isNotBlank(caseId)) {
			currentCase = dowJonesSamCaseService.findByDowJonesSamCase(caseId);
			if (currentCase != null) {
				log.info("[tid={}] Using existing case. caseId={}, currentCount={}",
						tid, currentCase.getDowJonesSamCaseId(), currentCase.getAssociationOccupyCount());
			}
		}

		return new BatchProcessingContext(currentCase);
	}

	/**
	 * Process single grouped list
	 */
	private void processGroupedList(String tid, List<AntiSocialCheckUserBO> groupedList, BatchProcessingContext context) throws Exception {
		log.debug("[tid={}] Processing group with size={}", tid, groupedList.size());

		List<AntiSocialCheckUserBO> remainingUsers = new ArrayList<>(groupedList);

		while (!remainingUsers.isEmpty()) {
			// Ensure available case
			context.ensureAvailableCase(tid);

			// Calculate current batch size
			int batchSize = calculateBatchSize(context.getCurrentCase(), remainingUsers.size());

			// Process current batch
			List<AntiSocialCheckUserBO> currentBatch = remainingUsers.subList(0, batchSize);
			processBatchAndUpdateCase(tid, currentBatch, context);

			// Remove processed users
			remainingUsers = remainingUsers.subList(batchSize, remainingUsers.size());

			// If current case is full, mark as needing new case
			if (isCaseFull(context.getCurrentCase())) {
				context.markCaseAsFull(tid);
			}
		}
	}

	/**
	 * Calculate batch size
	 */
	private int calculateBatchSize(DowJonesSamCase currentCase, int remainingUserCount) {
		int maxCapacity = dowJonesConfig.getCount().intValue();
		int currentCount = currentCase.getAssociationOccupyCount().intValue();
		int remainingCapacity = maxCapacity - currentCount;

		return Math.min(remainingUserCount, remainingCapacity);
	}

	/**
	 * Process batch and update case
	 */
	private void processBatchAndUpdateCase(String tid, List<AntiSocialCheckUserBO> batch, BatchProcessingContext context) throws Exception {
		DowJonesSamCase currentCase = context.getCurrentCase();

		log.debug("[tid={}] Processing batch - caseId={}, batchSize={}, currentCount={}",
				tid, currentCase.getDowJonesSamCaseId(), batch.size(), currentCase.getAssociationOccupyCount());

		// Process batch
		processBatch(tid, batch, currentCase);

		// Update case count
		Long newCount = currentCase.getAssociationOccupyCount() + batch.size();
		currentCase.setAssociationOccupyCount(newCount);

		// Check if maximum capacity is reached
		if (dowJonesConfig.getCount().equals(newCount)) {
			currentCase.setActive(true);
			log.info("[tid={}] Case reached full capacity. caseId={}, count={}",
					tid, currentCase.getDowJonesSamCaseId(), newCount);
		}

		// Save updates
		dowJonesSamCaseService.save(currentCase);
		log.debug("[tid={}] Updated case. caseId={}, new count={}",
				tid, currentCase.getDowJonesSamCaseId(), newCount);
	}

	/**
	 * Check if case is full
	 */
	private boolean isCaseFull(DowJonesSamCase currentCase) {
		return currentCase.getAssociationOccupyCount().intValue() >= dowJonesConfig.getCount().intValue();
	}

	/**
	 * Batch processing context class to manage state during batch processing
	 */
	private class BatchProcessingContext {
        /**
         * -- GETTER --
         * Get current case
         */
        @Getter
        private DowJonesSamCase currentCase;
		private boolean needNewCase;

		public BatchProcessingContext(DowJonesSamCase initialCase) {
			this.currentCase = initialCase;
			this.needNewCase = (initialCase == null);
		}

		/**
		 * Ensure available case
		 */
		public void ensureAvailableCase(String tid) throws Exception {
			if (needNewCase || currentCase == null) {
				currentCase = createNewCase(tid);
				needNewCase = false;
				log.info("[tid={}] Created new case. caseId={}", tid, currentCase.getDowJonesSamCaseId());
			}
		}

		/**
		 * Mark current case as full, need new case
		 */
		public void markCaseAsFull(String tid) {
			log.debug("[tid={}] Current case is full (count={}), will create new case next time",
					tid, currentCase.getAssociationOccupyCount());
			needNewCase = true;
		}

    }

	/**
	 * Generate case group name
	 *
	 * @param tid Transaction ID
	 * @return Case group name
	 */
	private String generateCaseGroupName(String tid) {
		// Option 1: Based on user type enum value
		String userTypeCode = UserTypeEnum.PERSONAL.name().toLowerCase();

		// Option 2: Based on timestamp to ensure uniqueness (simple and effective)
		long timestamp = System.currentTimeMillis();
		String timestampSuffix = String.valueOf(timestamp % 100000); // Take last 5 digits

		// Option 3: Based on transaction ID hash value
		String tidHash = String.valueOf(Math.abs(tid.hashCode()) % 1000);

		// Combined generation: group_personal_12345_abc
		return String.format("group_%s_%s_%s",
				userTypeCode,
				timestampSuffix,
				tidHash);
	}

	private DowJonesSamCase createNewCase(String tid) throws Exception {
		String groupName = generateCaseGroupName(tid);
		DowJonesCaseCreateRes dowJonesCaseCreateRes = dowJonesManager.createCaseGroup(tid, groupName);
		if (null == dowJonesCaseCreateRes || null == dowJonesCaseCreateRes.getData() || null == dowJonesCaseCreateRes.getData().getAttributes()) {
			log.error("[tid={}] findOrCreateCase dow jones api result error. userId={},dowJonesCaseCreateRes={}", tid, null, JsonUtil.encode(dowJonesCaseCreateRes));
			throw new DowJonesAntisocialCheckException(String.format("[tid=%s] dow jones api result error.", tid));
		}
		DowJonesSamCase dowJonesSamCase = getDowJonesSamCase(dowJonesCaseCreateRes);
		dowJonesSamCaseService.save(dowJonesSamCase);
		return dowJonesSamCaseService.findByDowJonesSamCase(dowJonesCaseCreateRes.getCaseId());
	}

	private DowJonesSamCase getDowJonesSamCase(DowJonesCaseCreateRes dowJonesCaseCreateRes) {
		DowJonesCaseCreateRes.Attributes attributes = dowJonesCaseCreateRes.getData().getAttributes();
		DowJonesSamCase dowJonesSamCase = new DowJonesSamCase();
		dowJonesSamCase.setDowJonesSamCaseId(dowJonesCaseCreateRes.getCaseId());
		dowJonesSamCase.setDowJonesSamCaseName(attributes.getCaseName());
		dowJonesSamCase.setDowJonesSamExternalId(attributes.getExternalId());
		dowJonesSamCase.setUserId(0L);
		dowJonesSamCase.setUserType(UserTypeEnum.PERSONAL.getType());
		dowJonesSamCase.setSearchType(dowJonesConfig.getSearchType().getType());
		dowJonesSamCase.setEnabled(Boolean.TRUE);
		dowJonesSamCase.setActive(Boolean.FALSE);
		dowJonesSamCase.setAssociationOccupyCount(0L);
		dowJonesSamCase.setCreatedAt(new Date());
		dowJonesSamCase.setUpdatedAt(new Date());
		return dowJonesSamCase;
	}


	private void processBatch(String tid, List<AntiSocialCheckUserBO> batch, DowJonesSamCase dowJonesSamCase) throws Exception {
		customTransactionManager.execute(entityManager -> {
			List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoDTOList = Lists.newArrayList();
			log.info("[tid={}] DowJonesAntisocialCheckService hasRisk start. caseId={},batch_size={}", tid, dowJonesSamCase.getDowJonesSamCaseId(), batch.size());
			for (AntiSocialCheckUserBO antiSocialCheckUserBO : batch) {
				User user = userService.findOne(antiSocialCheckUserBO.getId());
				user.setCaseId(dowJonesSamCase.getId());
				userService.save(user, entityManager);
				userRelationDataInfoDTOList.add(new DowJonesUserRelationDataInfoDTO()
						.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.USER.getType())
						.setUserRelationDataId(antiSocialCheckUserBO.getId())
						.setFirstName(antiSocialCheckUserBO.getUserInfoFirstName())
						.setLastName(antiSocialCheckUserBO.getUserInfoLastName())
						.setBirthday(antiSocialCheckUserBO.getUserInfoBirthday())
						.setGender(antiSocialCheckUserBO.getUserInfoGender())
				);
			}
			Long startTime = System.currentTimeMillis();
			List<DowJonesBulkAssociationCaseCreateReq.Association> association =
					dowJonesManager.createAssociation(userRelationDataInfoDTOList);
			log.info("[tid={}] DowJonesAntisocialCheckService hasRisk . caseId={},userRelationDataInfoDTOList_size={}", tid, dowJonesSamCase.getId(), JsonUtil.encode(association));
			DowJonesBulkAssociationCaseCreateReq req = new DowJonesBulkAssociationCaseCreateReq();
			req.getData().getAttributes().setAssociations(association);

			DowJonesCaseAddAssociationRes caseBulkAssociation = dowJonesManager.createCaseBulkAssociation(
					tid, dowJonesSamCase.getDowJonesSamCaseId(), req);
			log.info("[tid={}] DowJonesAntisocialCheckService hasRisk case rel bulk association . caseId={},caseBulkAssociation={}", tid, dowJonesSamCase.getId(), JsonUtil.encode(caseBulkAssociation));
			DowJonesCaseAddAssociationRes statusBulkAssociation = dowJonesManager.getStatusBulkAssociation(
					tid, dowJonesSamCase.getDowJonesSamCaseId(), caseBulkAssociation.getData().getId());
			log.info("[tid={}] DowJonesAntisocialCheckService hasRisk get bulk association status . caseId={},statusBulkAssociation={}", tid, dowJonesSamCase.getId(), JsonUtil.encode(statusBulkAssociation));
			DowJonesBulkAssociation dowJonesBulkAssociation = getDowJonesBulkAssociation(statusBulkAssociation);
			dowJonesBulkAssociationService.save(dowJonesBulkAssociation, entityManager);
			Long endTime = System.currentTimeMillis();
			log.info("[tid={}] Execution time: {} seconds",  tid, (endTime - startTime) / 1000);
			log.info("[tid={}] statusBulkAssociation={}", tid, JsonUtil.encode(statusBulkAssociation));
		});
	}

	private static DowJonesBulkAssociation getDowJonesBulkAssociation(DowJonesCaseAddAssociationRes caseBulkAssociation) {
		DowJonesBulkAssociation dowJonesBulkAssociation = new DowJonesBulkAssociation();
		dowJonesBulkAssociation.setType(caseBulkAssociation.getData().getType());
		dowJonesBulkAssociation.setStatus(caseBulkAssociation.getData().getAttributes().getStatus());
		dowJonesBulkAssociation.setDowTransactionId(caseBulkAssociation.getData().getId());
		dowJonesBulkAssociation.setDowCaseId(caseBulkAssociation.getData().getAttributes().getCaseId());
		dowJonesBulkAssociation.setValidAssociations(caseBulkAssociation.getData().getAttributes().getValidAssociations());
		dowJonesBulkAssociation.setInvalidAssociations(caseBulkAssociation.getData().getAttributes().getInvalidAssociations());
		dowJonesBulkAssociation.setPendingAssociations(caseBulkAssociation.getData().getAttributes().getPendingAssociations());
		return dowJonesBulkAssociation;
	}

	public Map<Long, Boolean> checkRisk(String tid) throws Exception {
		log.info("[tid={}] caseGetMatches checkRisk start.", tid);
		Map<Long, Boolean> userAlertMap = new HashMap<>();

		List<DowJonesSamCase> dowJonesSamCaseList = dowJonesSamCaseService.findDowJonesSamCaseList();
		if (CollectionUtils.isEmpty(dowJonesSamCaseList)) {
			return userAlertMap;
		}

		processBulkAssociations(tid);
		processSamCases(tid, dowJonesSamCaseList, userAlertMap);

		log.info("[tid={}] userAlertMap={}", tid, JsonUtil.encode(userAlertMap));
		return userAlertMap;
	}
	private void processBulkAssociations(String tid) {
		List<DowJonesBulkAssociation> associations = dowJonesBulkAssociationService.findByCondition();
		if (CollectionUtils.isEmpty(associations)) {
			return;
		}

		for (DowJonesBulkAssociation association : associations) {
			updateAssociationStatus(tid, association);
		}
	}
    private void updateAssociationStatus(String tid, DowJonesBulkAssociation association) {
        try {
            Thread.sleep(500);
            DowJonesCaseAddAssociationRes status = dowJonesManager.getStatusBulkAssociation(
                    tid, association.getDowCaseId(), association.getDowTransactionId());

            if (status != null && status.getData() != null && status.getData().getAttributes() != null) {
                association.setStatus(status.getData().getAttributes().getStatus());
                association.setValidAssociations(status.getData().getAttributes().getValidAssociations());
                dowJonesBulkAssociationService.save(association);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("[tid={}] Thread interrupted while processing association: {}", tid, association.getDowCaseId(), e);
        } catch (Exception e) {
            log.warn("[tid={}] Error processing association: {}", tid, association.getDowCaseId(), e);
        }
    }

	private void processSamCases(String tid, List<DowJonesSamCase> samCases, Map<Long, Boolean> userAlertMap) {
		for (DowJonesSamCase samCase : samCases) {
			try {
				processSamCase(tid, samCase, userAlertMap);
			} catch (Exception e) {
				log.warn("[tid={}] Error processing samCase: {}", tid, samCase.getDowJonesSamCaseId(), e);
			}
		}
	}

	private void processSamCase(String tid, DowJonesSamCase samCase, Map<Long, Boolean> userAlertMap) throws Exception {
		Date startTime = new Date();
		DowJonesCaseGetMatchesRes matchesRes = dowJonesManager.caseGetMatchesPage(tid, samCase.getDowJonesSamCaseId());

		if (matchesRes == null || CollectionUtils.isEmpty(matchesRes.getData())) {
			return;
		}

		for (DowJonesCaseGetMatchesRes.Data data : matchesRes.getData()) {
			if (data != null && data.getAttributes() != null) {
				processMatchData(tid, samCase, data, userAlertMap, startTime, matchesRes);
			}
		}
	}

	private void processMatchData(String tid, DowJonesSamCase samCase,
								  DowJonesCaseGetMatchesRes.Data data,
								  Map<Long, Boolean> userAlertMap, Date startTime, DowJonesCaseGetMatchesRes matchesRes) {
		try {
			String[] userArray = data.getAttributes().getExternalId().split("_");
			Long userId = Long.valueOf(userArray[2]);

			User user = userService.findOne(userId);
			if (user == null) {
				return;
			}

			log.info("[tid={}] search start user_id = {}", tid, userId);
			saveAntisocialCheckRecord(tid, samCase, data, userId, startTime, matchesRes);
			userAlertMap.put(userId, matchesRes.getHasAlerts(data));
			log.info("[tid={}] search end user_id = {}", tid, userId);
		} catch (Exception e) {
			log.warn("[tid={}] Error processing match data for samCase: {}", tid, samCase.getDowJonesSamCaseId(), e);
		}
	}

	private void saveAntisocialCheckRecord(String tid, DowJonesSamCase samCase,
										   DowJonesCaseGetMatchesRes.Data data,
										   Long userId, Date startTime, DowJonesCaseGetMatchesRes matchesRes) {
		Date endTime = new Date();
		User user = userService.findOne(userId);

		DowJonesUserRelationDataInfoDTO userRelationDataInfoDTO = null;
		Long userInfoId = 0L;

		if (user.getUserInfo() != null) {
			userInfoId = user.getUserInfo().getId();
			userRelationDataInfoDTO = new DowJonesUserRelationDataInfoDTO()
					.setUserRelationDataType(DowJonesUserRelationDataTypeEnum.USER.getType())
					.setUserRelationDataId(userId)
					.setFirstName(user.getUserInfo().getFirstName())
					.setLastName(user.getUserInfo().getLastName())
					.setBirthday(user.getUserInfo().getBirthday());
		}

		List<DowJonesUserRelationDataInfoDTO> userRelationDataInfoList =
				userRelationDataInfoDTO != null ? List.of(userRelationDataInfoDTO) : Collections.emptyList();

		dowJonesAntisocialCheckMapper.insert(new DowJonesAntisocialCheckDO()
				.setUserId(userId)
				.setUserType(UserTypeEnum.PERSONAL.getType())
				.setApiType(DowJonesApiTypeEnum.SAM.getType())
				.setBusinessType(AntiSocialCheckBusinessTypeEnum.USER_CONTINUOUS_SCREENING.getType())
				.setBusinessId(null)
				.setUserInfoId(userInfoId)
				.setUserRelationDataInfoList(userRelationDataInfoList)
				.setCaseId(samCase.getId())
				.setHasRisk(matchesRes.getHasAlerts(data))
				.setStartTime(startTime)
				.setEndTime(endTime)
				.setDurationMillis(Math.abs(endTime.getTime() - startTime.getTime()))
				.setTraceId(tid)
				.setErrorReason(null)
				.setErrorMsg(null)
				.setErrorStackTrace(null)
				.setCreatedAt(endTime)
				.setUpdatedAt(endTime));
	}
}
