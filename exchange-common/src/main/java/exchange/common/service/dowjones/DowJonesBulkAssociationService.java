package exchange.common.service.dowjones;

import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.dowjones.DowJonesBulkAssociation;
import exchange.common.predicate.dowjones.DowJonesBulkAssociationPredicate;

import exchange.common.service.EntityService;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

@Service
public class DowJonesBulkAssociationService extends EntityService<DowJonesBulkAssociation, DowJonesBulkAssociationPredicate> {
    @Override
    public Class<DowJonesBulkAssociation> getEntityClass() {
        return DowJonesBulkAssociation.class;
    }

    public List<DowJonesBulkAssociation> findByCondition(){
        return  customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<>() {
            @Override
            public List<DowJonesBulkAssociation> query() {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(predicate.equalStatus(criteriaBuilder, root, "PROCESSING"));
                return getResultList(entityManager, criteriaQuery, root, predicates);
            }
        });
    }
}