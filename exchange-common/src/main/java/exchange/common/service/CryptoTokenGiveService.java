package exchange.common.service;

import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.cryptoToken.CryptoTokenGive;
import exchange.common.entity.cryptoToken.CryptoTokenGive_;
import exchange.common.predicate.CryptoTokenGivePredicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/11/25
 */
@Slf4j
@Service
public class CryptoTokenGiveService extends EntityService<CryptoTokenGive, CryptoTokenGivePredicate> {
	@Override
	public Class<CryptoTokenGive> getEntityClass() {
		return CryptoTokenGive.class;
	}

	public CryptoTokenGive findLastOne(Long userId, Integer batchSeqNo) {
		return customTransactionManager.find(
				getEntityClass(),
				new QueryExecutorReturner<CryptoTokenGive, CryptoTokenGive>() {
					@Override
					public CryptoTokenGive query() {
						List<Predicate> predicates = new ArrayList<>();
						predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
						predicates.add(predicate.equalBatchSeqNo(criteriaBuilder, root, batchSeqNo));

						return getSingleResult(entityManager, criteriaQuery, root, predicates, criteriaBuilder.desc(root.get(CryptoTokenGive_.id)));
					}
				});
	}
}
