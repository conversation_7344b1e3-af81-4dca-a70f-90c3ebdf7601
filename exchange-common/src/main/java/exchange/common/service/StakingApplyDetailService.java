package exchange.common.service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.DataSourceManager;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.QueryExecutorSum;
import exchange.common.component.RedisManager;
import exchange.common.component.RedisManager.LockParams;
import exchange.common.component.SesManager;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.MailNoreplyType;
import exchange.common.constant.StakingStatus;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.StakingApplyDetail;
import exchange.common.entity.StakingApplyDetail_;
import exchange.common.entity.StakingInfo;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.predicate.StakingApplyDetailPredicate;
import exchange.common.repos.StakingNetworkProfitRepository;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class StakingApplyDetailService extends EntityService<StakingApplyDetail, StakingApplyDetailPredicate> {

  private final UserService userService;
  
  private final StakingInfoService stakingInfoService;
  
  private final AssetService assetService;
  
  private final MailNoreplyService mailNoreplyService;
  
  private final SesManager sesManager;
  
  private final StakingNetworkProfitRepository stakingNetworkProfitRepository;
  
  private final StakingDateInfoService stakingDateInfoService;
  
  private final DataSourceManager dataSourceManager;  
  
  @Autowired
  private RedisManager redisManager;
  
  private static String getLockKey(Long userId, Currency currency) {
    return "lock:asset:" + userId + ":" + currency;
  }
  
  @Override
  public Class<StakingApplyDetail> getEntityClass() {
    return StakingApplyDetail.class;
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<StakingApplyDetail> root, Long stakingInfoId, Long userId, 
      Long applyDateFrom, Long applyDateTo, StakingStatus[] status, 
      Currency currency, Long cancelApplyDateFrom, Long cancelApplyDateTo, 
      Long expirationDateFrom, Long expirationDateTo, boolean checkRelationId) {
    List<Predicate> predicates = new ArrayList<>();
    
    if (stakingInfoId != null) {
      predicates.add(predicate.equalStakingInfoId(criteriaBuilder, root, stakingInfoId));
    }
    
    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }
    
    if (applyDateFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToApplyDateFrom(
              criteriaBuilder, root, new Date(applyDateFrom)));
    }

    if (applyDateTo != null) {
      predicates.add(predicate.lessThanApplyDateTo(criteriaBuilder, root, new Date(applyDateTo)));
    }
    
    if (status != null && status.length > 0) {
      predicates.add(predicate.inStakingStatus(criteriaBuilder, root, status));
    }
    
    if (currency != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
    }
    
    if (cancelApplyDateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToCancelApplyDateFrom(criteriaBuilder, root, new Date(cancelApplyDateFrom)));
    }
    
    if (cancelApplyDateTo != null) {
      predicates.add(predicate.lessThanCancelApplyDateTo(criteriaBuilder, root, new Date(cancelApplyDateTo)));
    }
    
    if (expirationDateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToExpirationDateFrom(criteriaBuilder, root, new Date(expirationDateFrom)));
    }
    
    if (expirationDateTo != null) {
      predicates.add(predicate.lessThanExpirationDateTo(criteriaBuilder, root, new Date(expirationDateTo)));
    }
    
    if (checkRelationId) {
      predicates.add(predicate.relationIdIsNull(criteriaBuilder, root));
    }
    
    return predicates;
  }
  
  public List<StakingApplyDetail> findByCondition(Long stakingInfoId, Long userId, 
      Long applyDateFrom, Long applyDateTo, StakingStatus[] status, 
      Currency currency, Long cancelApplyDateFrom, Long cancelApplyDateTo, 
      Long expirationDateFrom, Long expirationDateTo, boolean checkRelationId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByCondition(criteriaBuilder, root, 
                    stakingInfoId, 
                    userId, 
                    applyDateFrom, 
                    applyDateTo,
                    status,
                    currency,
                    cancelApplyDateFrom,
                    cancelApplyDateTo,
                    expirationDateFrom,
                    expirationDateTo,
                    checkRelationId);
              return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  public List<StakingApplyDetail> findRewardTargetByCondition(StakingStatus[] status,
      Long rewardStartDateFrom, Long expirationDateTo, Long cancelDate) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates = new ArrayList<>();
            
            if (status != null && status.length > 0) {
              predicates.add(predicate.inStakingStatus(criteriaBuilder, root, status));
            }
            
            if (rewardStartDateFrom != null) {
              predicates.add(
                  predicate.lessThanOrEqualToRewardStartDateFrom(
                      criteriaBuilder, root, new Date(rewardStartDateFrom)));
            }

            if (expirationDateTo != null) {
              predicates.add(predicate.greaterThanOrEqualToExpirationDateFrom(
                  criteriaBuilder, root, new Date(expirationDateTo)));
            }
            
            if (cancelDate != null) {
              predicates.add(predicate.lessThanCancelDate(criteriaBuilder, root, new Date(cancelDate)));
            }
            
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  public List<StakingApplyDetail> findByApplyDate(Long applyDateFrom, Long applyDateTo) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByApplyDate(criteriaBuilder, root, 
                    applyDateFrom, 
                    applyDateTo);
              return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  private List<Predicate> getPredicatesOfFindByApplyDate(CriteriaBuilder criteriaBuilder,
	      Root<StakingApplyDetail> root, Long applyDateFrom, Long applyDateTo) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.relationIdIsNull(criteriaBuilder, root));
    if (applyDateFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToApplyDateFrom(
              criteriaBuilder, root, new Date(applyDateFrom)));
    }
    if (applyDateTo != null) {
      predicates.add(predicate.lessThanApplyDateTo(criteriaBuilder, root, new Date(applyDateTo)));
    }
    
    return predicates;
  }
  
  public List<StakingApplyDetail> findByAmountToAccountDate(Long amountToAccountDateFrom, Long amountToAccountDateTo) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates =
            		getPredicatesOfFindByAmountToAccountDate(criteriaBuilder, root, 
                	amountToAccountDateFrom, 
                	amountToAccountDateTo);
              return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  private List<Predicate> getPredicatesOfFindByAmountToAccountDate(CriteriaBuilder criteriaBuilder,
	      Root<StakingApplyDetail> root, Long amountToAccountDateFrom, Long amountToAccountDateTo) {
    List<Predicate> predicates = new ArrayList<>();
    
    if (amountToAccountDateFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToAmountToAccountDateFrom(
              criteriaBuilder, root, new Date(amountToAccountDateFrom)));
    }
    if (amountToAccountDateTo != null) {
      predicates.add(predicate.lessThanAmountToAccountDateTo(criteriaBuilder, root, new Date(amountToAccountDateTo)));
    }
    
    return predicates;
  }
  
  
  //AssetSummary---findByApplyDate
  public List<StakingApplyDetail> findByApplyDateSummary(
	      Long userId,
	      Currency currency,
	      Long applyDateFrom, 
	      Long applyDateTo
	      ) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.relationIdIsNull(criteriaBuilder, root));
            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }
            if (currency != null) {
              predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
            }
            if (applyDateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToApplyDateFrom(
                      criteriaBuilder, root, new Date(applyDateFrom)));
            }
            if (applyDateTo != null) {
              predicates.add(predicate.lessThanApplyDateTo(criteriaBuilder, root, new Date(applyDateTo)));
            }
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(StakingApplyDetail_.id)));
          }
        });
  }
  
  //AssetSummary---findByAmountToAccount
  public List<StakingApplyDetail> findByAmountToAccountDateSummary(
	      Long userId,
	      Currency currency,
	      Long amountToAccountDateFrom, 
	      Long amountToAccountDateTo
	      ) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates = new ArrayList<>();
            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }
            if (currency != null) {
              predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
            }
            if (amountToAccountDateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToAmountToAccountDateFrom(
                      criteriaBuilder, root, new Date(amountToAccountDateFrom)));
            }
            if (amountToAccountDateTo != null) {
              predicates.add(predicate.lessThanAmountToAccountDateTo(criteriaBuilder, root, new Date(amountToAccountDateTo)));
            }
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(StakingApplyDetail_.id)));
          }
        });
  }
  
  //ExchangeSummary----findByApplyDate
 public BigDecimal sumExchangeByApplyDate(
	      Currency currency, Long applyDateFrom, Long applyDateTo) throws Exception {
	 return customTransactionManager.execute( entityManager -> {
		 var builder = entityManager.getCriteriaBuilder();
		 var cquery = builder.createQuery(BigDecimal.class);
		 var root = cquery.from(getEntityClass());
		 List<Predicate> predicates = new ArrayList<>();
		 predicates.add(predicate.relationIdIsNull(builder, root));
		 if (currency != null) {
			predicates.add(predicate.equalCurrency(builder, root, currency));
		 }
		 if (applyDateFrom != null) {
			predicates.add(predicate.greaterThanOrEqualToApplyDateFrom(builder, root, new Date(applyDateFrom)));
		 }
	     if (applyDateTo != null) {
	        predicates.add(predicate.lessThanApplyDateTo(builder, root, new Date(applyDateTo)));
	     }
	     var applyAmount = entityManager
	    	.createQuery(cquery.select(builder.sum(root.get(StakingApplyDetail_.applyAmount)))
	        .where(predicates.toArray(new Predicate[] {})))
	    	.getSingleResult();
	     if (applyAmount == null) {
	    	applyAmount = BigDecimal.ZERO;
	     }

	     return applyAmount;
    });
 }
 
// private BigDecimal sumExchangeApplyDate(
//	      EntityManager entityManager,
//	      List<Predicate> predicates) {
//	 var builder = entityManager.getCriteriaBuilder();
//	    var cquery = builder.createQuery(BigDecimal.class);
//	    var root = cquery.from(getEntityClass());
//	    var applyAmount = entityManager
//	        .createQuery(cquery.select(builder.sum(root.get(StakingApplyDetail_.applyAmount)))
//	            .where(predicates.toArray(new Predicate[] {})
//	        ))
//	        .getSingleResult();
//	    if (applyAmount == null) {
//	    	applyAmount = BigDecimal.ZERO;
//	    }
//   
//     return applyAmount;
// }
 
 
 //ExchangeSummary----findByAmountToAccountDate
 public Object[] sumExchangeByAmountToAccountDate(
	      Currency currency, Long amountToAccountDateFrom, Long amountToAccountDateTo,StakingStatus stakingStatus, boolean auto, boolean checkAuto) {
   return customTransactionManager.sum(
       getEntityClass(),
       new QueryExecutorSum<StakingApplyDetail>() {
         @Override
         public Object[] query() {
	    	  List<Predicate> predicates = new ArrayList<>();
	          
	          if (currency != null) {
	            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
	          }
	          if (amountToAccountDateFrom != null) {
	            predicates.add(
	                predicate.greaterThanOrEqualToAmountToAccountDateFrom(
	                      criteriaBuilder, root, new Date(amountToAccountDateFrom)));
	          }
	          if (amountToAccountDateTo != null) {
	            predicates.add(predicate.lessThanAmountToAccountDateTo(criteriaBuilder, root, new Date(amountToAccountDateTo)));
	          }
            
	          if (stakingStatus != null) {
                predicates.add(predicate.equalStakingStatus(criteriaBuilder, root, stakingStatus));
	          }
            
	          if (checkAuto) {	
	            predicates.add(predicate.isAutoContinue(criteriaBuilder, root, auto));
	          }
	        return sumExchangeAmountToAccountDate(entityManager, criteriaBuilder, criteriaQuery, root, predicates);
	      }
       });
 }
 
 private Object[] sumExchangeAmountToAccountDate(
	      EntityManager entityManager,
	      CriteriaBuilder criteriaBuilder,
	      CriteriaQuery<Object[]> criteriaQuery,
	      Root<StakingApplyDetail> root,
	      List<Predicate> predicates) {
	 
   criteriaQuery.multiselect(criteriaBuilder.sum(root.get(StakingApplyDetail_.APPLY_AMOUNT)),
		   criteriaBuilder.sum(root.get(StakingApplyDetail_.REWARD_ACCUMULATE)),
		   criteriaBuilder.sum(root.get(StakingApplyDetail_.AMOUNT_TO_ACCOUNT)),
		   criteriaBuilder.sum(root.get(StakingApplyDetail_.AMOUNT_TO_ACCOUNT)));


   if (!CollectionUtils.isEmpty(predicates)) {
     criteriaQuery.where(predicates.toArray(new Predicate[] {}));
   }
   try {
     return entityManager
         .createQuery(criteriaQuery)
         .setFirstResult(0)
         .setMaxResults(1)
         .getSingleResult();
   } catch (Exception e) {
     return null;
   }
 }
  
  
  public List<StakingApplyDetail> findByStakingControlId(Long stakingControlId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalStakingControlId(criteriaBuilder, root, stakingControlId));
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  public List<StakingApplyDetail> findByConditionForReport(Long userId, 
      Long amountToAccountDateFrom, 
      Long amountToAccountDateTo,
      StakingStatus stakingStatus
      ) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates = new ArrayList<>();
            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }
            if (amountToAccountDateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToAmountToAccountDateFrom(
                      criteriaBuilder, root, new Date(amountToAccountDateFrom)));
            }
            if (amountToAccountDateTo != null) {
              predicates.add(predicate.lessThanAmountToAccountDateTo(criteriaBuilder, root, new Date(amountToAccountDateTo)));
            }
            if (stakingStatus != null) {
              predicates.add(predicate.equalStakingStatus(criteriaBuilder, root, stakingStatus));
            }
            
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
  public StakingApplyDetail apply(Long userId, Long stakingInfoId, BigDecimal amount, boolean autoContinue) throws Exception {
    User user = userService.findOne(userId);
    StakingInfo stakingInfo = stakingInfoService.findOne(stakingInfoId);
    StakingApplyDetail stakingApplyDetail = new StakingApplyDetail();
    if (!redisManager.executeWithLock(getLockKey(userId, stakingInfo.getCurrency()), LockParams.LOCKKEY, () -> {
//    	return (StakingApplyDetail)
		customTransactionManager.execute(
	        entityManager -> {
	          // 申込履歴登録
	          Date sysDate = new Date();
	          Date stakeDate = new Date();
	          stakeDate = stakingDateInfoService.getStakingDate(stakingInfo.getCurrency());
	          long stakeDateFrom = stakeDate.getTime();
	          Date rewardStartDate = new Date();
	          Date expirationDate = new Date();
	          Date cancelDate = new Date();
	          Date cancelDisableFrom = new Date();
	          Date cancelDisableTo = new Date();
	          Date stakingDatePlan = new Date();
	          if(Currency.ETH.equals(stakingInfo.getCurrency())) {
	            // 報酬発生日 = stake実施日 + 1
	            rewardStartDate = new Date(stakeDateFrom + 1000*3600*24);
	            // 期間満了日 = stake実施日 + 期間
	            expirationDate = new Date(stakeDateFrom 
	                + 1000*3600*24*Long.valueOf(stakingInfo.getPeriod()));
	            // UNLOCK日 = 期間満了日 + 3
	            cancelDate = new Date(expirationDate.getTime() + 1000*3600*24*3);
	            cancelDisableFrom = null;
	            cancelDisableTo = null;
	            // Staking予定日
	            stakingDatePlan = new Date(stakeDateFrom);
	          } else {
	            // 報酬発生日 = stake実施日 + 2
	            rewardStartDate = new Date(stakeDateFrom + 1000*3600*24*2);
	            // 期間満了日 = stake実施日 + 1 + 期間
	            expirationDate = new Date(stakeDateFrom 
	                + 1000*3600*24*1 
	                + 1000*3600*24*Long.valueOf(stakingInfo.getPeriod()));
	            // UNLOCK日 = 期間満了日 + 3
	            cancelDate = new Date(expirationDate.getTime() + 1000*3600*24*3);
	            // 解除禁止期間 = stake実施日～stake実施日 + 6
	            cancelDisableFrom = new Date(stakeDateFrom);
	            cancelDisableTo = new Date(stakeDateFrom + 1000*3600*24*7 - 1);
	            // Staking予定日
	            stakingDatePlan = new Date(stakeDateFrom);
	          }
	          
	          BigDecimal yearRate = BigDecimal.ZERO;
	          // ネット予想年率　- ＣＢ設定年率
	          BigDecimal latestProfit = 
	              stakingNetworkProfitRepository.latestProfit(stakingInfo.getCurrency().name());
	          yearRate =latestProfit.subtract(stakingInfo.getCbFeeYearRate());
	          if(yearRate.compareTo(BigDecimal.ZERO) < 0) {
	            yearRate = BigDecimal.ZERO;
	          }
	          
	          stakingApplyDetail.setUserId(userId);
	          stakingApplyDetail.setStakingInfoId(stakingInfoId);
	          stakingApplyDetail.setCurrency(stakingInfo.getCurrency());
	          stakingApplyDetail.setYearRate(yearRate);
	          stakingApplyDetail.setStakingDatePlan(stakingDatePlan);
	          stakingApplyDetail.setApplyDate(sysDate);
	          stakingApplyDetail.setApplyAmount(amount);
	          stakingApplyDetail.setRewardAccumulate(BigDecimal.ZERO);
	          stakingApplyDetail.setStakingStatus(StakingStatus.APPLING);
	          stakingApplyDetail.setExpirationDate(expirationDate);
	          stakingApplyDetail.setAutoContinue(autoContinue);
	          stakingApplyDetail.setRewardStartDate(rewardStartDate);
	          stakingApplyDetail.setCancelDisableDateFrom(cancelDisableFrom);
	          stakingApplyDetail.setCancelDisableDateTo(cancelDisableTo);
	          stakingApplyDetail.setCancelDate(cancelDate);
	          stakingApplyDetail.setCreatedBy(user.getEmail());
	          stakingApplyDetail.setUpdatedBy(user.getEmail());
	          save(stakingApplyDetail);
	          // ユーザー資産更新
	          assetService.updateWithExternalLock(
	              userId,
	              stakingInfo.getCurrency(),
	              BigDecimal.ZERO,
	              amount,
	              entityManager);
	          return stakingApplyDetail;
	        });	
    })) {
    	log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(userId, stakingInfo.getCurrency()));
    	throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + userId);
    };
    if (stakingApplyDetail != null && StakingStatus.APPLING.equals(stakingApplyDetail.getStakingStatus())) {
    	// メール送信：申込確定
        MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.STAKING_APPLY_CONFIRM);
        try {
          // 通貨
          String currency = stakingApplyDetail.getCurrency().toString();
          // 期間
          String period = stakingInfo.getPeriod().toString();
          // 予想年率
          String yearRateMail = stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString();
          // 申込数量
          String applyAmount = stakingApplyDetail.getApplyAmount().stripTrailingZeros().toPlainString() + currency;
          // 自動継続
          String autoContinueText = "自動継続しない";
          if(stakingApplyDetail.isAutoContinue()) {
            autoContinueText = "同じ期間自動継続する";
          }
          // 申込日
          String applyDateMail = FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), 
              FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
          // 報酬発生日
          String rewardDateMail = FormatUtil.formatJst(stakingApplyDetail.getRewardStartDate(), 
              FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
          // 期間満了日
          String expirationDateMail = FormatUtil.formatJst(stakingApplyDetail.getExpirationDate(), 
              FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
          String cancelDisableDate = "";
          if(Currency.ADA.equals(stakingApplyDetail.getCurrency())) {
            // 申込解除禁止期間
            String cancelDisableDateFromMail = FormatUtil.formatJst(stakingApplyDetail.getCancelDisableDateFrom(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            String cancelDisableDateToMail = FormatUtil.formatJst(stakingApplyDetail.getCancelDisableDateTo(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            cancelDisableDate = "\r\n申込解除不可期間" + "　" + cancelDisableDateFromMail + "～" + cancelDisableDateToMail;
          }
          
          MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
          String[] args = new String[] {
              currency, 
              period, 
              yearRateMail, 
              applyAmount, 
              autoContinueText,
              applyDateMail,
              rewardDateMail,
              expirationDateMail,
              cancelDisableDate};
          String mailContent = messageFormat.format(args);
          sesManager.send(
              mailNoreply.getFromAddress(),
              user.getEmail(),
              mailNoreply.getTitle(),
              mailContent);
        } catch (Exception e) {
          log.error("send staking mail failed.please send mail manually,email:{}",
              user.getEmail());
        }
	}
    return stakingApplyDetail;
  }
  
  public StakingApplyDetail applyCancel(Long userId, StakingApplyDetail stakingApplyDetail) throws Exception {
    User user = userService.findOne(userId);
    StakingInfo stakingInfo = stakingInfoService.findOne(stakingApplyDetail.getStakingInfoId());
    return customTransactionManager.execute(
        entityManager -> {
          String currentDateString = FormatUtil.formatJst(new Date(), FormatPattern.YYYYMMDD);
          Date currentDate = FormatUtil.parseJst(currentDateString, FormatPattern.YYYYMMDD);
          long todayFrom = currentDate.getTime();
          long cancelDate = todayFrom + 1000*3600*24*3;
          boolean autoContinue = stakingApplyDetail.isAutoContinue();
          stakingApplyDetail.setRewardAccumulate(BigDecimal.ZERO);
          stakingApplyDetail.setStakingStatus(StakingStatus.CANCEL_APPLY);
          stakingApplyDetail.setCancelApplyDate(new Date());
          stakingApplyDetail.setCancelDate(new Date(cancelDate));
          stakingApplyDetail.setUpdatedBy(user.getEmail());
          save(stakingApplyDetail);
          // メール送信：解除
          MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.STAKING_CANCEL_APPLY);
          try {
            // 解除受付日
            String cancelApplyDateMail = FormatUtil.formatJst(stakingApplyDetail.getCancelApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 通貨
            String currency = stakingApplyDetail.getCurrency().toString();
            // 期間
            String period = stakingInfo.getPeriod().toString();
            // 予想年率
            String yearRateMail = stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString();
            // 申込数量
            String applyAmount = stakingApplyDetail.getApplyAmount().stripTrailingZeros().toPlainString() + currency;
            // 自動継続
            String autoContinueText = "自動継続しない";
            if(autoContinue) {
              autoContinueText = "同じ期間自動継続する";
            }
            // 申込日
            String applyDateMail = FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 期間満了日
            String expirationDateMail = FormatUtil.formatJst(stakingApplyDetail.getExpirationDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            
            MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
            String[] args = new String[] {
                cancelApplyDateMail,
                currency, 
                period, 
                yearRateMail, 
                applyAmount, 
                autoContinueText,
                applyDateMail,
                expirationDateMail};
            String mailContent = messageFormat.format(args);
            sesManager.send(
                mailNoreply.getFromAddress(),
                user.getEmail(),
                mailNoreply.getTitle(),
                mailContent);
          } catch (Exception e) {
            log.error("send staking mail failed.please send mail manually,email:{}",
                user.getEmail());
          }
          return stakingApplyDetail;
        });
  }
  
  public StakingApplyDetail boApplyCancel(String email, StakingApplyDetail stakingApplyDetail) throws Exception {
    User user = userService.findOne(stakingApplyDetail.getUserId());
    StakingInfo stakingInfo = stakingInfoService.findOne(stakingApplyDetail.getStakingInfoId());
    return customTransactionManager.execute(
        entityManager -> {
          String currentDateString = FormatUtil.formatJst(new Date(), FormatPattern.YYYYMMDD);
          Date currentDate = FormatUtil.parseJst(currentDateString, FormatPattern.YYYYMMDD);
          long todayFrom = currentDate.getTime();
          long cancelDate = todayFrom + 1000*3600*24*3;
          boolean autoContinue = stakingApplyDetail.isAutoContinue();
          stakingApplyDetail.setRewardAccumulate(BigDecimal.ZERO);
          stakingApplyDetail.setStakingStatus(StakingStatus.CANCEL_APPLY);
          stakingApplyDetail.setCancelApplyDate(new Date());
          stakingApplyDetail.setCancelDate(new Date(cancelDate));
          stakingApplyDetail.setUpdatedBy(email);
          save(stakingApplyDetail);
          // メール送信：解除
          MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.STAKING_CANCEL_APPLY);
          try {
            // 解除受付日
            String cancelApplyDateMail = FormatUtil.formatJst(stakingApplyDetail.getCancelApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 通貨
            String currency = stakingApplyDetail.getCurrency().toString();
            // 期間
            String period = stakingInfo.getPeriod().toString();
            // 予想年率
            String yearRateMail = stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString();
            // 申込数量
            String applyAmount = stakingApplyDetail.getApplyAmount().stripTrailingZeros().toPlainString() + currency;
            // 自動継続
            String autoContinueText = "自動継続しない";
            if(autoContinue) {
              autoContinueText = "同じ期間自動継続する";
            }
            // 申込日
            String applyDateMail = FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 期間満了日
            String expirationDateMail = FormatUtil.formatJst(stakingApplyDetail.getExpirationDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            
            MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
            String[] args = new String[] {
                cancelApplyDateMail,
                currency, 
                period, 
                yearRateMail, 
                applyAmount, 
                autoContinueText,
                applyDateMail,
                expirationDateMail};
            String mailContent = messageFormat.format(args);
            sesManager.send(
                mailNoreply.getFromAddress(),
                user.getEmail(),
                mailNoreply.getTitle(),
                mailContent);
          } catch (Exception e) {
            log.error("send staking mail failed.please send mail manually,email:{}",
                user.getEmail());
          }
          return stakingApplyDetail;
        });
  }
  
  public void stakingFound(StakingApplyDetail stakingApplyDetail, 
      BigDecimal onhandAmountDiff, BigDecimal lockedAmountDiff, 
      StakingStatus stakingStatus, MailNoreplyType mailType) throws Exception {
    User user = userService.findOne(stakingApplyDetail.getUserId());
    StakingInfo stakingInfo = stakingInfoService.findOne(stakingApplyDetail.getStakingInfoId());
    
    if (!redisManager.executeWithLock(getLockKey(stakingApplyDetail.getUserId(), stakingApplyDetail.getCurrency()), LockParams.LOCKKEY, () -> {
//    	return (StakingApplyDetail)
		customTransactionManager.execute(
	        entityManager -> {
	          // ユーザー資産更新
	          assetService.updateWithExternalLock(
	              stakingApplyDetail.getUserId(),
	              stakingApplyDetail.getCurrency(),
	              onhandAmountDiff,
	              lockedAmountDiff.negate(),
	              entityManager);
	          Date sysDate = new Date();
	          stakingApplyDetail.setStakingStatus(stakingStatus);
	          stakingApplyDetail.setAmountToAccount(onhandAmountDiff.add(lockedAmountDiff));
	          stakingApplyDetail.setAmountToAccountDate(sysDate);
	          stakingApplyDetail.setUpdatedBy("WORKER");
	          save(stakingApplyDetail);
	          return stakingApplyDetail;
	        });
    })) {
    	log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(stakingApplyDetail.getUserId(), stakingApplyDetail.getCurrency()));
    	throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + stakingApplyDetail.getUserId());
    };
    if (StakingStatus.EXPIRED_FUNDED.equals(stakingApplyDetail.getStakingStatus()) || StakingStatus.CANCEL_FUNDED.equals(stakingApplyDetail.getStakingStatus())) {
    	// メール送信：期間満了返金・解除返金
        MailNoreply mailNoreply = mailNoreplyService.findOne(mailType);
        try {
          if(MailNoreplyType.STAKING_EXPIRATION_FUNDED.equals(mailType)) {
            // 通貨
            String currency = stakingApplyDetail.getCurrency().toString();
            // 返金日
            String fundedDate = FormatUtil.formatJst(stakingApplyDetail.getAmountToAccountDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 返金数量
            String fundedAmount = stakingApplyDetail.getAmountToAccount().stripTrailingZeros().toPlainString() + currency;
            // 確定報酬
            String confirmedAmount = stakingApplyDetail.getRewardAccumulate().stripTrailingZeros().toPlainString() + currency;
            // 申込日
            String applyDateMail = FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
            String[] args = new String[] {
                fundedDate, 
                currency, 
                fundedAmount, 
                confirmedAmount,
                applyDateMail};
            String mailContent = messageFormat.format(args);
            sesManager.send(
                mailNoreply.getFromAddress(),
                user.getEmail(),
                mailNoreply.getTitle(),
                mailContent);
          } else {
            // 通貨
            String currency = stakingApplyDetail.getCurrency().toString();
            // 期間
            String period = stakingInfo.getPeriod().toString();
            // 予想年率
            String yearRateMail = stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString();
            // 申込数量
            String applyAmount = stakingApplyDetail.getApplyAmount().stripTrailingZeros().toPlainString() + currency;
            // 自動継続
            String autoContinueText = "自動継続しない";
            if(stakingApplyDetail.isAutoContinue()) {
              autoContinueText = "同じ期間自動継続する";
            }
            // 申込日
            String applyDateMail = FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 解除申込日
            String cancelApplyDateMail = FormatUtil.formatJst(stakingApplyDetail.getCancelApplyDate(), 
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            // 解除完了日
            String cancelDoneDate = FormatUtil.formatJst(stakingApplyDetail.getAmountToAccountDate(),
                FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
            
            MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
            String[] args = new String[] {
                currency, 
                period, 
                yearRateMail, 
                applyAmount, 
                autoContinueText,
                applyDateMail,
                cancelApplyDateMail,
                cancelDoneDate};
            String mailContent = messageFormat.format(args);
            sesManager.sendWithoutThread(
                mailNoreply.getFromAddress(),
                user.getEmail(),
                mailNoreply.getTitle(),
                mailContent);
          }
        } catch (Exception e) {
          log.error("send staking mail {} failed.please send mail manually,email:{}", mailType,
              user.getEmail());
        }
	}
    
  }
  
  public PageData<StakingApplyDetail> findByConditionPageData(Long id, Long userId, List<Long> ids,
      BigDecimal yearRateFrom, BigDecimal yearRateTo, Long applyAmountFrom, Long applyAmountTo,
      BigDecimal rewardAccumulateFrom, BigDecimal rewardAccumulateTo, String stakingStatus,
      Boolean autoContinue, Long applyTimeFrom, Long applyTimeTo, Long stakingDatePlanFrom,
      Long stakingDatePlanTo, Long cancelApplyDateFrom, Long cancelApplyDateTo,
      Long expirationDateFrom, Long expirationDateTo, Long cancelDateFrom, Long cancelDateTo,
      Long amountToAccountDateFrom, Long amountToAccountDateTo, Integer number, Integer size, boolean checkRelationId) {
    long count = customTransactionManager.count(getEntityClass(), new QueryExecutorCounter<>() {
      @Override
      public Long query() {
        return count(entityManager, criteriaBuilder, criteriaQuery, root,
            getPredicatesOfFindByCondition(criteriaBuilder, root, id, userId, ids, yearRateFrom,
                yearRateTo, applyAmountFrom, applyAmountTo, rewardAccumulateFrom,
                rewardAccumulateTo, stakingStatus, autoContinue, applyTimeFrom, applyTimeTo, 
                stakingDatePlanFrom, stakingDatePlanTo, cancelApplyDateFrom, cancelApplyDateTo, 
                expirationDateFrom, expirationDateTo, cancelDateFrom, cancelDateTo,
                amountToAccountDateFrom, amountToAccountDateTo, checkRelationId));
      }

    });

    return new PageData<StakingApplyDetail>(number, size, count,
        customTransactionManager.find(getEntityClass(),
            new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
              @Override
              public List<StakingApplyDetail> query() {
                List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                    id, userId, ids, yearRateFrom, yearRateTo, applyAmountFrom, applyAmountTo,
                    rewardAccumulateFrom, rewardAccumulateTo, stakingStatus, autoContinue, applyTimeFrom, applyTimeTo, 
                    stakingDatePlanFrom, stakingDatePlanTo, cancelApplyDateFrom, cancelApplyDateTo, 
                    expirationDateFrom, expirationDateTo, cancelDateFrom, cancelDateTo,
                    amountToAccountDateFrom, amountToAccountDateTo, checkRelationId);
                return getResultList(entityManager, criteriaQuery, root, predicates, number, size,
                    criteriaBuilder.desc(root.get(StakingApplyDetail_.id)));
              }
            }));
  }
  
  public List<StakingApplyDetail> findByCondition(Long id, Long userId, List<Long> ids,
      BigDecimal yearRateFrom, BigDecimal yearRateTo, Long applyAmountFrom, Long applyAmountTo,
      BigDecimal rewardAccumulateFrom, BigDecimal rewardAccumulateTo, String stakingStatus,
      Boolean autoContinue, Long applyTimeFrom, Long applyTimeTo, Long stakingDatePlanFrom,
      Long stakingDatePlanTo, Long cancelApplyDateFrom, Long cancelApplyDateTo,
      Long expirationDateFrom, Long expirationDateTo, Long cancelDateFrom, Long cancelDateTo,
      Long amountToAccountDateFrom, Long amountToAccountDateTo, boolean checkRelationId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingApplyDetail, List<StakingApplyDetail>>() {
          @Override
          public List<StakingApplyDetail> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                id, userId, ids, yearRateFrom, yearRateTo, applyAmountFrom, applyAmountTo,
                rewardAccumulateFrom, rewardAccumulateTo, stakingStatus, autoContinue, applyTimeFrom, applyTimeTo, 
                stakingDatePlanFrom, stakingDatePlanTo, cancelApplyDateFrom, cancelApplyDateTo, 
                expirationDateFrom, expirationDateTo, cancelDateFrom, cancelDateTo,
                amountToAccountDateFrom, amountToAccountDateTo, checkRelationId);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(StakingApplyDetail_.id)));
          }
        });

  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<StakingApplyDetail> root, Long id, Long userId, List<Long> ids, BigDecimal yearRateFrom,
      BigDecimal yearRateTo, Long applyAmountFrom, Long applyAmountTo, BigDecimal rewardAccumulateFrom,
      BigDecimal rewardAccumulateTo, String stakingStatus, Boolean autoContinue, Long applyTimeFrom,
      Long applyTimeTo, Long stakingDatePlanFrom, Long stakingDatePlanTo, Long cancelApplyDateFrom,
      Long cancelApplyDateTo, Long expirationDateFrom, Long expirationDateTo, Long cancelDateFrom,
      Long cancelDateTo, Long amountToAccountDateFrom, Long amountToAccountDateTo, boolean checkRelationId) {
    List<Predicate> predicates = new ArrayList<>();

    if (id != null) {
      predicates.add(predicate.equalId( criteriaBuilder, root, id));
      
    }

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (!CollectionUtils.isEmpty(ids)) {
      predicates.add(predicate.inStakingInfoIds(criteriaBuilder, root, ids));
    }
    
    if (!StringUtils.isEmpty(stakingStatus)) {
      predicates.add(predicate.equalStakingStatus(criteriaBuilder, root, StakingStatus.valueOfName(stakingStatus)));
    }
    
    if (autoContinue != null) {
      predicates.add(predicate.isAutoContinue(criteriaBuilder, root, autoContinue));
    }
    
    if (yearRateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToYearRateFrom(criteriaBuilder, root, yearRateFrom));
    }

    if (yearRateTo != null) {
      predicates.add(predicate.lessThanOrEqualToYearRateTo(criteriaBuilder, root, yearRateTo));
    }
    
    if (applyAmountFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToApplyAmountFrom(criteriaBuilder, root, new BigDecimal(applyAmountFrom)));
    }

    if (applyAmountTo != null) {
      predicates.add(predicate.lessThanOrEqualToApplyAmountTo(criteriaBuilder, root, new BigDecimal(applyAmountTo)));
    }

    if (rewardAccumulateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToRewardAccumulateFrom(criteriaBuilder, root, rewardAccumulateFrom));
    }

    if (rewardAccumulateTo != null) {
      predicates.add(predicate.lessThanOrEqualToRewardAccumulateTo(criteriaBuilder, root, rewardAccumulateTo));
    }
    
    if (applyTimeFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToApplyDateFrom(criteriaBuilder, root,
          new Date(applyTimeFrom)));
    }

    if (applyTimeTo != null) {
      predicates.add(predicate.lessThanApplyDateTo(criteriaBuilder, root, new Date(applyTimeTo)));
    }
    
    if (stakingDatePlanFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToStakingDatePlanFrom(criteriaBuilder, root,
          new Date(stakingDatePlanFrom)));
    }

    if (stakingDatePlanTo != null) {
      predicates.add(predicate.lessThanStakingDatePlanTo(criteriaBuilder, root, new Date(stakingDatePlanTo)));
    }
    
    if (cancelApplyDateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToCancelApplyDateFrom(criteriaBuilder, root,
          new Date(cancelApplyDateFrom)));
    }

    if (cancelApplyDateTo != null) {
      predicates.add(predicate.lessThanCancelApplyDateTo(criteriaBuilder, root, new Date(cancelApplyDateTo)));
    }
    
    if (expirationDateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToExpirationDateFrom(criteriaBuilder, root, new Date(expirationDateFrom)));
    }
    

    if (expirationDateTo != null) {
      predicates.add(predicate.lessThanExpirationDateTo(criteriaBuilder, root, new Date(expirationDateTo)));
    }
    
    if (cancelDateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToCancelDateFrom(criteriaBuilder, root, new Date(cancelDateFrom)));
    }
    
    if (cancelDateTo != null) {
      predicates.add(predicate.lessThanCancelDateTo(criteriaBuilder, root, new Date(cancelDateTo)));
    }
    
    if (amountToAccountDateFrom != null) {
      predicates.add(predicate.greaterThanOrEqualToAmountToAccountDateFrom(criteriaBuilder, root, new Date(amountToAccountDateFrom)));
    }
    
    if (amountToAccountDateTo != null) {
      predicates.add(predicate.lessThanAmountToAccountDateTo(criteriaBuilder, root, new Date(amountToAccountDateTo)));
    }
    
    if (checkRelationId) {
      predicates.add(predicate.relationIdIsNull(criteriaBuilder, root));

    }
 
    return predicates;
  }

  public List<Long> getIDByRelationId(Long relationId) {

    String sql =
        "select id from staking_apply_detail where relation_id = :relationId";
    Query query = dataSourceManager.getMasterEntityManagerFactory().createEntityManager()
        .createNativeQuery(sql);
    query.setParameter("relationId", relationId);
    @SuppressWarnings("unchecked")
    List<BigInteger> list = query.getResultList();
    List<Long> ids = new ArrayList<>();
    if(list != null && list.size() > 0) {
      for (BigInteger id : list) {
        ids.add(id.longValue());
      }
    }
    return ids;
  }
  
  public void stakingAutoContinueMailSend(StakingApplyDetail stakingApplyDetail) throws Exception {
    User user = userService.findOne(stakingApplyDetail.getUserId());
    StakingInfo stakingInfo = stakingInfoService.findOne(stakingApplyDetail.getStakingInfoId());
    try {
      // メール送信：自動継続
      MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.STAKING_AUTOCONTINUE);
      // 通貨
      String currency = stakingApplyDetail.getCurrency().toString();
      // 期間
      String period = stakingInfo.getPeriod().toString();
      // 予想年率
      String yearRateMail = stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString();
      // 申込数量
      String applyAmount = stakingApplyDetail.getApplyAmount().stripTrailingZeros().toPlainString() + currency;
      // 報酬発生日
      String rewardDateMail = FormatUtil.formatJst(stakingApplyDetail.getRewardStartDate(), 
          FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
      // 期間満了日
      String expirationDateMail = FormatUtil.formatJst(stakingApplyDetail.getExpirationDate(), 
          FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
      MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
      String[] args = new String[] {
          currency,
          period, 
          yearRateMail, 
          applyAmount, 
          rewardDateMail,
          expirationDateMail};
      String mailContent = messageFormat.format(args);
      sesManager.sendWithoutThread(
          mailNoreply.getFromAddress(),
          user.getEmail(),
          mailNoreply.getTitle(),
          mailContent);
    } catch (Exception e) {
      log.error("send staking mail {} failed.please send mail manually,email:{}", MailNoreplyType.STAKING_AUTOCONTINUE,
          user.getEmail());
    }
  }
  
  public void stakingExpiratedMailSend(StakingApplyDetail stakingApplyDetail) throws Exception {
    User user = userService.findOne(stakingApplyDetail.getUserId());
    StakingInfo stakingInfo = stakingInfoService.findOne(stakingApplyDetail.getStakingInfoId());
    try {
      // メール送信：期間満了
      MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.STAKING_EXPIRATED);
      // 通貨
      String currency = stakingApplyDetail.getCurrency().toString();
      // 期間
      String period = stakingInfo.getPeriod().toString();
      // 予想年率
      String yearRateMail = stakingInfo.getCusYearRateDisplay().stripTrailingZeros().toPlainString();
      // 申込数量
      String applyAmount = stakingApplyDetail.getApplyAmount().stripTrailingZeros().toPlainString() + currency;
      // 確定報酬
      String confirmedAmount = stakingApplyDetail.getRewardAccumulate().stripTrailingZeros().toPlainString() + currency;
      // 申込日
      String applyDateMail = FormatUtil.formatJst(stakingApplyDetail.getApplyDate(), 
          FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
      // 期間満了日
      String expirationDateMail = FormatUtil.formatJst(stakingApplyDetail.getExpirationDate(), 
          FormatUtil.FormatPattern.YYYY_MM_DD_MAIL);
      
      MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
      String[] args = new String[] {
          currency,
          period, 
          yearRateMail, 
          applyAmount, 
          confirmedAmount,
          applyDateMail,
          expirationDateMail};
      String mailContent = messageFormat.format(args);
      sesManager.sendWithoutThread(
          mailNoreply.getFromAddress(),
          user.getEmail(),
          mailNoreply.getTitle(),
          mailContent);
    } catch (Exception e) {
      log.error("send staking mail {} failed.please send mail manually,email:{}", MailNoreplyType.STAKING_EXPIRATED,
          user.getEmail());
    }
  }
}