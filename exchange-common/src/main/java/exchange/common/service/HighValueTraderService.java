package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.HighValueTrader;
import exchange.common.entity.HighValueTrader_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.HighValueTraderPredicate;

@Service
public class HighValueTraderService
    extends EntityService<HighValueTrader, HighValueTraderPredicate> {

  @Override
  public Class<HighValueTrader> getEntityClass() {
    return HighValueTrader.class;
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<HighValueTrader> root,
      Long userId,
      Long dateFrom,
      Long dateTo,
      Integer number,
      Integer size) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (dateFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
    }

    if (dateTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
    }

    return predicates;
  }

  @Override
  protected void fetch(Root<HighValueTrader> root) {
    super.fetch(root);
    root.fetch(HighValueTrader_.user, JoinType.LEFT);
  }

  public PageData<HighValueTrader> findByCondition(
      Long userId, Long dateFrom, Long dateTo, Integer number, Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder, root, userId, dateFrom, dateTo, number, size));
              }
            });

    return new PageData<HighValueTrader>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<HighValueTrader, List<HighValueTrader>>() {
              @Override
              public List<HighValueTrader> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder, root, userId, dateFrom, dateTo, number, size);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(HighValueTrader_.id)));
              }
            }));
  }
}
