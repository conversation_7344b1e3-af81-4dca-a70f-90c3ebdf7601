package exchange.common.service;

import com.google.common.collect.Lists;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.RedisClient;
import exchange.common.component.SesManager;
import exchange.common.component.TransactionExecutor;
import exchange.common.constant.CryptoToken;
import exchange.common.constant.CryptoTokenApplyStatus;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.MailNoreplyType;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.User;
import exchange.common.entity.cryptoToken.CryptoTokenApply;
import exchange.common.entity.cryptoToken.CryptoTokenApply_;
import exchange.common.entity.cryptoToken.CryptoTokenConfig;
import exchange.common.exception.CustomException;
import exchange.common.predicate.CryptoTokenApplyPredicate;
import exchange.common.util.ses.SesMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Slf4j
@Service
public class CryptoTokenApplyService extends EntityService<CryptoTokenApply, CryptoTokenApplyPredicate> {

	@Autowired
	private RedisClient redisClient;

	@Autowired
	private CryptoTokenConfigService cryptoTokenConfigService;

	@Autowired
	private MailNoreplyService mailNoreplyService;

	@Autowired
	private SesManager sesManager;

	@Autowired
	private UserService userService;

	@Override
	public Class<CryptoTokenApply> getEntityClass() {
		return CryptoTokenApply.class;
	}

	private String getCreateLockKey(Long userId, CryptoToken cryptoToken) {
		return "crypto-token:apply:create:" + userId + ":" + cryptoToken;
	}

	public String getUpdateLockKey(Long userId, CryptoToken cryptoToken) {
		return "crypto-token:apply:update:" + userId + ":" + cryptoToken;
	}

	public CryptoTokenApply create(Long userId, CryptoToken cryptoToken, Integer dataSourceType, EntityManager entityManager) throws Exception {
		String lockKey = getCreateLockKey(userId, cryptoToken);
		try{
			if (!redisClient.tryLock(lockKey)) {
				throw new CustomException(ErrorCode.REQUEST_ERROR_REQ_QUICK_FAIL);
			}
			CryptoTokenConfig cryptoTokenConfig = cryptoTokenConfigService.detail(cryptoToken);
			if (null == cryptoTokenConfig) {
				throw new CustomException(ErrorCode.REQUEST_ERROR_CRYPTO_TOKEN_NOT_FOUND);
			}
			Date now = new Date();
			if (CryptoTokenApply.DATA_SOURCE_TYPE_USER_APPLY.equals(dataSourceType)) {
				if (null != cryptoTokenConfig.getOpenApplyEndTime() && now.after(cryptoTokenConfig.getOpenApplyEndTime())) {
					throw new CustomException(ErrorCode.REQUEST_ERROR_CRYPTO_TOKEN_APPLY_TERMINATED);
				}
			}
			CryptoTokenApply oldCryptoTokenApply = findLastOne(userId, cryptoToken);
			if (null != oldCryptoTokenApply) {
				throw new CustomException(ErrorCode.REQUEST_ERROR_CRYPTO_TOKEN_CANNOT_REPEAT_APPLY);
			}

			CryptoTokenApply cryptoTokenApply = newEntity();
			cryptoTokenApply.setUserId(userId);
			cryptoTokenApply.setCryptoToken(cryptoToken);
			cryptoTokenApply.setApplyStatus(CryptoTokenApplyStatus.CREATE);
			cryptoTokenApply.setApplyTime(now);
			cryptoTokenApply.setAssignedQuantity(BigDecimal.ZERO);
			cryptoTokenApply.setDataSourceType(dataSourceType);

			if (null == entityManager) {
				customTransactionManager.execute(em -> {
					this.save(cryptoTokenApply, em);
				});
			} else {
				this.save(cryptoTokenApply, entityManager);
			}

			if (CryptoTokenApply.DATA_SOURCE_TYPE_USER_APPLY.equals(dataSourceType)) {
				// send email
				sendApplyCreateMail(userId);
			}

			return cryptoTokenApply;
		} finally{
			redisClient.unlock(lockKey);
		}
	}

	public void sendApplyCreateMail(Long userId) {
		User user = userService.findOne(userId);
		if (user == null) {
			log.info("sendApplyCreateMail failed. user not exists! userId:{}", userId);
			return;
		}

		try {
			MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.CRYPTO_TOKEN_APPLY_CREATE);
			if (null == mailNoreply) {
				log.info("sendApplyCreateMail failed. mail template not exists! userId:{}", userId);
				return;
			}
			String mailBody = mailNoreply.getContents();
			// send mail
			sesManager.sendAsync(new SesMessage().setFrom(mailNoreply.getFromAddress())
				.setToList(Lists.newArrayList(user.getEmail()))
				.setSubject(mailNoreply.getTitle())
				.setBody(mailBody)
			);
		} catch (Exception e) {
			log.error("sendApplyCreateMail error", e);
		}
	}


	public CryptoTokenApply findLastOne(Long userId, CryptoToken cryptoToken) {
		return customTransactionManager.find(
				getEntityClass(),
				new QueryExecutorReturner<CryptoTokenApply, CryptoTokenApply>() {
					@Override
					public CryptoTokenApply query() {
						List<Predicate> predicates = new ArrayList<>();
						predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
						predicates.add(predicate.equalCryptoToken(criteriaBuilder, root, cryptoToken));

						return getSingleResult(entityManager, criteriaQuery, root, predicates, criteriaBuilder.desc(root.get(CryptoTokenApply_.id)));
					}
				});
	}
}

