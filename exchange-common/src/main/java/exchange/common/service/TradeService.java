package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;
import exchange.common.component.HistoricalTransactionManager;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.RedisManager;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeAction;
import exchange.common.entity.Symbol;
import exchange.common.entity.Trade;
import exchange.common.entity.Trade_;
import exchange.common.model.TradeRowMapper;
import exchange.common.model.response.PageData;
import exchange.common.predicate.TradePredicate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class TradeService<
        E extends Trade, P extends TradePredicate<E>, R extends TradeRowMapper<E>>
    extends EntityService<E, P> {

  @Autowired protected HistoricalTransactionManager historicalTransactionManager;

  @Autowired protected RedisManager redisManager;

  @Autowired protected SymbolService symbolService;

  public abstract List<E> findLatestTaker(Long symbolId, int size);

  public abstract void archive(Symbol symbol);

  public abstract Class<R> getRowMapperClass();

  public R newRowMapper() {
    R rowMapper = null;

    try {
      rowMapper = getRowMapperClass().getDeclaredConstructor().newInstance();
    } catch (Exception e) {
      log.error("newMapper failed", e);
    }

    return rowMapper;
  }

  public E findFromHistory(Symbol symbol, Long id) {
    return historicalTransactionManager.findOneFromHistory(
        "select * from " + Trade.getTableName(symbol) + " where id = ?", id, newRowMapper());
  }

  public List<E> findAllFromHistory(Symbol symbol, List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return new ArrayList<>();
    }

    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("ids", ids);
    return historicalTransactionManager.findFromHistory(
        "select * from " + Trade.getTableName(symbol) + " where id in (:ids)",
        mapSqlParameterSource,
        newRowMapper());
  }

  public List<E> findByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      Integer number,
      Integer size) {
    return findByCondition(
        symbolId, userId, id, idFrom, idTo, dateFrom, dateTo, tradeAction, number, size, true);
  }

  public List<E> findByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      Integer number,
      Integer size,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            if (id != null) {
              predicates.add(predicate.equalId(criteriaBuilder, root, id));
            } else {
              if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
              }

              if (idTo != null) {
                predicates.add(predicate.lessThanId(criteriaBuilder, root, idTo));
              }

              if (dateFrom != null) {
                predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                        criteriaBuilder, root, new Date(dateFrom)));
              }

              if (dateTo != null) {
                predicates.add(
                    predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
              }

              if (tradeAction != null) {
                predicates.add(predicate.equalTradeAction(criteriaBuilder, root, tradeAction));
              }
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                number,
                size,
                isAscending
                    ? criteriaBuilder.asc(root.get(Trade_.id))
                    : criteriaBuilder.desc(root.get(Trade_.id)));
          }
        });
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      Integer number,
      Integer size) {
    return getPredicatesOfFindByCondition(
        criteriaBuilder,
        root,
        symbolId,
        userId,
        id,
        idFrom,
        idTo,
        dateFrom,
        dateTo,
        tradeAction,
        null,
        null,
        number,
        size);
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      OrderSide orderSide,
      OrderType orderType,
      Integer number,
      Integer size) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    } else {
      if (idFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
      }

      if (idTo != null) {
        predicates.add(predicate.lessThanId(criteriaBuilder, root, idTo));
      }

      if (dateFrom != null) {
        predicates.add(
            predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
      }

      if (dateTo != null) {
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
      }

      if (tradeAction != null) {
        predicates.add(predicate.equalTradeAction(criteriaBuilder, root, tradeAction));
      }

      if (orderSide != null) {
        predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
      }

      if (orderType != null) {
        predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
      }
    }

    return predicates;
  }

  public PageData<E> createPageData(List<E> content, Long count, Integer number, Integer size) {
    List<E> pageContents = new ArrayList<E>();

    int maxSize = (number * size + size) > content.size() ? content.size() : (number * size + size);

    for (int i = number * size; i < maxSize; i++) {

      pageContents.add(content.get(i));
    }

    return new PageData<E>(number, size, count, pageContents);
  }

  public PageData<E> findByConditionPageData(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      Integer number,
      Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        userId,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        number,
                        size));
              }
            });
    return new PageData<E>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, List<E>>() {
              @Override
              public List<E> query() {
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        userId,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        number,
                        size),
                    number,
                    size,
                    criteriaBuilder.desc(root.get(Trade_.id)));
              }
            }));
  }

  public List<E> findAllByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      OrderSide orderSide,
      OrderType orderType) {
    return new ArrayList<E>(
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, List<E>>() {
              @Override
              public List<E> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        userId,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        orderSide,
                        orderType,
                        null,
                        null);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.desc(root.get(Trade_.id)));
              }
            }));
  }

  public List<E> findAllByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      TradeAction tradeAction,
      EntityManager entityManager) {
    return new ArrayList<E>(
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    symbolId,
                    userId,
                    id,
                    idFrom,
                    idTo,
                    dateFrom,
                    dateTo,
                    tradeAction,
                    null,
                    null);
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(Trade_.id)));
          }
        }.execute(getEntityClass(), entityManager));
  }

  public List<E> findMakerByCondition(Long symbolId, Date from, Date to) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
            predicates.add(predicate.equalTradeAction(criteriaBuilder, root, TradeAction.MAKER));
            predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, from));
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, to));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(Trade_.createdAt)),
                criteriaBuilder.asc(root.get(Trade_.id)));
          }
        });
  }

  public List<E> findTakerByCondition(Long symbolId, Date from, Date to) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
            predicates.add(predicate.equalTradeAction(criteriaBuilder, root, TradeAction.TAKER));
            predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, from));
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, to));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(Trade_.createdAt)),
                criteriaBuilder.asc(root.get(Trade_.id)));
          }
        });
  }

  public List<E> findByUserIdAndGreaterThanId(Long userId, Long id) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            predicates.add(predicate.greaterThanId(criteriaBuilder, root, id));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                200,
                criteriaBuilder.asc(root.get(Trade_.id)));
          }
        });
  }
}
