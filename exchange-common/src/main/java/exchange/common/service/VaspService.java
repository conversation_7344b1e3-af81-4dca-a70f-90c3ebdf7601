package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.SygnaHubManager;
import exchange.common.constant.SygnaConstants;
import exchange.common.entity.Vasp;
import exchange.common.model.response.SygnaHubResponse;
import exchange.common.predicate.VaspPredicate;
import exchange.common.sygna.SygnaHubClient.VASPRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class VaspService extends EntityService<Vasp, VaspPredicate> {

  private final SygnaHubManager sygnaHubManager;
  
  @Override
  public Class<Vasp> getEntityClass() {
    return Vasp.class;
  }

  @Override
  protected void fetch(Root<Vasp> root) {
    super.fetch(root);
  }
  
  public void getSygnaVasps() throws Exception {
    SygnaHubResponse<List<VASPRes>> res = sygnaHubManager.getVASPs();
    List<VASPRes> vasps = res.getData();
    if(ObjectUtils.isEmpty(vasps)) {
      log.error("SygnaからVASP情報の取得が失敗しました。");
    } else {
      for(VASPRes vaspRes : vasps) {
        Vasp vaspForUpdate = findOneByVaspCode(vaspRes.getVaspCode());
        if(ObjectUtils.isNotEmpty(vaspForUpdate)) {
          vaspForUpdate.setVaspName(vaspRes.getVaspName());
          vaspForUpdate.setProtocol(vaspRes.getProtocol());
          if(SygnaConstants.SYGNA_BRIDGE.equals(vaspRes.getProtocol())
              || SygnaConstants.SYGNA_EMAILPROTOCOL.equals(vaspRes.getProtocol())) {
            vaspForUpdate.setCountry(vaspRes.getVaspCode().substring(4, 6));
          } else {
            vaspForUpdate.setCountry("UNKNOW");
          }
          save(vaspForUpdate);
        } else {
          Vasp vasp = new Vasp();
          vasp.setVaspName(vaspRes.getVaspName());
          vasp.setVaspCode(vaspRes.getVaspCode());
          vasp.setProtocol(vaspRes.getProtocol());
          if(SygnaConstants.SYGNA_BRIDGE.equals(vaspRes.getProtocol())
              || SygnaConstants.SYGNA_EMAILPROTOCOL.equals(vaspRes.getProtocol())) {
            vasp.setCountry(vaspRes.getVaspCode().substring(4, 6));
          } else {
            vasp.setCountry("UNKNOW");
          }
          save(vasp);
        }
      }
    }
  }
  
  public Vasp findOneByVaspCode(String vaspCode) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<Vasp, Vasp>() {
          @Override
          public Vasp query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalVaspCode(criteriaBuilder, root, vaspCode));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
}
