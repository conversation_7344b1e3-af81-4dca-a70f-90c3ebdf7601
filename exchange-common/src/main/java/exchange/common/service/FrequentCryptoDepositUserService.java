package exchange.common.service;

import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.common.entity.FrequentCryptoDepositUser_;
import exchange.common.predicate.FrequentCryptoDepositUserPredicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class FrequentCryptoDepositUserService extends
    EntityService<FrequentCryptoDepositUser, FrequentCryptoDepositUserPredicate> {

  @Override
  public Class<FrequentCryptoDepositUser> getEntityClass() {
    return FrequentCryptoDepositUser.class;
  }

  /**
   * ページングに使用する件数を取得する
   * @param userId
   * @param tmsStatus
   * @param fromTargetAt
   * @param toTargetAt
   * @return
   */
  public Long getCount(final Long userId,
      final TmsStatus tmsStatus, final Date fromTargetAt, final Date toTargetAt) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        userId,
                        tmsStatus,
                        fromTargetAt,
                        toTargetAt));
              }
            });
    return Long.valueOf(count);
  }

  public List<FrequentCryptoDepositUser> findByCondition(final Long userId,
      final TmsStatus tmsStatus, final Date fromTargetAt, final Date toTargetAt) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<>() {
          @Override
          public List<FrequentCryptoDepositUser> query() {
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                getPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    userId,
                    tmsStatus,
                    fromTargetAt,
                    toTargetAt),
                criteriaBuilder.desc(root.get(FrequentCryptoDepositUser_.id)));
          }
        });
  }

  public List<FrequentCryptoDepositUser> findByConditionPaging(
      final Long userId,
      final TmsStatus tmsStatus,
      final Date fromTargetAt,
      final Date toTargetAt,
      final Integer number,
      final Integer size) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<>() {
          @Override
          public List<FrequentCryptoDepositUser> query() {
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                getPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    userId,
                    tmsStatus,
                    fromTargetAt,
                    toTargetAt),
                number,
                size,
                criteriaBuilder.desc(root.get(FrequentCryptoDepositUser_.id)));
          }
        });
  }

  public void apply(Long id, TmsStatus tmsStatus) throws Exception {
    final var entity = findOne(id);
    if (entity == null) {
      return;
    }
    entity.setTmsStatus(tmsStatus);
    save(entity);
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<FrequentCryptoDepositUser> root, final Long userId, final TmsStatus tmsStatus,
      final Date fromTargetAt, final Date toTargetAt) {
    final var predicates = new ArrayList<Predicate>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (tmsStatus != null) {
      predicates.add(predicate.equalTmsStatus(criteriaBuilder, root, tmsStatus));
    }

    if (fromTargetAt != null) {
      predicates.add(predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, fromTargetAt));
    }

    if (toTargetAt != null) {
      predicates.add(predicate.lessThanOrEqualToTargetAt(criteriaBuilder, root, toTargetAt));
    }

    return predicates;
  }
}
