package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.StakingRewardRecord;
import exchange.common.predicate.StakingRewardRecordPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class StakingRewardRecordService extends EntityService<StakingRewardRecord, StakingRewardRecordPredicate> {

  @Override
  public Class<StakingRewardRecord> getEntityClass() {
    return StakingRewardRecord.class;
  }

  @Override
  protected void fetch(Root<StakingRewardRecord> root) {
    super.fetch(root);
  }
  
  public StakingRewardRecord findOneByRecordId(Long recordId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<StakingRewardRecord, StakingRewardRecord>() {
          @Override
          public StakingRewardRecord query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalRecordId(criteriaBuilder, root, recordId));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }
  
}
