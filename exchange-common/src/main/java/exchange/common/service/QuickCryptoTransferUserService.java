package exchange.common.service;

import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.QuickCryptoTransferUser;
import exchange.common.entity.QuickCryptoTransferUser_;
import exchange.common.predicate.QuickCryptoTransferUserPredicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class QuickCryptoTransferUserService extends
    EntityService<QuickCryptoTransferUser, QuickCryptoTransferUserPredicate> {

  @Override
  public Class<QuickCryptoTransferUser> getEntityClass() {
    return QuickCryptoTransferUser.class;
  }

  public Long getCount(
      final Long userId,
      final TmsStatus tmsStatus,
      final Date fromTargetAt,
      final Date toTargetAt) {
    final var count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        userId,
                        tmsStatus,
                        fromTargetAt,
                        toTargetAt));
              }
            });

    return Long.valueOf(count);
  }

  public List<QuickCryptoTransferUser> findByCondition(
      final Long userId,
      final TmsStatus tmsStatus,
      final Date fromTargetAt,
      final Date toTargetAt,
      final Integer number,
      final Integer size) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<>() {
          @Override
          public List<QuickCryptoTransferUser> query() {
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                getPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    userId,
                    tmsStatus,
                    fromTargetAt,
                    toTargetAt),
                number,
                size,
                criteriaBuilder.desc(root.get(QuickCryptoTransferUser_.id)));
          }
        });
  }

  public void apply(Long id, TmsStatus tmsStatus) throws Exception {
    final var entity = findOne(id);
    if (entity == null) {
      return;
    }
    entity.setTmsStatus(tmsStatus);
    save(entity);
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<QuickCryptoTransferUser> root, final Long userId, final TmsStatus tmsStatus,
      final Date fromTargetAt, final Date toTargetAt) {
    final var predicates = new ArrayList<Predicate>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (tmsStatus != null) {
      predicates.add(predicate.equalTmsStatus(criteriaBuilder, root, tmsStatus));
    }

    if (fromTargetAt != null) {
      predicates.add(predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, fromTargetAt));
    }

    if (toTargetAt != null) {
      predicates.add(predicate.lessThanOrEqualToTargetAt(criteriaBuilder, root, toTargetAt));
    }

    return predicates;
  }
}