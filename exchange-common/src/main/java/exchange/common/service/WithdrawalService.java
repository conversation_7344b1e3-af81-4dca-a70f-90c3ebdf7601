package exchange.common.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.ibm.icu.text.Transliterator;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.QueryExecutorSum;
import exchange.common.component.RedisManager;
import exchange.common.component.RedisManager.LockParams;
import exchange.common.component.SesManager;
import exchange.common.component.SygnaHubManager;
import exchange.common.component.WalletManager;
import exchange.common.config.ChainalysisConfig;
import exchange.common.config.EllipticConfig;
import exchange.common.config.WalletConfig;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.ChainalysisAlertLevel;
import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.LineFeed;
import exchange.common.constant.MailNoreplyType;
import exchange.common.constant.SygnaConstants;
import exchange.common.constant.TradeType;
import exchange.common.constant.WithdrawalPurpose;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.constant.WithdrawalType;
import exchange.common.entity.Asset;
import exchange.common.entity.Candlestick;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.SygnaCustomer;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.Vasp;
import exchange.common.entity.Withdrawal;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.entity.WithdrawalAudit;
import exchange.common.entity.Withdrawal_;
import exchange.common.exception.CustomException;
import exchange.common.exception.SygnaException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SygnaHubResponse;
import exchange.common.model.response.WalletResponse;
import exchange.common.model.response.WalletTransaction;
import exchange.common.predicate.WithdrawalPredicate;
import exchange.common.sygna.SygnaHubClient.AddrsInfoRes;
import exchange.common.sygna.SygnaHubClient.SygnaTransferProperties;
import exchange.common.sygna.SygnaHubClient.TransactionsRes;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.pos.service.PosCandlestickService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class WithdrawalService extends EntityService<Withdrawal, WithdrawalPredicate> {

  private final AssetService assetService;

  private final OrderbookService orderbookService;

  private final SymbolService symbolService;

  private final UserService userService;

  private final MailNoreplyService mailNoreplyService;

  private final SesManager sesManager;

  private final WalletManager walletManager;

  private final SygnaHubManager sygnaHubManager;

  private final VaspService vaspService;

  private final SygnaCustomerService sygnaCustomerService;
  private final SygnaCurrencyService sygnaCurrencyService;
  private final PosCandlestickService posCandlestickService;
  private final CurrencyPairConfigService currencyPairConfigService;

  @Autowired
  private EllipticConfig ellipticConfig;

  @Autowired
  private WalletConfig walletConfig;

  @Autowired
  private RedisManager redisManager;

  @Autowired
  private ChainalysisConfig chainalysisConfig;

  @Override
  public Class<Withdrawal> getEntityClass() {
    return Withdrawal.class;
  }

  @Override
  protected void fetch(Root<Withdrawal> root) {
    super.fetch(root);
    root.fetch(Withdrawal_.user, JoinType.LEFT);
  }

  private static String getLockKey(Long userId, Currency currency) {
    return "lock:asset:" + userId + ":" + currency;
  }

  private void checkInsiderAndRisker(Withdrawal withdrawal) {
    User user = userService.findOne(withdrawal.getUserId());
    StringBuilder stringBuilder = new StringBuilder();

    if (user.isInsider()) {
      stringBuilder.append("insider requested withdrawal. userId: " + user.getId() + ". ");
    }

    if (user.isRisker()) {
      stringBuilder.append("risker requested withdrawal. userId: " + user.getId() + ". ");
    }

    if (stringBuilder.length() > 0) {
      stringBuilder.append("currency: " + withdrawal.getCurrency() + ", amount: "
          + withdrawal.getAmount().toPlainString() + ".");
      log.info(stringBuilder.toString());
    }
  }

  public Withdrawal request(Long userId, CurrencyConfig currencyConfig, BigDecimal amount,
      String comment, WithdrawalAccount withdrawalAccount) throws Exception {
	  Withdrawal withdrawal = new Withdrawal();
	  if (!redisManager.executeWithLock(getLockKey(userId, currencyConfig.getCurrency()), LockParams.LOCKKEY, () -> {
//		 return (Withdrawal)
		 customTransactionManager.execute(entityManager -> {
		      withdrawal.setUserId(userId);
		      withdrawal.setCurrency(currencyConfig.getCurrency());
		      withdrawal.setAmount(amount);
		      withdrawal.setWithdrawalFee(currencyConfig.getWithdrawalFee());
		      withdrawal.setTransactionFee(currencyConfig.getTransactionFee());
		      withdrawal.setWithdrawalAccountId(withdrawalAccount.getId());
		      withdrawal.setAddress(withdrawalAccount.getAddress());
		      withdrawal.setFailedNumber(0);
		      withdrawal.setComment(comment);
		      withdrawal.setJpyConversion(findJpyConversion(withdrawal.getCurrency()));
		      save(withdrawal, entityManager);
		      assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
		          withdrawal.getAmount(), entityManager);
		      checkInsiderAndRisker(withdrawal);
		      return withdrawal;
		    });
	  })) {
		  log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(userId,currencyConfig.getCurrency()));
		  throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + userId);
	  };
    return withdrawal;
  }

  public void apply(Long id) throws Exception {
    Withdrawal withdrawal = findOne(id);

    if (withdrawal == null) {
      return;
    }

    customTransactionManager.execute(entityManager -> {
      withdrawal.setWithdrawalStatus(WithdrawalStatus.DONE);
      withdrawal.setJpyConversion(findJpyConversion(withdrawal.getCurrency()));
      assetService.update(withdrawal.getUserId(), withdrawal.getCurrency(),
          withdrawal.getAmount().negate(), withdrawal.getAmount().negate(), entityManager);

      Asset asset =
          assetService.findOne(withdrawal.getUserId(), withdrawal.getCurrency(), entityManager);
      withdrawal.setAssetAmount(asset.getOnhandAmount());
      save(withdrawal, entityManager);
    });
  }

  @Override
  public Withdrawal save(Withdrawal entity, EntityManager entityManager) throws Exception {
    super.save(entity, entityManager);
    saveAudit(entity, entityManager);
    return entity;
  }

  public void saveAudit(Withdrawal entity, EntityManager entityManager) {
    WithdrawalAudit record = new WithdrawalAudit();
    record.setWithdrawalId(entity.getId());
    record.setStatus(entity.getWithdrawalStatus());
    Timestamp d = new Timestamp(System.currentTimeMillis());
    record.setCreatedAt(d);
    record.setCreatedBy(String.valueOf(entity.getUserId()));
    entityManager.persist(record);
  }

  public void saveapply(Withdrawal withdrawal) throws Exception {
    customTransactionManager.execute(entityManager -> {
      save(withdrawal, entityManager);
    });
  }

  public Withdrawal applyNew(Long userId, Currency currency, WithdrawalType withdrawalType,
      WithdrawalPurpose withdrawalPurpose, BigDecimal amount, BigDecimal withdrawalFee,
      BigDecimal transactionFee, String transactionId, Long withdrawalAccountId, String address,
      String comment, Date createdAt) throws Exception {
    return customTransactionManager.execute(entityManager -> {
      Withdrawal withdrawal = new Withdrawal();
      withdrawal.setUserId(userId);
      withdrawal.setCurrency(currency);
      withdrawal.setWithdrawalType(withdrawalType);
      withdrawal.setWithdrawalPurpose(withdrawalPurpose);
      withdrawal.setAmount(amount);
      withdrawal.setWithdrawalFee(withdrawalFee);
      withdrawal.setTransactionFee(transactionFee);
      withdrawal.setTransactionId(transactionId);
      withdrawal.setWithdrawalAccountId(withdrawalAccountId);
      withdrawal.setAddress(address);
      withdrawal.setComment(comment);
      withdrawal.setWithdrawalStatus(WithdrawalStatus.DONE);
      withdrawal.setCreatedAt(createdAt);
       withdrawal.setJpyConversion(findJpyConversion(withdrawal.getCurrency()));
      assetService.update(withdrawal.getUserId(), withdrawal.getCurrency(),
          withdrawal.getAmount().negate(), BigDecimal.ZERO, entityManager);
      Asset asset =
          assetService.findOne(withdrawal.getUserId(), withdrawal.getCurrency(), entityManager);
      withdrawal.setAssetAmount(asset.getOnhandAmount());
      save(withdrawal, entityManager);
      return withdrawal;
    });
  }

  private BigDecimal findJpyConversion(Currency currency) throws Exception {
//    if (currency == Currency.JPY) {
//      return BigDecimal.valueOf(1);
//    }
//    if (currency == Currency.ETH) {
//      // ETH SYMBOL ID:7
//      Candlestick latest = posCandlestickService.findOneByCondition(7l, CandlestickType.P1D, null,
//          null, false);
//      if(latest == null) {
//        return BigDecimal.ZERO;
//      }
//      return latest.getClose();
//    }
//    Symbol symbol =
//        symbolService.findByCondition(TradeType.SPOT, CurrencyPair.valueOf(currency, Currency.JPY));
//    return orderbookService.get(symbol).getBestBid();

    if (currency == Currency.JPY) {
      return BigDecimal.valueOf(1);
    }

    // UNYO-758 NIDTの場合、CBから取得、以外の場合、whaleFinから取得
    if (Currency.NIDT.equals(currency)) {
      Symbol symbol =
          symbolService.findByCondition(TradeType.SPOT, CurrencyPair.valueOf(currency, Currency.JPY));
      return orderbookService.get(symbol).getBestBid();
    } else {
      List<CurrencyPairConfig> currencyPairConfig = currencyPairConfigService
          .findAllByCondition(TradeType.POS, CurrencyPair.valueOf(currency, Currency.JPY), true);
      if (CollectionUtils.isEmpty(currencyPairConfig)) {
        if (currency == Currency.ETH) {
          // ETH SYMBOL ID:7
          Candlestick latest =
              posCandlestickService.findOneByCondition(7l, CandlestickType.P1D, null, null, false, false);
          if (latest == null) {
            return BigDecimal.ZERO;
          }
          return latest.getClose();
        } else {
          Symbol symbol =
              symbolService.findByCondition(TradeType.SPOT, CurrencyPair.valueOf(currency, Currency.JPY));
          if (symbol != null) {
            return orderbookService.get(symbol).getBestBid();
          } else {
            return BigDecimal.ZERO;
          }

        }
      } else {
        Symbol symbol =
            symbolService.findByCondition(TradeType.POS, CurrencyPair.valueOf(currency, Currency.JPY));
        return orderbookService.getPos(symbol).getBestBid();
      }
    }
  }

  public List<Withdrawal> findByUserIdAndWithdrawalStatus(Long userId,
      WithdrawalStatus withdrawalStatus) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {
          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            predicates
                .add(predicate.equalWithdrawalStatus(criteriaBuilder, root, withdrawalStatus));
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(Withdrawal_.id)));
          }
        });
  }

  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<Withdrawal> root, Long userId, Currency currency, Long createdAtFrom, Long createdAtTo,
      Long updatedAtFrom, Long updatedAtTo, WithdrawalStatus withdrawalStatus) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (currency != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
    }

    if (createdAtFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(createdAtFrom)));
    }

    if (createdAtTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
    }

    if (updatedAtFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, new Date(updatedAtFrom)));
    }

    if (updatedAtTo != null) {
      predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updatedAtTo)));
    }

    if (withdrawalStatus != null) {
      predicates.add(predicate.equalWithdrawalStatus(criteriaBuilder, root, withdrawalStatus));
    }

    return predicates;
  }
//add param distinction
  private List<Predicate> getPredicatesOfFindByCondition(CriteriaBuilder criteriaBuilder,
      Root<Withdrawal> root, Long userId, Currency currency, Long createdAtFrom, Long createdAtTo,
      Long updatedAtFrom, Long updatedAtTo, WithdrawalStatus withdrawalStatus, String distinction) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (currency != null) {
      predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
    }

    if (createdAtFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(createdAtFrom)));
    }

    if (createdAtTo != null) {
      predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
    }

    if (updatedAtFrom != null) {
      predicates.add(
          predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, new Date(updatedAtFrom)));
    }

    if (updatedAtTo != null) {
      predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updatedAtTo)));
    }

    if (withdrawalStatus != null) {
      predicates.add(predicate.equalWithdrawalStatus(criteriaBuilder, root, withdrawalStatus));
    }

    if (distinction != null) {
      predicates.add(predicate.equalDistinction(criteriaBuilder, root, distinction));
    }

    return predicates;
  }

  public PageData<Withdrawal> findByConditionPageData(Long userId, Currency currency, Long dateFrom,
      Long dateTo, WithdrawalStatus withdrawalStatus, Integer number, Integer size) {
    return findByConditionPageData(userId, currency, dateFrom, dateTo, withdrawalStatus, number,
        size, false);
  }

  public PageData<Withdrawal> findByConditionPageData(Long userId, Currency currency, Long dateFrom,
      Long dateTo, WithdrawalStatus withdrawalStatus, Integer number, Integer size,
      boolean reverseOrder) {
    long count = customTransactionManager.count(getEntityClass(), new QueryExecutorCounter<>() {
      @Override
      public Long query() {
        return count(entityManager, criteriaBuilder, criteriaQuery, root,
            getPredicatesOfFindByCondition(criteriaBuilder, root, userId, currency, dateFrom,
                dateTo, null, null, withdrawalStatus));
      }
    });

    return new PageData<Withdrawal>(number, size, count, customTransactionManager
        .find(getEntityClass(), new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {
          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                userId, currency, dateFrom, dateTo, null, null, withdrawalStatus);
            return getResultList(entityManager, criteriaQuery, root, predicates, number, size,
                reverseOrder ? criteriaBuilder.desc(root.get(Withdrawal_.id))
                    : criteriaBuilder.asc(root.get(Withdrawal_.id)));
          }
        }));
  }
//add param distinction
  public PageData<Withdrawal> findByConditionPageData(Long userId, Currency currency, Long dateFrom,
      Long dateTo, String distinction, WithdrawalStatus withdrawalStatus, Integer number, Integer size,
      boolean reverseOrder) {
    long count = customTransactionManager.count(getEntityClass(), new QueryExecutorCounter<>() {
      @Override
      public Long query() {
        return count(entityManager, criteriaBuilder, criteriaQuery, root,
            getPredicatesOfFindByCondition(criteriaBuilder, root, userId, currency, dateFrom,
                dateTo, null, null, withdrawalStatus, distinction));
      }
    });

    return new PageData<Withdrawal>(number, size, count, customTransactionManager
        .find(getEntityClass(), new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {
          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root,
                userId, currency, dateFrom, dateTo, null, null, withdrawalStatus, distinction);
            return getResultList(entityManager, criteriaQuery, root, predicates, number, size,
                reverseOrder ? criteriaBuilder.desc(root.get(Withdrawal_.id))
                    : criteriaBuilder.asc(root.get(Withdrawal_.id)));
          }
        }));
  }

  public List<Withdrawal> findAllByCondition(Long userId, Currency currency, Long createdAtFrom,
      Long createdAtTo, Long updatedAtFrom, Long updatedAtTo, WithdrawalStatus withdrawalStatus) {
    List<Withdrawal> withdrawal = customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {

          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByCondition(criteriaBuilder, root, userId, currency,
                    createdAtFrom, createdAtTo, updatedAtFrom, updatedAtTo, withdrawalStatus);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(Withdrawal_.id)));
          }
        });
    return withdrawal;
  }
//add param distinction
  public List<Withdrawal> findAllByCondition(Long userId, Currency currency, Long createdAtFrom,
      Long createdAtTo, Long updatedAtFrom, Long updatedAtTo, WithdrawalStatus withdrawalStatus, String distinction) {
    List<Withdrawal> withdrawal = customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {

          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates =
                getPredicatesOfFindByCondition(criteriaBuilder, root, userId, currency,
                    createdAtFrom, createdAtTo, updatedAtFrom, updatedAtTo, withdrawalStatus, distinction);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.asc(root.get(Withdrawal_.id)));
          }
        });
    return withdrawal;
  }

  public List<Withdrawal> findByCondition(Long userId, Currency currency, Long dateFrom,
      Long dateTo, Long updateFrom, Long updateTo, WithdrawalStatus withdrawalStatus,
      String address, Integer number, Integer size) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {
          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            if (currency != null) {
              predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
            }

            if (dateFrom != null) {
              predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root,
                  new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            if (updateFrom != null) {
              predicates.add(predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root,
                  new Date(updateFrom)));
            }

            if (updateTo != null) {
              predicates
                  .add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updateTo)));
            }

            if (withdrawalStatus != null) {
              predicates
                  .add(predicate.equalWithdrawalStatus(criteriaBuilder, root, withdrawalStatus));
            }

            if (address != null) {
              predicates.add(predicate.equalAddress(criteriaBuilder, root, address));
            }

            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(Withdrawal_.id)));
          }
        });
  }

  public List<Withdrawal> findByConditionStatus(Long userId, Currency currency, Long dateFrom,
      Long dateTo, Long updateFrom, Long updateTo, WithdrawalStatus[] withdrawalStatus,
      String address, Integer number, Integer size) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {
          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            if (currency != null) {
              predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
            }

            if (dateFrom != null) {
              predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root,
                  new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            if (updateFrom != null) {
              predicates.add(predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root,
                  new Date(updateFrom)));
            }

            if (updateTo != null) {
              predicates
                  .add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updateTo)));
            }

            if (withdrawalStatus != null && withdrawalStatus.length != 0) {
              predicates
                  .add(predicate.equalWithdrawalStatusNew(criteriaBuilder, root, withdrawalStatus));
            }

            if (address != null) {
              predicates.add(predicate.equalAddress(criteriaBuilder, root, address));
            }

            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(Withdrawal_.id)));
          }
        });
  }

  public List<Withdrawal> findByLikeAddressStratWith(WithdrawalStatus withdrawalStatus,
      String address, Integer number, Integer size) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<Withdrawal, List<Withdrawal>>() {
          @Override
          public List<Withdrawal> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (withdrawalStatus != null) {
              predicates
                  .add(predicate.equalWithdrawalStatus(criteriaBuilder, root, withdrawalStatus));
            }

            if (address != null) {
              predicates.add(predicate.likeAddressStratWith(criteriaBuilder, root, address));
            }

            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(Withdrawal_.id)));
          }
        });
  }

  private Object[] sumExchange(EntityManager entityManager, CriteriaBuilder criteriaBuilder,
      CriteriaQuery<Object[]> criteriaQuery, Root<Withdrawal> root, List<Predicate> predicates) {

    criteriaQuery.multiselect(criteriaBuilder.sum(root.get(Withdrawal_.amount)),
        criteriaBuilder.sum(criteriaBuilder.prod(root.get(Withdrawal_.amount),
            root.get(Withdrawal_.jpyConversion))),
        criteriaBuilder.sum(root.get(Withdrawal_.withdrawalFee)),
        criteriaBuilder.sum(criteriaBuilder.prod(root.get(Withdrawal_.withdrawalFee),
            root.get(Withdrawal_.jpyConversion))),
        criteriaBuilder.sum(root.get(Withdrawal_.transactionFee)),
        criteriaBuilder.sum(criteriaBuilder.prod(root.get(Withdrawal_.transactionFee),
            root.get(Withdrawal_.jpyConversion))));

    if (!CollectionUtils.isEmpty(predicates)) {
      criteriaQuery.where(predicates.toArray(new Predicate[] {}));
    }

    try {
      return entityManager.createQuery(criteriaQuery).setFirstResult(0).setMaxResults(1)
          .getSingleResult();
    } catch (Exception e) {
      return null;
    }
  }

  // 取引所サマリ
  public Object[] sumExchangeByCondition(Currency currency, WithdrawalStatus withdrawalStatus,
      Long updatedAtFrom, Long updatedAtTo) {
    return customTransactionManager.sum(getEntityClass(), new QueryExecutorSum<Withdrawal>() {
      @Override
      public Object[] query() {
        List<Predicate> predicates = getPredicatesOfFindByCondition(criteriaBuilder, root, null,
            currency, null, null, updatedAtFrom, updatedAtTo, withdrawalStatus);

        return sumExchange(entityManager, criteriaBuilder, criteriaQuery, root, predicates);
      }
    });
  }

  public Withdrawal cancel(Long id) throws Exception {
    Withdrawal withdrawal = findOne(id);

    if (withdrawal == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
    }

    User user = userService.findOne(withdrawal.getUserId());

    if (!user.isActive()) {
      throw new CustomException(ErrorCode.ORDER_ERROR_HALT);
    }

    if (!redisManager.executeWithLock(
    		getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()),
    LockParams.LOCKKEY,
    () -> {
//    	return (Withdrawal)
			customTransactionManager.execute(entityManager -> {
			      withdrawal.setWithdrawalStatus(WithdrawalStatus.REJECTED);
			      save(withdrawal, entityManager);
			      assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
			              withdrawal.getAmount().negate(), entityManager);
			      return withdrawal;
			});
    })) {
    	log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()));
    	throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawal.getUserId());
    };
    if (withdrawal != null && WithdrawalStatus.REJECTED.equals(withdrawal.getWithdrawalStatus())) {
    	MailNoreplyType mailNoreplyType = MailNoreplyType.WITHDRAWAL_REJECTED;
    	MailNoreply mailNoreply = mailNoreplyService.findOne(mailNoreplyType);
    	// send mail
    	try {
    	  // 日時
    	  String Date = FormatUtil.formatJst(withdrawal.getUpdatedAt(),
    	      FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
    	  // 通貨
    	  String currency = withdrawal.getCurrency().toString();
    	  // 数量
    	  String amount = withdrawal.getAmount().toPlainString();
    	  // 送付元アドレス
    	  String address = withdrawal.getAddress();
    	  MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
    	  String[] args = new String[] {Date, currency, amount, address};
    	  String mailContent = messageFormat.format(args);
    	  sesManager.send(mailNoreply.getFromAddress(), user.getEmail(), mailNoreply.getTitle(),
            mailContent);
    	} catch (Exception e) {
    		log.error(e.getMessage(), e);
    	}
    }
    return withdrawal;
  }

  public boolean updateStatusWithWithdrawalResult(Long id, boolean isLast) throws Exception {

    boolean retryFlg = false;
    Withdrawal withdrawal = findOne(id);
    // amber連携 出金
    try {
      WalletResponse res = walletManager.withdrawal(withdrawal.getCurrency().getName(),
          withdrawal.getAddress(), withdrawal.getAmount().subtract(withdrawal.getWithdrawalFee())
              .subtract(withdrawal.getTransactionFee()));
      if (res.getCode() == 20000) {
        // withdrawal状態更新：送金中

        customTransactionManager.execute(entityManager -> {
          withdrawal.setWithdrawalStatus(WithdrawalStatus.FUNDING);
          withdrawal.setTransactionId(res.getData().toString());
          save(withdrawal, entityManager);
        });
      } else {
    	 if (!redisManager.executeWithLock(
    			 getLockKey(withdrawal.getUserId(), withdrawal.getCurrency()),
    	 LockParams.LOCKKEY,
    	 () -> {
//    		 return (Withdrawal)
    		 // withdrawal状態更新：送金失敗リトライ
    		 customTransactionManager.execute(entityManager -> {
    		   if (isLast) {
    			 withdrawal.setWithdrawalStatus(WithdrawalStatus.FUND_FAILED);
	             // 資産更新
	             assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
	                withdrawal.getAmount().negate(), entityManager);
	             save(withdrawal, entityManager);
	           }
	           return withdrawal;
    		 });
    	 })) {
    		 log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawal.getUserId(), withdrawal.getCurrency()));
    		 throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawal.getUserId());
    	 };

        if (isLast && withdrawal != null && WithdrawalStatus.FUND_FAILED.equals(withdrawal.getWithdrawalStatus())) {
        	// メール送信：送金失敗
            mailSend(withdrawal, MailNoreplyType.WITHDRAWAL_FAILED);
		}
        log.error("送金連携エラー.withdrawalId:" + id + "エラー:" + res.getMessage());
        // リトライ要
        retryFlg = true;
      }
    } catch (Exception e) {
      // ホットに暗号資産不足の場合、「承認」状態を保持し、次回のバッチでリトライ
      // 上記以外のエラー、2回送金リトライして、失敗の場合はステータス「送金失敗」になって、ユーザー資産のロック解除
      if (e.getMessage() != null && e.getMessage().contains("Not available balance.")) {
        // withdrawal状態更新：送金失敗リトライ
        customTransactionManager.execute(entityManager -> {
          withdrawal.setWithdrawalStatus(WithdrawalStatus.FUND_FAILED_RETRY);
          save(withdrawal, entityManager);
        });
      } else {
    	if (!redisManager.executeWithLock(
    			getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()),
    	LockParams.LOCKKEY,
    	() -> {
//    		return (Withdrawal)
			// withdrawal状態更新：送金失敗
	        customTransactionManager.execute(entityManager -> {
	          if (isLast) {
	            withdrawal.setWithdrawalStatus(WithdrawalStatus.FUND_FAILED);
	            // 資産更新
	            assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
	                withdrawal.getAmount().negate(), entityManager);
	            save(withdrawal, entityManager);
	          }
	          return withdrawal;
	        });
		})) {
    		log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()));
    		throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawal.getUserId());
    	};
    	if (isLast && withdrawal != null && WithdrawalStatus.FUND_FAILED.equals(withdrawal.getWithdrawalStatus())) {
    		// メール送信：送金失敗
            mailSend(withdrawal, MailNoreplyType.WITHDRAWAL_FAILED);
		}
      }
      log.error("送金連携エラー.withdrawalId:" + id + " エラー:" + e.getMessage());
      // リトライ要
      retryFlg = true;
    }

    return retryFlg;
  }

  private void mailSend(Withdrawal withdrawal, MailNoreplyType mailNoreplyType) throws Exception {
    // メール送信
    User user = userService.findOne(withdrawal.getUserId());
    MailNoreply mailNoreply = mailNoreplyService.findOne(mailNoreplyType);
    // 日時
    String Date = FormatUtil.formatJst(new Date(), FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
    // 通貨
    String currency = withdrawal.getCurrency().toString();
    // 数量
    String amount = withdrawal.getAmount().toPlainString();
    // 送付先アドレス
    String address = withdrawal.getAddress();
    if(currency.equals(Currency.XRP.name()) && address.indexOf("@") > 0) {
      address = address.substring(0, address.indexOf("@")) + "\r\n送金先宛先タグ：" + address.substring(address.indexOf("@") + 1);
    }
    MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
    String[] args = new String[] {Date, currency, amount, address};
    String mailContent = messageFormat.format(args);
    sesManager.send(mailNoreply.getFromAddress(), user.getEmail(), mailNoreply.getTitle(),
        mailContent);
  }

  public void updateStatusWithTxResult(Long id) throws Exception {
	Withdrawal withdrawal = findOne(id);
    // amber連携 トランザクション
    WalletResponse res = walletManager.detailTx(withdrawal.getTransactionId());
    if (res.getCode() == 20000) {
      WalletTransaction txDetail = (WalletTransaction) res.getData();
      log.info("withdrawalId:" + id + " TransactionId:" + withdrawal.getTransactionId()
          + " TransactionStatus:" + txDetail.getStatus());
      if (txDetail.getStatus() == 4) {
        redisManager.executeWithLock(
            getLockKey(withdrawal.getUserId(), withdrawal.getCurrency()),
            LockParams.EXECUTE_ORDER,
            () -> {
              return (Withdrawal)
               // withdrawal状態更新：送金完了
                  customTransactionManager.execute(entityManager -> {
                    withdrawal.setJpyConversion(findJpyConversion(withdrawal.getCurrency()));
                    withdrawal.setTransactionHash(txDetail.getTxHash());
                    withdrawal.setWithdrawalStatus(WithdrawalStatus.DONE);
                    save(withdrawal, entityManager);
                    // 資産更新
                    assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(),
                        withdrawal.getAmount().negate(), withdrawal.getAmount().negate(), entityManager);
                    return withdrawal;
                  });
            });

        if (withdrawal != null && WithdrawalStatus.DONE.equals(withdrawal.getWithdrawalStatus())) {
          // メール送信：送金完了
          MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.WITHDRAWAL_DONE);
          User user = userService.findOne(withdrawal.getUserId());
          String txTo = txDetail.getTxTo();
          if(txDetail.getSymbol().equals(Currency.XRP.name()) && txTo.indexOf("@") > 0) {
            txTo = txTo.substring(0, txTo.indexOf("@")) + "\r\n送金先宛先タグ：" + txTo.substring(txTo.indexOf("@") + 1);
          }
          String body = mailNoreply.getContents(user.getAntiPhishingCode(), null, LineFeed.HTML);
          body =
              body.replace("{0}", FormatUtil.formatJst(new Date(), FormatPattern.YYYY_MM_DD_HH_MM));
          body = body.replace("{1}", txDetail.getSymbol());
          body = body.replace("{2}", txDetail.getTxAmount());
          body = body.replace("{3}", txTo);
          body = body.replace("{4}", txDetail.getTxHash());
          sesManager.send(mailNoreply.getFromAddress(), user.getEmail(), mailNoreply.getTitle(),
              body);
          // txhashをsygnaに通知
          if (ObjectUtils.isNotEmpty(withdrawal.getSygnaTxId())) {
            SygnaHubResponse<List<TransactionsRes>> transactionsRes =
                sygnaHubManager.getTransactions(withdrawal.getSygnaTxId());
            List<TransactionsRes> transactionsList = transactionsRes.getData();
            // 14:Awaiting Originator VASP's TxID,14:Awaiting Originator VASP's TxID
            if (ObjectUtils.isNotEmpty(transactionsList)
                && transactionsList.get(0).getStatus() != null
                && (transactionsList.get(0).getStatus().equals(4)
                    || transactionsList.get(0).getStatus().equals(14))) {
              SygnaHubResponse<List<TransactionsRes>> patchRes =
                  sygnaHubManager.patchTxid(txDetail.getTxHash(), withdrawal.getSygnaTxId());
              if (!patchRes.getData().get(0).isSuccess()) {
                log.error("transaction hashの転送が失敗しました。" + withdrawal.getSygnaTxId());
                throw new SygnaException("transaction hashの転送が失敗しました。");
              }
            } else {
              log.info("transaction hashの転送が失敗しました。Sygna Hubで確認して、送付先VASPに連絡してください。"
                  + withdrawal.getSygnaTxId());
            }
          }
        }
      } else {
    	if (!redisManager.executeWithLock(
    			getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()),
    	LockParams.LOCKKEY,
    	() -> {
//    		return (Withdrawal)
			customTransactionManager.execute(entityManager -> {
	          if (txDetail.getStatus() == 5) {
	            withdrawal.setTransactionHash(txDetail.getTxHash());
	            withdrawal.setWithdrawalStatus(WithdrawalStatus.REJECTED);
	            // 資産更新
	            assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
	                withdrawal.getAmount().negate(), entityManager);
	            save(withdrawal, entityManager);
	          } else if (ObjectUtils.isNotEmpty(txDetail.getTxHash())
	              && ObjectUtils.isEmpty(withdrawal.getTransactionHash())) {
	            withdrawal.setTransactionHash(txDetail.getTxHash());
	            save(withdrawal, entityManager);
	          }
	          return withdrawal;
	        });
    	})) {
    		log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()));
    		throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawal.getUserId());
    	};
        if (txDetail.getStatus() == 5 && withdrawal != null && WithdrawalStatus.REJECTED.equals(withdrawal.getWithdrawalStatus())) {
	    	// メール送信：お取り扱い不可
	        mailSend(withdrawal, MailNoreplyType.WITHDRAWAL_REJECTED);
		}
      }
    } else {
      log.error("送金トランザクション取得失敗.withdrawalId:" + id + "error:" + res.getMessage());
    }
  }

  public Withdrawal delete(Long id) throws Exception {
    Withdrawal withdrawal = findOne(id);

    if (withdrawal == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
    }

    if (!(WithdrawalStatus.EXAMINING.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.SYGNA_COOPERATION.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.SYGNA_COOPERATION_UNABLE.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.AML_INFO_RECIVED.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.FIRST_AML_EXAMING.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.WAITING_SECOND_AML_EXAM.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.SECOND_AML_EXAMING.equals(withdrawal.getWithdrawalStatus())
        || WithdrawalStatus.AML_APPROVED.equals(withdrawal.getWithdrawalStatus()))) {

      throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
    }

    User user = userService.findOne(withdrawal.getUserId());

    if (!user.isActive()) {
      throw new CustomException(ErrorCode.ORDER_ERROR_HALT);
    }

    if (!redisManager.executeWithLock(
    		getLockKey(withdrawal.getUserId(), withdrawal.getCurrency()),
    LockParams.LOCKKEY,
    () -> {
//    	return (Withdrawal)
			customTransactionManager.execute(entityManager -> {
		      // 送金依頼キャンセル
		      withdrawal.setWithdrawalStatus(WithdrawalStatus.FUND_CANCEL);
		      save(withdrawal, entityManager);
		      assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
		          withdrawal.getAmount().negate(), entityManager);
		      return withdrawal;
    	});
	})) {
    	log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawal.getUserId(), withdrawal.getCurrency()));
    	throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawal.getUserId());
    };
    return withdrawal;
  }

  // Sygnaで連携可能出金リクエスト取得
  public List<SygnaTransferProperties> getSygnaTransferList(List<Withdrawal> waitWithdrawal)
      throws Exception {
    List<TreeMap<String, Object>> parameters = new ArrayList<>();
    for (Withdrawal withdrawal : waitWithdrawal) {
      TreeMap<String, Object> parameter = new TreeMap<>();
      List<String> addrsList = new ArrayList<>();
      addrsList.add(withdrawal.getAddress());
      parameter.put("addrs", addrsList);
      String currencyId = sygnaCurrencyService.getCurrencyId(withdrawal.getCurrency().name());
      // 連携できる出金をフィルター
      if (ObjectUtils.isEmpty(currencyId)) {
        log.info("取引所がサポートしていない通貨です。Sygmbol:" + withdrawal.getCurrency().name() + "　withdrawalId:"
            + withdrawal.getId());
        continue;
      }
      parameter.put("currency_id", currencyId);
      // beneficiary addrs
      parameters.add(parameter);
    }
    List<AddrsInfoRes> addrsInfoList = null;
    if (ObjectUtils.isNotEmpty(parameters)) {
      SygnaHubResponse<List<List<AddrsInfoRes>>> addrsInfoRes =
          sygnaHubManager.getAddressInformation(parameters);
      addrsInfoList = addrsInfoRes.getData().get(0);
    }
    if (ObjectUtils.isEmpty(addrsInfoList)) {
      log.info("アドレスタイプ取得失敗。");
      return null;
    }
    log.info(addrsInfoList.toString());
    List<SygnaTransferProperties> sygnaTransferList = new ArrayList<>();
    for (Withdrawal withdrawal : waitWithdrawal) {
      SygnaTransferProperties sygnaTransfer = new SygnaTransferProperties();
      AddrsInfoRes addrInfo = addrsInfoList.stream()
          .filter(x -> x.getAddress().equals(withdrawal.getAddress())).findFirst().orElse(null);
      if (SygnaConstants.DOMESTICE_EXCHANGE
          .equals(withdrawal.getWithdrawalAccount().getAddresstype())
          || SygnaConstants.ABROAD_EXCHANGE
              .equals(withdrawal.getWithdrawalAccount().getAddresstype())) {
        String vaspCode = withdrawal.getWithdrawalAccount().getExchange();
        Vasp vasp = vaspService.findOneByVaspCode(vaspCode);
        if (ObjectUtils.isEmpty(vasp)) {
          log.info(
              "VASPCODEがVASP一覧に存在しない。withdrawalID:" + withdrawal.getId() + " VASPCODE:" + vaspCode);
          transferByAddrType(addrInfo, withdrawal, sygnaTransferList);
        } else {
          if (SygnaConstants.SYGNA_BRIDGE.equals(vasp.getProtocol())) {
            sygnaTransfer.setVaspCode(vaspCode);
            sygnaTransfer.setWithdrawal(withdrawal);
            sygnaTransfer.setType(SygnaConstants.SYGNA_BRIDGE);
            sygnaTransferList.add(sygnaTransfer);
          } else if (SygnaConstants.SYGNA_EMAILPROTOCOL.equals(vasp.getProtocol())) {
            sygnaTransfer.setWithdrawal(withdrawal);
            sygnaTransfer.setType(SygnaConstants.SYGNA_EMAILPROTOCOL);
            sygnaTransferList.add(sygnaTransfer);
          } else {
            transferByAddrType(addrInfo, withdrawal, sygnaTransferList);
          }
        }
      } else {
        transferByAddrType(addrInfo, withdrawal, sygnaTransferList);
      }
    }
    return sygnaTransferList;
  }

  private void transferByAddrType(AddrsInfoRes addrInfo, Withdrawal withdrawal,
      List<SygnaTransferProperties> sygnaTransferList) throws Exception {
    SygnaTransferProperties sygnaTransfer = new SygnaTransferProperties();
    if (ObjectUtils.isEmpty(addrInfo)) {
      log.error("アドレスタイプ取得失敗。withdrawalID:" + withdrawal.getId());
    } else {
      if (SygnaConstants.SYGNA_VASP.equals(addrInfo.getType())) {
        String vaspCode = addrInfo.getExtraData().getVaspCode();
        sygnaTransfer.setVaspCode(vaspCode);
        sygnaTransfer.setWithdrawal(withdrawal);
        sygnaTransfer.setType(SygnaConstants.SYGNA_BRIDGE);
        sygnaTransferList.add(sygnaTransfer);
      } else if (SygnaConstants.EMAIL_PROTOCOL.equals(addrInfo.getType())) {
        sygnaTransfer.setWithdrawal(withdrawal);
        sygnaTransfer.setType(SygnaConstants.SYGNA_EMAILPROTOCOL);
        sygnaTransferList.add(sygnaTransfer);
      } else if (SygnaConstants.PRIVATE_WALLET.equals(addrInfo.getType())
          || SygnaConstants.FAILED.equals(addrInfo.getType())) {
        // Sygna連携不可
        withdrawal.setWithdrawalStatus(WithdrawalStatus.SYGNA_COOPERATION_UNABLE);
        save(withdrawal);
      } else {
        // 通知不可VASP
        User user = userService.findOne(withdrawal.getUserId());
        if (!redisManager.executeWithLock(
        		getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()),
        LockParams.LOCKKEY,
        () -> {
//        	return (Withdrawal)
        	customTransactionManager.execute(entityManager -> {
		          withdrawal.setWithdrawalStatus(WithdrawalStatus.REJECTED);
		          save(withdrawal, entityManager);
		          assetService.updateWithExternalLock(withdrawal.getUserId(), withdrawal.getCurrency(), BigDecimal.ZERO,
		                  withdrawal.getAmount().negate(), entityManager);
		          return withdrawal;
        		});
        })) {
        	log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawal.getUserId(),withdrawal.getCurrency()));
        	throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawal.getUserId());
        };
	    if (withdrawal != null && WithdrawalStatus.REJECTED.equals(withdrawal.getWithdrawalStatus())) {
	    	MailNoreplyType mailNoreplyType = MailNoreplyType.WITHDRAWAL_REJECTED;
	    	MailNoreply mailNoreply = mailNoreplyService.findOne(mailNoreplyType);
	    	// send mail
	    	try {
	    		// 日時
	    		String Date = FormatUtil.formatJst(withdrawal.getUpdatedAt(),
	    				FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
	            // 通貨
	            String currency = withdrawal.getCurrency().toString();
	            // 数量
	            String amount = withdrawal.getAmount().toPlainString();
	            // 送付元アドレス
	            String address = withdrawal.getAddress();
	            MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
	            String[] args = new String[] {Date, currency, amount, address};
	            String mailContent = messageFormat.format(args);
	            sesManager.send(mailNoreply.getFromAddress(), user.getEmail(), mailNoreply.getTitle(),
	                mailContent);
	          } catch (Exception e) {
	            log.error(e.getMessage(), e);
	          }
	      }
      }
    }
  }

  // Date Transfer新規
  public void createTransfer(List<SygnaTransferProperties> sygnaTransferList) throws Exception {
    List<SygnaTransferProperties> waitSygnaTransferList = new ArrayList<>();
    waitSygnaTransferList.addAll(sygnaTransferList);
    // 連携できる出金をフィルター
    for (SygnaTransferProperties sygnaTransfer : sygnaTransferList) {
      SygnaCustomer checkSygnaCustomer =
          sygnaCustomerService.findOneByUserId(sygnaTransfer.getWithdrawal().getUserId());
      if (ObjectUtils.isEmpty(checkSygnaCustomer)) {
        log.info("ユーザーがSygnaへ連携していません。UserId:" + sygnaTransfer.getWithdrawal().getUserId());
        waitSygnaTransferList.remove(sygnaTransfer);
        continue;
      }
    }
    for (SygnaTransferProperties sygnaTransfer : waitSygnaTransferList) {
      List<TreeMap<String, Object>> params = setTransactionsParams(sygnaTransfer);
      if (ObjectUtils.isNotEmpty(params)) {
        SygnaHubResponse<List<TransactionsRes>> res = sygnaHubManager.createTransactions(params);
        List<TransactionsRes> txReslist = res.getData();
        if (ObjectUtils.isNotEmpty(txReslist)) {
          TransactionsRes txRes = txReslist.get(0);
          Withdrawal withdrawalForUpdate = findOne(sygnaTransfer.getWithdrawal().getId());
          if (txRes.isSuccess()) {
            withdrawalForUpdate.setSygnaTxId(txRes.getId());
            if (SygnaConstants.SYGNA_EMAILPROTOCOL.equals(sygnaTransfer.getType())) {
              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.VASP_MAIL_SENDED);
              withdrawalForUpdate.setEmailProtocolFlg(true);
            } else {
              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.SYGNA_COOPERATION);
            }
            save(withdrawalForUpdate);
          } else {
            log.warn("Sygna連携失敗。withdrawalId:{}, errorCode:{}, errorMessage:{}",
                withdrawalForUpdate.getId(), txRes.getError().getCode(),
                txRes.getError().getMessage());
            // 連携失敗
            User user = userService.findOne(withdrawalForUpdate.getUserId());
            if (!redisManager.executeWithLock(
            		getLockKey(withdrawalForUpdate.getUserId(), withdrawalForUpdate.getCurrency()),
            LockParams.LOCKKEY,
            () -> {
//            	return (Withdrawal)
            	customTransactionManager.execute(entityManager -> {
	              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.REJECTED);
	              save(withdrawalForUpdate, entityManager);
	              assetService.updateWithExternalLock(withdrawalForUpdate.getUserId(),
	                      withdrawalForUpdate.getCurrency(), BigDecimal.ZERO,
	                      withdrawalForUpdate.getAmount().negate(), entityManager);
	              return withdrawalForUpdate;
	            });
            })) {
            	log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawalForUpdate.getUserId(), withdrawalForUpdate.getCurrency()));
            	throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawalForUpdate.getUserId());
            };
            if (withdrawalForUpdate != null && WithdrawalStatus.REJECTED.equals(withdrawalForUpdate.getWithdrawalStatus())) {
            	MailNoreplyType mailNoreplyType = MailNoreplyType.WITHDRAWAL_REJECTED;
                MailNoreply mailNoreply = mailNoreplyService.findOne(mailNoreplyType);
                // send mail
                try {
                	// 日時
                	String Date = FormatUtil.formatJst(new Date(),
                			FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
                	// 通貨
                	String currency = withdrawalForUpdate.getCurrency().toString();
                	// 数量
                	String amount = withdrawalForUpdate.getAmount().toPlainString();
    	            // 送付元アドレス
    	            String address = withdrawalForUpdate.getAddress();
    	            MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
    	            String[] args = new String[] {Date, currency, amount, address};
    	            String mailContent = messageFormat.format(args);
    	            sesManager.send(mailNoreply.getFromAddress(), user.getEmail(),
    	            		mailNoreply.getTitle(), mailContent);
                } catch (Exception e) {
                	log.error(e.getMessage(), e);
                }
			}
          }
        }
        Thread.sleep(1000);
      }
    }
  }

  // BO status同期化
  public void updateStatus(List<Withdrawal> waitWithdrawal) throws Exception {
    String txIds = waitWithdrawal.stream().map(Withdrawal::getSygnaTxId).distinct()
        .collect(Collectors.joining("&id="));
    SygnaHubResponse<List<TransactionsRes>> transactionsRes =
        sygnaHubManager.getTransactions(txIds);
    List<TransactionsRes> transactionsList = transactionsRes.getData();
    List<Withdrawal> withdrawalRetransfer = new ArrayList<>();
    for (TransactionsRes txRes : transactionsList) {
      Withdrawal withdrawalForUpdate = waitWithdrawal.stream()
          .filter(x -> x.getSygnaTxId().equals(txRes.getId())).findFirst().orElse(null);
      if (ObjectUtils.isNotEmpty(withdrawalForUpdate)) {
        // 0:Processing,1:Blocked,2:Pending On Beneficiary,3:Transfer To Beneficiary Failed,
        // 4:Pending On Originator,5:Transfer To Originator Failed,6:Confirmed,7:Confirm
        // Failed,8:Rejected
        // 9:Expired,10:Canceled,14:Awaiting Originator VASP's TxID
        switch (txRes.getStatus()) {
          case 0:
            if (WithdrawalStatus.SYGNA_COOPERATION
                .equals(withdrawalForUpdate.getWithdrawalStatus())) {
              // sygnaからデータの転送が失敗、再度create transacionの対象になる
              withdrawalRetransfer.add(withdrawalForUpdate);
            }
            break;
          case 1:
            if (WithdrawalStatus.SYGNA_COOPERATION
                .equals(withdrawalForUpdate.getWithdrawalStatus())) {
              withdrawalForUpdate.setChainalysisMaxAlertLevel(ChainalysisAlertLevel.ofCode(txRes.getRiskScore()));
              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.AML_INFO_RECIVED);
              save(withdrawalForUpdate);
            }
            break;
          // SYGNA一時対応
          // case 2:
          // if(WithdrawalStatus.SYGNA_COOPERATION.equals(withdrawalForUpdate.getWithdrawalStatus()))
          // {
          // withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.AML_APPROVED);
          // save(withdrawalForUpdate);
          // }
          // break;
          // case 3,5,7,9:
          // // sygnaからデータの転送が失敗の場合、再度create transacionの対象になる
          // withdrawalRetransfer.add(withdrawalForUpdate);
          // break;
          case 4, 14:
            // 受取人が送金通知を許可
            withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.APPROVED);
            save(withdrawalForUpdate);
            break;
          // SYGNA一時対応
          // case 8:
          // // 受取人が送金通知を却下
          // customTransactionManager.execute(
          // entityManager -> {
          // withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.VASP_RESTITUTION_FROM_RECEIVER);
          // // 資産ロック解除
          // assetService.update(
          // withdrawalForUpdate.getUserId(),
          // withdrawalForUpdate.getCurrency(),
          // BigDecimal.ZERO,
          // withdrawalForUpdate.getAmount().negate(),
          // entityManager);
          // save(withdrawalForUpdate, entityManager);
          // // メール送信：送金却下
          // mailSend(withdrawalForUpdate, MailNoreplyType.WITHDRAWAL_REJECTED);
          // });
          // break;
          // SYGNA一時対応
          default:
            if (ObjectUtils.isEmpty(txRes.getTransferId())) {
              withdrawalRetransfer.add(withdrawalForUpdate);
            } else {
              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.APPROVED);
              save(withdrawalForUpdate);
            }
            break;
        }
      }
    }
    try {
      List<SygnaTransferProperties> withdrawalWithSygna =
          getSygnaTransferList(withdrawalRetransfer);
      createTransfer(withdrawalWithSygna);
    } catch (Exception e) {
      log.info("Sygna通知連携失敗しました。次回実施でリトライ予定です。");
    }
  }

  // EmailProtocol status同期化
  public void updateStatusEmail(List<Withdrawal> waitWithdrawal) throws Exception {
    String txIds = waitWithdrawal.stream().map(Withdrawal::getSygnaTxId).distinct()
        .collect(Collectors.joining("&id="));
    SygnaHubResponse<List<TransactionsRes>> transactionsRes =
        sygnaHubManager.getTransactions(txIds);
    List<TransactionsRes> transactionsList = transactionsRes.getData();
    List<String> sygnaTxIds = new ArrayList<>();
    // SygnaからCurrencyIdを取得
    List<Withdrawal> withdrawalRetransfer = new ArrayList<>();
    for (TransactionsRes txRes : transactionsList) {
      Withdrawal withdrawalForUpdate = waitWithdrawal.stream()
          .filter(x -> x.getSygnaTxId().equals(txRes.getId())).findFirst().orElse(null);
      if (ObjectUtils.isNotEmpty(withdrawalForUpdate)) {
        // 0:Processing,1:Blocked,2:Pending On Beneficiary,3:Transfer To Beneficiary Failed,
        // 4:Pending On Originator,5:Transfer To Originator Failed,6:Confirmed,7:Confirm
        // Failed,8:Rejected
        // 9:Expired,10:Canceled,14:Awaiting Originator VASP's TxID
        switch (txRes.getStatus()) {
          case 1:
            // メール通知要のため、unblock予定
            sygnaTxIds.add(withdrawalForUpdate.getSygnaTxId());
            break;
          case 0, 3, 5, 7, 9:
            // sygnaからデータの転送が失敗の場合、再度create transacionの対象になる
            withdrawalRetransfer.add(withdrawalForUpdate);
            break;
          case 4, 14:
            boolean isHighRisk = true;
            if (chainalysisConfig.enabled()) {
              isHighRisk = (txRes.getRiskScore() != null && ChainalysisAlertLevel.ofCode(txRes.getRiskScore()) != null
                      && ChainalysisAlertLevel.ofCode(txRes.getRiskScore()).getRiskScore() >= chainalysisConfig.getAlertLevelLimit().getRiskScore())
                || (txRes.getSanctionScore() != null && txRes.getSanctionScore() > 0);
              if (ChainalysisAlertLevel.ofCode(txRes.getRiskScore()) != null) {
                withdrawalForUpdate.setChainalysisMaxAlertLevel(ChainalysisAlertLevel.ofCode(txRes.getRiskScore()));
                withdrawalForUpdate.setRiskScore(ChainalysisAlertLevel.ofCode(txRes.getRiskScore()).getRiskScore());
              }
            } else {
              isHighRisk = (txRes.getRiskScore() != null && Float.parseFloat(txRes.getRiskScore()) >= ellipticConfig.getRiskScoreLimit())
                || (txRes.getSanctionScore() != null && txRes.getSanctionScore() > 0);
              if (null != txRes.getRiskScore()) {
                withdrawalForUpdate.setRiskScore((int)Float.parseFloat(txRes.getRiskScore()));
              }
            }
            if (isHighRisk) {
              // 受取人が送金通知を許可
              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.AML_INFO_RECIVED);
              save(withdrawalForUpdate);
            } else {
              withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.APPROVED);
              save(withdrawalForUpdate);
            }
            break;
          case 8:
        	if (!redisManager.executeWithLock(
        			getLockKey(withdrawalForUpdate.getUserId(),withdrawalForUpdate.getCurrency()),
        	LockParams.LOCKKEY,
        	() -> {
//        		return (Withdrawal)
	        	// 受取人が送金通知を却下
	            customTransactionManager.execute(entityManager -> {
	              withdrawalForUpdate
	                  .setWithdrawalStatus(WithdrawalStatus.VASP_RESTITUTION_FROM_RECEIVER);
	              // 資産ロック解除
	              assetService.updateWithExternalLock(withdrawalForUpdate.getUserId(),
	                  withdrawalForUpdate.getCurrency(), BigDecimal.ZERO,
	                  withdrawalForUpdate.getAmount().negate(), entityManager);
	              save(withdrawalForUpdate, entityManager);
	              return withdrawalForUpdate;
	            });
        	})) {
        		log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(withdrawalForUpdate.getUserId(),withdrawalForUpdate.getCurrency()));
        		throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + withdrawalForUpdate.getUserId());
        	};
        	if (withdrawalForUpdate != null && WithdrawalStatus.VASP_RESTITUTION_FROM_RECEIVER.equals(withdrawalForUpdate.getWithdrawalStatus())) {
                // メール送信：送金却下
                mailSend(withdrawalForUpdate, MailNoreplyType.WITHDRAWAL_REJECTED);
			}
            break;
        }
      }
    }
    try {
      // unblock
      sygnaHubManager.emailPatchStatus(sygnaTxIds);
    } catch (Exception e) {
      log.info("Email通知のブロック解除が失敗しました。次回実施でリトライ予定です。");
    }
    try {
      List<SygnaTransferProperties> withdrawalWithSygna =
          getSygnaTransferList(withdrawalRetransfer);
      createTransfer(withdrawalWithSygna);
    } catch (Exception e) {
      log.info("Sygna MailProtocol通知連携失敗しました。次回実施でリトライ予定です。");
    }
  }

  public List<TreeMap<String, Object>> setTransactionsParams(SygnaTransferProperties sygnaTransfer)
      throws Exception {
    List<TreeMap<String, Object>> parameters = new ArrayList<>();
    TreeMap<String, Object> parameter = new TreeMap<>();
    // beneficiary filed
    TreeMap<String, Object> beneficiaryMap = new TreeMap<>();
    // beneficiary addrs
    List<TreeMap<String, Object>> beneficiaryAddrsMapList = new ArrayList<>();
    TreeMap<String, Object> beneficiaryAddrsMap = new TreeMap<>();

    // XRP
    String address = sygnaTransfer.getWithdrawal().getAddress();
    int index = address.indexOf("@");
    if(Currency.XRP.equals(sygnaTransfer.getWithdrawal().getCurrency()) && index > 0) {
      TreeMap<String, Object> extraInfo = new TreeMap<>();
      beneficiaryAddrsMap.put("address", address.substring(0, index));
      extraInfo.put("tag", address.substring(index + 1));
      beneficiaryAddrsMap.put("extra_info", extraInfo);
    } else {
      beneficiaryAddrsMap.put("address", address);
    }

    beneficiaryAddrsMapList.add(beneficiaryAddrsMap);
    beneficiaryMap.put("addrs", beneficiaryAddrsMapList);
    if (SygnaConstants.SYGNA_BRIDGE.equals(sygnaTransfer.getType())) {
      // 受取人情報初期化
      String naturalPersonFirstName = "";
      String naturalPersonLastName = "";
      String naturalPersonPhoneticFirstName = "";
      String naturalPersonPhoneticLastName = "";
      String naturalPersonLocalFirstName = "";
      String naturalPersonLocalLastName = "";
      String legalPersonName = "";
      String legalPersonPhoneticName = "";
      String legalPersonLocalName = "";
      // お客様本人の種類
      Transliterator transLatin = Transliterator.getInstance("Katakana-Latin; Latin-ASCII");
      Transliterator transHalf = Transliterator.getInstance("Fullwidth-Halfwidth");
      String ownUserRecipientType =
          sygnaTransfer.getWithdrawal().getUser().getAuthorities().get(0).getAuthority();
      if ("お客様本人".equals(sygnaTransfer.getWithdrawal().getWithdrawalAccount().getOwnertype())) {
        if ("PERSONAL".equals(ownUserRecipientType)) {
          UserInfo ownUserInfo = sygnaTransfer.getWithdrawal().getUser().getUserInfo();
          if (ownUserInfo.getFirstName().matches("^[\\uff21-\\uff3a|\\uff41-\\uff5a]+$")) {
            naturalPersonFirstName =
                transHalf.transliterate(ownUserInfo.getFirstName()).replace("'", "");;
          } else {
            naturalPersonFirstName =
                transHalf.transliterate(transLatin.transliterate(ownUserInfo.getFirstKana()))
                    .replace("'", "");;
          }
          if (ownUserInfo.getLastName().matches("^[\\uff21-\\uff3a|\\uff41-\\uff5a]+$")) {
            naturalPersonLastName =
                transHalf.transliterate(ownUserInfo.getLastName()).replace("'", "");;
          } else {
            naturalPersonLastName =
                transHalf.transliterate(transLatin.transliterate(ownUserInfo.getLastKana()))
                    .replace("'", "");;
          }
          naturalPersonPhoneticFirstName = ownUserInfo.getFirstKana();
          naturalPersonPhoneticLastName = ownUserInfo.getLastKana();
          naturalPersonLocalFirstName = ownUserInfo.getFirstName();
          naturalPersonLocalLastName = ownUserInfo.getLastName();
        } else if ("CORPORATE".equals(ownUserRecipientType)) {
          UserInfoCorporate ownUserInfoCorporate =
              sygnaTransfer.getWithdrawal().getUser().getUserInfoCorporate();
          // name
          String corporateKana = ownUserInfoCorporate.getNameKana().replace("カブシキガイシャ", "Co.,Ltd")
              .replace("ゴウドウガイシャ", "LLC").replace("ホールディングス", "Holdings");
          legalPersonName =
              transHalf.transliterate(transLatin.transliterate(corporateKana)).replace("'", "");;
          legalPersonPhoneticName = ownUserInfoCorporate.getNameKana();
          legalPersonLocalName = ownUserInfoCorporate.getName();
        }
      } else if ("お客様本人以外"
          .equals(sygnaTransfer.getWithdrawal().getWithdrawalAccount().getOwnertype())) {
        if ("個人".equals(sygnaTransfer.getWithdrawal().getWithdrawalAccount().getRecipienttype())) {
          naturalPersonFirstName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getFirst_name_english();
          naturalPersonLastName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getLast_name_english();
          naturalPersonPhoneticFirstName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getFirst_name_kana();
          naturalPersonPhoneticLastName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getLast_name_kana();
          naturalPersonLocalFirstName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getFirst_name();
          naturalPersonLocalLastName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getLast_name();
        } else if ("法人"
            .equals(sygnaTransfer.getWithdrawal().getWithdrawalAccount().getRecipienttype())) {
          legalPersonName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getLegalname_english();
          legalPersonPhoneticName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getLegalname_kana();
          legalPersonLocalName =
              sygnaTransfer.getWithdrawal().getWithdrawalAccount().getLegalname();
        }
      }
      // beneficiary info
      TreeMap<String, Object> privateInfoMap = new TreeMap<>();
      TreeMap<String, Object> personNameMap = new TreeMap<>();
      // natural person
      if (ObjectUtils.isNotEmpty(naturalPersonLastName)) {
        // name
        TreeMap<String, Object> nameMap = new TreeMap<>();
        nameMap.put("first_name", naturalPersonFirstName);
        nameMap.put("last_name", naturalPersonLastName);
        personNameMap.put("name", nameMap);
        // local name
        TreeMap<String, Object> localNameMap = new TreeMap<>();
        localNameMap.put("first_name", naturalPersonLocalFirstName);
        localNameMap.put("last_name", naturalPersonLocalLastName);
        personNameMap.put("local_name", localNameMap);
        // phonetic name
        TreeMap<String, Object> phoneticNameMap = new TreeMap<>();
        phoneticNameMap.put("first_name", naturalPersonPhoneticFirstName);
        phoneticNameMap.put("last_name", naturalPersonPhoneticLastName);
        personNameMap.put("phonetic_name", phoneticNameMap);
        privateInfoMap.put("natural_person_name", personNameMap);
        // beneficiary customer_type
        beneficiaryMap.put("customer_type", 0);
        // legal person
      } else if (ObjectUtils.isNotEmpty(legalPersonName)) {
        // name
        personNameMap.put("name", legalPersonName);
        // local name
        personNameMap.put("local_name", legalPersonLocalName);
        // phonetic name
        personNameMap.put("phonetic_name", legalPersonPhoneticName);
        privateInfoMap.put("legal_person_name", personNameMap);
        // beneficiary customer_type
        beneficiaryMap.put("customer_type", 1);
      }
      beneficiaryMap.put("private_info", privateInfoMap);
      // beneficiary vasp_code
      beneficiaryMap.put("vasp_code", sygnaTransfer.getVaspCode());
    }
    parameter.put("beneficiary", beneficiaryMap);
    // originator filed
    TreeMap<String, Object> originatorMap = new TreeMap<>();
    // originator addrs
    List<TreeMap<String, Object>> originatorAddrsMapList = new ArrayList<>();
    TreeMap<String, Object> originatorAddrsMap = new TreeMap<>();
    // own hot wallet address
    originatorAddrsMap.put("address",
        walletConfig.getWithdrawalFrom(sygnaTransfer.getWithdrawal().getCurrency().name()));
    originatorAddrsMapList.add(originatorAddrsMap);
    originatorMap.put("addrs", originatorAddrsMapList);
    parameter.put("originator", originatorMap);
    // protocol
    parameter.put("protocol", sygnaTransfer.getType());
    // currency_id
    String currencyId =
        sygnaCurrencyService.getCurrencyId(sygnaTransfer.getWithdrawal().getCurrency().name());
    // 連携できる出金をフィルター
    if (ObjectUtils.isEmpty(currencyId)) {
      log.info("取引所がサポートしていない通貨です。Sygmbol:" + sygnaTransfer.getWithdrawal().getCurrency().name()
          + "　withdrawalId:" + sygnaTransfer.getWithdrawal().getId());
      return null;
    }
    parameter.put("currency_id", currencyId);
    // customer_id
    SygnaCustomer sygnaCustomer =
        sygnaCustomerService.findOneByUserId(sygnaTransfer.getWithdrawal().getUserId());
    parameter.put("customer_id", sygnaCustomer.getSygnaCustomerId());
    // cypto amount
    parameter.put("value", sygnaTransfer.getWithdrawal().getAmount()
        .subtract(sygnaTransfer.getWithdrawal().getWithdrawalFee()));
    parameters.add(parameter);
    return parameters;
  }
}
