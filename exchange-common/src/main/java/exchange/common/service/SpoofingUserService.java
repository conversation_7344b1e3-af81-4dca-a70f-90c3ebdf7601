package exchange.common.service;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import exchange.common.entity.SpoofingUser;
import exchange.common.predicate.EntityPredicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class SpoofingUserService extends EntityService<SpoofingUser, SpoofingUserPredicate> {
  @Override
  public Class<SpoofingUser> getEntityClass() {
    return SpoofingUser.class;
  }
}


@Component
class SpoofingUserPredicate extends EntityPredicate<SpoofingUser> {
}
