package exchange.common.service;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.Predicate;

import org.springframework.stereotype.Service;

import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.entity.SystemConfig;
import exchange.common.predicate.SystemConfigPredicate;

@Service
public class SystemConfigService extends EntityService<SystemConfig, SystemConfigPredicate> {

  @Override
  public Class<SystemConfig> getEntityClass() {
    return SystemConfig.class;
  }

  public SystemConfig findByCondition(Currency currency, CurrencyPair currencyPair, String name) {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<SystemConfig, SystemConfig>() {
      @Override
      public SystemConfig query() {
        List<Predicate> predicates = new ArrayList<>();

        if (currency != null) {
          predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
        }

        if (currencyPair != null) {
          predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
        }

        predicates.add(predicate.equalName(criteriaBuilder, root, name));
        return getSingleResult(entityManager, criteriaQuery, root, predicates);
      }
    });
  }
}
