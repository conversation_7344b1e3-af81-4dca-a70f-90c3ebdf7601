package exchange.common.service;

import java.util.List;
import org.springframework.stereotype.Service;
import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.predicate.UserInfoCorporateOwnerPredicate;

@Service
public class UserInfoCorporateOwnerService
    extends EntityService<UserInfoCorporateOwner, UserInfoCorporateOwnerPredicate> {

  @Override
  public Class<UserInfoCorporateOwner> getEntityClass() {
    return UserInfoCorporateOwner.class;
  }

  public void saveAll(Long userId, List<UserInfoCorporateOwner> owners) {
    owners.forEach(owner -> save(owner));
  }
}
