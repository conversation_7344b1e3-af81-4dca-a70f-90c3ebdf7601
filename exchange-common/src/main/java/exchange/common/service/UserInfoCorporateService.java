package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserInfoCorporate_;
import exchange.common.predicate.UserInfoCorporatePredicate;

@Service
public class UserInfoCorporateService
    extends EntityService<UserInfoCorporate, UserInfoCorporatePredicate> {

  @Override
  public Class<UserInfoCorporate> getEntityClass() {
    return UserInfoCorporate.class;
  }

  /*
   * 【運用】predicates作成メソッドは共通化して使用する
   * インデックス順でaddする
   */
  protected List<Predicate> createPredicates(
      CriteriaBuilder criteriaBuilder,
      Root<UserInfoCorporate> root,
      Long userId,
      List<Long> userIds,
      Integer financialAssetsCode,
      List<Integer> investmentPurposes) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    } else {
      if (!CollectionUtils.isEmpty(userIds)) {
        predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
      }
    }

    if (financialAssetsCode != null) {
      predicates.add(predicate.lessThanFinancialAssets(criteriaBuilder, root, financialAssetsCode));
    }

    if (!CollectionUtils.isEmpty(investmentPurposes)) {
      predicates.add(predicate.inInvestmentPurposes(criteriaBuilder, root, investmentPurposes));
    }

    return predicates;
  }

  public List<UserInfoCorporate> findByCondition(
      Long userId,
      List<Long> userIds,
      Integer financialAssetsCode,
      List<Integer> investmentPurposes,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<UserInfoCorporate, List<UserInfoCorporate>>() {
          @Override
          public List<UserInfoCorporate> query() {
            List<Predicate> predicates =
                createPredicates(
                    criteriaBuilder,
                    root,
                    userId,
                    userIds,
                    financialAssetsCode,
                    investmentPurposes);

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                isAscending
                    ? criteriaBuilder.asc(root.get(UserInfoCorporate_.id))
                    : criteriaBuilder.desc(root.get(UserInfoCorporate_.id)));
          }
        });
  }
}
