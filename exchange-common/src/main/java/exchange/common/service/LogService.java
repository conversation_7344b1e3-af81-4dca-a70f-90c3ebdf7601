package exchange.common.service;

import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

import exchange.common.core.tracer.TraceIdHolder;
import org.springframework.stereotype.Service;
import exchange.common.config.LogConfig;
import exchange.common.constant.LogObjectType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.argument.StructuredArguments;

@Slf4j
@RequiredArgsConstructor
@Service
public class LogService {

  private final LogConfig logConfig;

  public void logRequestStart(HttpServletRequest request) {
    String message = "*** start "
        + request.getMethod()
        + " "
        + request.getRequestURL().toString()
        + " ***";

    if (logConfig.isOutputRequestDetails()) {
      Map<String, Object> requestAttributes = createRequestAttributes(request);
      Map<String, Object> requestHeaders = createRequestHeadersMap(request);

      log.info(message,
          StructuredArguments.value("tid", TraceIdHolder.getTraceIdOrUnknown()),
          StructuredArguments.value("objectType", LogObjectType.REQUEST_START),
          StructuredArguments.value("requestAttributes", requestAttributes),
          StructuredArguments.value("requestHeaders", requestHeaders)
          );
    } else {
      log.info(message,
          StructuredArguments.value("tid", TraceIdHolder.getTraceIdOrUnknown()),
          StructuredArguments.value("objectType", LogObjectType.REQUEST_START)
          );
    }
  }

  private Map<String, Object> createRequestAttributes(HttpServletRequest request) {
    Map<String, Object> requestAttributes = new HashMap<>();

    requestAttributes.put("method", request.getMethod());
    requestAttributes.put("path", request.getRequestURI());
    requestAttributes.put("remoteAddr", request.getRemoteAddr());
    return requestAttributes;
  }

  private Map<String, Object> createRequestHeadersMap(HttpServletRequest request) {
    Map<String, Object> requestHeaders = new HashMap<>();

    Enumeration<String> e = request.getHeaderNames();

    while (e.hasMoreElements()) {
      String headerName = e.nextElement();
      List<String> headerValues = Collections.list(request.getHeaders(headerName));

      if (headerValues == null || headerValues.size() <= 1) {
        String headerValue = headerValues == null || headerValues.isEmpty() ? null : headerValues.get(0);
        requestHeaders.put(headerName, headerValue);
      } else {
        requestHeaders.put(headerName, headerValues);
      }
    }
    return requestHeaders;
  }

  public void logRequestEnd(HttpServletRequest request, long durationMillis) {
    String message = "*** end "
        + request.getMethod()
        + " "
        + request.getRequestURL().toString()
        + " ***";

    log.info(message,
        StructuredArguments.value("objectType", LogObjectType.REQUEST_END),
        StructuredArguments.value("durationMillis", durationMillis)
        );
  }
}
