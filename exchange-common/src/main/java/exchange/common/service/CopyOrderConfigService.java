package exchange.common.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.entity.CopyOrderConfig;
import exchange.common.entity.CopyOrderConfig_;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.predicate.CopyOrderConfigPredicate;
import exchange.common.util.CalculatorUtil;

@Service
public class CopyOrderConfigService
    extends EntityService<CopyOrderConfig, CopyOrderConfigPredicate> {

  @Override
  public Class<CopyOrderConfig> getEntityClass() {
    return CopyOrderConfig.class;
  }

  protected List<Predicate> createPredicates(
      CriteriaBuilder criteriaBuilder,
      Root<CopyOrderConfig> root,
      Long symbolId,
      Exchange exchange,
      Boolean enabled) {
    List<Predicate> predicates = new ArrayList<>();

    if (symbolId != null) {
      predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
    }

    if (exchange != null) {
      predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
    }

    if (enabled != null) {
      predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
    }

    return predicates;
  }

  public CopyOrderConfig findOne(Long symbolId, Exchange exchange, Boolean enabled) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<CopyOrderConfig, CopyOrderConfig>() {
          @Override
          public CopyOrderConfig query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
            predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));

            if (enabled != null) {
              predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
            }

            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<CopyOrderConfig> findAllByCondition(
      Long symbolId, Exchange exchange, Boolean enabled) {
    List<CopyOrderConfig> copyOrderConfigs =
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<CopyOrderConfig, List<CopyOrderConfig>>() {

              @Override
              public List<CopyOrderConfig> query() {
                List<Predicate> predicates =
                    createPredicates(criteriaBuilder, root, symbolId, exchange, enabled);

                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.asc(root.get(CopyOrderConfig_.id)));
              }
            });

    return copyOrderConfigs;
  }

  public PageData<CopyOrderConfig> findByConditionPageData(
      Long symbolId, Exchange exchange, Boolean enabled, Integer number, Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    createPredicates(criteriaBuilder, root, symbolId, exchange, enabled));
              }
            });
    return new PageData<CopyOrderConfig>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<CopyOrderConfig, List<CopyOrderConfig>>() {
              @Override
              public List<CopyOrderConfig> query() {
                List<Predicate> predicates =
                    createPredicates(criteriaBuilder, root, symbolId, exchange, enabled);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(CopyOrderConfig_.id)));
              }
            }));
  }

  /** スプレッド加味した価格を算出(桁数処理) */
  public BigDecimal calculatePriceWithSpread(
      Symbol symbol, CopyOrderConfig copyOrderConfig, OrderSide orderSide, BigDecimal priceScaled)
      throws Exception {
    // 売り(ask)はより高く、買い(bid)はより安い価格でコピーする
    // ※マイナススプレッド%の場合、売り(ask)はより安く、買い(bid)はより高くなる
    BigDecimal copySpread = calculateCopySpread(symbol, copyOrderConfig, priceScaled, orderSide);

    // 桁数処理後の価格 +- 桁数処理後のスプレッド
    BigDecimal priceWithSpread =
        orderSide.isSell() ? priceScaled.add(copySpread) : priceScaled.subtract(copySpread);

    // スプレッド加減算後の価格チェック
    if (priceWithSpread.signum() <= 0) {
      throw new CustomException(
          ErrorCode.DEALING_ERROR_INVALID_PRICE,
          "spotCopyOrdererLog,priceScaled,"
              + priceScaled
              + ",spread%,"
              + copyOrderConfig.getSpreadPercent());
    }

    return priceWithSpread;
  }

  private BigDecimal calculateCopySpread(
      Symbol symbol, CopyOrderConfig copyOrderConfig, BigDecimal priceScaled, OrderSide orderSide) {

    BigDecimal spread =
        CalculatorUtil.calculatePercentage(priceScaled, copyOrderConfig.getSpreadPercent());

    // 桁数処理
    // 価格：売り(ask)の場合:切り上げ、買い(bid)の場合：切り下げ
    // ※切り上げは単純切り上げ (例：小数点以下0桁とするとき、120.1=> 121, 120.01も121となる)
    return (orderSide == OrderSide.SELL)
        ? symbol.getCurrencyPair().getScaledPrice(spread, RoundingMode.UP)
        : symbol.getCurrencyPair().getScaledPrice(spread, RoundingMode.DOWN);
  }
}
