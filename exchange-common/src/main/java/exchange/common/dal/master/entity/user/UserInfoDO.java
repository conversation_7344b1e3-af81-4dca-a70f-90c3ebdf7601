package exchange.common.dal.master.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import exchange.common.constant.Country;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/10
 */
@Accessors(chain = true)
@Data
@TableName("user_info")
public class UserInfoDO implements Serializable {

	@Serial
	private static final long serialVersionUID = 3257353672406592034L;

	@TableId(value = "ID", type = IdType.AUTO)
	private Long id;

	@TableField("user_id")
	private Long userId;

	@TableField("first_name")
	private String firstName;

	@TableField("last_name")
	private String lastName;

	@TableField("first_kana")
	private String firstKana;

	@TableField("last_kana")
	private String lastKana;

	@TableField("nationality")
	private String nationality;

	@TableField("zip_code")
	private String zipCode;

	@TableField("prefecture")
	private String prefecture;

	@TableField("city")
	private String city;

	@TableField("address")
	private String address;

	@TableField("building")
	private String building;

	@TableField("birthday")
	private String birthday;

	@TableField("gender")
	private Integer gender;

	@TableField("phone_number")
	private String phoneNumber;

	@TableField("occupation")
	private Integer occupation;

	@TableField("industry")
	private Integer industry;

	@TableField("work_place")
	private String workPlace;

	@TableField("position")
	private String position;

	@TableField("priceFrom")
	private Integer priceFrom;

	@TableField("income")
	private Integer income;

	@TableField("financial_assets")
	private Integer financialAssets;

	@TableField("purpose")
	private Integer purpose;


	@TableField("investment_purposes")
	private Integer investmentPurposes;

	@TableField("crypto_experience")
	private Integer cryptoExperience;

	@TableField("fx_experience")
	private Integer fxExperience;

	@TableField("stocks_experience")
	private Integer stocksExperience;

	@TableField("fund_experience")
	private Integer fundExperience;

	@TableField("application_history")
	private Integer applicationHistory;

	@TableField("application_history_other")
	private String applicationHistoryOther;

	@TableField("foreign_peps")
	private boolean foreignPeps;

	@TableField("country")
	private Country country;

	@TableField("antisocial_status")
	private String antisocialStatus;

	@TableField("residence_card_expired_at")
	private Date residenceCardExpiredAt;

	@TableField("insider")
	private boolean insider = false;

	@TableField("created_at")
	private Date createdAt;

	@TableField("updated_at")
	private Date updatedAt;
}
