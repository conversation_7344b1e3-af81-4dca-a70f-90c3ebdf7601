package exchange.common.dal.master.bo.cryptoToken;

import exchange.common.constant.CryptoToken;
import exchange.common.constant.CryptoTokenApplyStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/5
 */
@Accessors(chain = true)
@Data
public class CryptoTokenGiveReportBO implements Serializable {
	@Serial
	private static final long serialVersionUID = -3121604463848983369L;

	private Long id;
	private Integer batchSeqNo;
	private Long userId;
	private String email;
	private CryptoToken cryptoToken;
	private Integer type;
	private Date applyTime;
	private BigDecimal nidtAmount;
	private CryptoTokenApplyStatus applyStatus;
	private BigDecimal assignedQuantity;
	private Date updatedAt;
	private BigDecimal totalAssignedQuantity;
	private Integer count;
	private Integer dataSourceType;
}
