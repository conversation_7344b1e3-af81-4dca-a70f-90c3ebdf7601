package exchange.common.dal.master.mapper.dowjones;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import exchange.common.dal.master.entity.dowjones.DowJonesSamCaseDO;
import exchange.common.mybatis.BatchMapper;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Repository
public interface DowJonesSamCaseMapper extends BatchMapper<DowJonesSamCaseDO> {

	default DowJonesSamCaseDO findOneByUserId(Long userId) {
		return this.selectOne(new LambdaQueryWrapper<DowJonesSamCaseDO>()
			.eq(DowJonesSamCaseDO::getUserId, userId)
			.eq(DowJonesSamCaseDO::getEnabled, Boolean.TRUE)
			.last("limit 1")
		);
	}

	default void updateSearchTypeById(Long id, String searchType) {
    	this.update(null, new LambdaUpdateWrapper<DowJonesSamCaseDO>()
            .eq(DowJonesSamCaseDO::getId, id)
            .set(DowJonesSamCaseDO::getSearchType, searchType)
            .set(DowJonesSamCaseDO::getUpdatedAt, new Date())
		);
	}
}
