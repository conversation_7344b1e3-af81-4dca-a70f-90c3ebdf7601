package exchange.common.dal.master.entity.dowjones;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import exchange.common.constant.DowJonesUserRelationDataTypeEnum;
import exchange.common.service.dowjones.dto.DowJonesUserRelationDataInfoDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Accessors(chain = true)
@Data
@TableName(value = "dow_jones_sam_association", autoResultMap = true)
public class DowJonesSamAssociationDO implements Serializable {

	@Serial
	private static final long serialVersionUID = -151644543640395690L;

	@TableId(value = "ID", type = IdType.AUTO)
	private Long id;

	@TableField("case_id")
	private Long caseId;

	@TableField("dow_jones_sam_association_id")
	private String dowJonesSamAssociationId;

	/**
	 * {@link DowJonesUserRelationDataTypeEnum}
	 */
	@TableField("user_relation_data_type")
	private String userRelationDataType;

	@TableField("user_relation_data_id")
	private Long userRelationDataId;

	@TableField(value = "user_relation_data_info", typeHandler = DowJonesUserRelationDataInfoDTO.SinglePropertyTypeHandler.class)
	private DowJonesUserRelationDataInfoDTO userRelationDataInfo;

	@TableField("enabled")
	private Boolean enabled;

	@TableField("created_at")
	private Date createdAt;

	@TableField("updated_at")
	private Date updatedAt;
}
