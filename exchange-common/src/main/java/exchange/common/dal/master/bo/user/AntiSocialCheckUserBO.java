package exchange.common.dal.master.bo.user;

import exchange.common.constant.KycStatus;
import exchange.common.constant.UserStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: wen.y
 * @date: 2024/12/10
 */
@Accessors(chain = true)
@Data
public class AntiSocialCheckUserBO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1580707609465298862L;

	private Long id;
	private String email;
	private Long userInfoId;
	private Long userInfoCorporateId;
	private UserStatus userStatus;
	private KycStatus kycStatus;
	private Long userKycId;
	private String userInfoFirstName;
	private String userInfoLastName;
	private String userInfoCorporateName;
	private String userInfoBirthday;
	private Integer userInfoGender;
}
