package exchange.common.dal.master.entity.cryptoToken;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import exchange.common.constant.CryptoToken;
import exchange.common.constant.CryptoTokenApplyStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/5
 */
@Accessors(chain = true)
@Data
@TableName("crypto_token_give")
public class CryptoTokenGiveDO implements Serializable {

	@Serial
	private static final long serialVersionUID = -6400012527189067129L;

	@TableId(value = "ID", type = IdType.AUTO)
	private Long id;

	@TableField("batch_seq_no")
	private Integer batchSeqNo;

	@TableField("apply_id")
	private Long applyId;

	@TableField("user_id")
	private Long userId;

	@TableField("crypto_token")
	private CryptoToken cryptoToken;

	@TableField("type")
	private Integer type;

	@TableField("apply_status")
	private CryptoTokenApplyStatus applyStatus;

	@TableField("assigned_quantity")
	private BigDecimal assignedQuantity;

	@TableField("total_assigned_quantity")
	private BigDecimal totalAssignedQuantity;

	@TableField("created_at")
	private Date createdAt;

	@TableField("updated_at")
	private Date updatedAt;

}
