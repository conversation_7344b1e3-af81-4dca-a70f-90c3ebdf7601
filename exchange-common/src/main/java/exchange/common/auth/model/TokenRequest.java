
package exchange.common.auth.model;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * TokenRequest
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaClientCodegen", date = "2018-12-21T11:18:42.855Z")
public class TokenRequest {
  @SerializedName("grant_type")
  private String grantType = null;

  @SerializedName("code")
  private String code = null;

  @SerializedName("refresh_token")
  private String refreshToken = null;

  @SerializedName("redirect_uri")
  private String redirectUri = null;

  @SerializedName("client_id")
  private String clientId = null;

  @SerializedName("client_secret")
  private String clientSecret = null;

  public TokenRequest grantType(String grantType) {
    this.grantType = grantType;
    return this;
  }

   /**
   * 新規発行時はauthorization_code固定。再発行時はrefresh_token固定。
   * @return grantType
  **/
  public String getGrantType() {
    return grantType;
  }

  public void setGrantType(String grantType) {
    this.grantType = grantType;
  }

  public TokenRequest code(String code) {
    this.code = code;
    return this;
  }

   /**
   * 新規発行時のみ必須 認可エンドポイントにて当社から返却した認可コード  minLength: 1 maxLength: 128 
   * @return code
  **/
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public TokenRequest refreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
    return this;
  }

   /**
   * 再発行時のみ必須 トークンエンドポイント(新規発行)にて当社から返却したリフレッシュトークン。  minLength: 1 maxLength: 128 
   * @return refreshToken
  **/
  public String getRefreshToken() {
    return refreshToken;
  }

  public void setRefreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
  }

  public TokenRequest redirectUri(String redirectUri) {
    this.redirectUri = redirectUri;
    return this;
  }

   /**
   * 新規発行時のみ必須 貴社が指定する認可コードをリダイレクトするためのURI。 事前に登録済みのリダイレクトURIであることが必要、それ以外の場合はエラーとする。 ※認可エンドポイント要求時の「redirect_uri」と同じ値。  minLength: 1 maxLength: 256 
   * @return redirectUri
  **/
  public String getRedirectUri() {
    return redirectUri;
  }

  public void setRedirectUri(String redirectUri) {
    this.redirectUri = redirectUri;
  }

  public TokenRequest clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * クライアントID。(貴社認証用の項目) (当社が事前に発行する貴社向けのID) 事前に登録する「クライアント認証方式」にclient_secret_basic(ベーシック認証)を設定した場合、設定不要。 「クライアント認証方式」にclient_secret_post(パラメーター認証)を設定した場合、必須。  minLength: 1 maxLength: 128 
   * @return clientId
  **/
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public TokenRequest clientSecret(String clientSecret) {
    this.clientSecret = clientSecret;
    return this;
  }

   /**
   * クライアントシークレット。(貴社認証用の項目) (当社が事前に発行する貴社向けのID) 事前に登録する「クライアント認証方式」にclient_secret_basic(ベーシック認証)を設定した場合、設定不要。 「クライアント認証方式」にclient_secret_post(パラメーター認証)を設定した場合、必須。  minLength: 1 maxLength: 128 
   * @return clientSecret
  **/
  public String getClientSecret() {
    return clientSecret;
  }

  public void setClientSecret(String clientSecret) {
    this.clientSecret = clientSecret;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TokenRequest tokenRequest = (TokenRequest) o;
    return Objects.equals(this.grantType, tokenRequest.grantType) &&
        Objects.equals(this.code, tokenRequest.code) &&
        Objects.equals(this.refreshToken, tokenRequest.refreshToken) &&
        Objects.equals(this.redirectUri, tokenRequest.redirectUri) &&
        Objects.equals(this.clientId, tokenRequest.clientId) &&
        Objects.equals(this.clientSecret, tokenRequest.clientSecret);
  }

  @Override
  public int hashCode() {
    return Objects.hash(grantType, code, refreshToken, redirectUri, clientId, clientSecret);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TokenRequest {\n");
    
    sb.append("    grantType: ").append(toIndentedString(grantType)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    refreshToken: ").append(toIndentedString(refreshToken)).append("\n");
    sb.append("    redirectUri: ").append(toIndentedString(redirectUri)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientSecret: ").append(toIndentedString(clientSecret)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

