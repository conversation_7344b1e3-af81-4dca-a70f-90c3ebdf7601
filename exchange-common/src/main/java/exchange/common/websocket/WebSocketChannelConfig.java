package exchange.common.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.listener.ChannelTopic;
@Configuration
public class WebSocketChannelConfig {

  @Bean
  ChannelTopic orderBookTopic() {
    return new ChannelTopic("pubsub:orderbook");
  }

  @Bean
  ChannelTopic candlestickTopic() {
    return new ChannelTopic("pubsub:candlestick");
  }

  @Bean
  ChannelTopic assetTopic() {
    return new ChannelTopic("pubsub:asset");
  }
  @Bean
  ChannelTopic spotOrderUpdateTopic() {
    return new ChannelTopic("pubsub:spotorder");
  }
  @Bean
  ChannelTopic spotTradeUpdateTopic() {
    return new ChannelTopic("pubsub:spottrade");
  }

  @Bean
  ChannelTopic tickerTopic() {
    return new ChannelTopic("pubsub:ticker");
  }

  @Bean
  ChannelTopic tradesTopic() {
    return new ChannelTopic("pubsub:trades");
  }

  @Bean
  ChannelTopic currencyPairTopic() {
    return new ChannelTopic("pubsub:currencypair");
  }

  @Bean
  ChannelTopic posCandlestickTopic() {
    return new ChannelTopic("pubsub:poscandlestick");
  }

  @Bean
  ChannelTopic posPriceTopic() {
    return new ChannelTopic("pubsub:posprice");
  }

  @Bean
  ChannelTopic posTradeUpdateTopic() {
    return new ChannelTopic("pubsub:postrade");
  }
}
