package exchange.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.json.JsonParserFactory;

@Slf4j
public class JsonUtil {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

  public static String encode(Object value) {
    try {
      return OBJECT_MAPPER.writeValueAsString(value);
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
    }

    return "";
  }

  public static Map<String, Object> decode(String json) {
    return JsonParserFactory.getJsonParser().parseMap(json);
  }

  public static <T> T decode(String json, Class<T> clazz) {
    if (StringUtils.isEmpty(json)) {
      return null;
    }

    try {
      return OBJECT_MAPPER.readValue(json, clazz);
    } catch (IOException e) {

      log.error("failed to parse json: {}",e.getMessage(), e);

      return null;
    }
  }

  public static <T> T decode(String json, TypeReference<T> typeReference) {
    if (StringUtils.isEmpty(json)) {
      return null;
    }

    try {
      return OBJECT_MAPPER.readValue(json, typeReference);
    } catch (IOException e) {

      log.error("failed to parse json: {}",e.getMessage(), e);

      return null;
    }
  }
}
