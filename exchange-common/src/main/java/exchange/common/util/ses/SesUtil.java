package exchange.common.util.ses;

import exchange.common.config.SesConfig;
import exchange.common.util.CharsetUtil;
import exchange.common.util.CollectionUtil;
import exchange.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: wen.y
 * @date: 2024/10/18
 */
@Slf4j
public class SesUtil {
	private static final String COMPANY_NAME = "BACKSEAT";
	private static final ExecutorService executor = new ThreadPoolExecutor(5, 30,
			1L, TimeUnit.MINUTES,
			new LinkedBlockingQueue<>());

	public static boolean sendAsync(SesConfig sesConfig, SesMessage sesMessage) throws Exception {
		return send(sesConfig, sesMessage, true);
	}

	public static boolean sendSync(SesConfig sesConfig, SesMessage sesMessage) throws Exception {
		return send(sesConfig, sesMessage, false);
	}

	public static boolean send(SesConfig sesConfig, SesMessage sesMessage, boolean isAsync) throws Exception {
		if (isAsync) {
			executor.submit(() -> send(sesConfig, sesMessage));
			return true;
		}
		return send(sesConfig, sesMessage);
	}

	public static boolean send(SesConfig sesConfig, SesMessage sesMessage) throws  Exception {
		Properties props = System.getProperties();
		props.put("mail.transport.protocol", "smtp");
		props.put("mail.smtp.port", sesConfig.getPort());
		props.put("mail.smtp.starttls.enable", "true");
		props.put("mail.smtp.auth", "true");
		Session session = Session.getDefaultInstance(props);

		MimeMessage mimeMessage = new MimeMessage(session);
		mimeMessage.setSubject(sesMessage.getSubject(), CharsetUtil.UTF_8.getCode());
		mimeMessage.setFrom(new InternetAddress(sesMessage.getFrom(), COMPANY_NAME, CharsetUtil.UTF_8.getCode()));
		setAddressArray(mimeMessage, Message.RecipientType.TO, sesMessage.getToList());
		setAddressArray(mimeMessage, Message.RecipientType.CC, sesMessage.getCcList());
		setAddressArray(mimeMessage, Message.RecipientType.BCC, sesMessage.getBccList());

		Transport transport = session.getTransport();

		try {
			Multipart multipart = new MimeMultipart();

			MimeBodyPart textBodyPart = new MimeBodyPart();
			textBodyPart.setText(sesMessage.getBody(), CharsetUtil.UTF_8.getCode());
			multipart.addBodyPart(textBodyPart);

			if (CollectionUtil.isNotEmpty(sesMessage.getAttachmentList())) {
				for (SesMessage.Attachment attachment : sesMessage.getAttachmentList()) {
					MimeBodyPart attachmentBodyPart = new MimeBodyPart();
					DataSource source = new ByteArrayDataSource(attachment.getIn(), "application/octet-stream");
					attachmentBodyPart.setDataHandler(new DataHandler(source));
					attachmentBodyPart.setFileName(MimeUtility.encodeText(attachment.getFileName(), "UTF-8", null));
					multipart.addBodyPart(attachmentBodyPart);
				}
			}
			mimeMessage.setContent(multipart);

			transport.connect(sesConfig.getHost(), sesConfig.getUsername(), sesConfig.getPassword());
			transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
			log.info("send success mail: {} to: {} cc: {} bcc: {}", sesMessage.getSubject(), sesMessage.getToList(), sesMessage.getCcList(), sesMessage.getBccList());
		} catch (Exception e) {
			log.error("send error mail: {} to: {} cc: {} bcc: {}", sesMessage.getSubject(), sesMessage.getToList(), sesMessage.getCcList(), sesMessage.getBccList(), e);
			throw e;
		} finally {
			try {
				transport.close();
			} catch (Exception e) {
				log.error("send close error mail: {} to: {} cc: {} bcc: {}", sesMessage.getSubject(), sesMessage.getToList(), sesMessage.getCcList(), sesMessage.getBccList(), e);
				throw e;
			}
		}

		return true;
	}

	private static void setAddressArray(MimeMessage mimeMessage, Message.RecipientType recipientType, List<String> list) throws Exception {
		if (CollectionUtil.isEmpty(list)) {
			return;
		}
		List<InternetAddress> addrList = new ArrayList<>();
		for (String item : list) {
			addrList.add(new InternetAddress(item));
		}
		Address[] addresses = addrList.toArray(new InternetAddress[]{});
		mimeMessage.setRecipients(recipientType, addresses);
	}
}
