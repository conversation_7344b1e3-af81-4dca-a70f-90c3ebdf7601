package exchange.common.util;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * use for ekyc
 *
 * <AUTHOR>
 */
public final class KycUtil {

  private KycUtil() {}

  public static String hmacSha256Signature(String secret, String payload, String time) {
    String data = StringUtils.isEmpty(payload) ? time : payload + time;
    byte[] signBytes =
        SignatureUtil.doFinal(
            SignatureUtil.Algorithm.HMAC_SHA256,
            secret.getBytes(StandardCharsets.UTF_8),
            data.getBytes(StandardCharsets.UTF_8));

    if (signBytes != null) {
      StringBuilder sb = new StringBuilder();
      for (byte signByte : signBytes) {
        sb.append(String.format("%02x", signByte & 0xff));
      }
      return sb.toString();
    }
    return StringUtils.EMPTY;
  }
}
