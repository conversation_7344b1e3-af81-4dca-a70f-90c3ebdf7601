package exchange.common.predicate;

import exchange.common.constant.TmsStatus;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.common.entity.FrequentCryptoDepositUser_;
import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;

@Component
public class FrequentCryptoDepositUserPredicate extends EntityPredicate<FrequentCryptoDepositUser> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<FrequentCryptoDepositUser> root, Long userId) {
    return criteriaBuilder.equal(root.get(FrequentCryptoDepositUser_.userId), userId);
  }

  public Predicate equalTmsStatus(
      CriteriaBuilder criteriaBuilder, Root<FrequentCryptoDepositUser> root, TmsStatus tmsStatus) {
    return criteriaBuilder.equal(root.get(FrequentCryptoDepositUser_.tmsStatus), tmsStatus);
  }

  public Predicate greaterThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder, Root<FrequentCryptoDepositUser> root, Date targetAt) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(FrequentCryptoDepositUser_.targetAt), targetAt);
  }

  public Predicate lessThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder, Root<FrequentCryptoDepositUser> root, Date targetAt) {
    return criteriaBuilder.lessThanOrEqualTo(root.get(FrequentCryptoDepositUser_.targetAt), targetAt);
  }
}
