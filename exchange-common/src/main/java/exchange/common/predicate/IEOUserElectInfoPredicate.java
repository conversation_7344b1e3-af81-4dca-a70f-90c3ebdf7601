package exchange.common.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.IEOHasOrHasNotChanged;
import exchange.common.constant.IEOMailStatus;
import exchange.common.entity.IEOUserElectInfo;
import exchange.common.entity.IEOUserElectInfo_;

@Component
public class IEOUserElectInfoPredicate extends EntityPredicate<IEOUserElectInfo> {

  public Predicate equalUserId(
      CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root, Long userId) {
    return criteriaBuilder.equal(root.get(IEOUserElectInfo_.userId), userId);
  }
  
  public Predicate equalIeoRecruitInfoId(
      CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root, Long ieoRecruitInfoId) {
    return criteriaBuilder.equal(root.get(IEOUserElectInfo_.ieoRecruitInfoId), ieoRecruitInfoId);
  }

  public Predicate inUserIds(CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root,
      List<Long> userIds) {
    return root.get(IEOUserElectInfo_.userId).in(userIds);
  }
  
  public Predicate notInUserIds(CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root,
      List<Long> excludeUserIds) {
    return root.get(IEOUserElectInfo_.userId).in(excludeUserIds).not();
  }
  
  public Predicate equalHasChangeElectAmount(
      CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root, IEOHasOrHasNotChanged ieoHasOrHasNotChanged) {
    return criteriaBuilder.equal(root.get(IEOUserElectInfo_.hasChangeElectAmount), ieoHasOrHasNotChanged);
  }
  
  public Predicate equalIEOMailStatus(
	      CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root, IEOMailStatus ieoMailStatus) {
	    return criteriaBuilder.equal(root.get(IEOUserElectInfo_.mailStatus), ieoMailStatus);
	  }
  
  public Predicate notEqualIeoRecruitInfoId(
      CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root, Long ieoRecruitInfoId) {
    return criteriaBuilder.notEqual(root.get(IEOUserElectInfo_.ieoRecruitInfoId), ieoRecruitInfoId);
  }
  
  public Predicate inIds(CriteriaBuilder criteriaBuilder, Root<IEOUserElectInfo> root,
      List<Long> ids) {
    return root.get(IEOUserElectInfo_.id).in(ids);
  }
}