package exchange.common.predicate;

import java.util.Date;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import exchange.common.constant.CandlestickType;
import exchange.common.entity.Candlestick;
import exchange.common.entity.Candlestick_;

public abstract class CandlestickPredicate<E extends Candlestick> extends EntityPredicate<E> {

  public Predicate equalSymbolId(CriteriaBuilder criteriaBuilder, Root<E> root, Long symbolId) {
    return criteriaBuilder.equal(root.get(Candlestick_.symbolId), symbolId);
  }

  public Predicate equalCandlestickType(CriteriaBuilder criteriaBuilder, Root<E> root, CandlestickType candlestickType) {
    return criteriaBuilder.equal(root.get(Candlestick_.candlestickType), candlestickType);
  }

  public Predicate equalTargetAt(CriteriaBuilder criteriaBuilder, Root<E> root, Date targetAt) {
    return criteriaBuilder.equal(root.get(Candlestick_.targetAt), targetAt);
  }

  public Predicate greaterThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder, Root<E> root, Date targetAt) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(Candlestick_.targetAt), targetAt);
  }

  public Predicate lessThanTargetAt(CriteriaBuilder criteriaBuilder, Root<E> root, Date targetAt) {
    return criteriaBuilder.lessThan(root.get(Candlestick_.targetAt), targetAt);
  }
}
