package exchange.common.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeAction;
import exchange.common.entity.CopyTrade;
import exchange.common.entity.CopyTrade_;

public abstract class CopyTradePredicate<E extends CopyTrade> extends EntityPredicate<E> {

  public Predicate equalSymbolId(CriteriaBuilder criteriaBuilder, Root<E> root, Long symbolId) {
    return criteriaBuilder.equal(root.get(CopyTrade_.symbolId), symbolId);
  }

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<E> root, Long userId) {
    return criteriaBuilder.equal(root.get(CopyTrade_.userId), userId);
  }

  public Predicate inUserIds(CriteriaBuilder criteriaBuilder, Root<E> root, List<Long> userIds) {
    return root.get(CopyTrade_.userId).in(userIds);
  }

  public Predicate inExceptUserIds(
      CriteriaBuilder criteriaBuilder, Root<E> root, List<Long> exceptUserIds) {
    return root.get(CopyTrade_.userId).in(exceptUserIds).not();
  }

  public Predicate equalOrderSide(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderSide orderSide) {
    return criteriaBuilder.equal(root.get(CopyTrade_.orderSide), orderSide);
  }

  public Predicate equalOrderType(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderType orderType) {
    return criteriaBuilder.equal(root.get(CopyTrade_.orderType), orderType);
  }

  public Predicate inOrderType(Root<E> root, OrderType... orderTypes) {
    return root.get(CopyTrade_.orderType).in((Object[]) orderTypes);
  }

  public Predicate notInOrderType(
      CriteriaBuilder criteriaBuilder, Root<E> root, OrderType... orderTypes) {
    return criteriaBuilder.not(inOrderType(root, orderTypes));
  }

  public Predicate equalTradeAction(
      CriteriaBuilder criteriaBuilder, Root<E> root, TradeAction tradeAction) {
    return criteriaBuilder.equal(root.get(CopyTrade_.tradeAction), tradeAction);
  }

  public Predicate equalExchange(CriteriaBuilder criteriaBuilder, Root<E> root, Exchange exchange) {
    return criteriaBuilder.equal(root.get(CopyTrade_.exchange), exchange);
  }

  public Predicate equalExchangeId(CriteriaBuilder criteriaBuilder, Root<E> root, Long exchangeId) {
    return criteriaBuilder.equal(root.get(CopyTrade_.exchangeId), exchangeId);
  }

  public Predicate equalCoverOrderId(
      CriteriaBuilder criteriaBuilder, Root<E> root, Long coverOrderId) {
    return criteriaBuilder.equal(root.get(CopyTrade_.coverOrderId), coverOrderId);
  }

  public Predicate inCoverOrderIds(
      CriteriaBuilder criteriaBuilder, Root<E> root, List<Long> coverOrderIds) {
    return root.get(CopyTrade_.coverOrderId).in(coverOrderIds);
  }

  public Predicate isNullOfCoverOrderId(Root<E> root) {
    return root.get(CopyTrade_.coverOrderId).isNull();
  }

  public Predicate isNotNullOfCoverOrderId(Root<E> root) {
    return root.get(CopyTrade_.coverOrderId).isNotNull();
  }
}
