package exchange.common.predicate;

import exchange.common.entity.PosMMConfig;
import exchange.common.entity.PosMMConfig_;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

@Component
public class PosMMConfigPredicate extends EntityPredicate<PosMMConfig> {
    public Predicate equalSymbol(CriteriaBuilder criteriaBuilder, Root<PosMMConfig> root,
                                 Long symbolId) {
        return criteriaBuilder.equal(root.get(PosMMConfig_.SYMBOL), symbolId);
    }
}
