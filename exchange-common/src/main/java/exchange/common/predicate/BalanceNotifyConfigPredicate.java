package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Exchange;
import exchange.common.entity.BalanceNotifyConfig;
import exchange.common.entity.BalanceNotifyConfig_;

@Component
public class BalanceNotifyConfigPredicate extends EntityPredicate<BalanceNotifyConfig> {

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<BalanceNotifyConfig> root, String currency) {
    return criteriaBuilder.equal(root.get(BalanceNotifyConfig_.currency), currency);
  }
  
  public Predicate equalUserId(
      CriteriaBuilder criteriaBuilder, Root<BalanceNotifyConfig> root, Long userId) {
    return criteriaBuilder.equal(root.get(BalanceNotifyConfig_.userId), userId);
  }
  
  public Predicate equalExchange(
      CriteriaBuilder criteriaBuilder, Root<BalanceNotifyConfig> root, Exchange exchange) {
    return criteriaBuilder.equal(root.get(BalanceNotifyConfig_.targetPos), exchange);
  }
  
  public Predicate equalEnabled(
      CriteriaBuilder criteriaBuilder, Root<BalanceNotifyConfig> root, boolean enabled) {
    return criteriaBuilder.equal(root.get(BalanceNotifyConfig_.enabled), enabled);
  }

}
