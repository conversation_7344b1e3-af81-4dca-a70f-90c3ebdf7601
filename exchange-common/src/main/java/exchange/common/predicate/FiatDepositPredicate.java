package exchange.common.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.FiatDepositStatus;
import exchange.common.entity.FiatDeposit;
import exchange.common.entity.FiatDeposit_;

@Component
public class FiatDepositPredicate extends EntityPredicate<FiatDeposit> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<FiatDeposit> root,
      Long userId) {
    return criteriaBuilder.equal(root.get(FiatDeposit_.userId), userId);
  }

  public Predicate equalDepositStatus(CriteriaBuilder criteriaBuilder,
      Root<FiatDeposit> root, FiatDepositStatus depositStatus) {
    return criteriaBuilder.equal(root.get(FiatDeposit_.fiatDepositStatus),
        depositStatus);
  }

  public Predicate inFiatDepositStatus(CriteriaBuilder criteriaBuilder,
      Root<FiatDeposit> root, List<FiatDepositStatus> fiatDepositStatusList) {
    return root.get(FiatDeposit_.fiatDepositStatus).in(fiatDepositStatusList);
  }
}
