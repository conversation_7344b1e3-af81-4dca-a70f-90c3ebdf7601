package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.entity.SygnaCustomer;
import exchange.common.entity.SygnaCustomer_;

import java.util.Collection;

@Component
public class SygnaCustomerPredicate extends EntityPredicate<SygnaCustomer> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<SygnaCustomer> root, Long userId) {
    return criteriaBuilder.equal(root.get(SygnaCustomer_.userId), userId);
  }

  public Predicate inUserId(Root<SygnaCustomer> root, Collection<?> userIds) {
    return root.get(SygnaCustomer_.userId).in(userIds);
  }

  public Predicate equalSygnaCustomerId(CriteriaBuilder criteriaBuilder, Root<SygnaCustomer> root, String sygnaCustomerId) {
    return criteriaBuilder.equal(root.get(SygnaCustomer_.sygnaCustomerId), sygnaCustomerId);
  }

}
