package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.CurrencyPairConfig_;

@Component
public class CurrencyPairConfigPredicate extends EntityPredicate<CurrencyPairConfig> {

  public Predicate equalTradeType(
      CriteriaBuilder criteriaBuilder, Root<CurrencyPairConfig> root, TradeType tradeType) {
    return criteriaBuilder.equal(root.get(CurrencyPairConfig_.tradeType), tradeType);
  }

  public Predicate equalCurrencyPair(
      CriteriaBuilder criteriaBuilder, Root<CurrencyPairConfig> root, CurrencyPair currencyPair) {
    return criteriaBuilder.equal(root.get(CurrencyPairConfig_.currencyPair), currencyPair);
  }

  public Predicate isEnabled(
      CriteriaBuilder criteriaBuilder, Root<CurrencyPairConfig> root, boolean enabled) {
    return enabled
        ? criteriaBuilder.isTrue(root.get(CurrencyPairConfig_.enabled))
        : criteriaBuilder.isFalse(root.get(CurrencyPairConfig_.enabled));
  }
}
