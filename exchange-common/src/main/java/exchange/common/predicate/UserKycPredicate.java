package exchange.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.KycMailStatus;
import exchange.common.constant.KycReapplyType;
import exchange.common.constant.KycStatus;
import exchange.common.constant.KycType;
import exchange.common.entity.UserKyc;
import exchange.common.entity.UserKyc_;

@Component
public class UserKycPredicate extends EntityPredicate<UserKyc> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<UserKyc> root, Long userId) {
    return criteriaBuilder.equal(root.get(UserKyc_.userId), userId);
  }

  public Predicate equalKycType(
      CriteriaBuilder criteriaBuilder, Root<UserKyc> root, KycType kycType) {
    return criteriaBuilder.equal(root.get(UserKyc_.kycType), kycType);
  }

  public Predicate equalKycStatus(
      CriteriaBuilder criteriaBuilder, Root<UserKyc> root, KycStatus kycStatus) {
    return criteriaBuilder.equal(root.get(UserKyc_.kycStatus), kycStatus);
  }

  public Predicate equalKycMailStatus(
      CriteriaBuilder criteriaBuilder, Root<UserKyc> root, KycMailStatus kycMailStatus) {
    return criteriaBuilder.equal(root.get(UserKyc_.kycMailStatus), kycMailStatus);
  }

  public Predicate greaterThanOrEqualToMailSendAt(
      CriteriaBuilder criteriaBuilder, Root<UserKyc> root, Date mailSendAt) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(UserKyc_.mailSendAt), mailSendAt);
  }

  public Predicate lessThanMailSendAt(
      CriteriaBuilder criteriaBuilder, Root<UserKyc> root, Date mailSendAt) {
    return criteriaBuilder.lessThan(root.get(UserKyc_.mailSendAt), mailSendAt);
  }
}
