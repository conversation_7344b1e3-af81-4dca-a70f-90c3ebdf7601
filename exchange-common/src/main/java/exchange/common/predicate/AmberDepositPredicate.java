package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.entity.AmberDeposit;
import exchange.common.entity.AmberDeposit_;

@Component
public class AmberDepositPredicate extends EntityPredicate<AmberDeposit> {

  public Predicate lessThanTransactionStatus(CriteriaBuilder criteriaBuilder, Root<AmberDeposit> root, Integer transactionStatus) {
    return criteriaBuilder.lessThan(root.get(AmberDeposit_.transactionStatus), transactionStatus);
  }
  
  public Predicate equalTransactionId(CriteriaBuilder criteriaBuilder, Root<AmberDeposit> root, String transactionId) {
    return criteriaBuilder.equal(root.get(AmberDeposit_.transactionId), transactionId);
  }
}
