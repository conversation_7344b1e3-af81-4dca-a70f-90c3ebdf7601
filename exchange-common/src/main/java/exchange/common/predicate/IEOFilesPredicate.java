package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.entity.IEOFiles;
import exchange.common.entity.IEOFiles_;

@Component
public class IEOFilesPredicate extends EntityPredicate<IEOFiles> {

  public Predicate equalIeoRecruitId(CriteriaBuilder criteriaBuilder, Root<IEOFiles> root, Long ieoRecruitId) {
    return criteriaBuilder.equal(root.get(IEOFiles_.ieoRecruitId), ieoRecruitId);
  }

  public Predicate equalFileType(CriteriaBuilder criteriaBuilder, Root<IEOFiles> root, String fileType) {
    return criteriaBuilder.equal(root.get(IEOFiles_.fileType), fileType);
  }

  public Predicate equalYear(CriteriaBuilder criteriaBuilder, Root<IEOFiles> root, String year) {
    return criteriaBuilder.equal(root.get(IEOFiles_.year), year);
  }

  public Predicate equalMonth(CriteriaBuilder criteriaBuilder, Root<IEOFiles> root, String month) {
    return criteriaBuilder.equal(root.get(IEOFiles_.month), month);
  }
}