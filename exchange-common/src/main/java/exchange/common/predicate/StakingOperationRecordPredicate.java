package exchange.common.predicate;

import java.math.BigDecimal;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.entity.StakingOperationRecord;
import exchange.common.entity.StakingOperationRecord_;

@Component
public class StakingOperationRecordPredicate extends EntityPredicate<StakingOperationRecord> {
  public Predicate equalOperationId(
      CriteriaBuilder criteriaBuilder, Root<StakingOperationRecord> root, long operationId) {
    return criteriaBuilder.equal(root.get(StakingOperationRecord_.operationId), operationId);
  }
  
  public Predicate greaterThanOrAmountFrom(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, BigDecimal amountFrom) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingOperationRecord_.amount), amountFrom);
  }
  
  public Predicate lessThanAmountTo(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, BigDecimal amountTo) {
    return criteriaBuilder.lessThan(root.get(StakingOperationRecord_.amount), amountTo);
  }
  
  public Predicate greaterThanOrGasFeeFrom(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, BigDecimal feeFrom) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingOperationRecord_.gasFee), feeFrom);
  }
  
  public Predicate lessThanGasFeeTo(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, BigDecimal feeTo) {
    return criteriaBuilder.lessThan(root.get(StakingOperationRecord_.gasFee), feeTo);
  }
  
  public Predicate equalType(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, String type) {
    return criteriaBuilder.equal(root.get(StakingOperationRecord_.type), type);
  }
  
  public Predicate equalCurrency(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, Currency currency) {
    return criteriaBuilder.equal(root.get(StakingOperationRecord_.currency), currency);
  }
  
  public Predicate equalStatus(CriteriaBuilder criteriaBuilder,
      Root<StakingOperationRecord> root, String status) {
    return criteriaBuilder.equal(root.get(StakingOperationRecord_.operationStatus), status);
  }
}
