package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Component;

import exchange.common.constant.Authority;
import exchange.common.entity.UserAuthority;
import exchange.common.entity.UserAuthority_;

@Component
public class UserAuthorityPredicate extends EntityPredicate<UserAuthority> {
  public Predicate equalAuthority(CriteriaBuilder criteriaBuilder, Root<UserAuthority> root, Authority authority) {  	  
	return criteriaBuilder.equal(root.get(UserAuthority_.authority), authority);
  }
  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<UserAuthority> root, Long userId) {    
	return criteriaBuilder.equal(root.get(UserAuthority_.userId), userId);
  }
}
