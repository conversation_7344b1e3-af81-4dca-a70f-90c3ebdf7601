package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.entity.SygnaDepositTransfer;
import exchange.common.entity.SygnaDepositTransfer_;

@Component
public class SygnaDepositTransferPredicate extends EntityPredicate<SygnaDepositTransfer> {
  
  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, Long userId) {
    return criteriaBuilder.equal(root.get(SygnaDepositTransfer_.userId), userId);
  }
  
  public Predicate equalTxHash(CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, String txHash) {
    return criteriaBuilder.equal(root.get(SygnaDepositTransfer_.txHash), txHash);
  }
  
  public Predicate inStatus(CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, Integer[] status) {
    return root.get(SygnaDepositTransfer_.status).in((Object[]) status);
  }
  
  public Predicate equalCurrency(CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, Currency currency) {
    return criteriaBuilder.equal(root.get(SygnaDepositTransfer_.currency), currency);
  }
  
  public Predicate inDepositStatus(CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, DepositStatus[] depositStatus) {
    return root.get(SygnaDepositTransfer_.depositStatus).in((Object[]) depositStatus);
  }
  
  public Predicate equalDepositStatus(
      CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, DepositStatus depositStatus) {
    return criteriaBuilder.equal(root.get(SygnaDepositTransfer_.depositStatus), depositStatus);
  }
  
  public Predicate equalSygnaTxId(CriteriaBuilder criteriaBuilder, Root<SygnaDepositTransfer> root, String sygnaTxId) {
    return criteriaBuilder.equal(root.get(SygnaDepositTransfer_.sygnaTxId), sygnaTxId);
  }
  
}
