package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.entity.StakingPool;
import exchange.common.entity.StakingPool_;

@Component
public class StakingPoolPredicate extends EntityPredicate<StakingPool> {

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<StakingPool> root, Currency currency) {
    return criteriaBuilder.equal(root.get(StakingPool_.currency), currency);
  }
  
}
