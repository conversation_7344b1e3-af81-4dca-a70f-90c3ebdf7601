package exchange.common.predicate.dowjones;

import exchange.common.constant.UserTypeEnum;
import exchange.common.entity.dowjones.DowJonesSamCase;
import exchange.common.entity.dowjones.DowJonesSamCase_;
import exchange.common.predicate.EntityPredicate;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Component
public class DowJonesSamCasePredicate extends EntityPredicate<DowJonesSamCase> {

    public Predicate greaterThanUserCount(CriteriaBuilder criteriaBuilder, Root<DowJonesSamCase> root, Long count) {
        return criteriaBuilder.greaterThan(root.get(DowJonesSamCase_.ASSOCIATION_OCCUPY_COUNT), count);
    }

    public Predicate equalActive(CriteriaBuilder criteriaBuilder, Root<DowJonesSamCase> root, Boolean active) {
        return criteriaBuilder.equal(root.get(DowJonesSamCase_.ACTIVE), active);
    }

    public Predicate equalUserType(CriteriaBuilder criteriaBuilder, Root<DowJonesSamCase> root, UserTypeEnum userType) {
        return criteriaBuilder.equal(root.get(DowJonesSamCase_.USER_TYPE).as(Integer.class), userType.getType());
    }

    public Predicate equalDowJonesSamCaseId(CriteriaBuilder criteriaBuilder, Root<DowJonesSamCase> root, String caseId) {
        return criteriaBuilder.equal(root.get(DowJonesSamCase_.DOW_JONES_SAM_CASE_ID), caseId);
    }
}
