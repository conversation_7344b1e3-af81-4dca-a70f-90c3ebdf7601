package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Component;

import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.entity.UserInfoCorporateOwner_;

@Component
public class UserInfoCorporateOwnerPredicate extends EntityPredicate<UserInfoCorporateOwner> {
  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<UserInfoCorporateOwner> root, Long userId) {
    return criteriaBuilder.equal(root.get(UserInfoCorporateOwner_.userId), userId);
  }
}
