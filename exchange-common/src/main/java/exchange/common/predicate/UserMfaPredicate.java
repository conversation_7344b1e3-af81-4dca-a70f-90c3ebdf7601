package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.MfaType;
import exchange.common.entity.UserMfa;
import exchange.common.entity.UserMfa_;

@Component
public class UserMfaPredicate extends EntityPredicate<UserMfa> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<UserMfa> root, Long userId) {
    return criteriaBuilder.equal(root.get(UserMfa_.userId), userId);
  }

  public Predicate equalMfaType(
      CriteriaBuilder criteriaBuilder, Root<UserMfa> root, MfaType mfaType) {
    return criteriaBuilder.equal(root.get(UserMfa_.mfaType), mfaType);
  }
}
