package exchange.common.predicate;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.constant.StakingControlDealings;
import exchange.common.constant.StakingControlOperationStatus;
import exchange.common.constant.StakingControlOperations;
import exchange.common.entity.StakingControl;
import exchange.common.entity.StakingControl_;

@Component
public class StakingControlPredicate extends EntityPredicate<StakingControl> {
  
  public Predicate greaterThanOrEqualToOperationDate(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, Date operationDate) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingControl_.operationDate), operationDate);
  }
  
  public Predicate lessThanOperationDate(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, Date operationDate) {
    return criteriaBuilder.lessThan(root.get(StakingControl_.operationDate), operationDate);
  }
  
  public Predicate operationIdIsNull(CriteriaBuilder criteriaBuilder, Root<StakingControl> root) {
    return root.get(StakingControl_.operationId).isNull();
  }
  
  public Predicate operationIdIsNotNull(CriteriaBuilder criteriaBuilder, Root<StakingControl> root) {
    return root.get(StakingControl_.operationId).isNotNull();
  }
  
  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, Currency currency) {
    return criteriaBuilder.equal(root.get(StakingControl_.currency), currency);
  }
  
  public Predicate inOperationStatus(CriteriaBuilder criteriaBuilder, 
      Root<StakingControl> root, StakingControlOperationStatus[] operationStatus) {
    return root.get(StakingControl_.operationStatus).in((Object[]) operationStatus);
  }
  
  public Predicate equalOperationId(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, long operationId) {
    return criteriaBuilder.equal(root.get(StakingControl_.operationId), operationId);
  }
  
  public Predicate equalOperation(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, StakingControlOperations operation) {
    return criteriaBuilder.equal(root.get(StakingControl_.operation), operation);
  }
  
  public Predicate notEqualOperation(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, StakingControlOperations operation) {
    return criteriaBuilder.equal(root.get(StakingControl_.operation), operation).not();
  }
  
  public Predicate notInIds(Root<StakingControl> root, List<Long> ids) {
    return root.get(StakingControl_.id).in(ids).not();
  }
  
  public Predicate equalOperationStatus(CriteriaBuilder criteriaBuilder, 
      Root<StakingControl> root, StakingControlOperationStatus operationStatus) {
    return criteriaBuilder.equal(root.get(StakingControl_.operationStatus), operationStatus);
  }
  
  public Predicate isNullOperationStatus(CriteriaBuilder criteriaBuilder,Root<StakingControl> root) {
    return root.get(StakingControl_.operationStatus).isNull();
  }
  
  public Predicate greaterThanOrEqualToStakeAmountAccumulatePlanFrom(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal stakeAmountAccumulatePlanFrom) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingControl_.stakeAmountAccumulatePlan), stakeAmountAccumulatePlanFrom);
  }
  
  public Predicate lessThanStakeAmountAccumulatePlanTo(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal stakeAmountAccumulatePlanTo) {
    return criteriaBuilder.lessThan(root.get(StakingControl_.stakeAmountAccumulatePlan), stakeAmountAccumulatePlanTo);
  }
  
  public Predicate greaterThanOrEqualToUnstakeAmountPlanFrom(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal unstakeAmountPlanFrom) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingControl_.unstakeAmountPlan), unstakeAmountPlanFrom);
  }
  
  public Predicate lessThanUnstakeAmountPlanTo(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal unstakeAmountPlanTo) {
    return criteriaBuilder.lessThan(root.get(StakingControl_.unstakeAmountPlan), unstakeAmountPlanTo);
  }
  
  public Predicate inOperationIds(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, List<Long> operationIds) {
    return root.get(StakingControl_.operationId).in(operationIds);
  }
  
  public Predicate isStakeRunDate(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, boolean stakeRunDateFlg) {
    return criteriaBuilder.equal(root.get(StakingControl_.stakeRunDateFlg), stakeRunDateFlg);
  }
  
  public Predicate greaterThanOrEqualToStakingPoolAmountFrom(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal stakingPoolAmountFrom) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingControl_.stakingPoolAmount), stakingPoolAmountFrom);
  }
  
  public Predicate lessThanStakingPoolAmountTo(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal stakingPoolAmountTo) {
    return criteriaBuilder.lessThan(root.get(StakingControl_.stakingPoolAmount), stakingPoolAmountTo);
  }
  
  public Predicate greaterThanOrEqualToCancelPrepareAmountFrom(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal cancelPrepareAmountFrom) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(StakingControl_.cancelPrepareAmount), cancelPrepareAmountFrom);
  }
  
  public Predicate lessThanCancelPrepareAmountTo(CriteriaBuilder criteriaBuilder,
      Root<StakingControl> root, BigDecimal cancelPrepareAmountTo) {
    return criteriaBuilder.lessThan(root.get(StakingControl_.cancelPrepareAmount), cancelPrepareAmountTo);
  }
  
  public Predicate equalStakingControlDealing(
      CriteriaBuilder criteriaBuilder, Root<StakingControl> root, StakingControlDealings stakingControlDealing) {
    return criteriaBuilder.equal(root.get(StakingControl_.stakingControlDealing), stakingControlDealing);
  }
  
 
}
