package exchange.common.predicate;

import exchange.common.constant.CryptoToken;
import exchange.common.entity.cryptoToken.CryptoTokenConfig;
import exchange.common.entity.cryptoToken.CryptoTokenConfig_;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Component
public class CryptoTokenConfigPredicate extends EntityPredicate<CryptoTokenConfig> {

	public Predicate equalCryptoToken(CriteriaBuilder criteriaBuilder, Root<CryptoTokenConfig> root, CryptoToken cryptoToken) {
		return criteriaBuilder.equal(root.get(CryptoTokenConfig_.cryptoToken), cryptoToken);
	}

	public Predicate isEnabled(CriteriaBuilder criteriaBuilder, Root<CryptoTokenConfig> root, boolean enabled) {
		return enabled
				? criteriaBuilder.isTrue(root.get(CryptoTokenConfig_.enabled))
				: criteriaBuilder.isFalse(root.get(CryptoTokenConfig_.enabled));
	}
}
