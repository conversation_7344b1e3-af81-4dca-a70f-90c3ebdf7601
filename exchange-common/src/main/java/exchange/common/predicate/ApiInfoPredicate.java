package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Component;

import exchange.common.entity.ApiInfo;
import exchange.common.entity.ApiInfo_;

@Component
public class ApiInfoPredicate extends EntityPredicate<ApiInfo> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<ApiInfo> root, Long userId) {
    return criteriaBuilder.equal(root.get(ApiInfo_.userId), userId);
  }

  public Predicate equalApiKey(CriteriaBuilder criteriaBuilder, Root<ApiInfo> root, String apiKey) {
    return criteriaBuilder.equal(root.get(ApiInfo_.apiKey), apiKey);
  }
}
