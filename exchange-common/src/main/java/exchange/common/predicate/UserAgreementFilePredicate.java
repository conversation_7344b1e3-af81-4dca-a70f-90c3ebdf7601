package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Component;

import exchange.common.constant.UserAgreementType;
import exchange.common.entity.UserAgreementFile;
import exchange.common.entity.UserAgreementFile_;

@Component
public class UserAgreementFilePredicate extends EntityPredicate<UserAgreementFile> {

  public Predicate equalUserAgreementType(CriteriaBuilder criteriaBuilder, Root<UserAgreementFile> root,
      UserAgreementType userAgreementType) {
    return criteriaBuilder.equal(root.get(UserAgreementFile_.userAgreementType), userAgreementType);
  }

  public Predicate equalVersion(CriteriaBuilder criteriaBuilder, Root<UserAgreementFile> root, String version) {
    return criteriaBuilder.equal(root.get(UserAgreementFile_.version), version);
  }

}
