package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.Deposit_;

@Component
public class DepositPredicate extends EntityPredicate<Deposit> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<Deposit> root, Long userId) {
    return criteriaBuilder.equal(root.get(Deposit_.userId), userId);
  }

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, Currency currency) {
    return criteriaBuilder.equal(root.get(Deposit_.currency), currency);
  }

  public Predicate likeTransactionId(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, String transactionId) {
    return criteriaBuilder.like(root.get(Deposit_.transactionId), "%" + transactionId + "%");
  }

  public Predicate equalDepositStatus(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, DepositStatus depositStatus) {
    return criteriaBuilder.equal(root.get(Deposit_.depositStatus), depositStatus);
  }

  public Predicate equalDepositStatusNew(
		  CriteriaBuilder criteriaBuilder, Root<Deposit> root, DepositStatus[] depositStatus) {
		  return root.get(Deposit_.depositStatus).in((Object[]) depositStatus);
	  }
  
  public Predicate equalAddress(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, String address) {
    return criteriaBuilder.equal(root.get(Deposit_.address), address);
  }
  
  public Predicate equalTransactionId(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, String transactionId) {
    return criteriaBuilder.equal(root.get(Deposit_.transactionId), transactionId);
  }

  public Predicate equalDistinction(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, String distinction) {
    return criteriaBuilder.equal(root.get(Deposit_.distinction), distinction);
  }
  
  public Predicate likeAddressStratWith(
      CriteriaBuilder criteriaBuilder, Root<Deposit> root, String address) {
    return criteriaBuilder.like(root.get(Deposit_.address), address + "%");
  }

}
