package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.entity.SygnaCustomerCurrency;
import exchange.common.entity.SygnaCustomerCurrency_;

@Component
public class SygnaCustomerCurrencyPredicate extends EntityPredicate<SygnaCustomerCurrency> {

  public Predicate equalSygnaCustomerTableId(CriteriaBuilder criteriaBuilder, Root<SygnaCustomerCurrency> root, Long sygnaCustomerTableId) {
    return criteriaBuilder.equal(root.get(SygnaCustomerCurrency_.sygnaCustomerTableId), sygnaCustomerTableId);
  }
}
