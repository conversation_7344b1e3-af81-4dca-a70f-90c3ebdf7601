package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.entity.Withdrawal;
import exchange.common.entity.Withdrawal_;

@Component
public class WithdrawalPredicate extends EntityPredicate<Withdrawal> {

  public Predicate equalUserId(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, Long userId) {
    return criteriaBuilder.equal(root.get(Withdrawal_.userId), userId);
  }

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, Currency currency) {
    return criteriaBuilder.equal(root.get(Withdrawal_.currency), currency);
  }

  public Predicate likeAddress(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, String address) {
    return criteriaBuilder.like(root.get(Withdrawal_.address), "%" + address + "%");
  }

  public Predicate likeTransactionId(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, String transactionId) {
    return criteriaBuilder.like(root.get(Withdrawal_.transactionId), "%" + transactionId + "%");
  }

  public Predicate equalWithdrawalStatus(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, WithdrawalStatus withdrawalStatus) {
    return criteriaBuilder.equal(root.get(Withdrawal_.withdrawalStatus), withdrawalStatus);
  }
  
  public Predicate equalWithdrawalStatusNew(
	  CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, WithdrawalStatus[] withdrawalStatus) {
	  return root.get(Withdrawal_.withdrawalStatus).in((Object[]) withdrawalStatus);
  }
  

  public Predicate equalAddress(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, String address) {
    return criteriaBuilder.equal(root.get(Withdrawal_.address), address);
  }

  public Predicate equalDistinction(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, String distinction) {
    return criteriaBuilder.equal(root.get(Withdrawal_.distinction), distinction);
  }
  
  public Predicate likeAddressStratWith(
      CriteriaBuilder criteriaBuilder, Root<Withdrawal> root, String address) {
    return criteriaBuilder.like(root.get(Withdrawal_.address), address + "%");
  }

}
