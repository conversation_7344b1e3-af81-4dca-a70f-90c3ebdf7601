package exchange.common.predicate;

import exchange.common.constant.TmsStatus;
import exchange.common.entity.MultiPrivateWalletWithdrawalUser;
import exchange.common.entity.MultiPrivateWalletWithdrawalUser_;
import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;

@Component
public class MultiPrivateWalletWithdrawalUserPredicate extends EntityPredicate<MultiPrivateWalletWithdrawalUser> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<MultiPrivateWalletWithdrawalUser> root, Long userId) {
    return criteriaBuilder.equal(root.get(MultiPrivateWalletWithdrawalUser_.userId), userId);
  }

  public Predicate equalTmsStatus(
      CriteriaBuilder criteriaBuilder, Root<MultiPrivateWalletWithdrawalUser> root, TmsStatus tmsStatus) {
    return criteriaBuilder.equal(root.get(MultiPrivateWalletWithdrawalUser_.tmsStatus), tmsStatus);
  }

  public Predicate greaterThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder, Root<MultiPrivateWalletWithdrawalUser> root, Date targetAt) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(MultiPrivateWalletWithdrawalUser_.targetAt), targetAt);
  }

  public Predicate lessThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder, Root<MultiPrivateWalletWithdrawalUser> root, Date targetAt) {
    return criteriaBuilder.lessThanOrEqualTo(root.get(MultiPrivateWalletWithdrawalUser_.targetAt), targetAt);
  }
}
