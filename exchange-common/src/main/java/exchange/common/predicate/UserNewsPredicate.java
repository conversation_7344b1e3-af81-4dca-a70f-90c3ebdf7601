package exchange.common.predicate;


import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Component;

import exchange.common.entity.UserNews;
import exchange.common.entity.UserNews_;

@Component
public class UserNewsPredicate extends EntityPredicate<UserNews> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<UserNews> root, Long userId) {
    return criteriaBuilder.equal(root.get(UserNews_.userId), userId);
  }
 
}