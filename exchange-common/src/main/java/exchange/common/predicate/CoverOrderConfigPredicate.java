package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import exchange.common.constant.TradeType;
import org.springframework.stereotype.Component;
import exchange.common.constant.Exchange;
import exchange.common.entity.CoverOrderConfig;
import exchange.common.entity.CoverOrderConfig_;

@Component
public class CoverOrderConfigPredicate extends EntityPredicate<CoverOrderConfig> {

  public Predicate equalSymbolId(
      CriteriaBuilder criteriaBuilder, Root<CoverOrderConfig> root, Long symbolId) {
    return criteriaBuilder.equal(root.get(CoverOrderConfig_.symbolId), symbolId);
  }

  public Predicate equalExchange(
      CriteriaBuilder criteriaBuilder, Root<CoverOrderConfig> root, Exchange exchange) {
    return criteriaBuilder.equal(root.get(CoverOrderConfig_.exchange), exchange);
  }

  public Predicate isEnabled(
      CriteriaBuilder criteriaBuilder, Root<CoverOrderConfig> root, boolean enabled) {
    return enabled
        ? criteriaBuilder.isTrue(root.get(CoverOrderConfig_.enabled))
        : criteriaBuilder.isFalse(root.get(CoverOrderConfig_.enabled));
  }

  public Predicate equalTradeType(
          CriteriaBuilder criteriaBuilder, Root<CoverOrderConfig> root, TradeType tradeType) {
    return criteriaBuilder.equal(root.get(CoverOrderConfig_.tradeType), tradeType);
  }
}
