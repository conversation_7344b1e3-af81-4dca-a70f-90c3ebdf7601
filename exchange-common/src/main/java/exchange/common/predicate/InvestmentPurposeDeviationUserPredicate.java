package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.entity.InvestmentPurposeDeviationUser;
import exchange.common.entity.InvestmentPurposeDeviationUser_;

@Component
public class InvestmentPurposeDeviationUserPredicate
    extends EntityPredicate<InvestmentPurposeDeviationUser> {

  public Predicate equalUserId(
      CriteriaBuilder criteriaBuilder, Root<InvestmentPurposeDeviationUser> root, Long userId) {
    return criteriaBuilder.equal(root.get(InvestmentPurposeDeviationUser_.userId), userId);
  }
}
