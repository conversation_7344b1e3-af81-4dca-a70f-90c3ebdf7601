package exchange.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import exchange.common.constant.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "withdrawal")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class Withdrawal extends AbstractEntity {

  private static final long serialVersionUID = -7416671196554065701L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;

  @Getter
  @Setter
  @Column(name = "withdrawal_channel", nullable = false)
  @Enumerated(EnumType.STRING)
  private WithdrawalChannel withdrawalChannel = WithdrawalChannel.UNKNOWN;

  @Getter
  @Setter
  @Column(name = "withdrawal_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private WithdrawalType withdrawalType;

  @Getter
  @Setter
  @Column(name = "withdrawal_purpose", nullable = false)
  @Enumerated(EnumType.STRING)
  private WithdrawalPurpose withdrawalPurpose;

  @Getter
  @Setter
  @Column(name = "amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal amount;

  @Getter
  @Setter
  @Column(name = "asset_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal assetAmount;

  @Getter
  @Setter
  @Column(name = "withdrawal_fee", precision = 34, scale = 20, nullable = false)
  private BigDecimal withdrawalFee;

  @Getter
  @Setter
  @Column(name = "transaction_fee", precision = 34, scale = 20, nullable = false)
  private BigDecimal transactionFee;

  @Getter
  @Setter
  @Column(name = "jpy_conversion", precision = 34, scale = 20, nullable = true)
  private BigDecimal jpyConversion;

  @Getter
  @Setter
  @Column(name = "comment")
  private String comment;

  @Getter
  @Setter
  @Column(name = "withdrawal_account_id", nullable = false)
  private Long withdrawalAccountId;

  @Getter
  @Setter
  @Column(name = "address")
  private String address;

  @Getter
  @Setter
  @Column(name = "distinction")
  private String distinction = "OUT";

  @Getter
  @Setter
  @Column(name = "transaction_id")
  private String transactionId;

  @Getter
  @Setter
  @Column(name = "withdrawal_status", nullable = false)
  @Enumerated(EnumType.STRING)
  private WithdrawalStatus withdrawalStatus = WithdrawalStatus.EXAMINING;

  @Getter
  @Setter
  @Column(name = "risk_score")
  private Integer riskScore;

  @Getter
  @Setter
  @Column(name = "failed_number")
  private Integer failedNumber;

  @Getter
  @Setter
  @Column(name = "transaction_hash")
  private String transactionHash;

  @Getter
  @Setter
  @Column(name = "sygna_tx_id")
  private String sygnaTxId;

  @Getter
  @Setter
  @Column(name = "sanction_match", nullable = false)
  private boolean sanctionMatch = false;

  @Getter
  @Setter
  @Column(name = "email_protocol_flg", nullable = false)
  private boolean emailProtocolFlg = false;

  @Getter
  @Setter
  @Column(name = "chainalysis_external_id", nullable = false)
  private String chainalysisExternalId;

  @Getter
  @Setter
  @Column(name = "chainalysis_max_alert_level", nullable = false)
  @Enumerated(EnumType.STRING)
  private ChainalysisAlertLevel chainalysisMaxAlertLevel;

  @Getter
  @Setter
  @CreatedBy
  @Column(name = "created_by")
  private String createdBy;

  @Getter
  @Setter
  @LastModifiedBy
  @Column(name = "updated_by")
  private String updatedBy;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;

  @Getter
  @Setter
  @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
    name = "withdrawal_account_id",
    referencedColumnName = "id",
    foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
    insertable = false,
    updatable = false)
  @Fetch(FetchMode.JOIN)
  @NotFound(action = NotFoundAction.IGNORE)
  private WithdrawalAccount withdrawalAccount;

  public Withdrawal(
      User user,
      Currency currency,
      BigDecimal amount,
      BigDecimal withdrawalFee,
      BigDecimal transactionFee) {
    userId = user.getId();
    this.currency = currency;
    this.amount = amount;
    this.withdrawalFee = withdrawalFee;
    this.transactionFee = transactionFee;
  }

  @JsonIgnore
  public BigDecimal nextBalance(BigDecimal balance) {
    return balance.subtract(amount);
  }

  @JsonIgnore
  public BigDecimal ownNextBalance(BigDecimal balance) {
    return balance.subtract(amount);
  }
}
