package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "bank")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class Bank extends AbstractEntity {

  private static final long serialVersionUID = -8572975693891497381L;

  @Getter
  @Setter
  @Column(name = "bank_code", nullable = false)
  private Integer bankCode;

  @Getter
  @Setter
  @Column(name = "branch_code", nullable = false)
  private Integer branchCode;

  @Getter
  @Setter
  @Column(name = "bank_name_kana", nullable = false)
  private String bankNameKana;

  @Getter
  @Setter
  @Column(name = "bank_name", nullable = false)
  private String bankName;

  @Getter
  @Setter
  @Column(name = "branch_name_kana", nullable = false)
  private String branchNameKana;

  @Getter
  @Setter
  @Column(name = "branch_name", nullable = false)
  private String branchName;
}
