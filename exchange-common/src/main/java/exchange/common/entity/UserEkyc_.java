package exchange.common.entity;

import java.util.Date;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import exchange.common.constant.KycStatus;


@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserEkyc.class)
public class UserEkyc_ extends AbstractEntity_ {

	  public static volatile SingularAttribute<UserEkyc, Long> userId;
	  public static volatile SingularAttribute<UserEkyc, String> docType;
	  public static volatile SingularAttribute<UserEkyc, String> url;
	  public static volatile SingularAttribute<UserEkyc, KycStatus> status;
	  public static volatile SingularAttribute<UserEkyc, String> referenceId;
	  public static volatile SingularAttribute<UserEkyc, Long> applicantId;

	  public static final String USER_ID = "userId";
	  public static final String DOC_TYPE = "docType";
	  public static final String URL = "url";
	  public static final String STATUS = "status";
	  public static final String REFERENCE_ID = "referenceId";
	  public static final String APPLICANT_ID = "applicantId";

}
