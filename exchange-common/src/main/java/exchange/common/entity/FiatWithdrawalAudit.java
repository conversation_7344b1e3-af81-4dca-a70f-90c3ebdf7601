package exchange.common.entity;

import exchange.common.constant.FiatWithdrawalStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;

@Entity
@Table(name = "fiat_withdrawal_audit")
@ToString(callSuper = true, doNotUseGetters = true)
public class FiatWithdrawalAudit extends AbstractEntity {

    @Getter
    @Setter
    @Column(name = "fiat_withdrawal_id")
    private Long fiatWithdrawalId;

    @Getter
    @Setter
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private FiatWithdrawalStatus status;

    @Getter
    @Setter
    @Column(name = "created_by")
    private String createdBy;

}
