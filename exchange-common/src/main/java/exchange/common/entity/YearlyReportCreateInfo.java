package exchange.common.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "yearly_report_create_info")
@ToString(callSuper = true, doNotUseGetters = true)
public class YearlyReportCreateInfo extends AbstractEntity {

  private static final long serialVersionUID = 3643980084099700186L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "year", nullable = false)
  private Integer year;

  @Getter
  @Setter
  @Column(name = "status", nullable = false)
  private String status;

  @Getter
  @Setter
  @Column(name = "error_msg", nullable = false)
  private String errorMsg;
  
  @Getter
  @Setter
  @CreatedBy
  @Column(name = "created_by")
  private String createdBy;

  @Getter
  @Setter
  @LastModifiedBy
  @Column(name = "updated_by")
  private String updatedBy;
  
  @Getter
  @Setter
  @Column(name = "download_flg", nullable = false)
  private boolean downloadFlg = false;
  
  @Getter
  @Setter
  @CreatedDate
  @Column(name = "latest_download_time")
  private Date latestDownloadTime;

  @Getter
  @Setter
  @Column(name = "s3_file_name")
  private String s3FileName;
}
