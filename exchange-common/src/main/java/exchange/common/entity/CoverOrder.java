package exchange.common.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@MappedSuperclass
@ToString(callSuper = true, doNotUseGetters = true)
public abstract class CoverOrder extends AbstractEntity {

  private static final long serialVersionUID = 7334252265445637056L;

  public static String getTableName(Exchange exchange, Symbol symbol) {
    return symbol.getTradeType().toLowerCase()
        + "_cover_order_"
        + exchange.toLowerCase()
        + "_"
        + symbol.getCurrencyPair().toLowerCase();
  }

  @Getter
  @Setter
  @Column(name = "symbol_id", nullable = false)
  private Long symbolId;

  @Getter
  @Setter
  @Column(name = "order_side", nullable = false)
  @Enumerated(EnumType.STRING)
  private OrderSide orderSide;

  @Getter
  @Setter
  @Column(name = "order_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private OrderType orderType;

  @Getter
  @Setter
  @Column(name = "price", precision = 34, scale = 20, nullable = false)
  private BigDecimal price;

  @Getter
  @Setter
  @Column(name = "average_price", precision = 34, scale = 20)
  private BigDecimal averagePrice = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal amount;

  @Getter
  @Setter
  @Column(name = "remaining_amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal remainingAmount;

  @Getter
  @Setter
  @Column(name = "fee", precision = 34, scale = 20)
  private BigDecimal fee;

  @Getter
  @Setter
  @Column(name = "exchange", nullable = false)
  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  // カバー注文発注先の注文ID
  @Getter
  @Setter
  @Column(name = "exchange_order_id", nullable = false)
  private Long exchangeOrderId;

  // カバー注文発注日時 => created_at

  public CoverOrder setProperties(
      Long symbolId,
      OrderSide orderSide,
      OrderType orderType,
      BigDecimal price,
      BigDecimal amount,
      BigDecimal fee,
      Exchange exchange,
      Long exchangeOrderId) {
    this.symbolId = symbolId;
    this.orderSide = orderSide;
    this.orderType = orderType;
    this.price = price;
    this.amount = amount;
    this.remainingAmount = amount;
    this.fee = fee;
    this.exchange = exchange;
    this.exchangeOrderId = exchangeOrderId;
    return this;
  }
}
