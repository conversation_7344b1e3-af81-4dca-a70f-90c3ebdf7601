package exchange.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(BestPrice.class)
public abstract class BestPrice_ extends AbstractEntity_ {

  public static volatile SingularAttribute<BestPrice, Long> symbolId;
  public static volatile SingularAttribute<BestPrice, BigDecimal> bestAsk;
  public static volatile SingularAttribute<BestPrice, BigDecimal> bestBid;

  public static final String SYMBOL_ID = "symbolId";
  public static final String BESTASK = "bestAsk";
  public static final String BESTBID = "bestBid";
}
