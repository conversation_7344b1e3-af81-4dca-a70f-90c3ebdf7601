package exchange.common.entity;

import exchange.common.constant.KycSubStatus;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_kyc_sub")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserKycSub extends AbstractEntity {

  @Getter
  @Setter
  @Column(name = "kyc_id", nullable = false)
  private Long kycId;

  @Getter
  @Setter
  @Column(name = "kyc_sub_status", nullable = false)
  @Enumerated(EnumType.STRING)
  private KycSubStatus kycSubStatus;
}
