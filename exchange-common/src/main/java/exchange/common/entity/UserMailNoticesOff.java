package exchange.common.entity;

import exchange.common.constant.NoticesType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "user_mail_notices_off")
public class UserMailNoticesOff extends AbstractEntity {

    @Column(name = "user_id")
    private Long userId ;

    @Column(name = "notices_type")
    @Enumerated(EnumType.STRING)
    private NoticesType noticesType ;

    @Column(name = "notices_enabled")
    private Boolean noticesEnabled ;

}
