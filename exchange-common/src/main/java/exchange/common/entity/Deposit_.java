package exchange.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositChannel;
import exchange.common.constant.DepositPurpose;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.DepositType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Deposit.class)
public abstract class Deposit_ extends AbstractEntity_ {

  public static volatile SingularAttribute<Deposit, Long> userId;
  public static volatile SingularAttribute<Deposit, Currency> currency;
  public static volatile SingularAttribute<Deposit, Long> depositAccountId;
  public static volatile SingularAttribute<Deposit, DepositChannel> depositChannel;
  public static volatile SingularAttribute<Deposit, DepositPurpose> depositPurpose;
  public static volatile SingularAttribute<Deposit, DepositType> depositType;
  public static volatile SingularAttribute<Deposit, BigDecimal> amount;
  public static volatile SingularAttribute<Deposit, BigDecimal> assetAmount;
  public static volatile SingularAttribute<Deposit, BigDecimal> fee;
  public static volatile SingularAttribute<Deposit, BigDecimal> jpyConversion;
  public static volatile SingularAttribute<Deposit, String> address;
  public static volatile SingularAttribute<Deposit, String> transactionId;
  public static volatile SingularAttribute<Deposit, Integer> transactionIndex;
  public static volatile SingularAttribute<Deposit, DepositStatus> depositStatus;
  public static volatile SingularAttribute<Deposit, String> comment;
  public static volatile SingularAttribute<Deposit, Integer> riskScore;
  public static volatile SingularAttribute<Deposit, String> transactionHash;
  public static volatile SingularAttribute<Deposit, User> user;
  public static volatile SingularAttribute<Deposit, String> distinction;

  public static final String USER_ID = "userId";
  public static final String CURRENCY = "currency";
  public static final String DEPOSIT_ACCOUNT_ID = "depositAccountId";
  public static final String DEPOSIT_CHANNEL = "depositChannel";
  public static final String DEPOSIT_PURPOSE = "depositPurpose";
  public static final String DEPOSIT_TYPE = "depositType";
  public static final String AMOUNT = "amount";
  public static final String ASSET_AMOUNT = "assetAmount";
  public static final String FEE = "fee";
  public static final String JPY_CONVERSION = "jpyConversion";
  public static final String ADDRESS = "address";
  public static final String TRANSACTION_ID = "transactionId";
  public static final String TRANSACTION_INDEX = "transactionIndex";
  public static final String DEPOSIT_STATUS = "depositStatus";
  public static final String COMMENT = "comment";
  public static final String RISKSCORE = "riskScore";
  public static final String TRANSACTION_HASH = "transactionHash";
  public static final String USER = "user";
  public static final String DISTINCTION = "distinction";

}
