package exchange.common.entity;

import exchange.common.constant.KycStatus;
import exchange.common.constant.UserNoteTypeEnum;
import lombok.*;

import javax.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "user_note")
public class UserNote extends AbstractEntity {

    @Column(name = "user_id")
    private Long userId;
    private String note;
    @Column(name = "note_type")
    @Enumerated(EnumType.STRING)
    private UserNoteTypeEnum noteType;
    @Column(name = "current_kyc_status")
    @Enumerated(EnumType.STRING)
    private KycStatus currentKycStatus;
    private String operator;
}
