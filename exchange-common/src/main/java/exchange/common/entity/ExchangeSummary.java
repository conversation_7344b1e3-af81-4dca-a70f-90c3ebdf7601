package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.Currency;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "exchange_summary")
@ToString(callSuper = true, doNotUseGetters = true)
public class ExchangeSummary extends AbstractEntity {

  private static final long serialVersionUID = 8299489298687748835L;

  @Getter
  @Setter
  @Column(name = "target_at", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date targetAt;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;

  @Getter
  @Setter
  @Column(name = "current_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal currentAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "jpy_conversion", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal jpyConversion = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "deposit_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal depositAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "deposit_amount_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal depositAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "deposit_fee", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal depositFee = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "deposit_fee_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal depositFeeJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "withdrawal_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal withdrawalAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "withdrawal_amount_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal withdrawalAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "withdrawal_fee", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal withdrawalFee = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "withdrawal_fee_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal withdrawalFeeJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "transaction_fee", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal transactionFee = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "transaction_fee_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal transactionFeeJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "exchange_spot_trade_buy_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal exchangeSpotTradeBuyAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "exchange_spot_trade_buy_amount_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal exchangeSpotTradeBuyAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "exchange_spot_trade_sell_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal exchangeSpotTradeSellAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(
      name = "exchange_spot_trade_sell_amount_jpy",
      nullable = false,
      precision = 34,
      scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal exchangeSpotTradeSellAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "exchange_spot_trade_fee", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal exchangeSpotTradeFee = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "exchange_spot_trade_fee_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal exchangeSpotTradeFeeJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(
      name = "simple_market_pos_trade_buy_amount",
      nullable = false,
      precision = 34,
      scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketPosTradeBuyAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(
      name = "simple_market_pos_trade_buy_amount_jpy",
      nullable = false,
      precision = 34,
      scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketPosTradeBuyAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(
      name = "simple_market_pos_trade_sell_amount",
      nullable = false,
      precision = 34,
      scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketPosTradeSellAmount = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(
      name = "simple_market_pos_trade_sell_amount_jpy",
      nullable = false,
      precision = 34,
      scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketPosTradeSellAmountJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "simple_market_pos_trade_fee", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketPosTradeFee = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "simple_market_pos_trade_fee_jpy", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketPosTradeFeeJpy = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "simple_market_spot_trade_profit", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketSpotTradeProfit = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(
      name = "simple_market_spot_trade_profit_jpy",
      nullable = false,
      precision = 34,
      scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal simpleMarketSpotTradeProfitJpy = BigDecimal.ZERO;
  
  @Getter
  @Setter
  @Column(name = "staking_lock_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal stakingLockAmount = BigDecimal.ZERO;
  
  @Getter
  @Setter
  @Column(name = "expiration_not_continue_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal expirationNotContinueAmount = BigDecimal.ZERO;
  
  @Getter
  @Setter
  @Column(name = "expiration_not_continue_reward", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal expirationNotContinueReward = BigDecimal.ZERO;
  
  @Getter
  @Setter
  @Column(name = "cancel_amount", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal cancelAmount = BigDecimal.ZERO;
  
  @Getter
  @Setter
  @Column(name = "expiration_continue_reward", nullable = false, precision = 34, scale = 20)
  @JsonSerialize(using = BigDecimalSerializer.class)
  private BigDecimal expirationContinueReward = BigDecimal.ZERO;
}
