package exchange.common.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import exchange.common.constant.Currency;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "deposit_account")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class DepositAccount extends AbstractEntity {

  private static final long serialVersionUID = -8482480025846331649L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;

  @Getter
  @Setter
  @Column(name = "address", nullable = false)
  private String address;

  @Getter
  @Setter
  @Column(name = "destination_tag")
  private Long destinationTag;

  @Getter
  @Setter
  @Column(name = "enabled")
  private boolean enabled;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT), insertable = false, updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;
}
