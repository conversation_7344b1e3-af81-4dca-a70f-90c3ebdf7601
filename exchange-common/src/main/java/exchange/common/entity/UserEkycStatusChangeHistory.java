package exchange.common.entity;

import exchange.common.constant.KycStatus;
import lombok.*;

import javax.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "user_ekyc_status_change_history")
public class UserEkycStatusChangeHistory extends AbstractEntity {

  @Column(name = "user_id")
  private Long userId;

  @Column(name = "applicant_id")
  private Long applicantId;

  @Column(name = "before_status")
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private KycStatus beforeStatus = KycStatus.NONE;

  @Column(name = "after_status")
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private KycStatus afterStatus = KycStatus.NONE;

  @Column(name = "url")
  private String url;

  @Column(name = "reason")
  private String reason;
}
