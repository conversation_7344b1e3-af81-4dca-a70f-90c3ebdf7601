package exchange.common.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "user_status_history")
public class UserStatusHistory extends AbstractEntity {

  @Column(name = "user_id")
  private Long userId;

  @Column(name = "user_info_id")
  private Long userInfoId;

  @Column(name = "before_status")
  private String beforeStatus;

  @Column(name = "after_status")
  private String afterStatus;

  @Column(name = "reason")
  private String reason;

  @Column(name = "staff_id")
  private Long staffId;
}
