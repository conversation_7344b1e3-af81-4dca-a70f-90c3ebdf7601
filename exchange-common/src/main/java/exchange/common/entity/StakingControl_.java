package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(StakingControl.class)
public abstract class StakingControl_ extends AbstractEntity_ {

  public static volatile SingularAttribute<StakingControl, Date> operationDate;
  public static volatile SingularAttribute<StakingControl, Currency> currency;
  public static volatile SingularAttribute<StakingControl, BigDecimal> stakingTotalApplyAmount;
  public static volatile SingularAttribute<StakingControl, BigDecimal> expirationNotContinueAmount;
  public static volatile SingularAttribute<StakingControl, BigDecimal> expirationContinueAmount;
  public static volatile SingularAttribute<StakingControl, BigDecimal> unstakeAmount;
  public static volatile SingularAttribute<StakingControl, BigDecimal> stakeAmountAccumulatePlan;
  public static volatile SingularAttribute<StakingControl, BigDecimal> unstakeAmountPlan;
  public static volatile SingularAttribute<StakingControl, BigDecimal> stakingPoolAmount;
  public static volatile SingularAttribute<StakingControl, BigDecimal> cancelPrepareAmount;
  public static volatile SingularAttribute<StakingControl, String> stakingControlDealing;
  public static volatile SingularAttribute<StakingControl, String> operation;
  public static volatile SingularAttribute<StakingControl, String> operationStatus;
  public static volatile SingularAttribute<StakingControl, Long> operationId;
  public static volatile SingularAttribute<StakingControl, Boolean> stakeRunDateFlg;

  public static final String OPERATION_DATE = "operationDate";
  public static final String CURRENCY = "currency";
  public static final String STAKING_TOTAL_APPLY_AMOUNT = "stakingTotalApplyAmount";
  public static final String EXPIRATION_NOT_CONTINUE_AMOUNT = "expirationNotContinueAmount";
  public static final String EXPIRATION_CONTINUE_AMOUNT = "expirationContinueAmount";
  public static final String UNSTAKE_AMOUNT = "unstakeAmount";
  public static final String STAKE_AMOUNT_ACCUMULATE_PLAN = "stakeAmountAccumulatePlan";
  public static final String UNSTAKE_AMOUNT_PLAN = "unstakeAmountPlan";
  public static final String OPERATION = "operation";
  public static final String OPERATION_STATUS = "operationStatus";
  public static final String OPERATION_ID = "operationId";
  public static final String STAKE_RUN_DATE_FLG = "stakeRunDateFlg";

}

