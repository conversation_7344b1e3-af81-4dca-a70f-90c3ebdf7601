package exchange.common.entity;

import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import exchange.common.constant.IEOHasOrHasNotChanged;
import exchange.common.constant.IEOMailStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "ieo_user_elect_info")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class IEOUserElectInfo extends AbstractEntity {

  private static final long serialVersionUID = -7416671196554065701L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;
  
  @Getter
  @Setter
  @Column(name = "ieo_recruit_info_id", nullable = false)
  private Long ieoRecruitInfoId;

  @Getter
  @Setter
  @Column(name = "total_apply_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal totalApplyAmount;

  @Getter
  @Setter
  @Column(name = "total_elect_target_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal totalElectTargetAmount;
  
  @Getter
  @Setter
  @Column(name = "total_elect_win_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal totalElectWinAmount;
  
  @Getter
  @Setter
  @Column(name = "apply_limit_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal applyLimitAmount;
  
  @Getter
  @Setter
  @Column(name = "income_financial_assets_limit_price", precision = 34, scale = 20, nullable = true)
  private BigDecimal incomeFinancialAssetsLimitPrice;

  @Getter
  @Setter
  @Column(name = "has_change_elect_amount", nullable = true)
  @Enumerated(EnumType.STRING)
  private IEOHasOrHasNotChanged hasChangeElectAmount = IEOHasOrHasNotChanged.HASNOT;
  
  @Getter
  @Setter
  @Column(name = "mail_status", nullable = false)
  @Enumerated(EnumType.STRING)
  private IEOMailStatus mailStatus = IEOMailStatus.NOT_SEND;
  
  @Getter
  @Setter
  @Column(name = "comment" , nullable = true)
  private String comment;
  
  @Getter
  @Setter
  @CreatedBy
  @Column(name = "created_by")
  private String createdBy;
  
  @Getter
  @Setter
  @LastModifiedBy
  @Column(name = "updated_by")
  private String updatedBy;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;
  
  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "ieo_recruit_info_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private IEORecruitInfo ieoRecruitInfo;

  

  public IEOUserElectInfo(
      User user) {
    userId = user.getId();

  }
}