package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import exchange.common.constant.Currency;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "staking_info")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class StakingInfo extends AbstractEntity {

  private static final long serialVersionUID = -8572975693891497381L;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;
  
  @Getter
  @Setter
  @Column(name = "cb_fee_year_rate", precision = 34, scale = 20, nullable = false)
  private BigDecimal cbFeeYearRate;
  
  @Getter
  @Setter
  @Column(name = "cus_year_rate_display", precision = 34, scale = 20, nullable = false)
  private BigDecimal cusYearRateDisplay;
  
  @Getter
  @Setter
  @Column(name = "year_rate_from_chain", precision = 34, scale = 20, nullable = false)
  private BigDecimal yearRateFromChain;
  
  @Getter
  @Setter
  @Column(name = "period", nullable = false)
  private String period;
  
  @Getter
  @Setter
  @Column(name = "total_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal totalAmount;

  @Getter
  @Setter
  @Column(name = "min_apply_amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal minApplyAmount;
  
  @Getter
  @Setter
  @Column(name = "min_apply_unit", precision = 34, scale = 20, nullable = false)
  private BigDecimal minApplyUnit;

  @Getter
  @Setter
  @Column(name = "enabled", nullable = false)
  private boolean enabled = false;
  
  @Getter
  @Setter
  @Column(name = "continue_enabled", nullable = false)
  private boolean continueEnabled = false;

  @Getter
  @Setter
  @Column(name = "apply_date_from")
  @Temporal(TemporalType.TIMESTAMP)
  private Date applyDateFrom;
  
  @Getter
  @Setter
  @Column(name = "apply_date_to")
  @Temporal(TemporalType.TIMESTAMP)
  private Date applyDateTo;
  
  @Getter
  @Setter
  @Column(name = "user_limit_flg", nullable = false)
  private boolean userLimitFlg = false;
  
  @Getter
  @Setter
  @Column(name = "limit_users")
  private String limitUsers;
  
  @Getter
  @Setter
  @Column(name = "created_by")
  private String createdBy;

  @Getter
  @Setter
  @Column(name = "updated_by")
  private String updatedBy;

}
