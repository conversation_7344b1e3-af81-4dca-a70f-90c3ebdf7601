package exchange.common.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import exchange.common.constant.WithdrawalStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Entity
@Table(name = "withdrawal_audit")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class WithdrawalAudit extends AbstractEntity {

  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  private Long id;

  @Getter
  @Setter
  @Column(name = "withdrawal_id")
  private Long withdrawalId;

  @Getter
  @Setter
  @Column(name = "status", nullable = false)
  @Enumerated(EnumType.STRING)
  private WithdrawalStatus status;

  @Getter
  @Setter
  @CreatedBy
  @Column(name = "created_by")
  private String createdBy;

  @Getter
  @Setter
  @CreatedDate
  @Column(name = "created_at")
  private Date createdAt;
  
  @Getter
  @Setter
  @Column(name = "updated_at")
  private Date updatedAt;
}
