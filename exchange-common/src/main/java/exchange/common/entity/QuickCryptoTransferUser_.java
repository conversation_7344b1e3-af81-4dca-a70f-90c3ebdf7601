package exchange.common.entity;

import exchange.common.constant.TmsStatus;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(QuickCryptoTransferUser.class)
public class QuickCryptoTransferUser_ extends AbstractEntity_ {

  public static volatile SingularAttribute<QuickCryptoTransferUser, Date> targetAt;
  public static volatile SingularAttribute<QuickCryptoTransferUser, Long> userId;
  public static volatile SingularAttribute<QuickCryptoTransferUser, TmsStatus> tmsStatus;
  public static volatile SingularAttribute<QuickCryptoTransferUser, User> user;

  public static final String TARGET_AT = "targetAt";
  public static final String USER_ID = "userId";
  public static final String TMS_STATUS = "tmsStatus";
  public static final String USER = "user";
}
