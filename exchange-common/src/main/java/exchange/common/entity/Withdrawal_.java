package exchange.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;
import exchange.common.constant.WithdrawalChannel;
import exchange.common.constant.WithdrawalPurpose;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.constant.WithdrawalType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Withdrawal.class)
public abstract class Withdrawal_ extends AbstractEntity_ {

  public static volatile SingularAttribute<Withdrawal, Long> userId;
  public static volatile SingularAttribute<Withdrawal, Currency> currency;
  public static volatile SingularAttribute<Withdrawal, WithdrawalChannel> withdrawalChannel;
  public static volatile SingularAttribute<Withdrawal, WithdrawalType> withdrawalType;
  public static volatile SingularAttribute<Withdrawal, WithdrawalPurpose> withdrawalPurpose;
  public static volatile SingularAttribute<Withdrawal, BigDecimal> amount;
  public static volatile SingularAttribute<Withdrawal, BigDecimal> assetAmount;
  public static volatile SingularAttribute<Withdrawal, BigDecimal> withdrawalFee;
  public static volatile SingularAttribute<Withdrawal, BigDecimal> transactionFee;
  public static volatile SingularAttribute<Withdrawal, BigDecimal> jpyConversion;
  public static volatile SingularAttribute<Withdrawal, String> comment;
  public static volatile SingularAttribute<Withdrawal, Long> withdrawalAccountId;
  public static volatile SingularAttribute<Withdrawal, String> address;
  public static volatile SingularAttribute<Withdrawal, String> transactionId;
  public static volatile SingularAttribute<Withdrawal, WithdrawalStatus> withdrawalStatus;
  public static volatile SingularAttribute<Deposit, Integer> riskScore;
  public static volatile SingularAttribute<Deposit, Integer> failedNumber;
  public static volatile SingularAttribute<Deposit, String> transactionHash;
  public static volatile SingularAttribute<Withdrawal, User> user;
  public static volatile SingularAttribute<Withdrawal, String> createdBy;
  public static volatile SingularAttribute<Withdrawal, String> updatedBy;
  public static volatile SingularAttribute<Withdrawal, String> distinction;
  //  public static volatile SingularAttribute<Withdrawal, WithdrawalAccount> withdrawalAccount;

  public static final String USER_ID = "userId";
  public static final String CURRENCY = "currency";
  public static final String WITHDRAWAL_CHANNEL = "withdrawalChannel";
  public static final String WITHDRAWAL_TYPE = "withdrawalType";
  public static final String WITHDRAWAL_PURPOSE = "withdrawalPurpose";
  public static final String AMOUNT = "amount";
  public static final String WITHDRAWAL_FEE = "withdrawalFee";
  public static final String TRANSACTION_FEE = "transactionFee";
  public static final String JPY_CONVERSION = "jpyConversion";
  public static final String COMMENT = "comment";
  public static final String WITHDRAWAL_ACCOUNT_ID = "withdrawalAccountId";
  public static final String ADDRESS = "address";
  public static final String TRANSACTION_ID = "transactionId";
  public static final String WITHDRAWAL_STATUS = "withdrawalStatus";
  public static final String CREATED_BY = "createdBy";
  public static final String UPDATED_BY = "updatedBy";
  public static final String USER = "user";
  //  public static final String WITHDRAWAL_ACCOUNT = "withdrawalAccount";
  public static final String DISTINCTION = "distinction";
}
