package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Setter
@Getter
@Table(name = "pos_control")
public class PosControl extends AbstractEntity{

    private static final long serialVersionUID = -1420006231955824101L;
    
    @Getter
    @Setter
    @Column(name = "display_flg", nullable = false)
    private boolean displayFlg = false;
    
    @Getter
    @Setter
    @Column(name = "operator_id", nullable = false)
    private String operatorId;
}
