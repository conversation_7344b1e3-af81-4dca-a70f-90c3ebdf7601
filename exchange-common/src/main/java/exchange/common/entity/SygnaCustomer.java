package exchange.common.entity;

import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "sygna_customer")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class SygnaCustomer extends AbstractEntity {

  private static final long serialVersionUID = 1668544912839772152L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;
  
  @Getter
  @Setter
  @Column(name = "sygna_customer_id", nullable = false)
  private String sygnaCustomerId;
  
  @Getter
  @Setter
  @Column(name = "user_info_change_flg", nullable = false)
  private boolean userInfoChangeFlg = false;
  
  @Getter
  @Setter
  @OneToMany(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "sygna_customer_table_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @NotFound(action = NotFoundAction.IGNORE)
  @Fetch(FetchMode.JOIN)
  private Set<SygnaCustomerCurrency> createdCurrency;
}
