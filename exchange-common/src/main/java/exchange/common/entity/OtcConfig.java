package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import exchange.common.constant.Currency;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "otc_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class OtcConfig extends AbstractEntity {

	private static final long serialVersionUID = -344741863775650520L;

	@Getter
	@Setter
	@Column(name = "currency")
	@Enumerated(EnumType.STRING)
	private Currency currency;

	@Getter
	@Setter
	@Column(name = "address")
	private String address;

	@Getter
	@Setter
	@Column(name = "enabled", nullable = false)
	private boolean enabled = false;


}
