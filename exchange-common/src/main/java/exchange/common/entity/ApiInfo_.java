package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ApiInfo.class)
public abstract class ApiInfo_ extends AbstractEntity_ {

  public static volatile SingularAttribute<ApiInfo, String> apiKey;
  public static volatile SingularAttribute<ApiInfo, String> label;
  public static volatile SingularAttribute<ApiInfo, String> secret;
  public static volatile SingularAttribute<ApiInfo, Long> userId;
  public static volatile SingularAttribute<ApiInfo, Boolean> enabled;
  public static volatile SingularAttribute<ApiInfo, Boolean> canGetAssetInfo;
  public static volatile SingularAttribute<ApiInfo, Boolean> canGetOrderInfo;
  public static volatile SingularAttribute<ApiInfo, Boolean> canCreateNewOrder;
  public static volatile SingularAttribute<ApiInfo, Boolean> canDeleteOrder;
  public static volatile SingularAttribute<ApiInfo, Boolean> canGetTradeInfo;
  public static volatile SingularAttribute<ApiInfo, Boolean> unlimited;
  public static volatile SingularAttribute<ApiInfo, User> user;

  public static final String API_KEY = "apiKey";
  public static final String LABEL = "label";
  public static final String SECRET = "secret";
  public static final String USER_ID = "userId";
  public static final String ENABLED = "enabled";
  public static final String CAN_GET_ASSET_INFO = "canGetAssetInfo";
  public static final String CAN_GET_ORDER_INFO = "canGetOrderInfo";
  public static final String CAN_CREATE_NEW_ORDER = "canCreateNewOrder";
  public static final String CAN_DELETE_ORDER = "canDeleteOrder";
  public static final String CAN_GET_TRADE_INFO = "canGetTradeInfo";
  public static final String UNLIMITED = "unlimited";
  public static final String USER = "user";
}
