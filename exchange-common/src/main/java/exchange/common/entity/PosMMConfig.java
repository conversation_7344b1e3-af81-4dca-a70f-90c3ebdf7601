package exchange.common.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Setter
@Getter
@Table(name = "pos_market_maker_config")
public class PosMMConfig extends AbstractEntity{

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.EAGER)
    @JoinColumn(name = "symbol_id", referencedColumnName = "id",insertable = false,
            updatable = false)
    private Symbol symbol;

    private String exchange;

    private boolean enabled;

    private BigDecimal quantity;
}
