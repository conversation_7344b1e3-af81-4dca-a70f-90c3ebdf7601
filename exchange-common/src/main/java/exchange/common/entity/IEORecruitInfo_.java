package exchange.common.entity;

import java.math.BigDecimal;
import java.sql.Blob;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.IEOBoProcessStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(IEORecruitInfo.class)
public abstract class IEORecruitInfo_ extends AbstractEntity_ {

  public static volatile SingularAttribute<IEORecruitInfo, String> deal;
  public static volatile SingularAttribute<IEORecruitInfo, String> branchNameTicker;
  public static volatile SingularAttribute<IEORecruitInfo, String> branchNameKana;
  public static volatile SingularAttribute<IEORecruitInfo, String> outline;
  public static volatile SingularAttribute<IEORecruitInfo, Date> recruitDateFrom;
  public static volatile SingularAttribute<IEORecruitInfo, Date> recruitDateTo;
  public static volatile SingularAttribute<IEORecruitInfo, Date> raffleDate;
  public static volatile SingularAttribute<IEORecruitInfo, Date> quotaDateFrom;
  public static volatile SingularAttribute<IEORecruitInfo, Date> quotaDateTo;
  public static volatile SingularAttribute<IEORecruitInfo, IEOBoProcessStatus> boProcessStatus;
  public static volatile SingularAttribute<IEORecruitInfo, BigDecimal> saleAmount;
  public static volatile SingularAttribute<IEORecruitInfo, BigDecimal> unitPrice;
  public static volatile SingularAttribute<IEORecruitInfo, BigDecimal> feeRatio;
  public static volatile SingularAttribute<IEORecruitInfo, BigDecimal> shareAmount;
  public static volatile SingularAttribute<IEORecruitInfo, BigDecimal> applySharesMax;
  public static volatile SingularAttribute<IEORecruitInfo, BigDecimal> applySharesMin;

  public static final String DEAL = "deal";
  public static final String BRANCH_NAME_TICKER = "branchNameTicker";
  public static final String BRANCH_NAME_KANA = "branchNameKana";
  public static final String OUTLINE = "outline";
  public static final String RECRUIT_DATE_FROM = "recruitDateFrom";
  public static final String RECRUIT_DATE_TO = "recruitDateTo";
  public static final String RAFFLE_DATE = "raffleDate";
  public static final String QUOTA_DATE_FROM = "quotaDateFrom";
  public static final String QUOTA_DATE_TO = "quotaDateTo";
  public static final String BO_PROCESS_STATUS = "boProcessStatus";
  public static final String SALE_AMOUNT = "saleAmount";
  public static final String UNIT_PRICE = "unitPrice";
  public static final String FEE_RATIO = "feeRatio";
  public static final String SHARE_AMOUNT = "shareAmount";
  public static final String APPLY_SHARES_MAX = "applySharesMax";
  public static final String APPLY_SHARES_MIN = "applySharesMin";

}
