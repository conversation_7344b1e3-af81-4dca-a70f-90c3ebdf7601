package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "mail")
@ToString(callSuper = true, doNotUseGetters = true)
public class Mail extends AbstractEntity {

  private static final long serialVersionUID = -6722377105797603952L;

  @Getter
  @Setter
  @Column(name = "mail_template_id", nullable = false)
  private Long mailTemplateId;
  
  @Getter
  @Setter
  @Column(name = "title", nullable = false)
  private String title;

  @Getter
  @Setter
  @Column(name = "contents")
  private String contents;
  
  @Getter
  @Setter
  @Column(name = "send", nullable = false)
  private boolean send = true;
}
