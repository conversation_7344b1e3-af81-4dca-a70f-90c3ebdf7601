package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import exchange.common.constant.Currency;
import exchange.common.constant.TmsStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "excessive_deposit_user_by_one_time")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ExcessiveDepositUserByOneTime extends AbstractEntity {

  private static final long serialVersionUID = -1742465857593293667L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "email", nullable = false)
  private String email;
  
  @Getter
  @Setter
  @Column(name = "currency")
  private String currency;

  @Getter
  @Setter
  @Column(name = "amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal amount;

  @Getter
  @Setter
  @Column(name = "financial_assets", nullable = false)
  private Integer financialAssets;

  @Getter
  @Setter
  @Column(name = "income", nullable = false)
  private Integer income;

  @Getter
  @Setter
  @Column(name = "tms_status")
  @Enumerated(EnumType.STRING)
  private TmsStatus tmsStatus;

  @Getter
  @Setter
  @Column(name = "target_at", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date targetAt;
}
