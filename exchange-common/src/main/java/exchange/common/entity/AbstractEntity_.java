package exchange.common.entity;

import java.util.Date;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AbstractEntity.class)
public abstract class AbstractEntity_ {

  public static volatile SingularAttribute<AbstractEntity, Long> id;
  public static volatile SingularAttribute<AbstractEntity, Date> createdAt;
  public static volatile SingularAttribute<AbstractEntity, Date> updatedAt;

  public static final String ID = "id";
  public static final String CREATED_AT = "createdAt";
  public static final String UPDATED_AT = "updatedAt";

}

