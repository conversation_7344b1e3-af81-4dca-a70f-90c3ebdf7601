package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;
import exchange.common.constant.StakingStatus;
import exchange.common.constant.StakingWalletStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(StakingApplyDetail.class)
public abstract class StakingApplyDetail_ extends AbstractEntity_ {

  public static volatile SingularAttribute<StakingApplyDetail, Long> userId;
  public static volatile SingularAttribute<StakingApplyDetail, Long> stakingInfoId;
  public static volatile SingularAttribute<StakingApplyDetail, Currency> currency;
  public static volatile SingularAttribute<StakingApplyDetail, Date> applyDate;
  public static volatile SingularAttribute<StakingApplyDetail, BigDecimal> applyAmount;
  public static volatile SingularAttribute<StakingApplyDetail, BigDecimal> yearRate;
  public static volatile SingularAttribute<StakingApplyDetail, StakingStatus> stakingStatus;
  public static volatile SingularAttribute<StakingApplyDetail, StakingWalletStatus> walletStatus;
  public static volatile SingularAttribute<StakingApplyDetail, BigDecimal> rewardAccumulate;
  public static volatile SingularAttribute<StakingApplyDetail, Date> expirationDate;
  public static volatile SingularAttribute<StakingApplyDetail, Date> stakingDatePlan;
  public static volatile SingularAttribute<StakingApplyDetail, Date> cancelApplyDate;
  public static volatile SingularAttribute<StakingApplyDetail, Date> cancelDate;
  public static volatile SingularAttribute<StakingApplyDetail, BigDecimal> amountToAccount;
  public static volatile SingularAttribute<StakingApplyDetail, Date> amountToAccountDate;
  public static volatile SingularAttribute<StakingApplyDetail, Boolean> autoContinue;
  public static volatile SingularAttribute<StakingApplyDetail, Long> stakingControlId;
  public static volatile SingularAttribute<StakingApplyDetail, Long> relationId;
  public static volatile SingularAttribute<StakingApplyDetail, Date> rewardStartDate;

  public static final String USER_ID = "userId";
  public static final String STAKING_INFO_ID = "stakingInfoId";
  public static final String APPLY_DATE = "applyDate";
  public static final String APPLY_AMOUNT = "applyAmount";
  public static final String YEAR_RATE = "yearRate";
  public static final String STAKING_STATUS = "stakingStatus";
  public static final String WALLET_STATUS = "walletStatus";
  public static final String REWARD_ACCUMULATE = "rewardAccumulate";
  public static final String EXPIRATION_DATE = "expirationDate";
  public static final String STAKING_DATE_PLAN = "stakingDatePlan";
  public static final String CANCEL_APPLY_DATE = "cancelApplyDate";
  public static final String CANCEL_DATE = "cancelDate";
  public static final String AMOUNT_TO_ACCOUNT = "amountToAccount";
  public static final String AMOUNT_TO_ACCOUNT_DATE = "amountToAccountDate";

}

