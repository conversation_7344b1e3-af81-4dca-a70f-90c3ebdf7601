package exchange.common.entity.cryptoToken;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.CryptoToken;
import exchange.common.entity.AbstractEntity;
import exchange.common.entity.User;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.*;
import java.io.Serial;
import java.math.BigDecimal;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Entity
@NoArgsConstructor
@Table(name = "crypto_token_asset")
@ToString(callSuper = true, doNotUseGetters = true)
public class CryptoTokenAsset extends AbstractEntity {

	@Serial
	private static final long serialVersionUID = 3212036453001532921L;

	@Getter
	@Setter
	@Column(name = "user_id", nullable = false)
	private Long userId;

	@Setter
	@Getter
	@Column(name = "crypto_token")
	@Enumerated(EnumType.STRING)
	private CryptoToken cryptoToken;

	@Getter
	@Setter
	@Column(name = "onhand_amount", precision = 34, scale = 20, nullable = false)
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal onhandAmount;

	@Getter
	@Setter
	@ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
	@JoinColumn(
			name = "user_id",
			referencedColumnName = "id",
			foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
			insertable = false,
			updatable = false)
	@Fetch(FetchMode.JOIN)
	private User user;

	@Getter
	@Setter
	@ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
	@JoinColumn(
			name = "crypto_token",
			referencedColumnName = "crypto_token",
			foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
			insertable = false,
			updatable = false)
	@Fetch(FetchMode.JOIN)
	private CryptoTokenConfig cryptoTokenConfig;
}
