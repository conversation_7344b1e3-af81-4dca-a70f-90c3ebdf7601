package exchange.common.entity.cryptoToken;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.CryptoToken;
import exchange.common.constant.CryptoTokenApplyStatus;
import exchange.common.entity.AbstractEntity;
import exchange.common.entity.User;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.*;
import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Entity
@NoArgsConstructor
@Table(name = "crypto_token_apply")
@ToString(callSuper = true, doNotUseGetters = true)
public class CryptoTokenApply extends AbstractEntity {

	public static final Integer DATA_SOURCE_TYPE_USER_APPLY = 1;

	public static final Integer DATA_SOURCE_TYPE_GIVE_GEN = 2;

	@Serial
	private static final long serialVersionUID = -7204101529400642185L;

	@Getter
	@Setter
	@Column(name = "user_id", nullable = false)
	private Long userId;

	@Setter
	@Getter
	@Column(name = "crypto_token")
	@Enumerated(EnumType.STRING)
	private CryptoToken cryptoToken;

	@Getter
	@Setter
	@Column(name = "apply_status", nullable = false)
	@Enumerated(EnumType.STRING)
	private CryptoTokenApplyStatus applyStatus;

	@Getter
	@Setter
	@Column(name = "apply_time", nullable = false)
	@Temporal(TemporalType.TIMESTAMP)
	private Date applyTime;

	@Getter
	@Setter
	@Column(name = "assigned_quantity", precision = 34, scale = 20, nullable = true)
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal assignedQuantity;

	@Getter
	@Setter
	@Column(name = "approved_time", nullable = true)
	@Temporal(TemporalType.TIMESTAMP)
	private Date approvedTime;

	@Getter
	@Setter
	@Column(name = "data_source_type", nullable = false)
	private Integer dataSourceType;

	@Getter
	@Setter
	@ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
	@JoinColumn(
			name = "user_id",
			referencedColumnName = "id",
			foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
			insertable = false,
			updatable = false)
	@Fetch(FetchMode.JOIN)
	private User user;

	@Getter
	@Setter
	@ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
	@JoinColumn(
			name = "crypto_token",
			referencedColumnName = "crypto_token",
			foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
			insertable = false,
			updatable = false)
	@Fetch(FetchMode.JOIN)
	private CryptoTokenConfig cryptoTokenConfig;
}
