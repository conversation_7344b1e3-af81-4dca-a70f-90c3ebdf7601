package exchange.common.entity.cryptoToken;

import exchange.common.constant.CryptoToken;
import exchange.common.entity.AbstractEntity_;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/10/10
 */
@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CryptoTokenConfig.class)
public class CryptoTokenConfig_ extends AbstractEntity_ {

	public static volatile SingularAttribute<CryptoTokenConfig, CryptoToken> cryptoToken;
	public static volatile SingularAttribute<CryptoTokenConfig, Date> openApplyEndTime;

	public static volatile SingularAttribute<CryptoTokenConfig, Boolean> enabled;

	public static final String CRYPTO_TOKEN = "cryptoToken";
	private static final String OPEN_APPLY_END_TIME = "openApplyEndTime";
	public static final String ENABLED = "enabled";
}
