package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(SygnaDepositTransfer.class)
public abstract class SygnaDepositTransfer_ extends AbstractEntity_ {

  public static volatile SingularAttribute<SygnaDepositTransfer, Long> userId;
  public static volatile SingularAttribute<SygnaDepositTransfer, String> sygnaTxId;
  public static volatile SingularAttribute<SygnaDepositTransfer, Integer> status;
  public static volatile SingularAttribute<SygnaDepositTransfer, DepositStatus> depositStatus;
  public static volatile SingularAttribute<SygnaDepositTransfer, String> addrFrom;
  public static volatile SingularAttribute<SygnaDepositTransfer, String> addrTo;
  public static volatile SingularAttribute<SygnaDepositTransfer, Currency> currency;
  public static volatile SingularAttribute<SygnaDepositTransfer, BigDecimal> amount;
  public static volatile SingularAttribute<SygnaDepositTransfer, String> txHash;
  public static volatile SingularAttribute<SygnaDepositTransfer, Date> sygnaCreatedTime;

  public static final String USER_ID = "userId";
  public static final String SYGNA_TX_ID = "sygnaTxId";
  public static final String STATUS = "status";
  public static final String DEPOSIT_STATUS = "depositStatus";
  public static final String ADDR_FROM = "addrFrom";
  public static final String ADDR_TO = "addrTo";
  public static final String CURRENCY = "currency";
  public static final String AMOUNT = "amount";
  public static final String TX_HASH = "txHash";
  public static final String SYGNA_CREATED_TIME = "sygnaCreatedTime";
}
