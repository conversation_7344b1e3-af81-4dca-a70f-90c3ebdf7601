package exchange.common.entity;

import java.util.Date;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import com.fasterxml.jackson.annotation.JsonIgnore;
import exchange.common.util.SignatureUtil;
import exchange.common.util.SignatureUtil.Algorithm;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "api_info")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ApiInfo extends AbstractEntity {

  private static final long serialVersionUID = 9053986342292109823L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "label", nullable = false)
  private String label;

  @Getter
  @Setter
  @Column(name = "api_key", nullable = false)
  private String apiKey;

  @Getter
  @Setter
  @Column(name = "secret", nullable = false)
  @JsonIgnore
  private String secret;

  @Getter
  @Setter
  @Column(name = "enabled", nullable = false)
  private boolean enabled = true;

  @Getter
  @Setter
  @Column(name = "can_get_asset_info", nullable = false)
  private boolean canGetAssetInfo = false;

  @Getter
  @Setter
  @Column(name = "can_get_order_info", nullable = false)
  private boolean canGetOrderInfo = false;

  @Getter
  @Setter
  @Column(name = "can_create_new_order", nullable = false)
  private boolean canCreateNewOrder = false;

  @Getter
  @Setter
  @Column(name = "can_delete_order", nullable = false)
  private boolean canDeleteOrder = false;

  @Getter
  @Setter
  @Column(name = "can_get_trade_info", nullable = false)
  private boolean canGetTradeInfo = false;

  @Getter
  @Setter
  @Column(name = "unlimited", nullable = false)
  private boolean unlimited = false;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;

  public ApiInfo(
      Long userId,
      String label,
      boolean canGetAssetInfo,
      boolean canGetOrderInfo,
      boolean canCreateNewOrder,
      boolean canDeleteOrder,
      boolean canGetTradeInfo) {
    this.userId = userId;
    this.label = label;
    this.canGetAssetInfo = canGetAssetInfo;
    this.canGetOrderInfo = canGetOrderInfo;
    this.canCreateNewOrder = canCreateNewOrder;
    this.canDeleteOrder = canDeleteOrder;
    this.canGetTradeInfo = canGetTradeInfo;
    this.apiKey = SignatureUtil.getHash(Algorithm.MD5, userId.toString() + new Date().getTime());
    this.secret = SignatureUtil.getHash(Algorithm.SHA256, apiKey + new Date().getTime());
  }
}
