package exchange.common.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginAttempt {

  @Getter @Setter private Long userId;

  @Getter @Setter private Long attempt = 1L;

  @Getter @Setter private Long timestamp;

  public LoginAttempt(Long userId) {
    this.userId = userId;
    this.attempt = 1L;
    this.timestamp = new Date().getTime();
  }
}
