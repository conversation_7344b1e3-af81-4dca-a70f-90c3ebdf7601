package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AffiliateInfo.class)
public abstract class AffiliateInfo_ extends AbstractEntity_ {

  public static volatile SingularAttribute<AffiliateInfo, String> affiliateName;
  public static volatile SingularAttribute<AffiliateInfo, String> identify;

  public static final String AFFILIATE_NAME = "affiliateName";
  public static final String IDENTIFY = "identify";
}
