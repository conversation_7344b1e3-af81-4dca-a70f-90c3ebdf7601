package exchange.common.entity.dowjones;

import exchange.common.constant.AntiSocialCheckBusinessTypeEnum;
import exchange.common.constant.DowJonesApiTypeEnum;
import exchange.common.constant.UserTypeEnum;
import exchange.common.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serial;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Entity
@NoArgsConstructor
@Table(name = "dow_jones_antisocial_check")
@ToString(callSuper = true, doNotUseGetters = true)
public class DowJonesAntisocialCheck extends AbstractEntity {

	@Serial
	private static final long serialVersionUID = -8086067128326575307L;

	@Getter
	@Setter
	@Column(name = "user_id")
	private Long userId;

	/**
	 * {@link UserTypeEnum}
	 */
	@Getter
	@Setter
	@Column(name = "user_type")
	private Integer userType;

	/**
	 * {@link DowJonesApiTypeEnum}
	 */
	@Getter
	@Setter
	@Column(name = "api_type")
	private Integer apiType;

	/**
	 * {@link AntiSocialCheckBusinessTypeEnum}
	 */
	@Getter
	@Setter
	@Column(name = "business_type")
	private Integer businessType;

	@Getter
	@Setter
	@Column(name = "business_id")
	private String businessId;

	@Getter
	@Setter
	@Column(name = "user_info_id")
	private Long userInfoId;

	@Getter
	@Setter
	@Column(name = "user_info_corporate_id")
	private Long userInfoCorporateId;

	@Getter
	@Setter
	@Column(name = "user_relation_data_info_list")
	private String userRelationDataInfoList;

	@Getter
	@Setter
	@Column(name = "case_id")
	private Long caseId;

	@Getter
	@Setter
	@Column(name = "has_risk")
	private Boolean hasRisk;

	@Getter
	@Setter
	@Column(name = "start_time")
	private Date startTime;

	@Getter
	@Setter
	@Column(name = "end_time")
	private Date endTime;

	@Getter
	@Setter
	@Column(name = "duration_millis")
	private Date durationMillis;

	@Getter
	@Setter
	@Column(name = "trace_id")
	private String traceId;

	@Getter
	@Setter
	@Column(name = "error_reason")
	private String errorReason;

	@Getter
	@Setter
	@Column(name = "error_msg")
	private String errorMsg;

	@Getter
	@Setter
	@Column(name = "error_stack_trace")
	private String errorStackTrace;
}
