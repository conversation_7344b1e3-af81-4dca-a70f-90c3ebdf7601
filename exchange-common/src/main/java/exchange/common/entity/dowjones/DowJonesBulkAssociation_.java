package exchange.common.entity.dowjones;


import exchange.common.entity.AbstractEntity_;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(DowJonesBulkAssociation.class)
public class DowJonesBulkAssociation_ extends AbstractEntity_ {

    public static volatile SingularAttribute<DowJonesBulkAssociation, String> dowTransactionId;
    public static volatile SingularAttribute<DowJonesBulkAssociation, String> type;
    public static volatile SingularAttribute<DowJonesBulkAssociation, String> status;
    public static volatile SingularAttribute<DowJonesBulkAssociation, String> dowCaseId;
    public static volatile SingularAttribute<DowJonesBulkAssociation, Integer> validAssociations;
    public static volatile SingularAttribute<DowJonesBulkAssociation, Integer> invalidAssociations;
    public static volatile SingularAttribute<DowJonesBulkAssociation, Integer> pendingAssociations;

}