package exchange.common.entity.dowjones;

import exchange.common.entity.AbstractEntity_;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(DowJonesSamCase.class)
public class DowJonesSamCase_ extends AbstractEntity_ {
	public static volatile SingularAttribute<DowJonesSamCase, String> dowJonesSamCaseId;
	public static volatile SingularAttribute<DowJonesSamCase, String> dowJonesSamCaseName;
	public static volatile SingularAttribute<DowJonesSamCase, String> dowJonesSamExternalId;
	public static volatile SingularAttribute<DowJonesSamCase, Long> userId;
	public static volatile SingularAttribute<DowJonesSamCase, String> traceId;
	public static volatile SingularAttribute<DowJonesSamCase, Boolean> enabled;
	public static volatile SingularAttribute<DowJonesSamCase, Long> associationOccupyCount;
	public static volatile SingularAttribute<DowJonesSamCase, Boolean> active;
	public static volatile SingularAttribute<DowJonesSamCase, Long> userType;

	public static final String DOW_JONES_SAM_CASE_ID = "dowJonesSamCaseId";
	public static final String DOW_JONES_SAM_CASE_NAME = "dowJonesSamCaseName";
	public static final String DOW_JONES_SAM_EXTERNAL_ID = "dowJonesSamExternalId";
	public static final String USER_ID = "userId";
	public static final String ENABLED = "enabled";
	public static final String ASSOCIATION_OCCUPY_COUNT = "associationOccupyCount";
	public static final String ACTIVE = "active";
	public static final String USER_TYPE = "userType";

}
