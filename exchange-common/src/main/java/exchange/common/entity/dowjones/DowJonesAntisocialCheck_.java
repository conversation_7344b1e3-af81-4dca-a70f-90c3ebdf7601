package exchange.common.entity.dowjones;

import exchange.common.entity.AbstractEntity_;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(DowJonesAntisocialCheck.class)
public class DowJonesAntisocialCheck_ extends AbstractEntity_ {
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Long> userId;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Integer> userType;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Integer> apiType;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Long> userInfoId;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Long> userInfoCorporateId;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, String> userRelationDataInfoList;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Long> caseId;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, Boolean> hasRisk;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, String> traceId;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, String> errorReason;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, String> errorMsg;
	public static volatile SingularAttribute<DowJonesAntisocialCheck, String> errorStackTrace;

	public static final String USER_ID = "userId";
	public static final String USER_TYPE = "userType";
	public static final String API_TYPE = "apiType";
	public static final String USER_INFO_ID = "userInfoId";
	public static final String USER_INFO_CORPORATE_ID = "userInfoCorporateId";
	public static final String USER_RELATION_DATA_INFO_LIST = "userRelationDataInfoList";
	public static final String CASE_ID = "caseId";
	public static final String HAS_RISK = "hasRisk";
	public static final String TRACE_ID = "traceId";
	public static final String ERROR_REASON = "errorReason";
	public static final String ERROR_MSG = "errorMsg";

}
