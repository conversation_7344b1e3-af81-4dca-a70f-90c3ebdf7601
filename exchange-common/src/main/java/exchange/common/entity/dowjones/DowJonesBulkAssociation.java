package exchange.common.entity.dowjones;


import exchange.common.entity.AbstractEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@NoArgsConstructor
@Table(name = "dow_jones_bulk_association")
@ToString(callSuper = true, doNotUseGetters = true)
@Data
public class DowJonesBulkAssociation  extends AbstractEntity {

    @Column(name = "dow_transaction_id")
    private String dowTransactionId;

    @Column(name = "type")
    private String type;

    @Column(name = "status")
    private String status;

    @Column(name = "dow_case_id")
    private String dowCaseId;

    @Column(name = "valid_associations")
    private int validAssociations;

    @Column(name = "invalid_associations")
    private int invalidAssociations;

    @Column(name = "pending_associations")
    private int pendingAssociations;

}