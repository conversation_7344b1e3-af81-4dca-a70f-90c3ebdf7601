package exchange.common.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import exchange.common.constant.AccountType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "bank_account")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class BankAccount extends AbstractEntity {

  private static final long serialVersionUID = -8572975693891497381L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "bank_id", nullable = false)
  private Long bankId;

  @Getter
  @Setter
  @Column(name = "account_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private AccountType accountType;

  @Getter
  @Setter
  @Column(name = "account_number", nullable = false)
  private String accountNumber;

  @Getter
  @Setter
  @Column(name = "account_name", nullable = false)
  private String accountName;

  @Getter
  @Setter
  @Column(name = "deleted", nullable = false)
  private boolean deleted = false;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "bank_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private Bank bank;

  public BankAccount(User user, Bank bank) {
    userId = user.getId();
    bankId = bank.getId();
  }
}
