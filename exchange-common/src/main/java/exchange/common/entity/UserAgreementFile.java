package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import exchange.common.constant.UserAgreementType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_agreement_file")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserAgreementFile extends AbstractEntity {

  private static final long serialVersionUID = -4759634693167070434L;

  @Getter
  @Setter
  @Column(name = "user_agreement_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private UserAgreementType userAgreementType;

  @Getter
  @Setter
  @Column(name = "version", nullable = true)
  private String version;

  public UserAgreementFile(UserAgreementType userAgreementType, String version) {
    this.userAgreementType = userAgreementType;
    this.version = version;
  }
}


