package exchange.common.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import exchange.common.constant.Exchange;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "balance_notify_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class BalanceNotifyConfig extends AbstractEntity {

  private static final long serialVersionUID = -244741863775650520L;

  @Getter
  @Setter
  @Column(name = "currency")
  private String currency;
  
  @Getter
  @Setter
  @Column(name = "user_id")
  private Long userId;
  
  @Getter
  @Setter
  @Column(name = "default_balance", precision = 34, scale = 20, nullable = false)
  private BigDecimal defaultBalance = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "limit_balance_percent", precision = 34, scale = 20, nullable = false)
  private BigDecimal limitBalancePercent = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "target_pos", nullable = false)
  @Enumerated(EnumType.STRING)
  private Exchange targetPos;

  @Getter
  @Setter
  @Column(name = "mail_to", nullable = false)
  private String mailTo;
  
  @Getter
  @Setter
  @Column(name = "enabled", nullable = false)
  private boolean enabled = false;
}
