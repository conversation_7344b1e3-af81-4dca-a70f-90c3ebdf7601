package exchange.common.entity;

import java.util.Date;
import javax.annotation.processing.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;
import exchange.common.constant.TmsStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ExcessiveDepositByPeriod.class)
public abstract class ExcessiveDepositByPeriod_ extends AbstractEntity_ {

  public static volatile SingularAttribute<ExcessiveDepositByPeriod, Currency> currency;
  public static volatile SingularAttribute<ExcessiveDepositByPeriod, Long> userId;
  public static volatile SingularAttribute<ExcessiveDepositByPeriod, String> email;
  public static volatile SingularAttribute<ExcessiveDepositByPeriod, Integer> income;
  public static volatile SingularAttribute<ExcessiveDepositByPeriod, Integer> financialAssets;
  public static volatile SingularAttribute<ExcessiveDepositByPeriod, TmsStatus> tmsStatus;
  public static volatile SingularAttribute<ExcessiveDepositByPeriod, Date> targetAt;
  
  public static final String CURRENCY = "currency";
  public static final String USER_ID = "userId";
  public static final String EMAIL = "email";
  public static final String INCOME = "income";
  public static final String FINANCIAL_ASSETS = "financialAssets";
  public static final String TMS_STATUS = "tmsStatus";
  public static final String TARGET_AT = "targetAt";
}
