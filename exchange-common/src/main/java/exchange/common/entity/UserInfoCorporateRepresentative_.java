package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Country;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserInfoCorporateRepresentative.class)
public abstract class UserInfoCorporateRepresentative_ extends AbstractEntity_ {
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, Long> userId;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> firstName;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> lastName;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> firstKana;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> lastKana;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> position;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> nationality;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> zipCode;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> prefecture;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> city;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> address;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> building;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, String> birthday;
  public static volatile SingularAttribute<UserInfoCorporateRepresentative, Integer> gender;
  public static volatile SingularAttribute<UserInfoCorporate, Country> country;
  public static volatile SingularAttribute<UserInfoCorporate, String> phoneNumber;

  public static final String USER_ID = "userId";
  public static final String FIRST_NAME = "firstName";
  public static final String LAST_NAME = "lastName";
  public static final String FIRST_KANA = "firstKana";
  public static final String LAST_KANA = "lastKana";
  public static final String POSITION = "position";
  public static final String NATIONALITY = "nationality";
  public static final String ZIP_CODE = "zipCode";
  public static final String PREFECTURE = "prefecture";
  public static final String CITY = "city";
  public static final String ADDRESS = "address";
  public static final String BUILDING = "building";
  public static final String BIRTHDAY = "birthday";
  public static final String GENDER = "gender";
  public static final String COUNTRY = "country";
  public static final String PHONE_NUMBER = "phoneNumber";
}
