package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "zip_code")
public class ZipCode extends  AbstractEntity{

    @Column(name = "code")
    private String code ;

    @Column(name = "old_zip_code")
    private String oldZipCode ;

    @Column(name = "zip_code")
    private String zipCode ;

    @Column(name = "prefectures")
    private String prefectures ;

    @Column(name = "prefectures_kana")
    private String prefecturesKana ;

    @Column(name = "city")
    private String city ;

    @Column(name = "city_kana")
    private String cityKana ;

    @Column(name = "town_area")
    private String townArea ;

    @Column(name = "town_area_kana")
    private String townAreaKana ;

    @Column(name = "has_another_area")
    private Boolean hasAnotherArea ;

    @Column(name = "has_sub_code")
    private Boolean hasSubCode ;

    @Column(name = "has_area_number")
    private Boolean hasAreaNumber ;

    @Column(name = "has_same_zip_code_area")
    private Boolean hasSameZipCodeArea ;

    @Column(name = "update_type")
    private Integer updateType ;

    @Column(name = "update_reason")
    private Integer updateReason ;

    @Column(name = "version")
    private Integer version ;
}
