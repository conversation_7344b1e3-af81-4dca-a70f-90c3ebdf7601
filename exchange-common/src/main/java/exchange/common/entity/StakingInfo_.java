package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(StakingInfo.class)
public abstract class StakingInfo_ extends AbstractEntity_ {

  public static volatile SingularAttribute<StakingInfo, Currency> currency;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> yearRate;
  public static volatile SingularAttribute<StakingInfo, String> period;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> totalAmount;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> minApplyAmount;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> maxApplyAmount;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> cbFeeYearRate;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> cusYearRateDisplay;
  public static volatile SingularAttribute<StakingInfo, BigDecimal> yearRateFromChain;
  public static volatile SingularAttribute<StakingInfo, Boolean> enabled;
  public static volatile SingularAttribute<StakingInfo, Date> applyDateFrom;
  public static volatile SingularAttribute<StakingInfo, Date> applyDateTo;

  public static final String Currency = "currency";
  public static final String YEAR_RATE = "yearRate";
  public static final String PERIOD = "period";
  public static final String TOTAL_AMOUNT = "totalAmount";
  public static final String MIN_APPLY_AMOUNT = "minApplyAmount";
  public static final String MAX_APPLY_AMOUNT = "maxApplyAmount";
  public static final String CB_FEE_YEAR_RATE = "cbFeeYearRate";
  public static final String CUS_YEAR_RATE_DISPLAY = "cusYearRateDisplay";
  public static final String YEAR_RATE_FROM_CHAIN = "yearRateFromChain";
  public static final String ENABLED = "enabled";
  public static final String APPLY_DATE_FROM = "applyDateFrom";
  public static final String APPLY_DATE_TO = "applyDateTo";

}

