package exchange.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Exchange;
import exchange.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CoverOrderConfig.class)
public abstract class CoverOrderConfig_ extends AbstractEntity_ {

  public static volatile SingularAttribute<CoverOrderConfig, Long> symbolId;
  public static volatile SingularAttribute<CoverOrderConfig, Exchange> exchange;
  public static volatile SingularAttribute<CoverOrderConfig, TradeType> tradeType;
  public static volatile SingularAttribute<CoverOrderConfig, BigDecimal> rangePricePercent;
  public static volatile SingularAttribute<CoverOrderConfig, BigDecimal> minOrderAmount;
  public static volatile SingularAttribute<CoverOrderConfig, Boolean> enabled;

  public static final String SYMBOL_ID = "symbolId";
  public static final String EXCHANGE = "exchange";
  public static final String RANGE_PRICE_PERCENT = "rangePricePercent";
  public static final String MIN_ORDER_AMOUNT = "minOrderAmount";
  public static final String ENABLED = "enabled";
  public static final String TRADE_TYPE = "tradeType";
}
