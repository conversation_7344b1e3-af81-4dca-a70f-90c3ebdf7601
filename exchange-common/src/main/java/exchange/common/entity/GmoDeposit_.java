package exchange.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(GmoDeposit.class)
public class GmoDeposit_ extends AbstractEntity_ {

  public static volatile SingularAttribute<GmoDeposit, String> itemKey;
  public static volatile SingularAttribute<GmoDeposit, Long> fiatDepositId;
  public static volatile SingularAttribute<GmoDeposit, Date> transactionDate;
  public static volatile SingularAttribute<GmoDeposit, String> vaAccountNameKana;
  public static volatile SingularAttribute<GmoDeposit, String> remitterNameKana;
  public static volatile SingularAttribute<GmoDeposit, String> paymentBankName;
  public static volatile SingularAttribute<GmoDeposit, String> paymentBranchName;
  public static volatile SingularAttribute<GmoDeposit, String> partnerName;
  public static volatile SingularAttribute<GmoDeposit, String> remarks;
  public static volatile SingularAttribute<GmoDeposit, Long> depositAmount;
  public static volatile SingularAttribute<GmoDeposit, String> vaId;
  public static volatile SingularAttribute<GmoDeposit, FiatDeposit> fiatDeposit;

  public static final String ITEM_KEY = "itemKey";
  public static final String FIAT_DEPOSIT_ID = "fiatDepositId";
  public static final String TRANSACTION_DATE = "transactionDate";
  public static final String VA_ACCOUNT_NAME_KANA = "vaAccountNameKana";
  public static final String REMITTER_NAME_KANA = "remitterNameKana";
  public static final String PAYMENT_BANK_NAME = "paymentBankName";
  public static final String PAYMENT_BRANCH_NAME = "paymentBranchName";
  public static final String PARTNER_NAME = "partnerName";
  public static final String REMARKS = "remarks";
  public static final String DEPOSIT_AMOUNT = "depositAmount";
  public static final String VA_ID = "vaId";
  public static final String FIAT_DEPOSIT = "fiatDeposit";
}
