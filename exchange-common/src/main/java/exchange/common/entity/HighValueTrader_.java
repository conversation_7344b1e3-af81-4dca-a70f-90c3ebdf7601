package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(HighValueTrader.class)
public abstract class HighValueTrader_ extends AbstractEntity_ {

  public static volatile SingularAttribute<HighValueTrader, Integer> spotOrderLimits;
  public static volatile SingularAttribute<HighValueTrader, Integer> spotTradeMarkets;
  public static volatile SingularAttribute<HighValueTrader, Long> userId;
  public static volatile SingularAttribute<HighValueTrader, User> user;
  public static volatile SingularAttribute<HighValueTrader, TradeType> tradeType;
  public static volatile SingularAttribute<HighValueTrader, CurrencyPair> currencyPair;

  public static final String SPOT_ORDER_LIMITS = "spotOrderLimits";
  public static final String SPOT_TRADE_MARKETS = "spotTradeMarkets";
  public static final String USER_ID = "userId";
  public static final String USER = "user";
  public static final String TRADE_TYPE = "tradeType";
  public static final String CURRENCY_PAIR = "currencyPair";
}
