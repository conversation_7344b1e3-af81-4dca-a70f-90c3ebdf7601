package exchange.common.entity;

import java.math.BigDecimal;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Order.class)
public abstract class Order_ extends AbstractEntity_ {

  public static volatile SingularAttribute<Order, Long> symbolId;
  public static volatile SingularAttribute<Order, Long> userId;
  public static volatile SingularAttribute<Order, BigDecimal> amount;

  public static final String SYMBOL_ID = "symbolId";
  public static final String USER_ID = "userId";
  public static final String AMOUNT = "amount";

}

