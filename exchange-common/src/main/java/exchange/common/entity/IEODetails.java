package exchange.common.entity;

import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import exchange.common.constant.IEOChannel;
import exchange.common.constant.IEODetailsStatus;
import exchange.common.constant.IEOOperation;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "ieo_details")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class IEODetails extends AbstractEntity {

  private static final long serialVersionUID = -7416671196554065701L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;
  
  @Getter
  @Setter
  @Column(name = "ieo_recruit_info_id", nullable = false)
  private Long ieoRecruitInfoId;

  @Getter
  @Setter
  @Column(name = "apply_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal applyAmount;

  @Getter
  @Setter
  @Column(name = "win_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal winAmount;

  @Getter
  @Setter
  @Column(name = "ieo_detail_status", nullable = true)
  @Enumerated(EnumType.STRING)
  private IEODetailsStatus ieoDetailStatus;
  
  @Getter
  @Setter
  @Column(name = "ieo_channel", nullable = false)
  @Enumerated(EnumType.STRING)
  private IEOChannel ieoChannel = IEOChannel.UNKNOWN;
  
  @Getter
  @Setter
  @Column(name = "operation", nullable = false)
  @Enumerated(EnumType.STRING)
  private IEOOperation operation = IEOOperation.NEW;
  
  @Getter
  @Setter
  @Column(name = "comment" , nullable = true)
  private String comment;
  
  @Getter
  @Setter
  @CreatedBy
  @Column(name = "created_by")
  private String createdBy;
  
  @Getter
  @Setter
  @LastModifiedBy
  @Column(name = "updated_by")
  private String updatedBy;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;
  
  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "ieo_recruit_info_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private IEORecruitInfo ieoRecruitInfo;

  

  public IEODetails(
      User user,
      BigDecimal applyAmount,
      BigDecimal winAmount) {
    userId = user.getId();
    this.applyAmount = applyAmount;
    this.winAmount = winAmount;
  }
}
