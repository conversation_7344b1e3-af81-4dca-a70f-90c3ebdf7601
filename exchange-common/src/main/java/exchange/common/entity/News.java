package exchange.common.entity;

import java.sql.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import exchange.common.constant.NewsType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "news")
@ToString(callSuper = true, doNotUseGetters = true)
public class News extends AbstractEntity {

  private static final long serialVersionUID = -1318929503160277144L;
  
  @Getter
  @Setter
  @Column(name = "news_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private NewsType newsType;

  @Getter
  @Setter
  @Column(name = "title", nullable = false)
  private String title;

  @Getter
  @Setter
  @Column(name = "contents")
  private String contents;
  
  @Getter
  @Setter
  @Column(name = "link")
  private String link;
  
  @Getter
  @Setter
  @Column(name = "date")
  private Date date;
  
  @Getter
  @Setter
  @Column(name = "enabled", nullable = false)
  private boolean enabled = true;

}
