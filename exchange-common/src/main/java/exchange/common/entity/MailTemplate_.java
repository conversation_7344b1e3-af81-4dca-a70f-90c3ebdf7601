package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MailTemplate.class)
public abstract class MailTemplate_ extends AbstractEntity_ {
  public static volatile SingularAttribute<MailTemplate, String> title;
  public static volatile SingularAttribute<MailTemplate, String> header;
  public static volatile SingularAttribute<MailTemplate, String> footer;
  public static final String TITLE = "title";
  public static final String HEADER = "header";
  public static final String FOOTER = "footer";
}
