package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Deposit.class)
public abstract class SygnaCustomerCurrency_ extends AbstractEntity_ {

  public static volatile SingularAttribute<SygnaCustomerCurrency, Long> sygnaCustomerTableId;
  public static volatile SingularAttribute<SygnaCustomerCurrency, String> createdCurrency;

  public static final String SYGNA_CUSTOMER_TABLE_ID = "sygnaCustomerTableId";
  public static final String CREATED_CURRENCY = "createdCurrency";
}
