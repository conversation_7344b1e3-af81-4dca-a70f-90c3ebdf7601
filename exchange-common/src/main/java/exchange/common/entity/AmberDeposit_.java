package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Deposit.class)
public abstract class AmberDeposit_ extends AbstractEntity_ {

  public static volatile SingularAttribute<AmberDeposit, String> transactionId;
  public static volatile SingularAttribute<AmberDeposit, Integer> transactionStatus;

  public static final String TRANSACTION_ID = "transactionId";
  public static final String TRANSACTION_STATUS = "transactionStatus";
}
