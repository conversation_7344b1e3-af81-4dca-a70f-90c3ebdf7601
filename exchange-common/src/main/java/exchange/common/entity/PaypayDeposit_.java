package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PaypayDeposit.class)
public abstract class PaypayDeposit_ extends AbstractEntity_ {

  public static volatile SingularAttribute<PaypayDeposit, String> tranId;
  public static volatile SingularAttribute<PaypayDeposit, Long> fiatDepositId;
  public static volatile SingularAttribute<PaypayDeposit, String> dataKbn;
  public static volatile SingularAttribute<PaypayDeposit, String> shokaiNo;
  public static volatile SingularAttribute<PaypayDeposit, String> kanjoDate;
  public static volatile SingularAttribute<PaypayDeposit, String> kisanDate;
  public static volatile SingularAttribute<PaypayDeposit, Long> amount;
  public static volatile SingularAttribute<PaypayDeposit, Long> anotherAmount;
  public static volatile SingularAttribute<PaypayDeposit, String> outputCode;
  public static volatile SingularAttribute<PaypayDeposit, String> outputName;
  public static volatile SingularAttribute<PaypayDeposit, String> rmtBankName;
  public static volatile SingularAttribute<PaypayDeposit, String> rmtBrName;
  public static volatile SingularAttribute<PaypayDeposit, String> cancelKind;
  public static volatile SingularAttribute<PaypayDeposit, String> dummy;
  public static volatile SingularAttribute<PaypayDeposit, String> comment;
  public static volatile SingularAttribute<PaypayDeposit, FiatDeposit> fiatDeposit;

  public static final String TRAN_ID = "tranId";
  public static final String FIAT_DEPOSIT_ID = "fiatDepositId";
  public static final String DATA_KUBUN = "dataKbn";
  public static final String SHOKAI_NO = "shokaiNo";
  public static final String KANJO_DATE = "kanjoDate";
  public static final String KISAN_DATE = "kisanDate";
  public static final String AMOUNT = "amount";
  public static final String ANOTHER_AMOUNT = "anotherAmount";
  public static final String OUTPUT_CODE = "outputCode";
  public static final String OUTPUT_NAME = "outputName";
  public static final String RMT_BANK_NAME = "rmtBankName";
  public static final String RMT_BR_NAME = "rmtBrName";
  public static final String CANCEL_KIND = "cancelKind";
  public static final String DUMMY = "dummy";
  public static final String COMMENT = "comment";
  public static final String FIAT_DEPOSIT = "fiatDeposit";
}
