package exchange.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.math.RoundingMode;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import exchange.common.constant.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "deposit")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class Deposit extends AbstractEntity {

  private static final long serialVersionUID = -1742465857593293667L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;

  @Getter
  @Setter
  @Column(name = "deposit_account_id", nullable = false)
  private Long depositAccountId;

  @Getter
  @Setter
  @Column(name = "deposit_channel", nullable = false)
  @Enumerated(EnumType.STRING)
  private DepositChannel depositChannel = DepositChannel.UNKNOWN;

  @Getter
  @Setter
  @Column(name = "deposit_purpose", nullable = false)
  @Enumerated(EnumType.STRING)
  private DepositPurpose depositPurpose;

  @Getter
  @Setter
  @Column(name = "deposit_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private DepositType depositType;

  @Getter
  @Setter
  @Column(name = "amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal amount;

  @Getter
  @Setter
  @Column(name = "asset_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal assetAmount;

  @Getter
  @Setter
  @Column(name = "fee", precision = 34, scale = 20, nullable = false)
  private BigDecimal fee;

  @Getter
  @Setter
  @Column(name = "jpy_conversion", precision = 34, scale = 20, nullable = true)
  private BigDecimal jpyConversion;

  @Getter
  @Setter
  @Column(name = "address", nullable = false)
  private String address;

  @Getter
  @Setter
  @Column(name = "distinction", nullable = false)
  private String distinction = "IN";

  @Getter
  @Setter
  @Column(name = "ownertype", nullable = false)
  private String ownertype;

  @Getter
  @Setter
  @Column(name = "recipienttype", nullable = false)
  private String recipienttype;

  @Getter
  @Setter
  @Column(name = "last_name", nullable = false)
  private String last_name;

  @Getter
  @Setter
  @Column(name = "first_name", nullable = false)
  private String first_name;

  @Getter
  @Setter
  @Column(name = "last_name_kana", nullable = false)
  private String last_name_kana;

  @Getter
  @Setter
  @Column(name = "first_name_kana", nullable = false)
  private String first_name_kana;

  @Getter
  @Setter
  @Column(name = "last_name_english", nullable = false)
  private String last_name_english;

  @Getter
  @Setter
  @Column(name = "first_name_english", nullable = false)
  private String first_name_english;

  @Getter
  @Setter
  @Column(name = "legalname", nullable = false)
  private String legalname;

  @Getter
  @Setter
  @Column(name = "legalname_kana", nullable = false)
  private String legalname_kana;

  @Getter
  @Setter
  @Column(name = "legalname_english", nullable = false)
  private String legalname_english;

  @Getter
  @Setter
  @Column(name = "addresstype", nullable = false)
  private String addresstype;

  @Getter
  @Setter
  @Column(name = "exchange", nullable = false)
  private String exchange;

  @Getter
  @Setter
  @Column(name = "purpose", nullable = false)
  private String purpose;

  @Getter
  @Setter
  @Column(name = "area", nullable = false)
  private String area;

  @Getter
  @Setter
  @Column(name = "aregion", nullable = false)
  private String aregion;

  @Getter
  @Setter
  @Column(name = "risk_score", nullable = false)
  private Integer riskScore;

  @Getter
  @Setter
  @Column(name = "transaction_hash", nullable = false)
  private String transactionHash;

  @Getter
  @Setter
  @Column(name = "transaction_id")
  private String transactionId;

  @Getter
  @Setter
  @Column(name = "transaction_index")
  private Long transactionIndex;

  @Getter
  @Setter
  @Column(name = "deposit_status", nullable = false)
  @Enumerated(EnumType.STRING)
  private DepositStatus depositStatus = DepositStatus.FOUND;

  @Getter
  @Setter
  @Column(name = "comment")
  private String comment;

  @Getter
  @Setter
  @Column(name = "sygna_tx_id")
  private String sygnaTxId;

  @Getter
  @Setter
  @Column(name = "sanction_match", nullable = false)
  private boolean sanctionMatch = false;

  @Getter
  @Setter
  @Column(name = "sygna_notify_flg")
  private String sygnaNotifyFlg;

  @Getter
  @Setter
  @Column(name = "chainalysis_external_id", nullable = false)
  private String chainalysisExternalId;

  @Getter
  @Setter
  @Column(name = "chainalysis_max_alert_level", nullable = false)
  @Enumerated(EnumType.STRING)
  private ChainalysisAlertLevel chainalysisMaxAlertLevel;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private User user;

  @Getter
  @Setter
  @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
    name = "deposit_account_id",
    referencedColumnName = "id",
    foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
    insertable = false,
    updatable = false)
  @Fetch(FetchMode.JOIN)
  private DepositAccount depositAccount;

  public Deposit(User user, Currency currency, BigDecimal amount, String comment) {
    userId = user.getId();
    this.currency = currency;
    this.amount = amount;
    this.comment = comment;
  }

  @JsonIgnore
  public BigDecimal nextBalance(BigDecimal balance) {
    return balance.add(currency.getScaledAmount(amount.subtract(fee), RoundingMode.FLOOR));
  }

  @JsonIgnore
  public BigDecimal ownNextBalance(BigDecimal balance) {
    return balance.add(currency.getScaledAmount(amount.subtract(fee), RoundingMode.FLOOR));
  }
}
