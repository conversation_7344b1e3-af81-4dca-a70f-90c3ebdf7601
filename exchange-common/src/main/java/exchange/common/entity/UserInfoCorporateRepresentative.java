package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import com.ibm.icu.text.Transliterator;
import exchange.common.constant.Country;
import exchange.common.model.request.UserInfoCorporateForm;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "user_info_corporate_representative")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserInfoCorporateRepresentative extends AbstractEntity {

  private static final long serialVersionUID = -8955673772046322349L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "first_name", nullable = false)
  private String firstName;

  @Getter
  @Setter
  @Column(name = "last_name", nullable = false)
  private String lastName;

  @Getter
  @Setter
  @Column(name = "first_kana", nullable = false)
  private String firstKana;

  @Getter
  @Setter
  @Column(name = "last_kana", nullable = false)
  private String lastKana;

  @Getter
  @Setter
  @Column(name = "nationality", nullable = false)
  private String nationality;

  @Getter
  @Setter
  @Column(name = "position", nullable = false)
  private String position;

  @Getter
  @Setter
  @Column(name = "zip_code", nullable = false)
  private String zipCode;

  @Getter
  @Setter
  @Column(name = "prefecture", nullable = false)
  private String prefecture;

  @Getter
  @Setter
  @Column(name = "city", nullable = false)
  private String city;

  @Getter
  @Setter
  @Column(name = "address", nullable = false)
  private String address;

  @Getter
  @Setter
  @Column(name = "building", nullable = false)
  private String building;

  @Getter
  @Setter
  @Column(name = "birthday", nullable = false)
  private String birthday;

  @Getter
  @Setter
  @Column(name = "gender")
  private Integer gender;

  @Getter
  @Setter
  @Column(name = "country")
  @Enumerated(EnumType.STRING)
  private Country country;

  @Getter
  @Setter
  @Column(name = "phone_number", nullable = false)
  private String phoneNumber;

  public UserInfoCorporateRepresentative(Long userId) {
    this.userId = userId;
  }

  public UserInfoCorporateRepresentative setProperties(
      long userId, UserInfoCorporateForm.Representative representative) throws Exception {
    Transliterator ts = Transliterator.getInstance("Halfwidth-Fullwidth");
    this.userId = userId;
    this.firstName = ts.transliterate(representative.getFirstName());
    this.lastName = ts.transliterate(representative.getLastName());
    this.firstKana = ts.transliterate(representative.getFirstKana());
    this.lastKana = ts.transliterate(representative.getLastKana());
    this.position = ts.transliterate(representative.getPosition());
    this.nationality = representative.getNationality();
    this.zipCode = representative.getZipCode();
    this.prefecture = representative.getPrefecture();
    this.city = ts.transliterate(representative.getCity());
    this.address = ts.transliterate(representative.getAddress());
    if (!StringUtils.isEmpty(representative.getBuilding())) {
      this.building = ts.transliterate(representative.getBuilding());
    } else {
      this.building = null;
    }
    this.birthday = representative.getBirthday();
    this.gender = Integer.parseInt(representative.getGender());

    if (!StringUtils.isEmpty(representative.getCountry())) {
      this.country = Country.valueOfName(representative.getCountry());
    } else {
      this.country = null;
    }
    this.phoneNumber = representative.getPhoneNumber();

    return this;
  }
}
