package exchange.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(IEOElectResult.class)
public abstract class IEOElectResult_ extends AbstractEntity_ {

  public static volatile SingularAttribute<IEOElectResult, Long> userId;
  public static volatile SingularAttribute<IEOElectResult, Long> ieoRecruitInfoId;
  public static volatile SingularAttribute<IEOElectResult, Long> ieoElectNum;
  public static volatile SingularAttribute<IEOElectResult, BigDecimal> electWinAmount;
  public static volatile SingularAttribute<IEOElectResult, IEORecruitInfo> ieoRecruitInfo;
  public static volatile SingularAttribute<IEOElectResult, String> createdBy;
  public static volatile SingularAttribute<IEOElectResult, String> updatedBy;

  public static final String USER_ID = "userId";
  public static final String IEO_RECRUIT_INFO_ID = "ieoRecruitInfoId";

}
