package exchange.pos.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderOperator;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.PosOrderStatus;
import exchange.common.model.OrderRowMapper;
import exchange.common.util.FormatUtil;
import exchange.pos.entity.PosOrder;
import exchange.pos.entity.PosOrder_;

public abstract class AbstractPosOrderRowMapper extends OrderRowMapper<PosOrder> {

  @Override
  public PosOrder mapRow(ResultSet rs, int rowNum) throws SQLException {
	  PosOrder order = super.mapRow(rs, rowNum);

    if (order != null) {
      order.setOrderSide(
          OrderSide.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(PosOrder_.orderSide.getName()))));
      order.setOrderType(
          OrderType.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(PosOrder_.orderType.getName()))));
      order.setOrderChannel(
          OrderChannel.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(PosOrder_.orderChannel.getName()))));
      order.setPrice(rs.getBigDecimal(FormatUtil.formatCamelToSnake(PosOrder_.price.getName())));
      order.setMmPrice(
          rs.getBigDecimal(FormatUtil.formatCamelToSnake(PosOrder_.mmPrice.getName())));
      order.setAmount(
    		  rs.getBigDecimal(FormatUtil.formatCamelToSnake(PosOrder_.amount.getName())));
      order.setRemainingAmount(
          rs.getBigDecimal(FormatUtil.formatCamelToSnake(PosOrder_.remainingAmount.getName())));
      order.setOrderStatus(
          PosOrderStatus.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(PosOrder_.orderStatus.getName()))));
      order.setOrderOperator(
          OrderOperator.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(PosOrder_.orderOperator.getName()))));
    }

    return order;
  }
}
