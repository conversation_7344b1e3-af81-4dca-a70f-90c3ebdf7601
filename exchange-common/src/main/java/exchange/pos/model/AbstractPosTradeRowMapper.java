package exchange.pos.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderType;
import exchange.common.model.TradeRowMapper;
import exchange.common.util.FormatUtil;
import exchange.pos.entity.PosTrade;
import exchange.spot.entity.SpotTrade_;

public abstract class AbstractPosTradeRowMapper extends TradeRowMapper<PosTrade> {

  @Override
  public PosTrade mapRow(ResultSet rs, int rowNum) throws SQLException {
	  PosTrade trade = super.mapRow(rs, rowNum);

    if (trade != null) {
      trade.setOrderType(
          OrderType.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(SpotTrade_.orderType.getName()))));
      trade.setOrderChannel(
          OrderChannel.valueOf(
              rs.getString(FormatUtil.formatCamelToSnake(SpotTrade_.orderChannel.getName()))));
      trade.setJpyConversion(
          rs.getBigDecimal(FormatUtil.formatCamelToSnake(SpotTrade_.jpyConversion.getName())));
      trade.setAssetAmount(
          rs.getBigDecimal(FormatUtil.formatCamelToSnake(SpotTrade_.assetAmount.getName())));
    }

    return trade;
  }
}
