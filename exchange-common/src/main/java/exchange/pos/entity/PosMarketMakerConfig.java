package exchange.pos.entity;

import exchange.common.constant.Exchange;
import exchange.common.entity.AbstractEntity;
import java.math.BigDecimal;
import javax.persistence.*;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@NoArgsConstructor
@Setter
@Getter
@Table(name = "pos_market_maker_config")
public class PosMarketMakerConfig extends AbstractEntity {
  @Column(name = "symbol_id")
  private Long symbolId;

  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  private Boolean enabled;

  private BigDecimal quantity;
}
