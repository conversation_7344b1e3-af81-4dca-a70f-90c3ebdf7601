package exchange.pos.entity;

import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.entity.AbstractEntity_;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PosCoverOrder.class)
public abstract class PosCoverOrder_ extends AbstractEntity_ {

  public static volatile SingularAttribute<PosCoverOrder, Long> symbolId;
  public static volatile SingularAttribute<PosCoverOrder, OrderSide> orderSide;
  public static volatile SingularAttribute<PosCoverOrder, OrderType> orderType;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> price;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> averagePrice;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> amount;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> remainingAmount;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> remainingAmountManualNidt;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> fee;
  public static volatile SingularAttribute<PosCoverOrder, Exchange> exchange;
  public static volatile SingularAttribute<PosCoverOrder, Long> exchangeOrderId;
  public static volatile SingularAttribute<PosCoverOrder, List<OrderStatus>> orderStatus;
  public static volatile SingularAttribute<PosCoverOrder, BigDecimal> usdtPrice;
  public static volatile SingularAttribute<PosCoverOrder, Long> posCustomizeOrderId;

  public static final String SYMBOL_ID = "symbolId";
  public static final String ORDER_SIDE = "orderSide";
  public static final String ORDER_TYPE = "orderType";
  public static final String PRICE = "price";
  public static final String AVERAGE_PRICE = "averagePrice";
  public static final String AMOUNT = "amount";
  public static final String REMAINING_AMOUNT = "remainingAmount";
  public static final String FEE = "fee";
  public static final String EXCHANGE = "exchange";
  public static final String EXCHANGE_ORDER_ID = "exchangeOrderId";
  public static final String ORDER_STATUS = "orderStatus";
  public static final String USDT_PRICE = "usdtPrice";
}
