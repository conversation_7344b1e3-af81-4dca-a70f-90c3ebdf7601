package exchange.pos.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderType;
import exchange.common.entity.Trade;
import exchange.common.serializer.BigDecimalSerializer;
import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@NoArgsConstructor
@Table(name = "pos_trade")
public class PosTrade extends Trade {

  private static final long serialVersionUID = -6424284956090933377L;

  @Getter
  @Setter
  @Column(name = "order_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private OrderType orderType;

  @Getter
  @Setter
  @Column(name = "order_channel", nullable = false)
  @Enumerated(EnumType.STRING)
  private OrderChannel orderChannel = OrderChannel.UNKNOWN;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  @Column(name = "jpy_conversion", precision = 34, scale = 20, nullable = false)
  private BigDecimal jpyConversion;

  @Getter
  @Setter
  @JsonSerialize(using = BigDecimalSerializer.class)
  @Column(name = "asset_amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal assetAmount;

  public PosTrade setAnotherProperties(
      BigDecimal fee,
      OrderType orderType,
      OrderChannel orderChannel,
      BigDecimal jpyConversion,
      BigDecimal assetAmount) {
    setFee(fee);
    this.orderType = orderType;
    this.orderChannel = orderChannel;
    this.jpyConversion = jpyConversion;
    this.assetAmount = assetAmount;
    return this;
  }

  @JsonIgnore
  public BigDecimal nextBaseBalance(BigDecimal balance) {
    return getOrderSide().isBuy()
        ? balance.add(getAmount())
        : balance.subtract(getAmount());
  }

  @JsonIgnore
  public BigDecimal nextQuoteBalance(BigDecimal balance) {
    return getOrderSide().isBuy()
        ? balance.subtract(assetAmount).subtract(getFee())
        : balance.add(assetAmount).subtract(getFee());
  }

  @JsonIgnore
  public BigDecimal ownNextBaseBalance(BigDecimal balance) {
    return getOrderSide().isBuy()
        ? balance.add(getAmount())
        : balance.subtract(getAmount());
  }

  @JsonIgnore
  public BigDecimal ownNextQuoteBalance(BigDecimal balance) {
    return getOrderSide().isBuy()
        ? balance.subtract(getAssetAmount()).subtract(getFee())
        : balance.add(getAssetAmount()).subtract(getFee());
  }
}
