package exchange.pos.entity;

import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderType;
import exchange.common.entity.Trade_;
import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PosTrade.class)
public abstract class PosTrade_ extends Trade_ {

  public static volatile SingularAttribute<PosTrade, OrderType> orderType;
  public static volatile SingularAttribute<PosTrade, OrderChannel> orderChannel;
  public static volatile SingularAttribute<PosTrade, BigDecimal> jpyConversion;
  public static volatile SingularAttribute<PosTrade, Long> targetOrderId;
  public static volatile SingularAttribute<PosTrade, Long> targetUserId;
  public static volatile SingularAttribute<PosTrade, BigDecimal> assetAmount;

  public static final String ORDER_TYPE = "orderType";
  public static final String ORDER_CHANNEL = "orderChannel";
  public static final String JPY_CONVERSION = "jpyConversion";
  public static final String TARGET_ORDER_ID = "targetOrderId";
  public static final String TARGET_USER_ID = "targetUserId";
  public static final String ASSET_AMOUNT = "assetAmount";
}
