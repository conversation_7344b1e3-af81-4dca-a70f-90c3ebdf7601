package exchange.pos.entity;

import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@NoArgsConstructor
@Table(name = "pos_cover_order")
@Getter
@Setter
public class PosCoverOrder extends AbstractEntity {

  private static final long serialVersionUID = -823879874771347594L;

  @Column(name = "symbol_id")
  private Long symbolId;

  @Column(name = "order_id")
  private Long orderId;

  @Enumerated(EnumType.STRING)
  @Column(name = "order_side")
  private OrderSide orderSide;

  @Enumerated(EnumType.STRING)
  @Column(name = "order_type")
  private OrderType orderType;

  @Column(name = "price")
  private BigDecimal price;

  @Column(name = "average_price")
  private BigDecimal averagePrice;
  
  @Column(name = "usdt_price")
  private BigDecimal usdtPrice;

  @Column(name = "amount")
  private BigDecimal amount;

  @Column(name = "remaining_amount")
  private BigDecimal remainingAmount;

  @Column(name = "fee")
  private BigDecimal fee;

  @Column(name = "strategy")
  private String strategy;

  @Column(name = "exchange")
  @Enumerated(EnumType.STRING)
  private Exchange exchange;

  @Column(name = "exchange_order_id")
  private String exchangeOrderId;

  @Enumerated(EnumType.STRING)
  @Column(name = "order_status")
  private OrderStatus orderStatus = OrderStatus.WAITING;
  
  @Column(name = "amount_manual_nidt")
  private BigDecimal amountManualNidt = BigDecimal.ZERO;
  
  @Column(name = "remaining_amount_manual_nidt")
  private BigDecimal remainingAmountManualNidt = BigDecimal.ZERO;;
  
  @Column(name = "fee_manual_nidt")
  private BigDecimal feeManualNidt = BigDecimal.ZERO;;
  
  @Column(name = "average_price_manual")
  private BigDecimal averagePriceManual = BigDecimal.ZERO;;
  
  @Column(name = "pos_customize_order_id")
  private Long posCustomizeOrderId;
  
  @Getter
  @Setter
  @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "order_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @NotFound(action = NotFoundAction.IGNORE)
  @Fetch(FetchMode.JOIN)
  private PosOrder posOrder;
  
  @Getter
  @Setter
  @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "pos_customize_order_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @NotFound(action = NotFoundAction.IGNORE)
  @Fetch(FetchMode.JOIN)
  private PosCustomizeOrder posCustomizeOrder;

}
