package exchange.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.config.SpotOrderApiConfig;
import exchange.common.constant.CommonConstants;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.entity.ApiInfo;
import exchange.common.entity.CoverOrderConfig;
import exchange.common.entity.CoverOrder_;
import exchange.common.entity.Symbol;
import exchange.common.entity.Trade_;
import exchange.common.exception.CustomException;
import exchange.common.http.HttpManager;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SpotCoverOrderApiResponse;
import exchange.common.service.CoverOrderConfigService;
import exchange.common.service.EntityService;
import exchange.common.service.SymbolService;
import exchange.common.util.JsonUtil;
import exchange.pos.entity.PosCoverOrder;
import exchange.pos.entity.PosCustomizeOrder;
import exchange.pos.predicate.PosCoverOrderPredicate;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotTrade;
import exchange.spot.service.SpotOrderService;
import exchange.spot.service.SpotTradeService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PosCoverOrderService extends EntityService<PosCoverOrder, PosCoverOrderPredicate> {

  @Override
  public Class<PosCoverOrder> getEntityClass() {
    return PosCoverOrder.class;
  }

  public List<PosCoverOrder> findByCondition(Long symbolId, List<OrderStatus> status,
      Exchange exchange, BigDecimal remainingAmount,int number,int size) {
    return findByCondition(
        symbolId,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        status,
        exchange,
        null,
        remainingAmount,
        number,
        size,
        false);
  }
  public List<PosCoverOrder> findByCondition(
      Long symbolId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      OrderSide orderSide,
      OrderType orderType,
      List<OrderType> orderTypes,
      List<OrderType> exceptOrderTypes,
      List<OrderStatus> orderStatus,
      Exchange exchange,
      BigDecimal amount,
      BigDecimal remainingAmount,
      int number,
      int size,
      boolean isAscending) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCoverOrder, List<PosCoverOrder>>() {

          @Override
          public List<PosCoverOrder> query() {
            List<Predicate> predicates =
                createPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    symbolId,
                    id,
                    idFrom,
                    idTo,
                    dateFrom,
                    dateTo,
                    orderSide,
                    orderType,
                    CollectionUtils.isEmpty(orderTypes) ? null : orderTypes.toArray(new OrderType[orderTypes.size()]),
                    CollectionUtils.isEmpty(exceptOrderTypes) ? null : exceptOrderTypes.toArray(new OrderType[exceptOrderTypes.size()]),
                    CollectionUtils.isEmpty(orderStatus) ? null : orderStatus.toArray(new OrderStatus[orderStatus.size()]),
                    exchange,
                    amount,
                    remainingAmount);
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                number,
                size,
                isAscending
                    ? criteriaBuilder.asc(root.get(Trade_.id))
                    : criteriaBuilder.desc(root.get(Trade_.id)));
          }
        });
  }

  private List<Predicate> createPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<PosCoverOrder> root,
      Long symbolId,
      Long id,
      Long idFrom,
      Long idTo,
      Long createdAtFrom,
      Long createdAtTo,
      OrderSide orderSide,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderStatus[] orderStatus,
      Exchange exchange,
      BigDecimal amount,
      BigDecimal remainingAmount) {
    List<Predicate> predicates = new ArrayList<>();
    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    } else {
      if(symbolId != null) {
        predicates.add(predicate.equalSymbol(criteriaBuilder, root, symbolId));
      }

      if (idFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
      }
      if (idTo != null) {
        predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
      }
      if (createdAtFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root,
            new Date(createdAtFrom)));
      }
      if (createdAtTo != null) {
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
      }
      if (orderSide != null) {
        predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
      }
      if (orderType != null) {
        predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
      } else if (!ArrayUtils.isEmpty(orderTypes)) {
        predicates.add(predicate.inOrderType(root, orderTypes));
      } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
        predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
      }
      if(orderStatus != null) {
        predicates.add(predicate.inOrderStatus(root, orderStatus));
      }
      if(exchange != null) {
        predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
      }
      if(amount != null) {
        predicates.add(predicate.greaterThanOrEqualToAmount(criteriaBuilder, root, amount));
      }
      if(remainingAmount != null) {
        predicates.add(predicate.greaterThanRemainingAmount(criteriaBuilder, root, remainingAmount));
      }
    }
    return predicates;
  }
  
  public List<PosCoverOrder> findByCondition(Long symbolId,
      CurrencyPair currencyPair, Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      OrderType orderType,
      OrderSide orderSide,
      Exchange exchange,
      Long exchangeOrderId,
      BigDecimal greaterThanRemainingAmount,
      BigDecimal lessThanRemainingAmount) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCoverOrder, List<PosCoverOrder>>() {
          @Override
          public List<PosCoverOrder> query() {
            List<Predicate> predicates = createPredicatesOfFindByCondition(
                criteriaBuilder,
                root,
                currencyPair,
                symbolId,
                orderType,
                null,
                null,
                orderSide,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                exchange,
                exchangeOrderId,
                greaterThanRemainingAmount,
                lessThanRemainingAmount);
            return getResultList(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(CoverOrder_.id)));
          }
        });

  }
  
  public PageData<PosCoverOrder> findByConditionPageData(
      CurrencyPair currencyPair, Long symbolId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      OrderType orderType,
      OrderSide orderSide,
      Exchange exchange,
      Long exchangeOrderId,
      BigDecimal greaterThanRemainingAmount,
      BigDecimal lessThanRemainingAmount,
      Integer number,
      Integer size) {

    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        currencyPair,
                        symbolId,
                        orderType,
                        null,
                        null,
                        orderSide,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        exchange,
                        exchangeOrderId,
                        greaterThanRemainingAmount,
                        lessThanRemainingAmount));
              }
            });

    return new PageData<PosCoverOrder>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<PosCoverOrder, List<PosCoverOrder>>() {
              @Override
              public List<PosCoverOrder> query() {
                List<Predicate> predicates =
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        currencyPair,
                        symbolId,
                        orderType,
                        null,
                        null,
                        orderSide,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        exchange,
                        exchangeOrderId,
                        greaterThanRemainingAmount,
                        lessThanRemainingAmount);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(CoverOrder_.id)));
              }
            }));
  }
  
  private List<Predicate> createPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<PosCoverOrder> root,
      CurrencyPair currencyPair, Long symbolId,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderSide orderSide,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      Exchange exchange,
      Long exchangeOrderId,
      BigDecimal greaterThanRemainingAmount,
      BigDecimal lessThanRemainingAmount) {
    List<Predicate> predicates =
        createPredicates(
            criteriaBuilder, root, symbolId, orderType, orderTypes, exceptOrderTypes, orderSide);

    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    } else {
      if (idFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
      }

      if (idTo != null) {
        predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
      }

      if (dateFrom != null) {
        predicates.add(
            predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
      }

      if (dateTo != null) {
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
      }

      if (exchange != null) {
        predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
      }

//      if (exchangeOrderId != null) {
//        predicates.add(predicate.equalExchangeOrderId(criteriaBuilder, root, exchangeOrderId));
//      }
      if (CurrencyPair.NIDT_JPY.equals(currencyPair)) {
        if (greaterThanRemainingAmount != null) {
          predicates.add(predicate.greaterThanOrEqualToNidtRemainingAmount(criteriaBuilder,root, greaterThanRemainingAmount));
        }
        if (lessThanRemainingAmount != null) {
          predicates.add(predicate.lessThanNidtRemainingAmount(criteriaBuilder,root, lessThanRemainingAmount));
        }
      } else {
        if (greaterThanRemainingAmount != null) {
          predicates.add(predicate.greaterThanOrEqualToRemainingAmount(criteriaBuilder,root, greaterThanRemainingAmount));
        }
        if (lessThanRemainingAmount != null) {
          predicates.add(predicate.lessThanRemainingAmount(criteriaBuilder,root, lessThanRemainingAmount));
        }
      }
    }

    return predicates;
  }
  
  protected List<Predicate> createPredicates(
      CriteriaBuilder criteriaBuilder,
      Root<PosCoverOrder> root,
      Long symbolId,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderSide orderSide) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.equalSymbol(criteriaBuilder, root, symbolId));

    if (orderType != null) {
      predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
    } else if (!ArrayUtils.isEmpty(orderTypes)) {
      predicates.add(predicate.inOrderType(root, orderTypes));
    } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
      predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
    }

    if (orderSide != null) {
      predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
    }

    return predicates;
  }
  
  public List<PosCoverOrder> findByConditionForUncoverSum(
      Long symbolId,
      OrderSide orderSide,
      Long dateFrom,
      Long dateTo,
      Boolean isCovered) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCoverOrder, List<PosCoverOrder>>() {

          @Override
          public List<PosCoverOrder> query() {
            List<Predicate> predicates = new ArrayList<>();
            if(symbolId != null) {
              predicates.add(predicate.equalSymbol(criteriaBuilder, root, symbolId));
            }
            if (orderSide != null) {
			  predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
			}
            if (isCovered != null && isCovered == false) {
              predicates.add(predicate.isUncovered(criteriaBuilder, root, BigDecimal.ZERO));
            }
            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
            }
            if (dateTo != null) {
              predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }
            
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates);
            }
        });
  }
  
  public PosCoverOrder findOneByPosCustomizeOrderId(
      Long posCustomizeOrderId) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCoverOrder, PosCoverOrder>() {

          @Override
          public PosCoverOrder query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalPosCustomizeOrderId(criteriaBuilder, root, posCustomizeOrderId));
            
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates);
            }
        });
  }
  
  @Value("${exchange-pos.base-trade.coinbook.api-nidt-order:#{null}}")
  private String path;

  @Value("${exchange-pos.base-trade.coinbook.api-host:#{null}}")
  private String host;

  @Value("${exchange-pos.base-trade.coinbook.secret-buy:#{null}}")
  private String secretBuy;

  @Value("${exchange-pos.base-trade.coinbook.api-key-buy:#{null}}")
  private String apiKeyBuy;
  
  @Value("${exchange-pos.base-trade.coinbook.secret-sell:#{null}}")
  private String secretSell;

  @Value("${exchange-pos.base-trade.coinbook.api-key-sell:#{null}}")
  private String apiKeySell;
  
  @Autowired
  SymbolService symbolService;
  
  @Autowired
  private HttpManager<SpotCoverOrderApiResponse> ordersHttpManager;
  @Autowired
  CoverOrderConfigService coverOrderConfigService;

  @Autowired
  SpotOrderApiConfig spotOrderApiConfig;
  @Autowired
  PosCustomizeOrderService posCustomizeOrderService;
  
  @Transactional(
    rollbackFor = Exception.class,
    transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
  public void sendOrder(Long id, BigDecimal orderPrice) throws Exception {
  
    PosCoverOrder posCoverOrder = this.findOne(id);
    
    if(posCoverOrder == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }
    // 既にカスタマイズ注文した場合
    if(posCoverOrder.getPosCustomizeOrderId() != null) {
      log.warn("customize order has been completed.");
      throw new CustomException(ErrorCode.REQUEST_ERROR_EXCLUSION_LOCK);
    }
    
    //generate api info by config
    ApiInfo marketMakerApiInfo = new ApiInfo();
    
    ApiInfo marketMakerApiInfoBuy = new ApiInfo();
    marketMakerApiInfoBuy.setSecret(secretBuy);
    marketMakerApiInfoBuy.setApiKey(apiKeyBuy);
    
    ApiInfo marketMakerApiInfoSell = new ApiInfo();
    marketMakerApiInfoSell.setSecret(secretSell);
    marketMakerApiInfoSell.setApiKey(apiKeySell);
    
    Symbol exchangeSymbol = symbolService.findByCondition(TradeType.SPOT, CurrencyPair.NIDT_JPY); //5
    
    BigDecimal sendAmount = posCoverOrder.getAmountManualNidt();
    if(sendAmount.signum() < 1) {
      log.warn("invalid send amount.");
      throw new CustomException(ErrorCode.REQUEST_ERROR_EXCLUSION_LOCK);
    }
    if(sendAmount.compareTo(posCoverOrder.getRemainingAmountManualNidt()) != 0) {
      log.warn("manual order has been started.");
      throw new CustomException(ErrorCode.REQUEST_ERROR_EXCLUSION_LOCK);
    }
    Symbol posSymbol = symbolService.findByCondition(TradeType.POS, CurrencyPair.NIDT_JPY); //10
    CoverOrderConfig coverOrderConfig =
        coverOrderConfigService.findOne(posSymbol.getId(), Exchange.COINBOOK, null);
    if(sendAmount.compareTo(coverOrderConfig.getMinOrderAmount()) < 0) {
      log.warn("less than min cover order amount.");
      throw new CustomException(ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_MINMAX);
    }
    
    OrderSide orderSide = posCoverOrder.getOrderSide();
    
    if (OrderSide.BUY.equals(orderSide)) {
    	marketMakerApiInfo = marketMakerApiInfoBuy;
    } else {
		marketMakerApiInfo = marketMakerApiInfoSell;
	}
    log.info("pre send, pos order id :{},,send amount:{}, send price:{}",
        id, sendAmount, orderPrice);
    
    SpotCoverOrderApiResponse response = null;
    try {
      response = send(orderSide, marketMakerApiInfo, host+path, orderPrice, sendAmount, exchangeSymbol);
    } catch(CustomException e) {
      if(e.getDebugMessage() != null && 
        e.getDebugMessage().contains(
            String.valueOf(ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE.getCode()))) {
        throw new CustomException(ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE);
      } else {
        throw e;
      }
    }
    
    if(response == null) {
      log.warn("can not get response.");
      throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
    }
    // posCoverOrderにposCustomizeOrderIdを登録
    PosCustomizeOrder posCustomizeOrder = insertPosCustomizeOrder(posCoverOrder, response, orderPrice);
    posCoverOrder.setAmountManualNidt(posCoverOrder.getAmountManualNidt().subtract(posCustomizeOrder.getAmount()));
    posCoverOrder.setRemainingAmountManualNidt(posCoverOrder.getRemainingAmountManualNidt().subtract(posCustomizeOrder.getAmount()));
    posCoverOrder.setPosCustomizeOrderId(posCustomizeOrder.getId());
    this.save(posCoverOrder);
  }
  
  public SpotCoverOrderApiResponse send(OrderSide side,ApiInfo apiInfo,String url,BigDecimal price,BigDecimal amount,Symbol symbol) throws Exception {
    BigDecimal amountScaled = symbol.getCurrencyPair().getScaledAmount(amount,symbol.getCurrencyPair().getPosBasePrecision(),RoundingMode.HALF_UP);
    TreeMap<String, Object> treeMap = new TreeMap<>();
    treeMap.put("symbolId", symbol.getId());//exchange symbol
    treeMap.put("orderSide", side);
    treeMap.put("orderType", OrderType.LIMIT);
    if(side.isSell()){
      treeMap.put("price", symbol.getCurrencyPair().getScaledPrice(price,RoundingMode.DOWN));
    } else {
      treeMap.put("price", symbol.getCurrencyPair().getScaledPrice(price,RoundingMode.UP));
    }
    treeMap.put("amount", amountScaled);
    SpotCoverOrderApiResponse response = ordersHttpManager.doPostJson(url,
            treeMap,
            spotOrderApiConfig.createHeaderMapPostOrPut(treeMap, apiInfo),
            null,
            SpotCoverOrderApiResponse.class);
    log.info("order response info:{}",response);
    return response;
  }
  
  public PosCustomizeOrder insertPosCustomizeOrder(PosCoverOrder posCoverOrder,
      SpotCoverOrderApiResponse response, BigDecimal orderPrice){
    // 手数料計算
    Symbol symbol = symbolService.findOne(response.getSymbolId());
    List<SpotTrade> spotTrades = SpotTradeService.getBean(symbol).
        findByOrderId(response.getSymbolId(), response.getId(), null);
    BigDecimal fee = BigDecimal.ZERO;
    if(ObjectUtils.isNotEmpty(spotTrades)) {
      fee = spotTrades.stream().map(SpotTrade::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    List<SpotTrade> spotTradesHistory =
        SpotTradeService.getBean(symbol)
            .findFromHistory(
                symbol,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                response.getId(),
                null,
                null,
                null,
                null,
                null);
    if(ObjectUtils.isNotEmpty(spotTradesHistory)) {
      fee = spotTradesHistory.stream().map(SpotTrade::getFee).reduce(fee, BigDecimal::add);
    }
    PosCustomizeOrder posCustomizeOrder = new PosCustomizeOrder();
    posCustomizeOrder.setSymbolId(response.getSymbolId());
    posCustomizeOrder.setExchangeOrderId(response.getId().toString());
    posCustomizeOrder.setOrderSide(OrderSide.valueOfName(response.getOrderSide()));
    posCustomizeOrder.setOrderType(OrderType.valueOfName(response.getOrderType()));
    posCustomizeOrder.setPrice(orderPrice);
    posCustomizeOrder.setAveragePrice(response.getAveragePrice());
    posCustomizeOrder.setAmount(response.getAmount());
    posCustomizeOrder.setRemainingAmount(response.getRemainingAmount());
    posCustomizeOrder.setFee(fee);
    posCustomizeOrder.setStrategy("GTC");
    posCustomizeOrder.setExchange(Exchange.COINBOOK);
    posCustomizeOrder.setOrderStatus(OrderStatus.valueOfName(response.getOrderStatus()));
    return posCustomizeOrderService.save(posCustomizeOrder);
  }
  
  @Transactional(
      rollbackFor = Exception.class,
      transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
  public void posCustomizeOrderUpdate(Symbol symbol) throws Exception {
    List<PosCustomizeOrder> list = posCustomizeOrderService.findUnFullyFilledOrderByCondition(symbol.getId(),
        List.of(OrderStatus.WAITING, OrderStatus.UNFILLED,OrderStatus.PARTIALLY_FILLED), BigDecimal.ZERO);
    for (PosCustomizeOrder posCustomizeOrder : list) {
        if(posCustomizeOrder.getExchangeOrderId() == null) {
          continue;
        }
        Long spotOrderId = Long.valueOf(posCustomizeOrder.getExchangeOrderId());
        Long symbolId = posCustomizeOrder.getSymbolId();
        
        List<SpotOrder> orderList = SpotOrderService.getBean(symbol)
          .findOrders(symbol, List.of(spotOrderId), null, null).toList();
        
        log.info("DBMITO,posUnCoverOrderSummaryUpdaterLog,symbolId:{}; get order response:{}", symbolId,JsonUtil.encode(orderList));
        if(!CollectionUtils.isEmpty(orderList)){
            SpotOrder spotOrder = orderList.get(0);
            customTransactionManager.execute(entityManager -> {
                // 手数料計算
                Symbol symbolRes = symbolService.findOne(spotOrder.getSymbolId());
                List<SpotTrade> spotTrades = SpotTradeService.getBean(symbolRes).
                    findByOrderId(spotOrder.getSymbolId(), spotOrder.getId(), null);
                BigDecimal fee = BigDecimal.ZERO;
                if(ObjectUtils.isNotEmpty(spotTrades)) {
                  fee = spotTrades.stream().map(SpotTrade::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                List<SpotTrade> spotTradesHistory =
                    SpotTradeService.getBean(symbol)
                        .findFromHistory(
                            symbol,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            spotOrder.getId(),
                            null,
                            null,
                            null,
                            null,
                            null);
                if(ObjectUtils.isNotEmpty(spotTradesHistory)) {
                  fee = spotTradesHistory.stream().map(SpotTrade::getFee).reduce(fee, BigDecimal::add);
                }
                posCustomizeOrder.setRemainingAmount(spotOrder.getRemainingAmount());
                posCustomizeOrder.setFee(fee);
                posCustomizeOrder.setAveragePrice(spotOrder.getAveragePrice());
                posCustomizeOrder.setOrderStatus(spotOrder.getOrderStatus());
                posCustomizeOrderService.save(posCustomizeOrder);
                
                if(OrderStatus.CANCELED_UNFILLED.equals(spotOrder.getOrderStatus()) ||
                    OrderStatus.CANCELED_PARTIALLY_FILLED.equals(spotOrder.getOrderStatus())){
                  PosCoverOrder posCoverOrder = 
                      this.findOneByPosCustomizeOrderId(posCustomizeOrder.getId());
                  if(posCoverOrder != null) {
                    posCoverOrder.setAmountManualNidt(
                        posCoverOrder.getAmountManualNidt().add(spotOrder.getRemainingAmount()));
                    posCoverOrder.setRemainingAmountManualNidt(
                        posCoverOrder.getRemainingAmountManualNidt().add(spotOrder.getRemainingAmount()));
                    this.save(posCoverOrder);
                  }
                }
            });
            log.info("orderId:{};userId:{};OrderStatus:{}", spotOrder.getId(),
                spotOrder.getUserId(),
                spotOrder.getOrderStatus().toString());
        }
    }
  }
}
