package exchange.pos.service;


import org.springframework.stereotype.Service;

import exchange.common.service.EntityService;
import exchange.pos.entity.PosTrade;
import exchange.pos.model.PosTradeRowMapper;
import exchange.pos.predicate.PosTradePredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public abstract class PosTradeHistoryService extends EntityService<PosTrade, PosTradePredicate>  {
	  
  public abstract Class<PosTradeRowMapper> getRowMapperClass();
  
  public PosTradeRowMapper newRowMapper() {
	  PosTradeRowMapper rowMapper = null;

	    try {
	      rowMapper = getRowMapperClass().getDeclaredConstructor().newInstance();
	    } catch (Exception e) {
	      log.error("newRowMapper failed", e);
	    }

    return rowMapper;
  }
}
