package exchange.pos.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import exchange.common.component.CustomRedisTemplate;
import exchange.pos.entity.PosMarketMakerConfig;
import exchange.pos.repos.PosMarketMakerConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosMarketMakerConfigService {

  private final PosMarketMakerConfigRepository repository;
  private final CustomRedisTemplate<PosMarketMakerConfig> redisTemplate;

  public PosMarketMakerConfig findBySymbolId(Long symbolId) {
    PosMarketMakerConfig config = null;
    try {
      config = redisTemplate.getValue(getCacheKey(symbolId));
    } catch (Exception e) {
    }
    if (config == null) {
      config = repository.findBySymbolIdEqualsAndEnabledIsTrue(symbolId);
    }
    return config;
  }
  
  public PosMarketMakerConfig findBySymbolIdAll(Long symbolId) {
    PosMarketMakerConfig config = null;
    try {
      config = redisTemplate.getValue(getCacheKey(symbolId));
    } catch (Exception e) {
    }
    if (config == null) {
      config = repository.findBySymbolIdEquals(symbolId);
    }
    return config;
  }


  protected String getCacheKey(Long id) {
    return getClass().getSimpleName() + ":" + id;
  }
}
