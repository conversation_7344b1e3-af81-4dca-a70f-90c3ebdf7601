package exchange.pos.service;


import org.springframework.stereotype.Service;

import exchange.common.service.EntityService;
import exchange.pos.entity.PosOrder;
import exchange.pos.model.AbstractPosOrderRowMapper;
import exchange.pos.model.PosOrderRowMapper;
import exchange.pos.predicate.PosOrderPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public abstract class PosOrderHistoryService extends EntityService<PosOrder, PosOrderPredicate>  {
	  
  public abstract Class<PosOrderRowMapper> getRowMapperClass();
  
  public PosOrderRowMapper newRowMapper() {
	  PosOrderRowMapper rowMapper = null;

	    try {
	      rowMapper = getRowMapperClass().getDeclaredConstructor().newInstance();
	    } catch (Exception e) {
	      log.error("newRowMapper failed", e);
	    }

    return rowMapper;
  }
}
