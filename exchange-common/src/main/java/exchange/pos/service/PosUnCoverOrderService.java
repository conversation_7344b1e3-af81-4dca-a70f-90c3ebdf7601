package exchange.pos.service;

import org.springframework.stereotype.Service;
import exchange.common.service.EntityService;
import exchange.pos.entity.PosUnCoverOrder;
import exchange.pos.predicate.PosUnCoverOrderPredicate;

@Service
public class PosUnCoverOrderService extends EntityService<PosUnCoverOrder, PosUnCoverOrderPredicate> {

  @Override
  public Class<PosUnCoverOrder> getEntityClass() {
    return PosUnCoverOrder.class;
  }

}
