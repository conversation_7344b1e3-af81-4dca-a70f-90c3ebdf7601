//package exchange.common.service;
//
//import exchange.common.aml.WorldCheckOneClient;
//import exchange.common.aml.WorldCheckOneRes;
//import exchange.common.config.WorldCheckOneConfig;
//import exchange.common.constant.KycData;
//import exchange.common.constant.KycStatus;
//import exchange.common.entity.User;
//import exchange.common.entity.UserAntisocialCheck;
//import exchange.common.entity.UserInfo;
//import exchange.common.repos.UserAntisocialCheckRepository;
//import exchange.common.repos.UserInfoRepository;
//import exchange.common.repos.UserKycRepository;
//import exchange.common.repos.UserRepository;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.Collections;
//import java.util.List;
//import java.util.Optional;
//
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//class AntiSocialCheckServiceTest {
//
//  @Mock private WorldCheckOneConfig mockConfig;
//  @Mock private UserAntisocialCheckRepository mockRepository;
//  @Mock private UserInfoRepository mockUserInfoRepository;
//  @Mock private UserRepository mockUserRepository;
//  @Mock private UserEkycStatusChangeHistoryService mockUserEkycStatusChangeHistoryService;
//  @Mock private UserKycRepository mockKycRepository;
//
//  private AntiSocialCheckService antiSocialCheckServiceUnderTest;
//
//  @BeforeEach
//  void setUp() {
//    antiSocialCheckServiceUnderTest =
//        new AntiSocialCheckService(
//            mockConfig,
//            mockRepository,
//            mockUserInfoRepository,
//            mockUserRepository,
//            mockUserEkycStatusChangeHistoryService,
//            mockKycRepository);
//  }
//
//  @Test
//  void testAntiSocialCheck() {
//    // Setup
//    List<User> users = List.of(new User("email", "password"));
//    UserInfo userInfo = new UserInfo();
//    userInfo.setBirthday("19900101");
//    userInfo.setGender(1);
//    userInfo.setLastName("lastName");
//    userInfo.setFirstName("firstName");
//    userInfo.setUserId(1L);
//    userInfo.setId(1L);
//    users.get(0).setUserInfo(userInfo);
//    when(mockUserRepository.findByKycStatusAndUserInfoIdNotNull(KycStatus.DOCUMENT_CONFIRMED))
//        .thenReturn(users);
//
//    // Configure WorldCheckOneConfig.createClient(...).
//    WorldCheckOneClient worldCheckOneClient = mock(WorldCheckOneClient.class);
//    when(mockConfig.createClient()).thenReturn(worldCheckOneClient);
//
//    when(mockUserInfoRepository.findById(1L)).thenReturn(Optional.of(new UserInfo(1L)));
//
//    // Configure UserAntisocialCheckRepository.save(...).
//    final UserAntisocialCheck userAntisocialCheck =
//        new UserAntisocialCheck(1L, 1L, "code", "caseId", "checkGroupKey");
//
//    // Configure UserRepository.findById(...).
//    final Optional<User> userOptional = Optional.of(new User("email", "password"));
//    when(mockUserRepository.findById(1L)).thenReturn(userOptional);
//
//    WorldCheckOneRes res = WorldCheckOneRes.builder().kycData(KycData.NONE).build();
//    when(antiSocialCheckServiceUnderTest.requestScreening(worldCheckOneClient, userInfo))
//        .thenReturn(res);
//
//    // Run the test
//    antiSocialCheckServiceUnderTest.antiSocialCheck();
//
//    // verify
//    verify(mockUserRepository).findByKycStatusAndUserInfoIdNotNull(KycStatus.DOCUMENT_CONFIRMED);
//    verify(mockUserInfoRepository).findById(1L);
//    verify(mockConfig).createClient();
//    verify(mockUserRepository).findById(1L);
//  }
//
//  @Test
//  void testAntiSocialCheck_UserRepositoryFindByKycStatusAndUserInfoIdNotNullReturnsNoItems() {
//    // Setup
//    when(mockUserRepository.findByKycStatusAndUserInfoIdNotNull(KycStatus.DOCUMENT_CONFIRMED))
//        .thenReturn(Collections.emptyList());
//
//    // Run the test
//    antiSocialCheckServiceUnderTest.antiSocialCheck();
//
//    // Verify the results
//    verifyNoInteractions(mockUserInfoRepository);
//    verifyNoInteractions(mockRepository);
//    verify(mockUserRepository).findByKycStatusAndUserInfoIdNotNull(KycStatus.DOCUMENT_CONFIRMED);
//  }
//
//  @Test
//  void testSaveAntiSocialCheckResult() {
//    // Setup
//    final UserAntisocialCheck antisocialCheck =
//        new UserAntisocialCheck(0L, 0L, "code", "caseId", "checkGroupKey");
//    when(mockUserInfoRepository.findById(0L)).thenReturn(Optional.of(new UserInfo(0L)));
//
//    // Configure UserRepository.findById(...).
//    final Optional<User> userOptional = Optional.of(new User("email", "password"));
//    when(mockUserRepository.findById(0L)).thenReturn(userOptional);
//
//    // Run the test
//    antiSocialCheckServiceUnderTest.saveAntiSocialCheckResult(antisocialCheck);
//
//    // Verify the results
//    verify(mockUserEkycStatusChangeHistoryService)
//        .createAntiSocialHistory(
//            0L,
//            KycStatus.NONE,
//            KycStatus.REFINITIV_CHECKED,
//            "[UserAntiSocialCheckJob]: update status to REFINITIV_CHECKED");
//  }
//
//  @Test
//  void testHandleAntiSocialCheckError() {
//    // Setup
//    // Configure UserRepository.findById(...).
//    final Optional<User> userOptional = Optional.of(new User("email", "password"));
//    when(mockUserRepository.findById(0L)).thenReturn(userOptional);
//
//    // Run the test
//    antiSocialCheckServiceUnderTest.handleAntiSocialCheckError(0L, 0L);
//
//    // Verify the results
//    verify(mockUserEkycStatusChangeHistoryService)
//        .createAntiSocialHistory(
//            0L,
//            KycStatus.NONE,
//            KycStatus.DOCUMENT_CONFIRMED,
//            "[UserAntiSocialCheckJob]: fail to anti social check");
//  }
//}
