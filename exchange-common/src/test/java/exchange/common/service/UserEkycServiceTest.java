//package exchange.common.service;
//
//import exchange.common.component.EkycProxy;
//import exchange.common.component.SesManager;
//import exchange.common.config.EKycConfig;
//import exchange.common.constant.CommonConstants;
//import exchange.common.constant.KycStatus;
//import exchange.common.constant.MailNoreplyType;
//import exchange.common.entity.MailNoreply;
//import exchange.common.entity.User;
//import exchange.common.entity.UserEkyc;
//import exchange.common.entity.UserInfo;
//import exchange.common.exception.CustomException;
//import exchange.common.repos.UserEkycRepository;
//import exchange.common.repos.UserRepository;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//
//import java.nio.charset.StandardCharsets;
//import java.util.Base64;
//import java.util.Date;
//import java.util.List;
//import java.util.Optional;
//import java.util.function.Consumer;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//import static org.mockito.MockitoAnnotations.initMocks;
//
//class UserEkycServiceTest {
//
//  @Mock private UserEkycRepository mockRepository;
//  @Mock private UserRepository mockUserRepository;
//  @Mock private UserEkycRequestHistoryService mockUserEkycRequestHistoryService;
//  @Mock private EKycConfig mockEKycConfig;
//  @Mock private SesManager mockSesManager;
//  @Mock private MailNoreplyService mockMailNoreplyService;
//
//  private UserEkycService userEkycServiceUnderTest;
//
//  @BeforeEach
//  void setUp() {
//    initMocks(this);
//    userEkycServiceUnderTest =
//        new UserEkycService(
//            mockRepository,
//            mockUserRepository,
//            mockUserEkycRequestHistoryService,
//            mockEKycConfig,
//            mockSesManager,
//            mockMailNoreplyService);
//  }
//
//  @Test
//  void testInit() {
//    // Setup
//    // Configure MailNoreplyService.findOne(...).
//    final MailNoreply mailNoreply = new MailNoreply();
//    mailNoreply.setCreatedAt(new Date());
//    mailNoreply.setUpdatedAt(new Date());
//    mailNoreply.setMailNoreplyType(MailNoreplyType.ACCOUNT_CREATED);
//    mailNoreply.setFromAddress("fromAddress");
//    mailNoreply.setTitle("title");
//    mailNoreply.setContents("contents");
//    when(mockMailNoreplyService.findOne(MailNoreplyType.USER_EKYC_STARTED)).thenReturn(mailNoreply);
//
//    // Run the test
//    userEkycServiceUnderTest.init();
//    verify(mockMailNoreplyService).findOne(MailNoreplyType.USER_EKYC_STARTED);
//  }
//
//  @Test
//  void testCreateEkyc() throws Exception {
//    // Setup
//    // Configure UserRepository.findById(...).
//    User user = new User("email", "password");
//    UserInfo userInfo = new UserInfo();
//    userInfo.setLastName("lastName");
//    userInfo.setFirstName("firstName");
//    userInfo.setBirthday("1990-01-01");
//    user.setUserInfo(userInfo);
//    user.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
//    final Optional<User> userOptional = Optional.of(user);
//    when(mockUserRepository.findById(0L)).thenReturn(userOptional);
//
//    when(mockEKycConfig.getApiAuthKey()).thenReturn("result");
//    when(mockEKycConfig.getEnterpriseId()).thenReturn("result");
//    when(mockEKycConfig.getTransitionUrl()).thenReturn("result");
//
//    when(mockEKycConfig.getEkycUrlValidTime()).thenReturn(0L);
//    when(mockSesManager.send("fromAddress", "email", "title", "body")).thenReturn(true);
//    this.testInit();
//
//    try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
//      String applicantIdOutputPayload = "{\"applicant_id\": \"123456\"}";
//      String ekycUrlOutputPayload = "{\"ekycUrl\": \"123456\"}";
//      when(mockEKycConfig.getSecret()).thenReturn("the_secret");
//      when(mockEKycConfig.getApplicantIdUrl()).thenReturn("the_applicant_id_url");
//      when(mockEKycConfig.getEkycUrl()).thenReturn("the_ekyc_url");
//      mocked
//          .when(() -> EkycProxy.reqApi(anyString(), anyList(), anyString(), any(Consumer.class)))
//          .thenReturn(applicantIdOutputPayload, ekycUrlOutputPayload);
//
//      // Run the test
//      userEkycServiceUnderTest.createEkyc(0L);
//    }
//    // Verify the results
//    verify(mockSesManager).send("fromAddress", "email", "title", "contents");
//  }
//
//  @Test
//  void testCreateEkyc_UserRepositoryFindByIdReturnsAbsent() {
//    // Setup
//    when(mockUserRepository.findById(0L)).thenReturn(Optional.empty());
//
//    // Run the test
//    assertThrows(CustomException.class, () -> userEkycServiceUnderTest.createEkyc(0L));
//  }
//
//  @Test
//  void testCreateEkyc_UserInfoReturnsAbsent() {
//    // Setup
//    when(mockUserRepository.findById(0L)).thenReturn(Optional.of(new User("email", "password")));
//
//    // Run the test
//    assertThrows(CustomException.class, () -> userEkycServiceUnderTest.createEkyc(0L));
//  }
//
//  @Test
//  void testCreateEkyc_KycStatusIsDone() {
//    // Setup
//    User user = new User("email", "password");
//    UserInfo userInfo = new UserInfo();
//    user.setUserInfo(userInfo);
//    user.setKycStatus(KycStatus.DONE);
//    when(mockUserRepository.findById(0L)).thenReturn(Optional.of(user));
//
//    // Run the test
//    assertThrows(CustomException.class, () -> userEkycServiceUnderTest.createEkyc(0L));
//  }
//
//  @Test
//  void testCreateEkyc_KycStatusIsIllegal() {
//    // Setup
//    User user = new User("email", "password");
//    UserInfo userInfo = new UserInfo();
//    user.setUserInfo(userInfo);
//    user.setKycStatus(KycStatus.DOCUMENT_CONFIRMED);
//    when(mockUserRepository.findById(0L)).thenReturn(Optional.of(user));
//
//    // Run the test
//    assertThrows(CustomException.class, () -> userEkycServiceUnderTest.createEkyc(0L));
//  }
//
//  @Test
//  void testHasValidEkycUrl1() {
//    long now = System.currentTimeMillis();
//    long ekycUrlValidTime = 10800000L;
//    // Setup
//    when(mockEKycConfig.getEkycUrlValidTime()).thenReturn(ekycUrlValidTime);
//    when(mockRepository
//            .existsByUserIdAndCreatedAtGreaterThanEqualAndUrlNotNullAndApplicantIdNotNull(
//                0L, new Date(now - ekycUrlValidTime)))
//        .thenReturn(false);
//
//    // Run the test
//    final boolean result = userEkycServiceUnderTest.hasValidEkycUrl(0L);
//
//    // Verify the results
//    assertFalse(result);
//  }
//
//  @Test
//  void testHasValidEkycUrl2() {
//    // Setup
//    final UserEkyc userEkyc = new UserEkyc("docType", "url", KycStatus.NONE, "referenceId", 0L, 0L);
//    long now = System.currentTimeMillis();
//    long ekycUrlValidTime = 10800000L;
//    userEkyc.setCreatedAt(new Date(now - ekycUrlValidTime));
//    when(mockEKycConfig.getEkycUrlValidTime()).thenReturn(ekycUrlValidTime);
//
//    // Run the test
//    final boolean result = userEkycServiceUnderTest.hasValidEkycUrl(userEkyc);
//
//    // Verify the results
//    assertFalse(result);
//  }
//
//  @Test
//  void testGetAuthorizationToken() {
//    // Setup
//    String token =
//        Base64.getEncoder().encodeToString("testforekyc".getBytes(StandardCharsets.UTF_8));
//    List<String> exceptedResult =
//        List.of(CommonConstants.AUTHORIZATION, CommonConstants.AUTHORIZATION_VAL_PREFIX + token);
//    when(mockEKycConfig.getToken()).thenReturn(token);
//
//    // Run the test
//    final List<String> result = userEkycServiceUnderTest.getAuthorizationToken();
//
//    // Verify the results
//    assertEquals(exceptedResult, result);
//  }
//}
