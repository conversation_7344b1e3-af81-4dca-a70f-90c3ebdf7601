//package exchange.common.service;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import exchange.common.component.CustomTransactionManager;
//import exchange.common.component.SesManager;
//import exchange.common.config.GmoAuthorizationTokenConfiguration;
//import exchange.common.config.GmoConfig;
//import exchange.common.constant.KycStatus;
//import exchange.common.entity.OnetimeBankAccount;
//import exchange.common.entity.User;
//import exchange.common.entity.UserInfo;
//import exchange.common.model.request.GmoDepositForm;
//import exchange.common.model.response.DepositTransactionsData;
//import exchange.common.model.response.VaTransactionsData;
//import exchange.common.repos.GmoServiceRepository;
//import java.util.Date;
//import java.util.List;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.client.RestTemplate;
//@ExtendWith(MockitoExtension.class)
//class GmoDepositServiceTest {
//
//  @Mock
//  private GmoService mockGmoService;
//  @Mock
//  private RestTemplate mockRestTemplate;
//  @Mock
//  private GmoConfig mockGmoConfig;
//  @Mock
//  private UserService mockUserService;
//  @Mock
//  private OnetimeBankAccountService mockOnetimeBankAccountService;
//  @Mock
//  private FiatDepositService mockFiatDepositService;
//  @Mock
//  private AssetService mockAssetService;
//  @Mock
//  private GmoServiceRepository mockGmoServiceRepository;
//  @Mock
//  protected MailNoreplyService mailNoreplyService;
//  @Mock
//  protected SesManager sesManager;
//  @Mock
//  protected CustomTransactionManager customTransactionManager;
//
//  private GmoDepositService gmoDepositServiceTest;
//
//  @BeforeEach
//  void setUp() {
//    gmoDepositServiceTest = new GmoDepositService(mockGmoService,
//        mockRestTemplate,
//        mockGmoConfig,
//        mockUserService,
//        mockOnetimeBankAccountService,
//        mockFiatDepositService,
//        mockAssetService,
//        mockGmoServiceRepository,
//        mailNoreplyService,
//        sesManager);
//    gmoDepositServiceTest.customTransactionManager = customTransactionManager;
//  }
//
//  @Test
//  void invokeGmoDepositTransactions_error() {
//    when(mockGmoConfig.getStgBaseEndpoint()).thenReturn("https://stg-api.gmo-aozora.com/ganb/api");
//    when(mockGmoConfig.getDepositTransactions()).thenReturn("/corporation/v1/va/deposit-transactions");
//    DepositTransactionsData depositTransactionsData = new DepositTransactionsData();
//    depositTransactionsData.setErrorCode("WG_ERR_105");
//    depositTransactionsData.setErrorMessage("Authorization Error:The access token provided is expired, revoked, malformed, or invalid.");
//    ResponseEntity<DepositTransactionsData> responseEntity = new ResponseEntity<>(depositTransactionsData,HttpStatus.ACCEPTED);
//    when(mockRestTemplate.exchange(any(), any(), any(),(Class<DepositTransactionsData>) any())).thenReturn(responseEntity);
//    GmoDepositForm gmoDepositForm = new GmoDepositForm();
//    gmoDepositForm.setRaId("************");
//    gmoDepositForm.setDateFrom(String.valueOf(new Date()));
//    gmoDepositForm.setDateTo(String.valueOf(new Date()));
//    gmoDepositForm.setSortOrderCode("1");
//    gmoDepositForm.setAccessToken("123");
//    gmoDepositServiceTest.invokeGmoDepositTransactions(gmoDepositForm);
//    verify(mockRestTemplate,times(1)).exchange(any(),any(),any(),(Class<DepositTransactionsData>)any());
//  }
//
//  @Test
//  void invokeGmoDepositTransactions_ok() {
//    when(mockGmoConfig.getStgBaseEndpoint()).thenReturn("https://stg-api.gmo-aozora.com/ganb/api");
//    when(mockGmoConfig.getDepositTransactions()).thenReturn("/corporation/v1/va/deposit-transactions");
//    DepositTransactionsData depositTransactionsData = new DepositTransactionsData();
//    depositTransactionsData.setRaId("************");
//    depositTransactionsData.setRaBranchCode("101");
//    depositTransactionsData.setRaBranchNameKana("ﾎｳｼﾞﾝ");
//    depositTransactionsData.setRaAccountNumber("1002540");
//    depositTransactionsData.setRaHolderName("株式会社ｃｏｉｎｂｏｏｋＡＰＩ");
//    depositTransactionsData.setDateFrom("2023-01-04");
//    depositTransactionsData.setDateTo("2023-01-04");
//    depositTransactionsData.setBaseDate("2023-01-04");
//    depositTransactionsData.setBaseTime("16:08:36+09:00");
//    depositTransactionsData.setHasNext("1");
//    depositTransactionsData.setCount("1");
//    VaTransactionsData vaTransactionsData = new VaTransactionsData();
//    vaTransactionsData.setVaId("**********");
//    vaTransactionsData.setTransactionDate("2023-01-04");
//    vaTransactionsData.setValueDate("16:08:36+09:");
//    vaTransactionsData.setVaBranchCode("502");
//    vaTransactionsData.setVaBranchNameKana("ｱｼﾞｻｲ");
//    vaTransactionsData.setVaAccountNumber("7260417");
//    vaTransactionsData.setVaAccountNameKana("ｶ)ｺｲﾝﾌﾞﾂｸｴ-ﾋﾟ-ｱｲ");
//    vaTransactionsData.setDepositAmount("10000");
//    vaTransactionsData.setRemitterNameKana("ｶﾅｶﾅ");
//    vaTransactionsData.setPartnerName("ｼﾞ-ｴﾑｵ-ｱｵｿﾞﾗﾈﾂﾄ");
//    vaTransactionsData.setPaymentBranchName("ﾆｼ");
//    vaTransactionsData.setPartnerName("GMOあおぞらネット銀行");
//    vaTransactionsData.setRemarks("振込  カナカナ あじさい支店 7260417");
//    vaTransactionsData.setItemKey("20221229142850111750");
//    depositTransactionsData.setVaTransactions(List.of(vaTransactionsData));
//    ResponseEntity<DepositTransactionsData> responseEntity = new ResponseEntity<>(depositTransactionsData,HttpStatus.OK);
//    when(mockRestTemplate.exchange(any(), any(), any(),(Class<DepositTransactionsData>) any())).thenReturn(responseEntity);
//    GmoDepositForm gmoDepositForm = new GmoDepositForm();
//    gmoDepositForm.setRaId("************");
//    gmoDepositForm.setDateFrom(String.valueOf(new Date()));
//    gmoDepositForm.setDateTo(String.valueOf(new Date()));
//    gmoDepositForm.setSortOrderCode("1");
//    gmoDepositForm.setAccessToken("123");
//    gmoDepositServiceTest.invokeGmoDepositTransactions(gmoDepositForm);
//    verify(mockRestTemplate,times(1)).exchange(any(),any(),any(),(Class<DepositTransactionsData>)any());
//  }
//
//  @Test
//  public void calculate_gmoAuthorizationTokenConfiguration_is_null () throws Exception {
//    when(mockGmoService.getGmoAuthConfig()).thenReturn(null);
//    gmoDepositServiceTest.calculate();
//  }
//
//  @Test
//  public void calculate_raId_is_empty () throws Exception {
//    GmoAuthorizationTokenConfiguration configuration = new GmoAuthorizationTokenConfiguration();
//    configuration.setAccessToken("ncRvV1WQlQVhjgcTBfmy2RYqERf1FiHz");
//    when(mockGmoService.getGmoAuthConfig()).thenReturn(configuration);
//    when(mockGmoService.accounts(configuration.getAccessToken())).thenReturn(null);
//    gmoDepositServiceTest.calculate();
//  }
//
//  @Test
//  public void calculate_invokeGmoDepositTransactions_is_empty () throws Exception {
//    GmoDepositForm gmoDepositForm = new GmoDepositForm();
//    gmoDepositForm.setRaId("************");
//    gmoDepositForm.setDateFrom(String.valueOf(new Date()));
//    gmoDepositForm.setSortOrderCode("1");
//    gmoDepositForm.setAccessToken("ncRvV1WQlQVhjgcTB");
//    GmoAuthorizationTokenConfiguration configuration = new GmoAuthorizationTokenConfiguration();
//    configuration.setAccessToken("ncRvV1WQlQVhjgcTBfmy2RYqERf1FiHz");
//    when(mockGmoConfig.getStgBaseEndpoint()).thenReturn("https://stg-api.gmo-aozora.com/ganb/api");
//    when(mockGmoConfig.getDepositTransactions()).thenReturn("/corporation/v1/va/deposit-transactions");
//    when(mockGmoService.getGmoAuthConfig()).thenReturn(configuration);
//    when(mockGmoService.accounts(configuration.getAccessToken())).thenReturn("************");
//    DepositTransactionsData depositTransactionsData = new DepositTransactionsData();
//    depositTransactionsData.setErrorCode("WG_ERR_105");
//    depositTransactionsData.setErrorMessage("Authorization Error:The access token provided is expired, revoked, malformed, or invalid.");
//    ResponseEntity<DepositTransactionsData> responseEntity = new ResponseEntity<>(depositTransactionsData,HttpStatus.UNAUTHORIZED);
//    when(mockRestTemplate.exchange(any(), any(), any(),(Class<DepositTransactionsData>) any())).thenReturn(responseEntity);
//    gmoDepositServiceTest.calculate();
//  }
//
//  @Test
//  public void calculate_invokeGmoDepositTransactions_user_is_empty () throws Exception {
//    calculate ();
//    gmoDepositServiceTest.calculate();
//  }
//
//  @Test
//  public void calculate_invokeGmoDepositTransactions_user_is_not_empty () throws Exception {
//    calculate ();
//    OnetimeBankAccount account = new OnetimeBankAccount();
//    account.setUserId(1L);
//    when (mockOnetimeBankAccountService.findOneByAccountNumberAndVaId(any(),any())).thenReturn(account);
//    User user = new User ();
//    user.setId(1L);
//    user.setKycStatus(KycStatus.DOCUMENT_CONFIRMED);
//    when (mockUserService.findOne(any())).thenReturn(user);
//    gmoDepositServiceTest.calculate();
//  }
//  @Test
//  public void calculate_invokeGmoDepositTransactions_user_is_Done () throws Exception {
//    calculate ();
//    OnetimeBankAccount account = new OnetimeBankAccount();
//    account.setUserId(1L);
//    when (mockOnetimeBankAccountService.findOneByAccountNumberAndVaId(any(),any())).thenReturn(account);
//    User user = new User ();
//    user.setId(1L);
//    user.setKycStatus(KycStatus.DONE);
//    when (mockUserService.findOne(any())).thenReturn(user);
//    gmoDepositServiceTest.calculate();
//  }
//  @Test
//  public void calculate_invokeGmoDepositTransactions_user_is_Done_and_equals_kana () throws Exception {
//    calculate ();
//    OnetimeBankAccount account = new OnetimeBankAccount();
//    account.setUserId(1L);
//    when (mockOnetimeBankAccountService.findOneByAccountNumberAndVaId(any(),any())).thenReturn(account);
//    User user = new User ();
//    user.setId(1L);
//    user.setUserInfoId(1L);
//    user.setKycStatus(KycStatus.DONE);
//    UserInfo userInfo = new UserInfo();
//    userInfo.setLastKana("カナ");
//    userInfo.setFirstKana("カナ");
//    user.setUserInfo(userInfo);
//    when (mockUserService.findOne(any())).thenReturn(user);
//    gmoDepositServiceTest.calculate();
//  }
//
//  private void calculate () throws Exception {
//    GmoDepositForm gmoDepositForm = new GmoDepositForm();
//    gmoDepositForm.setRaId("************");
//    gmoDepositForm.setDateFrom(String.valueOf(new Date()));
//    gmoDepositForm.setSortOrderCode("1");
//    gmoDepositForm.setAccessToken("ncRvV1WQlQVhjgcTB");
//    GmoAuthorizationTokenConfiguration configuration = new GmoAuthorizationTokenConfiguration();
//    configuration.setAccessToken("ncRvV1WQlQVhjgcTBfmy2RYqERf1FiHz");
//    when(mockGmoConfig.getStgBaseEndpoint()).thenReturn("https://stg-api.gmo-aozora.com/ganb/api");
//    when(mockGmoConfig.getDepositTransactions()).thenReturn("/corporation/v1/va/deposit-transactions");
//    when(mockGmoService.getGmoAuthConfig()).thenReturn(configuration);
//    when(mockGmoService.accounts(configuration.getAccessToken())).thenReturn("************");
//    DepositTransactionsData depositTransactionsData = new DepositTransactionsData();
//    depositTransactionsData.setRaId("************");
//    depositTransactionsData.setRaBranchCode("101");
//    depositTransactionsData.setRaBranchNameKana("ﾎｳｼﾞﾝ");
//    depositTransactionsData.setRaAccountNumber("1002540");
//    depositTransactionsData.setRaHolderName("株式会社ｃｏｉｎｂｏｏｋＡＰＩ");
//    depositTransactionsData.setDateFrom("2023-01-04");
//    depositTransactionsData.setDateTo("2023-01-04");
//    depositTransactionsData.setBaseDate("2023-01-04");
//    depositTransactionsData.setBaseTime("16:08:36+09:00");
//    depositTransactionsData.setHasNext("1");
//    depositTransactionsData.setCount("1");
//    VaTransactionsData vaTransactionsData = new VaTransactionsData();
//    vaTransactionsData.setVaId("**********");
//    vaTransactionsData.setTransactionDate("2023-01-04");
//    vaTransactionsData.setValueDate("16:08:36+09:");
//    vaTransactionsData.setVaBranchCode("502");
//    vaTransactionsData.setVaBranchNameKana("ｱｼﾞｻｲ");
//    vaTransactionsData.setVaAccountNumber("7260417");
//    vaTransactionsData.setVaAccountNameKana("ｶ)ｺｲﾝﾌﾞﾂｸｴ-ﾋﾟ-ｱｲ");
//    vaTransactionsData.setDepositAmount("10000");
//    vaTransactionsData.setRemitterNameKana("ｶﾅ　ｶﾅ");
//    vaTransactionsData.setPartnerName("ｼﾞ-ｴﾑｵ-ｱｵｿﾞﾗﾈﾂﾄ");
//    vaTransactionsData.setPaymentBranchName("ﾆｼ");
//    vaTransactionsData.setPartnerName("GMOあおぞらネット銀行");
//    vaTransactionsData.setRemarks("振込  カナカナ あじさい支店 7260417");
//    vaTransactionsData.setItemKey("20221229142850111750");
//    depositTransactionsData.setVaTransactions(List.of(vaTransactionsData));
//    ResponseEntity<DepositTransactionsData> responseEntity = new ResponseEntity<>(depositTransactionsData,HttpStatus.OK);
//    when(mockRestTemplate.exchange(any(), any(), any(),(Class<DepositTransactionsData>) any())).thenReturn(responseEntity);
//  }
//}
