package exchange.admin.controller;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.entity.AssetSummary;
import exchange.common.entity.Deposit;
import exchange.common.entity.User;
import exchange.common.entity.Withdrawal;
import exchange.spot.entity.SpotTrade;
import java.util.Date;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
class V1ReportCryptoCurrencyStatementReadTest extends BaseReaderTest {

  @Autowired
  V1ReportCryptoCurrencyStatement controller;

  @BeforeEach
  public void beforeEach() {
    // 資産集計テーブルをリセットする
    executeSql(masterEM, "DELETE FROM asset_summary");

    // 約定テーブルをリセットする
    executeSql(masterEM, "DELETE FROM spot_trade_ada_jpy");
    executeSql(historyEM, "DELETE FROM spot_trade_ada_jpy");

    // 入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");

    // 出金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM withdrawal");
  }

  @DisplayName("userのみ取得される")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220101), toDate(20230101));

    // **************** 結果を検証する ****************
    assertThat(readDto.symbols()).as("シンボルデータあり").isNotEmpty();
    assertThat(readDto.users())
        .as("全てのユーザが取得されている")
        .extracting(User::getId)
        .containsExactly(1L, 2L, 3L, 4L);
    assertThat(readDto.trades()).as("取引なし").isEmpty();
    assertThat(readDto.deposits()).as("入金なし").isEmpty();
    assertThat(readDto.withdrawals()).as("出金なし").isEmpty();
  }

  @DisplayName("所定のassetSummaryが取得される")
  @Test
  void assetSummaryTest() {
    // **************** 初期値を設定する ****************
    insertAssetSummary(masterEM, 1L, Currency.ADA, 1L, "2022-01-31 15:00:00"); // JST 2022-02-01 00:00:00 x targetAt
    insertAssetSummary(masterEM, 2L, Currency.ADA, 1L, "2022-02-01 15:00:00"); // JST 2022-02-02 00:00:00 o
    insertAssetSummary(masterEM, 3L, Currency.ADA, 2L, "2022-02-01 15:00:00"); // JST 2022-02-02 00:00:00 o
    insertAssetSummary(masterEM, 4L, Currency.JPY, 1L, "2022-02-01 15:00:00"); // JST 2022-02-02 00:00:00 o
    insertAssetSummary(masterEM, 5L, Currency.ADA, 1L, "2022-02-02 15:00:00"); // JST 2022-02-03 00:00:00 x targetAt

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.assetSummaries())
        .as("所定の資産合計が取得されている")
        .extracting(AssetSummary::getId)
        .containsExactlyInAnyOrder(2L, 3L, 4L);
  }

  @DisplayName("所定のsportTradeが取得される")
  @Test
  void sportTradeTest() {
    // **************** 初期値を設定する ****************
    insertSpotTrade(masterEM, 1L, 11L, CurrencyPair.ADA_JPY, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertSpotTrade(masterEM, 2L, 12L, CurrencyPair.ADA_JPY, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotTrade(masterEM, 3L, 13L, CurrencyPair.ADA_JPY, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertSpotTrade(masterEM, 4L, 14L, CurrencyPair.ADA_JPY, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x targetAt
    insertSpotTrade(historyEM, 5L, 15L, CurrencyPair.ADA_JPY, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertSpotTrade(historyEM, 6L, 16L, CurrencyPair.ADA_JPY, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotTrade(historyEM, 7L, 17L, CurrencyPair.ADA_JPY, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertSpotTrade(historyEM, 8L, 18L, CurrencyPair.ADA_JPY, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x targetAt

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.trades())
        .as("所定のsportTradeが取得される")
        .extracting(SpotTrade::getId)
        .containsExactlyInAnyOrder(2L, 3L, 6L, 7L);
  }

  @DisplayName("所定の入金が取得される")
  @Test
  void depositTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertDeposit(masterEM, 3L, Currency.JPY, DepositStatus.DONE, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.REJECTED, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> x status
    insertDeposit(masterEM, 5L, Currency.ADA, DepositStatus.DONE, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertDeposit(masterEM, 6L, Currency.ADA, DepositStatus.DONE, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x targetAt

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits())
        .as("所定の入金が取得される")
        .extracting(Deposit::getId)
        .containsExactlyInAnyOrder(2L, 3L, 5L);
  }

  @DisplayName("所定の出金が取得される")
  @Test
  void withdrawalTest() {
    // **************** 初期値を設定する ****************
    insertWithdrawal(masterEM, 1L, Currency.ADA, WithdrawalStatus.DONE, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertWithdrawal(masterEM, 2L, Currency.ADA, WithdrawalStatus.DONE, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertWithdrawal(masterEM, 3L, Currency.JPY, WithdrawalStatus.DONE, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertWithdrawal(masterEM, 4L, Currency.ADA, WithdrawalStatus.REJECTED, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> x status
    insertWithdrawal(masterEM, 5L, Currency.ADA, WithdrawalStatus.DONE, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertWithdrawal(masterEM, 6L, Currency.ADA, WithdrawalStatus.DONE, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x targetAt

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawals())
        .as("所定の出金が取得される")
        .extracting(Withdrawal::getId)
        .containsExactlyInAnyOrder(2L, 3L, 5L);
  }
}