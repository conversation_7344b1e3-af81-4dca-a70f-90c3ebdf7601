package exchange.admin.controller;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.CancelReason;
import exchange.common.constant.CurrencyPair;
import exchange.common.entity.User;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotTrade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
class V1ReportOrderSlipControllerReadTest extends BaseReaderTest {

  @Autowired
  V1ReportOrderSlipController controller;

  @BeforeEach
  public void beforeEach() {
    // 資産集計テーブルをリセットする
    executeSql(masterEM, "DELETE FROM asset_summary");

    // 約定テーブルをリセットする
    executeSql(masterEM, "DELETE FROM spot_order_ada_jpy");
    executeSql(historyEM, "DELETE FROM spot_order_ada_jpy");

    // 約定テーブルをリセットする
    executeSql(masterEM, "DELETE FROM spot_trade_ada_jpy");
    executeSql(historyEM, "DELETE FROM spot_trade_ada_jpy");
  }

  @DisplayName("userのみ取得される")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220101), toDate(20230101));

    // **************** 結果を検証する ****************
    assertThat(readDto.symbols()).as("シンボルデータあり").isNotEmpty();
    assertThat(readDto.users())
        .as("全てのユーザが取得されている")
        .extracting(User::getId)
        .containsExactly(1L, 2L, 3L, 4L);
    assertThat(readDto.trades()).as("取引なし").isEmpty();
  }

  @DisplayName("所定の注文が取得される")
  @Test
  void sportOrderTest() {
    // **************** 初期値を設定する ****************
    // 注文
    insertSpotOrder(masterEM, 11L, CurrencyPair.ADA_JPY, null, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x updatedAt
    insertSpotOrder(masterEM, 12L, CurrencyPair.ADA_JPY, null, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotOrder(masterEM, 13L, CurrencyPair.ADA_JPY, null, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertSpotOrder(masterEM, 14L, CurrencyPair.ADA_JPY, null, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x updatedAt
    insertSpotOrder(historyEM, 15L, CurrencyPair.ADA_JPY, null, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x updatedAt
    insertSpotOrder(historyEM, 16L, CurrencyPair.ADA_JPY, null, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotOrder(historyEM, 17L, CurrencyPair.ADA_JPY, null, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertSpotOrder(historyEM, 18L, CurrencyPair.ADA_JPY, null, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x updatedAt

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.orders())
        .as("所定の注文が取得される")
        .extracting(SpotOrder::getId)
        .containsExactlyInAnyOrder(12L, 13L, 16L, 17L);
  }

  @DisplayName("注文のキャンセル理由が取得される")
  @Test
  void canceledSportOrderTest() {
    // **************** 初期値を設定する ****************
    // 注文
    insertSpotOrder(masterEM, 1L, CurrencyPair.ADA_JPY, CancelReason.CUSTOMER_CANCEL, "2022-02-03 14:59:59.999");
    insertSpotOrder(masterEM, 2L, CurrencyPair.ADA_JPY, CancelReason.SAME_CUSTOMER, "2022-02-03 14:59:59.999");
    insertSpotOrder(historyEM, 3L, CurrencyPair.ADA_JPY, CancelReason.EXPIRED, "2022-02-03 14:59:59.999");
    insertSpotOrder(historyEM, 4L, CurrencyPair.ADA_JPY, CancelReason.FORCE_CANCEL, "2022-02-03 14:59:59.999");

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.orders())
        .as("所定の注文が取得される")
        .extracting(SpotOrder::getId)
        .containsExactlyInAnyOrder(1L, 2L, 3L, 4L);

    assertThat(readDto.orders())
        .as("キャンセル理由")
        .extracting(SpotOrder::getCancelReason)
        .containsExactly(CancelReason.CUSTOMER_CANCEL, CancelReason.SAME_CUSTOMER, CancelReason.EXPIRED, CancelReason.FORCE_CANCEL);
  }

  @DisplayName("所定の約定が取得される")
  @Test
  void sportTradeTest() {
    // **************** 初期値を設定する ****************
    insertSpotTrade(masterEM, 1L, 11L, CurrencyPair.ADA_JPY, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertSpotTrade(masterEM, 2L, 12L, CurrencyPair.ADA_JPY, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotTrade(masterEM, 3L, 13L, CurrencyPair.ADA_JPY, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertSpotTrade(masterEM, 4L, 14L, CurrencyPair.ADA_JPY, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x targetAt
    insertSpotTrade(historyEM, 5L, 15L, CurrencyPair.ADA_JPY, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertSpotTrade(historyEM, 6L, 16L, CurrencyPair.ADA_JPY, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotTrade(historyEM, 7L, 17L, CurrencyPair.ADA_JPY, "2022-02-03 14:59:59.999"); // JST 2022-02-03 23:59:59.999 -> o
    insertSpotTrade(historyEM, 8L, 18L, CurrencyPair.ADA_JPY, "2022-02-03 15:00:00.000"); // JST 2022-02-04 00:00:00.000 -> x targetAt

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.trades())
        .as("所定の約定が取得される")
        .extracting(SpotTrade::getId)
        .containsExactlyInAnyOrder(2L, 3L, 6L, 7L);
  }

  @DisplayName("所定の注文と約定が取得される")
  @Test
  void sportOrderAndTradeTest() {
    // **************** 初期値を設定する ****************
    // 注文
    insertSpotOrder(masterEM, 11L, CurrencyPair.ADA_JPY, null, "2022-02-01 15:00:00.000"); // JST 2022-02-02 00:00:00.000 -> x updatedAt
    insertSpotOrder(masterEM, 12L, CurrencyPair.ADA_JPY, null, "2022-02-01 15:00:00.000"); // JST 2022-02-02 00:00:00.000 -> o from tradeId
    insertSpotOrder(historyEM, 13L, CurrencyPair.ADA_JPY, null, "2022-02-01 15:00:00.000"); // JST 2022-02-02 00:00:00.000 -> x updatedAt
    insertSpotOrder(historyEM, 14L, CurrencyPair.ADA_JPY, null, "2022-02-01 15:00:00.000"); // JST 2022-02-02 00:00:00.000 -> o from tradeId
    // 約定
    insertSpotTrade(masterEM, 1L, 11L, CurrencyPair.ADA_JPY, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertSpotTrade(masterEM, 2L, 12L, CurrencyPair.ADA_JPY, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o
    insertSpotTrade(historyEM, 3L, 13L, CurrencyPair.ADA_JPY, "2022-02-02 14:59:59.999"); // JST 2022-02-02 23:59:59.999 -> x targetAt
    insertSpotTrade(historyEM, 4L, 14L, CurrencyPair.ADA_JPY, "2022-02-02 15:00:00.000"); // JST 2022-02-03 00:00:00.000 -> o

    // **************** 処理を実行する ****************
    final var readDto = controller.read(toDate(20220203), toDate(20220204));

    // **************** 結果を検証する ****************
    assertThat(readDto.orders())
        .as("所定の注文が取得される")
        .extracting(SpotOrder::getId)
        .containsExactlyInAnyOrder(12L, 14L);

    assertThat(readDto.trades())
        .as("所定の約定が取得される")
        .extracting(SpotTrade::getId)
        .containsExactlyInAnyOrder(2L, 4L);
  }
}