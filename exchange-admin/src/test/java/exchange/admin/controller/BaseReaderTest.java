package exchange.admin.controller;

import exchange.common.component.DataSourceManager;
import exchange.common.component.HistoricalDataSourceManager;
import exchange.common.constant.CancelReason;
import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.Exchange;
import exchange.common.constant.FiatDepositStatus;
import exchange.common.constant.FiatWithdrawalStatus;
import exchange.common.constant.TradeType;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.entity.Symbol;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import javax.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = {exchange.admin.Application.class})
@AutoConfigureWebTestClient
@ActiveProfiles({"test"})
@Slf4j
public abstract class BaseReaderTest {

  private final Map<CurrencyPair, Long> currencyPairMap =
      new HashMap<>() {
        {
          put(CurrencyPair.ADA_JPY, 4L);
        }
      };

  protected Symbol createSymbol(CurrencyPair currencyPair) {
    final var symbol = new Symbol();
    symbol.setId(currencyPairMap.get(currencyPair));
    symbol.setCurrencyPair(currencyPair);
    symbol.setTradeType(TradeType.SPOT);
    return symbol;
  }

  @Autowired private DataSourceManager rdbDataSourceManager;
  @Autowired private HistoricalDataSourceManager dwhDataSourceManager;

  protected EntityManager masterEM;
  protected EntityManager historyEM;

  @BeforeAll
  void init() {
    masterEM = rdbDataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    historyEM = dwhDataSourceManager.getHistoricalEntityManagerFactory().createEntityManager();

    // ユーザの追加
    executeSql(masterEM, "DELETE FROM user");
    executeSql(
        masterEM,
        "insert into user (id, email, password, anti_phishing_code, account_non_expired, account_non_locked,\n"
            + "                  credentials_non_expired, enabled, user_status, kyc_status, user_kyc_id, level,\n"
            + "                  user_info_id, user_info_corporate_id, old_user_id, trade_uncapped, insider,\n"
            + "                  risker, created_at, updated_at)"
            + " VALUES "
            // ユーザ1 個人
            + "(1, 'email1', 'password', null, true, true, true, true, 'ACTIVE', 'DONE', 1, 2, 1, null, null, false, false, false, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ2 法人
            + "(2, 'email2', 'password', null, true, true, true, true, 'ACTIVE', 'DONE', 2, 2, 2, null, null, false, false, false, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ3 個人
            + "(3, 'email3', 'password', null, true, true, true, true, 'ACTIVE', 'DONE', 3, 2, 3, null, null, false, false, false, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ4 個人-運用口座
            + "(4, 'email4', 'password', null, true, true, true, true, 'ACTIVE', 'DONE', 4, 2, 4, null, null, true, false, false, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ5 個人-口座作成中
            + "(5, 'email5', 'password', null, true, true, true, true, 'ACTIVE', 'DONE', 5, 2, null, null, null, true, false, false, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000')");

    executeSql(masterEM, "DELETE FROM user_authority");
    executeSql(
        masterEM,
        "INSERT INTO user_authority (id, user_id, authority, created_at, updated_at)"
            + " VALUES "
            // ユーザ1 個人
            + "(1, 1, 'PERSONAL', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ2 法人
            + "(2, 2, 'CORPORATE', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ3 個人
            + "(3, 3, 'PERSONAL', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ4 運用口座
            + "(4, 4, 'PERSONAL', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ5 個人 - 口座作成中
            + "(5, 5, 'PERSONAL', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000');");

    executeSql(masterEM, "DELETE FROM user_info");
    executeSql(
        masterEM,
        "insert into user_info (id, user_id, first_name, last_name, first_kana, last_kana, nationality,\n"
            + "                       zip_code, prefecture, city, address, building, birthday, gender,\n"
            + "                       phone_number, occupation, industry, work_place, position, income,\n"
            + "                       financial_assets, purpose, investment_purposes,\n"
            + "                       crypto_experience, fx_experience, stocks_experience, fund_experience,\n"
            + "                       application_history, application_history_other, foreign_peps, country,\n"
            + "                       antisocial_status, residence_card_expired_at, created_at, updated_at)"
            + " VALUES "
            // ユーザ1 個人
            + "(1, 1, '', '', '', '', '', '', '', '', '', '', '', 1, '', 2, 2, '', '', 8, 8, 0, 0, 4, 4, 4, 4, 1, null, false, 'JP', null, null, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ2 法人
            + "(2, 2, '', '', '', '', '', '', '', '', '', '', '', 1, '', 2, 2, '', '', 8, 8, 0, 0, 4, 4, 4, 4, 1, null, false, 'JP', null, null, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ3 個人
            + "(3, 3, '', '', '', '', '', '', '', '', '', '', '', 1, '', 2, 2, '', '', 8, 8, 0, 0, 4, 4, 4, 4, 1, null, false, 'JP', null, null, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ4 運用口座
            + "(4, 4, '', '', '', '', '', '', '', '', '', '', '', 1, '', 2, 2, '', '', 8, 8, 0, 0, 4, 4, 4, 4, 1, null, false, 'JP', null, null, '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000');"
        // ユーザ5 個人 - 口座作成中
    );

    executeSql(masterEM, "DELETE FROM user_kyc");
    executeSql(
        masterEM,
        "insert into user_kyc (id, user_id, kyc_type, kyc_status, kyc_mail_status, mail_send_at,\n"
            + "                      judging_comment, aml_cft_comment, antisocial_status, created_at, updated_at)"
            + " VALUES "
            // ユーザ1 個人
            + "(1, 1, 'NONE', 'DONE', 'NONE', null, '', '', 'BEFORE_EXAMINATION', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ2 法人
            + "(2, 2, 'NONE', 'DONE', 'NONE', null, '', '', 'BEFORE_EXAMINATION', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ3 個人
            + "(3, 3, 'NONE', 'DONE', 'NONE', null, '', '', 'BEFORE_EXAMINATION', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ4 運用口座
            + "(4, 4, 'NONE', 'DONE', 'NONE', null, '', '', 'BEFORE_EXAMINATION', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000'),"
            // ユーザ5 個人 - 口座作成中
            + "(5, 5, 'NONE', 'DONE', 'NONE', null, '', '', 'BEFORE_EXAMINATION', '2022-02-02 02:02:02.000', '2022-02-02 02:02:02.000');"
    );
  }

  protected Date toDate(Integer date) {
    final var zdt = ZonedDateTime.parse(
        date + " 00:00:00+0900",
        DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ssZ"));
    return Date.from(zdt.toInstant());
  }

  /**
   * ローカルの時間をUTCにしている場合
   * @param date
   * @return
   */
  protected Date toDateLocalUtc(Integer date) {
    final var zdt = ZonedDateTime.parse(
        date + " 00:00:00+0000",
        DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ssZ"));
    return Date.from(zdt.toInstant());
  }

  protected void executeSql(EntityManager entityManager, String sqlString) {
    log.debug("sql for test data:" + sqlString);
    entityManager.getTransaction().begin();
    entityManager.createNativeQuery(sqlString).executeUpdate();
    entityManager.getTransaction().commit();
  }

  protected void insertAssetSummary(
      EntityManager entityManager, Long id, Currency currency, Long userId, String targetAt) {
    final var sql =
        "insert into asset_summary (id, user_id, target_at, currency, current_amount, jpy_conversion,\n"
            + "                           deposit_amount, deposit_amount_jpy, deposit_fee, deposit_fee_jpy,\n"
            + "                           withdrawal_amount, withdrawal_amount_jpy, withdrawal_fee,\n"
            + "                           withdrawal_fee_jpy, transaction_fee, transaction_fee_jpy,\n"
            + "                           spot_trade_buy_amount, spot_trade_buy_amount_jpy, spot_trade_sell_amount,\n"
            + "                           spot_trade_sell_amount_jpy, spot_trade_fee, spot_trade_fee_jpy,\n"
            + "                           created_at, updated_at)"
            + "values ("
            + id
            + "," + userId
            + ",'" + targetAt + "'"
            + "," + "'" + currency.name() + "'" // currency
            + ",10000000.********************"
            + ",0.********************"
            + ",0 ,0, 0, 0" // 2行目
            + ",0 ,0, 0" // 3行目
            + ",0 ,0, 0" // 4行目
            + ",0 ,0, 0" // 5行目
            + ",0 ,0, 0" // 6行目
            + ", '2022-02-02 02:02:02.000','2022-02-02 02:02:02.000'" // 7行目
            + ");";
    executeSql(entityManager, sql);
  }

  protected void insertSpotOrder(
      EntityManager entityManager,
      Long orderId,
      CurrencyPair currencyPair,
      CancelReason cancelReason,
      String updatedAt) {
    final var currencyPairName = currencyPair.toLowerCase();
    final var sql = "INSERT INTO spot_order_" + currencyPairName + " ("
        + "id, symbol_id, user_id, order_side, order_type, order_channel, price, average_price, amount, remaining_amount, order_status, order_operator, post_only, cancel_reason, created_at, updated_at"
        + ") VALUES ("
        + orderId + "," // 注文ID
        + currencyPairMap.get(currencyPair) + "," // symbol_id
        + "1, 'BUY', 'MARKET', 'PC_WEB', 999.********************, 0.********************, 998.********************, 0.********************, 'UNFILLED', 'USER', false,"
        + (cancelReason == null ? " null, " : "'" + cancelReason.name() + "',")
        + "'2022-07-19 11:31:53',"
        + "'" + updatedAt + "'"
        + ");";
    executeSql(entityManager, sql);
  }

  protected void insertSpotTrade(
      EntityManager entityManager,
      Long tradeId,
      Long orderId,
      CurrencyPair currencyPair,
      String createdAt) {
    final var currencyPairName = currencyPair.toLowerCase();
    final var sql = "insert into spot_trade_" + currencyPairName + "("
        + " id, symbol_id, user_id, order_side,"
        + " order_type, order_channel, price, amount,"
        + " jpy_conversion, trade_action, order_id, fee,"
        + " target_order_id, target_user_id, asset_amount, created_at, updated_at)"
        + "values ("
        + tradeId + "," // tradeId
        + currencyPairMap.get(currencyPair) + "," // symbol_id
        + "1, 'BUY', 'LIMIT', 'PC_WEB', 0, 0, 0, 'TAKER', "
        + orderId + "," // orderId
        + " 0, 0, 0, 0, "
        + "'" + createdAt + "',"
        + "'2022-07-19 11:31:53'"
        + ");";
    executeSql(entityManager, sql);
  }

  protected void insertFiatDeposit(
      EntityManager entityManager,
      Long id,
      FiatDepositStatus fiatDepositStatus,
      String updatedAt) {
    final var sql = "insert into fiat_deposit (id, user_id, bank_account_id, onetime_bank_account_id, amount, fee,\n"
        + "                          fiat_deposit_status, fiat_deposit_sub_status, comment, created_at,\n"
        + "                          updated_at)"
        + "values ("
        + id + "," // id
        + "1, 1, 1, 0, 0,"
        + "'" + fiatDepositStatus.name() + "',"
        + "null, null, '2022-07-19 11:31:53',"
        + "'" + updatedAt + "'"
        + ");";
    executeSql(entityManager, sql);

    final var sql2 = "insert into onetime_bank_account (id, user_id, branch_code, branch_name, account_number, created_at,\n"
        + "                                  updated_at, va_type_code, va_type_name, va_holder_name_kana,\n"
        + "                                  va_id)\n"
        + "values ("
        + id + "," // id
        + "1, '', '', "
        + "'" + id + "', " // account_number
        + "'2022-07-19 11:31:53', '2022-07-19 11:31:53', '', '', '', ''"
        + ");";
    executeSql(entityManager, sql2);
  }

  protected void insertFiatWithdrawal(
      EntityManager entityManager,
      Long id,
      FiatWithdrawalStatus fiatWithdrawalStatus,
      String updatedAt) {
    final var sql = "insert into fiat_withdrawal (id, user_id, bank_account_id, amount, fee, fiat_withdrawal_status,\n"
        + "                             comment, apply_no, created_at, updated_at, created_by, updated_by)"
        + "values ("
        + id + "," // id
        + "1, 1, 0, 0,"
        + "'" + fiatWithdrawalStatus.name() + "',"
        + "null, null, '2022-07-19 11:31:53',"
        + "'" + updatedAt + "',"
        + "null, null"
        + ");";
    executeSql(entityManager, sql);
  }

  protected void insertDeposit(
      EntityManager entityManager,
      Long id,
      Currency currency,
      DepositStatus depositStatus,
      String updatedAt) {
    final var sql = "insert into deposit (id, user_id, currency, deposit_account_id, deposit_channel, deposit_type,\n"
        + "                     deposit_purpose, amount, asset_amount, fee, jpy_conversion, address,\n"
        + "                     transaction_id, transaction_index, deposit_status, comment, ownertype,\n"
        + "                     recipienttype, last_name, first_name, last_name_kana, first_name_kana,\n"
        + "                     last_name_english, first_name_english, legalname, legalname_kana,\n"
        + "                     legalname_english, addresstype, exchange, area, aregion, purpose, risk_score,\n"
        + "                     transaction_hash, created_at, updated_at)"
        + "values ("
        + id + "," // id
        + "1,"
        + "'" + currency.name() + "',"
        + "1, DEFAULT, null, null, 1.********************, null, 1.********************, 1.********************, 'address', null, null, "
        + "'" + depositStatus.name() + "',"
        + " null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '2023-02-12 15:44:14.000', "
        + "'" + updatedAt + "'"
        + ");";
    executeSql(entityManager, sql);
  }

  protected void insertWithdrawal(
      EntityManager entityManager,
      Long id,
      Currency currency,
      WithdrawalStatus withdrawalStatus,
      String updatedAt) {
    final var sql = "INSERT INTO exchange.withdrawal (id, user_id, currency, withdrawal_channel, withdrawal_type, withdrawal_purpose, amount, asset_amount, withdrawal_fee, transaction_fee, jpy_conversion, comment, withdrawal_account_id, address, transaction_id, withdrawal_status, risk_score, failed_number, transaction_hash, created_at, updated_at, created_by, updated_by) "
        + "VALUES ("
        + id + "," // id
        + "1,"
        + "'" + currency.name() + "',"
        + "DEFAULT, null, null, 1.********************, 0.**********, 0.********************, 0.********************, null, null, 1, 'address', null,"
        + "'" + withdrawalStatus.name() + "',"
        + "null, DEFAULT, null, DEFAULT,"
        + "'" + updatedAt + "',"
        + " null, null);";
    executeSql(entityManager, sql);
  }
}
