package exchange.admin.controller;

import exchange.admin.entity.AdminUser;
import exchange.common.constant.Currency;
import exchange.common.constant.FiatWithdrawalStatus;
import exchange.common.entity.Asset;
import exchange.common.entity.BankAccount;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.User;
import exchange.common.model.request.FiatWithdrawalCsvForm;
import exchange.common.model.request.FiatWithdrawalInputForm;
import exchange.common.model.request.FiatWithdrawalPutForm;
import exchange.common.service.AssetService;
import exchange.common.service.BankAccountService;
import exchange.common.service.FiatWithdrawalService;
import exchange.common.service.UserService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"local"})
class V1FiatWithdrawalRestControllerTest {
    @MockBean
    BankAccountService bankAccountService;
    @SpyBean
    V1FiatWithdrawalRestController controller;
    @MockBean
    AssetService assetService;
    @SpyBean
    FiatWithdrawalService fiatWithdrawalService;
    @SpyBean
    UserService userService;

    @Test
    @DisplayName("管理画面出金登録成功:fiatWithdrawalAudit登録")
    @WithMockUser("testUser")
    void postFormRegist() throws Exception {
        var user = new AdminUser();
        var asset = new Asset();
        var form = new FiatWithdrawalInputForm();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(20L);
        user.setEmail("test");
        user.setPassword("test");
        form.setAmount(new BigDecimal("1"));
        form.setUserId(1L);
        form.setBankAccountId(111L);
        form.setFee(new BigDecimal(400));
        form.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING.toString());
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(new User()).when(userService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.post(user, form);
    }

    @Test
    @DisplayName("管理画面出金登録失敗:fiatWithdrawalAudit登録しない")
    void postFormRegistTransactionFailed() throws Exception {
        var user = new AdminUser();
        var asset = new Asset();
        var form = new FiatWithdrawalInputForm();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        form.setAmount(new BigDecimal("1"));
        form.setUserId(1L);
        form.setBankAccountId(111L);
        form.setFee(new BigDecimal(400));
        form.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING.toString());
        doThrow(new RuntimeException()).when(fiatWithdrawalService).saveAudit(any(), any());
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(new User()).when(userService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.post(user, form);
    }

    @Test
    @DisplayName("管理画面csv出金登録成功:fiatWithdrawalAudit登録")
    void postCsvRegist() throws Exception {
        var user = new AdminUser();
        var asset = new Asset();
        var input = new FiatWithdrawalCsvForm();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        FiatWithdrawalCsvForm[] form = new FiatWithdrawalCsvForm[1];
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        input.setAmount(new BigDecimal("1"));
        input.setUserId(1L);
        input.setBankAccountId(111L);
        input.setFee(new BigDecimal(800));
        input.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING.toString());
        form[0] = input;
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(new User()).when(userService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.post(user, form);
    }

    @Test
    @DisplayName("管理画面csv出金登録成功:fiatWithdrawalAudit登録しない")
    void postCsvRegistTransactionFailed() throws Exception {
        var user = new AdminUser();
        var asset = new Asset();
        var input = new FiatWithdrawalCsvForm();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        FiatWithdrawalCsvForm[] form = new FiatWithdrawalCsvForm[1];
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        input.setAmount(new BigDecimal("1"));
        input.setUserId(1L);
        input.setBankAccountId(111L);
        input.setFee(new BigDecimal(800));
        input.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING.toString());
        form[0] = input;
        doThrow(new RuntimeException()).when(fiatWithdrawalService).saveAudit(any(), any());
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(new User()).when(userService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.post(user, form);
    }

    @Test
    @DisplayName("管理画面出金put成功: fiatWithdrawalAudit登録")
    @WithMockUser("testAdmin2")
    void put() throws Exception {
        var user = new AdminUser();
        var asset = new Asset();
        var form = new FiatWithdrawalPutForm();
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        form.setId(11L);
        form.setFiatWithdrawalStatus(FiatWithdrawalStatus.DONE.toString());
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(new User()).when(userService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));
        controller.put(user, form);
    }

    @Test
    @DisplayName("管理画面出金put失敗: fiatWithdrawalAudit登録しない")
    void putTransactionFailed() throws Exception {
        var fiatwithdrawal = new FiatWithdrawal();
        var user = new AdminUser();
        var asset = new Asset();
        var form = new FiatWithdrawalPutForm();
        fiatwithdrawal.setId(1L);
        fiatwithdrawal.setUserId(1L);
        fiatwithdrawal.setBankAccountId(1L);
        fiatwithdrawal.setAmount(new BigDecimal("11111"));
        fiatwithdrawal.setFee(new BigDecimal("100"));
        fiatwithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING);
        fiatwithdrawal.setCreatedAt(new Date());
        fiatwithdrawal.setUpdatedAt(new Date());
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        form.setId(1L);
        form.setFiatWithdrawalStatus(FiatWithdrawalStatus.DONE.toString());
        doThrow(new RuntimeException()).when(fiatWithdrawalService).saveAudit(any(), any());
        doReturn(fiatwithdrawal).when(fiatWithdrawalService).findOne(any());
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(new BankAccount()).when(bankAccountService).findOne(any());
        doReturn(new User()).when(userService).findOne(any());
        doReturn(asset).when(assetService).findOne(any(), ArgumentMatchers.eq(Currency.JPY));

        controller.put(user, form);
    }


    @Test
    @DisplayName("管理画面出金cancel成功: fiatWithdrawalAudit登録")
    void delete() throws Exception {
        var fiatwithdrawal = new FiatWithdrawal();
        var user = new AdminUser();
        var asset = new Asset();
        fiatwithdrawal.setId(1L);
        fiatwithdrawal.setUserId(1L);
        fiatwithdrawal.setBankAccountId(1L);
        fiatwithdrawal.setAmount(new BigDecimal("11111"));
        fiatwithdrawal.setFee(new BigDecimal("100"));
        fiatwithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING);
        fiatwithdrawal.setCreatedAt(new Date());
        fiatwithdrawal.setUpdatedAt(new Date());
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(fiatwithdrawal).when(fiatWithdrawalService).findOne(any());
        controller.delete(user, fiatwithdrawal.getId());
    }

    @Test
    @DisplayName("管理画面出金cancel失敗: fiatWithdrawalAudit登録しない")
    void deleteTransactionFailed() throws Exception {
        var fiatwithdrawal = new FiatWithdrawal();
        var user = new AdminUser();
        var asset = new Asset();
        fiatwithdrawal.setId(1L);
        fiatwithdrawal.setUserId(1L);
        fiatwithdrawal.setBankAccountId(1L);
        fiatwithdrawal.setAmount(new BigDecimal("11111"));
        fiatwithdrawal.setFee(new BigDecimal("100"));
        fiatwithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.APPROVING);
        fiatwithdrawal.setCreatedAt(new Date());
        fiatwithdrawal.setUpdatedAt(new Date());
        asset.setUserId(1L);
        asset.setOnhandAmount(new BigDecimal("***********"));
        asset.setLockedAmount(new BigDecimal("***********"));
        user.setId(1L);
        user.setEmail("test");
        user.setCreatedAt(new Date());
        user.setPassword("test");
        doThrow(new RuntimeException()).when(fiatWithdrawalService).saveAudit(any(), any());
        doReturn(true).when(controller).hasAdminAuthority(any());
        doReturn(fiatwithdrawal).when(fiatWithdrawalService).findOne(any());
        controller.delete(user, fiatwithdrawal.getId());
    }
}