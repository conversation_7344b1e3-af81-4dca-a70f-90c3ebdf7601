package exchange.admin.controller;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.TmsStatus;
import exchange.common.entity.FrequentCryptoDepositUser;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class V1FrequentCryptoDepositRestControllerReadTest extends BaseReaderTest {

  @Autowired
  private V1FrequentCryptoDepositRestController controller;

  @BeforeEach
  public void beforeEach() {
    // 暗号資産高頻度入庫検知テーブルをリセットする
    executeSql(masterEM, "DELETE FROM frequent_crypto_deposit_user");
  }

  @Test
  @DisplayName("データ登録なしかつ、パラメータnull1")
  public void emptyTest1() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = controller.readPageData(null, null, null, null, 0, 30);

    // **************** 結果を検証する ****************
    assertThat(readDto.frequentCryptoDepositUsers()).as("テーブルデータなし").isEmpty();
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(0L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(30);
  }

  @Test
  @DisplayName("データ登録なしかつ、パラメータnull2")
  public void emptyTest2() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = controller.readAllData(null, null, null, null);

    // **************** 結果を検証する ****************
    assertThat(readDto.frequentCryptoDepositUsers()).as("テーブルデータなし").isEmpty();
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(0L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(Integer.MAX_VALUE);
  }

  @Test
  @DisplayName("1ページ目取得")
  public void readPageDataTest1() {
    // **************** 初期値を設定する ****************
    insertFrequentCryptoDepositUser(masterEM, 1L, 1L, TmsStatus.OPEN, "2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 4L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 5L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.readPageData(null, null, null, null, 0, 3);

    // **************** 結果を検証する ****************
    assertThat(readDto.frequentCryptoDepositUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.frequentCryptoDepositUsers())
        .as("id")
        .extracting(FrequentCryptoDepositUser::getId)
        .containsExactly(5L, 4L, 3L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(5L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(3);
  }

  @Test
  @DisplayName("2ページ目取得")
  public void readPageDataTest2() {
    // **************** 初期値を設定する ****************
    insertFrequentCryptoDepositUser(masterEM, 1L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 4L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 5L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.readPageData(null, null, null, null, 1, 3);

    // **************** 結果を検証する ****************
    assertThat(readDto.frequentCryptoDepositUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.frequentCryptoDepositUsers())
        .as("id")
        .extracting(FrequentCryptoDepositUser::getId)
        .containsExactly(2L, 1L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(5L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(1);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(3);
  }

  @Test
  @DisplayName("検索条件テスト1 ページングデータ取得関数")
  public void readConditionTest1() {
    // **************** 初期値を設定する ****************
    insertFrequentCryptoDepositUser(masterEM, 1L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-02 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-03 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 4L, 1L, TmsStatus.CLOSE,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 5L, 1L, TmsStatus.AML_SECOND_SCREENING,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 6L, 2L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 7L, 2L, TmsStatus.OPEN,"2023-03-02 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 8L, 2L, TmsStatus.OPEN,"2023-03-03 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 9L, 2L, TmsStatus.CLOSE,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 10L, 2L, TmsStatus.AML_SECOND_SCREENING,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.readPageData(1L, toDateLocalUtc(20230301).getTime(), toDateLocalUtc(20230301).getTime(),TmsStatus.OPEN, 0, 30);

    // **************** 結果を検証する ****************
    assertThat(readDto.frequentCryptoDepositUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.frequentCryptoDepositUsers())
        .as("id")
        .extracting(FrequentCryptoDepositUser::getId)
        .containsExactly(1L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(1L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(30);
  }

  @Test
  @DisplayName("検索条件テスト2 全件取得関数")
  public void readConditionTest2() {
    // **************** 初期値を設定する ****************
    insertFrequentCryptoDepositUser(masterEM, 1L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-02 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-03 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 4L, 1L, TmsStatus.CLOSE,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 5L, 1L, TmsStatus.AML_SECOND_SCREENING,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 6L, 2L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 7L, 2L, TmsStatus.OPEN,"2023-03-02 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 8L, 2L, TmsStatus.OPEN,"2023-03-03 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 9L, 2L, TmsStatus.CLOSE,"2023-03-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 10L, 2L, TmsStatus.AML_SECOND_SCREENING,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.readAllData(1L, toDateLocalUtc(20230301).getTime(), toDateLocalUtc(20230301).getTime(),TmsStatus.OPEN);

    // **************** 結果を検証する ****************
    assertThat(readDto.frequentCryptoDepositUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.frequentCryptoDepositUsers())
        .as("id")
        .extracting(FrequentCryptoDepositUser::getId)
        .containsExactly(1L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(1L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(Integer.MAX_VALUE);
  }

  // utils
  private void insertFrequentCryptoDepositUser(
      EntityManager entityManager,
      Long id,
      Long userId,
      TmsStatus tmsStatus,
      String targetAt) {
    final var sql = "INSERT INTO frequent_crypto_deposit_user (id, target_at, user_id, deposit_count, tms_status,\n"
        + "                                                   created_at, updated_at)\n"
        + "VALUES (" + id + ", '" + targetAt + "', " + userId + ", 3, '" + tmsStatus.name() + "', '2023-03-07 11:45:58.000',\n"
        + "        '2023-03-07 11:46:01.000');";
    executeSql(entityManager, sql);
  }
}
