package exchange.admin.controller;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.TmsStatus;
import exchange.common.entity.QuickCryptoTransferUser;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class V1QuickCryptoTransferUserRestControllerReadTest extends BaseReaderTest {

  @Autowired
  private V1QuickCryptoTransferUserRestController controller;

  @BeforeEach
  public void beforeEach() {
    // 入庫後短期出金検知テーブルをリセットする
    executeSql(masterEM, "DELETE FROM quick_crypto_transfer_user");
  }

  @Test
  @DisplayName("データ登録なしかつ、パラメータnull")
  public void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = controller.read(null, null, null, null, 0, 30);

    // **************** 結果を検証する ****************
    assertThat(readDto.quickCryptoTransferUsers()).as("テーブルデータなし").isEmpty();
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(0L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(30);
  }

  @Test
  @DisplayName("1ページ目取得")
  public void readPageDataTest1() {
    // **************** 初期値を設定する ****************
    insertQuickCryptoTransferUser(masterEM, 1L, 1L, TmsStatus.OPEN, "2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 4L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 5L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.read(null, null, null, null, 0, 3);

    // **************** 結果を検証する ****************
    assertThat(readDto.quickCryptoTransferUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.quickCryptoTransferUsers())
        .as("id")
        .extracting(QuickCryptoTransferUser::getId)
        .containsExactly(5L, 4L, 3L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(5L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(3);
  }

  @Test
  @DisplayName("2ページ目取得")
  public void readPageDataTest2() {
    // **************** 初期値を設定する ****************
    insertQuickCryptoTransferUser(masterEM, 1L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 4L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 5L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.read(null, null, null, null, 1, 3);

    // **************** 結果を検証する ****************
    assertThat(readDto.quickCryptoTransferUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.quickCryptoTransferUsers())
        .as("id")
        .extracting(QuickCryptoTransferUser::getId)
        .containsExactly(2L, 1L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(5L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(1);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(3);
  }

  @Test
  @DisplayName("検索条件テスト")
  public void readConditionTest1() {
    // **************** 初期値を設定する ****************
    insertQuickCryptoTransferUser(masterEM, 1L, 1L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 2L, 1L, TmsStatus.OPEN,"2023-03-02 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 3L, 1L, TmsStatus.OPEN,"2023-03-03 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 4L, 1L, TmsStatus.CLOSE,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 5L, 1L, TmsStatus.AML_SECOND_SCREENING,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 6L, 2L, TmsStatus.OPEN,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 7L, 2L, TmsStatus.OPEN,"2023-03-02 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 8L, 2L, TmsStatus.OPEN,"2023-03-03 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 9L, 2L, TmsStatus.CLOSE,"2023-03-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 10L, 2L, TmsStatus.AML_SECOND_SCREENING,"2023-03-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = controller.read(1L, toDateLocalUtc(20230301).getTime(), toDateLocalUtc(20230301).getTime(),TmsStatus.OPEN, 0, 30);

    // **************** 結果を検証する ****************
    assertThat(readDto.quickCryptoTransferUsers()).as("テーブルデータあり").isNotEmpty();
    assertThat(readDto.quickCryptoTransferUsers())
        .as("id")
        .extracting(QuickCryptoTransferUser::getId)
        .containsExactly(1L);
    assertThat(readDto.numberOfTotalRecord()).as("総件数").isEqualTo(1L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(30);
  }

  private void insertQuickCryptoTransferUser(
      EntityManager entityManager,
      Long id,
      Long userId,
      TmsStatus tmsStatus,
      String targetAt) {
    final var sql = "INSERT INTO quick_crypto_transfer_user (id, target_at, user_id, tms_status,\n"
        + "                                                   created_at, updated_at)\n"
        + "VALUES (" + id + ", '" + targetAt + "', " + userId + ", '" + tmsStatus + "', '2023-03-07 11:45:58.000',\n"
        + "        '2023-03-07 11:46:01.000');";
    executeSql(entityManager, sql);
  }
}
