package exchange.admin.controller;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.admin.controller.V1FrequentCryptoDepositRestController.ReadData;
import exchange.admin.model.response.FrequentCryptoDepositResponse;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.common.entity.User;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class V1FrequentCryptoDepositRestControllerProcessTest extends BaseProcessorTest {

  @Autowired
  private V1FrequentCryptoDepositRestController controller;

  @Test
  @DisplayName("データが空の場合")
  public void emptyTest1() {
    // **************** 初期値を設定する ****************
    final var readDto = new ReadData(List.of(), 0L, 0, 30);

    // **************** 処理を実行する ****************
    final var response = controller.processToPageData(readDto);

    // **************** 結果を検証する ****************
    assertThat(response.getContent()).as("データなし").isEmpty();
    assertThat(response.getTotalPages()).as("総ページ数").isEqualTo(0);
    assertThat(response.getTotalElements()).as("総件数").isEqualTo(0L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(30);
  }

  @Test
  @DisplayName("データが存在する場合")
  public void notEmptyTest1() {
    // **************** 初期値を設定する ****************
    final var readDto = new ReadData(List.of(
        generateEntity(1L, 1L, "email1", 2, TmsStatus.OPEN, "2023/03/01 12:34:56.789",
            "2023/03/01 12:34:56.789"),
        generateEntity(2L, 2L, "email2", 3, TmsStatus.CLOSE, "2023/03/02 00:00:00.000",
            "2023/03/02 12:34:56.789")
    ), 2L, 0, 30);

    // **************** 処理を実行する ****************
    final var response = controller.processToPageData(readDto);

    // **************** 結果を検証する ****************
    assertThat(response.getContent()).as("データあり").isNotEmpty();
    assertThat(response.getContent()).as("Id, UserId, email, 入庫回数, 作成日時, TMSステータス, 対象日時")
        .extracting(
            FrequentCryptoDepositResponse::getId,
            FrequentCryptoDepositResponse::getUserId,
            FrequentCryptoDepositResponse::getEmail,
            FrequentCryptoDepositResponse::getDepositCount,
            FrequentCryptoDepositResponse::getCreatedAt,
            FrequentCryptoDepositResponse::getTmsStatus,
            FrequentCryptoDepositResponse::getTargetAt
        )
        .containsExactly(
            Tuple.tuple(1L, 1L, "email1", 2, "2023/03/01 21:34:56", TmsStatus.OPEN, "2023/03/01"),
            Tuple.tuple(2L, 2L, "email2", 3, "2023/03/02 21:34:56", TmsStatus.CLOSE, "2023/03/02")
        );
    assertThat(response.getTotalPages()).as("総ページ数").isEqualTo(1);
    assertThat(response.getTotalElements()).as("総件数").isEqualTo(2L);
    assertThat(readDto.number()).as("ページNumber").isEqualTo(0);
    assertThat(readDto.size()).as("1ページあたりの件数").isEqualTo(30);
  }

  // utils
  private FrequentCryptoDepositUser generateEntity(
      final Long id,
      final Long userId,
      final String email,
      final Integer depositCount,
      final TmsStatus tmsStatus,
      final String targetAt,
      final String createdAt) {
    final var entity = new FrequentCryptoDepositUser();

    final var user = new User();
    user.setId(userId);
    user.setEmail(email);

    entity.setId(id);
    entity.setUser(user);
    entity.setUserId(userId);
    entity.setDepositCount(depositCount);
    entity.setTmsStatus(tmsStatus);
    entity.setTargetAt(toDateSSS(targetAt));
    entity.setCreatedAt(toDateSSS(createdAt));

    return entity;
  }
}
