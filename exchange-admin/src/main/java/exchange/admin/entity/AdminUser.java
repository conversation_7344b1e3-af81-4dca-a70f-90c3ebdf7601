package exchange.admin.entity;

import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.userdetails.UserDetails;
import com.fasterxml.jackson.annotation.JsonIgnore;
import exchange.admin.constant.AdminAuthority;
import exchange.admin.model.request.AdminUserPutForm;
import exchange.common.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "admin_user")
@ToString(callSuper = true, doNotUseGetters = true)
public class AdminUser extends AbstractEntity implements UserDetails, CredentialsContainer {

  private static final long serialVersionUID = -8213917601269364082L;

  @Getter
  @Setter
  @Column(name = "email", nullable = false)
  private String email;

  @JsonIgnore
  @Getter
  @Setter
  @Column(name = "password")
  private String password;

  @Getter
  @Setter
  @Column(name = "account_non_expired", nullable = false)
  private boolean accountNonExpired = true;

  @Getter
  @Setter
  @Column(name = "account_non_locked", nullable = false)
  private boolean accountNonLocked = true;

  @Getter
  @Setter
  @Column(name = "credentials_non_expired", nullable = false)
  private boolean credentialsNonExpired = true;

  @Getter
  @Setter
  @Column(name = "enabled", nullable = false)
  private boolean enabled = true;

  @Getter
  @Setter
  @Column(name = "password_force_change", nullable = false)
  private boolean passwordForceChange = true;

  @Getter
  @Setter
  @OneToMany(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  @JoinColumn(
      name = "admin_user_id",
      referencedColumnName = "id",
      foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
      insertable = false,
      updatable = false)
  @Fetch(FetchMode.JOIN)
  private List<AdminUserAuthority> authorities;

  public AdminUser(String email, String password) {
    this.email = email;
    this.password = password;
  }

  @Override
  public void eraseCredentials() {
    password = null;
  }

  @Override
  public String getUsername() {
    return email;
  }

  public AdminUser setParams(AdminUserPutForm form) {
    email = form.getEmail();
    accountNonExpired = form.isAccountNonExpired();
    accountNonLocked = form.isAccountNonLocked();
    credentialsNonExpired = form.isCredentialsNonExpired();
    enabled = form.isEnabled();
    return this;
  }

  @JsonIgnore
  public boolean isSuper() {
    for (AdminUserAuthority adminUserAuthority : authorities) {
      if (adminUserAuthority.getAdminAuthority() == AdminAuthority.SUPER) {
        return true;
      }
    }

    return false;
  }

  @Override
  public boolean equals(Object rhs) {
    if (!(rhs instanceof AdminUser)) {
      return false;
    }
    return this.email.equals(((AdminUser) rhs).getEmail());
  }

  @Override
  public int hashCode() {
    return this.email.hashCode();
  }
}
