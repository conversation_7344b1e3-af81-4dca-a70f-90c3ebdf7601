package exchange.admin.config;

import exchange.admin.model.AdminUserLog;
import exchange.common.core.tracer.TraceIdHolder;
import exchange.common.util.JsonUtil;
import java.io.IOException;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import exchange.common.service.LogService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
@Slf4j
public class HandlerInterceptorImpl implements HandlerInterceptor {
  public static final String START_DURATION = "startDuration";

  private final LogService logService;

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws IOException {
    TraceIdHolder.genAndSetTraceId();
    request.setAttribute(START_DURATION, System.currentTimeMillis());

    logService.logRequestStart(request);

    return true;
  }

  @Override
  public void postHandle(
      HttpServletRequest request,
      HttpServletResponse response,
      Object handler,
      ModelAndView modelAndView) {}

  @Override
  public void afterCompletion(
      HttpServletRequest request, HttpServletResponse response, Object handler, Exception e)
      throws IOException {
    if (request.getRequestURI().indexOf("healthcheck") < 0) {
      log.info(JsonUtil.encode(new AdminUserLog(request, response)));
    }
    log.info(
        "*** end [tid=" + TraceIdHolder.getTraceIdOrUnknown() + "] "
            + request.getRequestURL().toString()
            + " "
            + (new Date().getTime() - (long) request.getAttribute(START_DURATION))
            + " ***");
    TraceIdHolder.clear();
  }
}
