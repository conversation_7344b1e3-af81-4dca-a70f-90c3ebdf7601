package exchange.admin.component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;

import exchange.admin.entity.AdminUser;
import exchange.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class LogoutHandlerImpl implements LogoutHandler {

  @Override
  public void logout(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
	  AdminUser adminUser = null;
	try {
		  adminUser =
	        (AdminUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
	}catch (Exception e) {
		if (e.getMessage().contains("Cannot invoke \"org.springframework.security.core.Authentication.getPrincipal()\"")) {
			System.out.println(e.getMessage());
		} else {
			throw e;
		}
	}
    log.info(JsonUtil.encode(adminUser));
  }
}
