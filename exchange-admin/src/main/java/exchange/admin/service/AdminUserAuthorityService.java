package exchange.admin.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import exchange.admin.entity.AdminUserAuthority;
import exchange.admin.entity.AdminUserAuthority_;
import exchange.admin.predicate.AdminUserAuthorityPredicate;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.service.EntityService;

@Service
public class AdminUserAuthorityService
    extends EntityService<AdminUserAuthority, AdminUserAuthorityPredicate> {

  @Override
  public Class<AdminUserAuthority> getEntityClass() {
    return AdminUserAuthority.class;
  }

  public AdminUserAuthority findOne(Long adminUserId, String authority) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<AdminUserAuthority, AdminUserAuthority>() {
          @Override
          public AdminUserAuthority query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalAdminUserId(criteriaBuilder, root, adminUserId));
            predicates.add(predicate.equalAuthority(criteriaBuilder, root, authority));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public AdminUserAuthority findByAdminUserId(Long adminUserId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<AdminUserAuthority, AdminUserAuthority>() {
          @Override
          public AdminUserAuthority query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalAdminUserId(criteriaBuilder, root, adminUserId));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(AdminUserAuthority_.id)));
          }
        });
  }
}
