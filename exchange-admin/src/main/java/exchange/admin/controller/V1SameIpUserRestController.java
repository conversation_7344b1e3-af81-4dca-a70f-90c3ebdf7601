package exchange.admin.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.SameIpUser;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SameIpUserReportData;
import exchange.common.service.SameIpUserService;
import exchange.common.util.DateUnit;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/same-ip-user")
public class V1SameIpUserRestController extends ExchangeAdminController {

  private final SameIpUserService sameIpUserService;

  @Autowired private final CsvDownloadManager<SameIpUserReportData> downloadManager;

  private final String reportPreFix = "不適正取引検知_類似者_";

  @GetMapping
  public ResponseEntity<PageData<SameIpUser>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
    return ResponseEntity.ok(
        sameIpUserService.findByConditionPageData(dateFrom, dateTo, number, size));
  }

  @GetMapping("/download")
  public String download(
      HttpServletResponse response,
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo)
      throws Exception {

    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<SameIpUserReportData> reportDataList = new ArrayList<SameIpUserReportData>();

    List<SameIpUser> res = sameIpUserService.findAllByCondition(dateFrom, dateTo);
    for (SameIpUser sameIpUser : res) {
      reportDataList.add(new SameIpUserReportData().setProperties(sameIpUser));
    }
    downloadManager.download(response, reportDataList, reportPreFix, true);
    return null;
  }
}
