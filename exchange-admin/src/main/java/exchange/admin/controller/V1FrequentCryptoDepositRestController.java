package exchange.admin.controller;

import exchange.admin.entity.AdminUser;
import exchange.admin.model.response.FrequentCryptoDepositResponse;
import exchange.admin.model.response.ReportFrequentCryptoDepositResponse;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.TmsStatus;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.service.FrequentCryptoDepositUserService;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/frequent-crypto-deposit")
public class V1FrequentCryptoDepositRestController extends ExchangeAdminController {

  private final FrequentCryptoDepositUserService frequentCryptoDepositUserService;
  private final CsvDownloadManager<ReportFrequentCryptoDepositResponse> downloadManager;

  @GetMapping
  public ResponseEntity<PageData<FrequentCryptoDepositResponse>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "status", required = false) TmsStatus status,
      @RequestParam(
          value = "number",
          required = false,
          defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(
          value = "size",
          required = false,
          defaultValue = "" + ViewVariables.DEFAULT_SIZE)
          Integer size)
      throws Exception {

    // 権限チェック
    if (!(
        hasAdminAuthority(adminUser)
            || hasUpdateAuthority(adminUser)
            || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    // DBからデータを取得する
    final var readDto = readPageData(userId, dateFrom, dateTo, status, number, size);

    // 返却用のデータに変換する
    final var responsePageData = processToPageData(readDto);

    // レスポンス作成
    return ResponseEntity.ok(responsePageData);
  }

  @PutMapping("/apply")
  public ResponseEntity<Void> put(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "id") Long id,
      @RequestParam(value = "tmsStatus") TmsStatus tmsStatus)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    // ステータスチェック
    final var target = frequentCryptoDepositUserService.findOne(id);
    if (!target.getTmsStatus().nextStatuses().contains(tmsStatus)) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_TMS_STATUS);
    }

    // 指定のTMSステータスで更新をする
    frequentCryptoDepositUserService.apply(id, tmsStatus);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/download")
  public void download(HttpServletResponse response,
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "status", required = false) TmsStatus status)
      throws Exception {

    // 権限チェック
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    // DBからデータを取得する
    final var readDto = readAllData(userId, dateFrom, dateTo, status);

    // 返却用のデータに変換する
    final var responseDatas = readDto.frequentCryptoDepositUsers.stream()
        .map(entity -> ReportFrequentCryptoDepositResponse.create(entity)).toList();

    // ダウンロード情報を返却する
    downloadManager.download(response, responseDatas, "frequent_crypto_deposit_user", true);
  }

  public record ReadData(
      List<FrequentCryptoDepositUser> frequentCryptoDepositUsers,
      Long numberOfTotalRecord,
      Integer number,
      Integer size
  ) {

  }

  /**
   * ページング用のデータを取得する
   *
   * @param userId
   * @param dateFrom
   * @param dateTo
   * @param status
   * @param number
   * @param size
   * @return
   */
  ReadData readPageData(
      final Long userId,
      final Long dateFrom,
      final Long dateTo,
      final TmsStatus status,
      final Integer number,
      final Integer size) {
    // 日付型に変換
    final var toDateFrom = dateFrom != null ? new Date(dateFrom) : null;
    final var toDateTo = dateTo != null ? new Date(dateTo) : null;

    final var entities = frequentCryptoDepositUserService.findByConditionPaging(userId, status,
        toDateFrom, toDateTo, number, size);

    final var count = frequentCryptoDepositUserService.getCount(userId, status, toDateFrom,
        toDateTo);

    return new ReadData(entities, count, number, size);
  }

  /**
   * 条件に合う全件分のデータを取得する
   *
   * @param userId
   * @param dateFrom
   * @param dateTo
   * @param status
   * @return
   */
  ReadData readAllData(
      final Long userId,
      final Long dateFrom,
      final Long dateTo,
      final TmsStatus status) {
    // 日付型に変換
    final var toDateFrom = dateFrom != null ? new Date(dateFrom) : null;
    final var toDateTo = dateTo != null ? new Date(dateTo) : null;

    final var entities = frequentCryptoDepositUserService.findByCondition(userId, status,
        toDateFrom, toDateTo);

    final var count = frequentCryptoDepositUserService.getCount(userId, status, toDateFrom,
        toDateTo);

    return new ReadData(entities, count, 0, Integer.MAX_VALUE);
  }

  /**
   * ReadDtoを返却用のページングデータに加工する
   *
   * @param readDto
   * @return
   */
  PageData<FrequentCryptoDepositResponse> processToPageData(final ReadData readDto) {
    final var responseDatas = readDto.frequentCryptoDepositUsers.stream()
        .map(FrequentCryptoDepositResponse::create).toList();

    return new PageData<>(readDto.number, readDto.size, readDto.numberOfTotalRecord, responseDatas);
  }

}
