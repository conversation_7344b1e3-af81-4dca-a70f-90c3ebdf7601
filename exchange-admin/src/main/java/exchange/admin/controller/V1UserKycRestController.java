package exchange.admin.controller;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.util.StringUtils;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.request.UserKycInfoPutForm;
import exchange.admin.model.request.UserKycPutForm;
import exchange.admin.model.response.UserKycInformation;
import exchange.common.component.CustomLogger;
import exchange.common.component.CustomTransactionManager;
import exchange.common.component.S3Manager;
import exchange.common.component.S3Manager.Bucket;
import exchange.common.component.SesManager;
import exchange.common.config.S3Config;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.KycMailStatus;
import exchange.common.constant.KycStatus;
import exchange.common.constant.KycSubStatus;
import exchange.common.constant.KycType;
import exchange.common.constant.UserStatus;
import exchange.common.constant.UserNoteTypeEnum;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.entity.UserKyc;
import exchange.common.entity.UserKycSub;
import exchange.common.exception.CustomException;
import exchange.common.model.request.UserNoteCreateForm;
import exchange.common.model.response.PageData;
import exchange.common.model.response.UserKycData;
import exchange.common.model.response.UserKycData.KycImage;
import exchange.common.service.MailNoreplyService;
import exchange.common.service.OnetimeBankAccountService;
import exchange.common.service.UserInfoCorporateAgentService;
import exchange.common.service.UserInfoCorporateOwnerService;
import exchange.common.service.UserInfoCorporateRepresentativeService;
import exchange.common.service.UserInfoCorporateService;
import exchange.common.service.UserInfoService;
import exchange.common.service.UserKycService;
import exchange.common.service.UserKycSubService;
import exchange.common.service.UserNoteService;
import exchange.common.service.UserService;
import lombok.RequiredArgsConstructor;

@RequestMapping("/admin/v1/user-kyc")
@RequiredArgsConstructor
@RestController
public class V1UserKycRestController extends ExchangeAdminController {

  private static final CustomLogger log = new CustomLogger(ExchangeAdminController.class.getName());

  private final MailNoreplyService mailNoreplyService;

  private final SesManager sesManager;

  private final S3Config s3Config;

  private final S3Manager s3Manager;

  private final UserInfoService userInfoService;

  private final UserInfoCorporateService userInfoCorporateService;

  private final UserInfoCorporateRepresentativeService userInfoCorporateRepresentativeService;

  private final UserInfoCorporateAgentService userInfoCorporateAgentService;

  private final UserInfoCorporateOwnerService userInfoCorporateOwnerService;

  private final UserKycService userKycService;

  private final UserService userService;

  private final UserKycSubService userKycSubService;

  private final OnetimeBankAccountService onetimeBankAccountService;

  private final CustomTransactionManager customTransactionManager;

  private final UserNoteService userNoteService;

  @GetMapping()
  public ResponseEntity<PageData<UserKyc>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size) {
    PageData<UserKyc> userKycList;
    userKycList = userKycService.findByUserIdPageData(userId, number, size);
    return ResponseEntity.ok(userKycList);
  }

  @GetMapping("{id}")
  public ResponseEntity<UserKycData> getDetail(
      @AuthenticationPrincipal AdminUser adminUser, @PathVariable("id") Long id) throws Exception {

    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    UserKycData userKycData = new UserKycData();

    // UserKycセット
    UserKyc userKyc = userKycService.findOne(id);
    if (userKyc == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }
    userKycData.setUserKyc(userKyc);

    // Userセット
    User user = userService.findOne(userKyc.getUserId());
    if (user == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }
    userKycData.setUser(user);

    userKycData.setKycSubStatuses(userKycSubService.findByKycId(userKyc.getId()));

    // Userテーブルに入っているKycIdは最新のデータを指す
    userKycData.setLatestKycData(userKyc.getId().equals(user.getUserKycId()));

    // UserInfoセット(nullでもそのままセットする)
    UserInfo kycUserInfo = userInfoService.findOne(userKyc.getUserInfoId());
    List<UserInfo> userInfoAll = userInfoService.findAll();
    Map<Long, UserInfo> lastUserInfoMap = new HashMap();
    for(UserInfo userInfo : userInfoAll) {
    	Long userId = userInfo.getUserId();
    	if(!lastUserInfoMap.containsKey(userId) || (lastUserInfoMap.get(userId).getId() != null ? userInfo.getId() > lastUserInfoMap.get(userId).getId() : true)) {
    		lastUserInfoMap.put(userId, userInfo);
    	}
    }
    ArrayList<UserInfo> lastUserInfo = new ArrayList<>(lastUserInfoMap.values());
    
    if(userKycData.isLatestKycData()) {
    	UserInfo userInfoNew = null;
    	UserInfo userInfoFromUser = userInfoService.findOne(user.getUserInfoId());
    	if (kycUserInfo != null) {
			userInfoNew = kycUserInfo;
		} else if (userInfoFromUser != null) {
			userInfoNew = userInfoFromUser;
		}
    	if (userInfoNew != null) {
    		for(UserInfo userInfo : lastUserInfo) {
          	  if (userInfo.getFirstName().equals(userInfoNew.getFirstName()) && 
          	      userInfo.getBirthday().equals(userInfoNew.getBirthday()) && 
          	      (user.getUserStatus() == UserStatus.REVIEWING) &&
          	      !(userInfo.getUserId().equals(userInfoNew.getUserId()) && 
          	      userInfo.getId().equals(userInfoNew.getId()))){
      			userKycData.setFlag(false);
      			break;
      	      } else {
      	    	  userKycData.setFlag(true);
      	      }
            }
		}
    } else {
    	userKycData.setFlag(true);
	}
    
    if(kycUserInfo != null) {
    	userKycData.setUserInfo(kycUserInfo);
    } else if(userKycData.isLatestKycData()) {
		userKycData.setUserInfo(userInfoService.findOne(user.getUserInfoId()));
    }

    // UserInfoCorporate設置
    UserInfoCorporate corporate = null;
    UserInfoCorporate corporateLastest = userInfoCorporateService.findOne(user.getUserInfoCorporateId());
    UserInfoCorporate corporateFromKyc =
        userInfoCorporateService.findOne(userKyc.getUserInfoCorporateId());
    if(corporateFromKyc != null) {
      corporate = corporateFromKyc;
    } else if(userKycData.isLatestKycData()) {
      corporate = corporateLastest;
    }
    userKycData.setUserInfoCorporate(corporate);

    if (corporate != null) {
      userKycData.setRepresentative(
          userInfoCorporateRepresentativeService.findOne(corporate.getRepresentativeId()));
      userKycData.setAgent(userInfoCorporateAgentService.findOne(corporate.getAgentId()));

      List<UserInfoCorporateOwner> owners = new ArrayList<UserInfoCorporateOwner>();
      if (corporate.getOwnerIds() != null && !corporate.getOwnerIds().isEmpty()) {
        for (String ownerIdStr : corporate.getOwnerIds().split(",")) {
          Long ownerId = Long.valueOf(ownerIdStr);
          UserInfoCorporateOwner owner = userInfoCorporateOwnerService.findOne(ownerId);
          if (owner != null) {
            owners.add(owner);
          }
        }
      }
      userKycData.setOwners(owners);
    } else {
      userKycData.setRepresentative(null);
      userKycData.setAgent(null);
      userKycData.setOwners(new ArrayList<UserInfoCorporateOwner>());
    }

    if (userKyc.getKycType() == KycType.UPLOAD) {
      // 既存Kycの場合はアップロードされたファイルをセット
      Bucket bucket = new Bucket(s3Config.getKycBucket().getName());

      for (S3ObjectSummary s3ObjectSummary : s3Manager.listObjects(bucket, userKyc.getId() + "/")) {
        File tmpFile = new File(s3ObjectSummary.getKey());
        FileOutputStream fos = FileUtils.openOutputStream(tmpFile);
        try {
          // get image from s3
          IOUtils.copy(
              s3Manager.getObjectContent(bucket, s3ObjectSummary.getKey(), user.getId()), fos);

          // MIME type
          String contentType = Files.probeContentType(tmpFile.toPath());
          // file to base64
          byte[] data = Files.readAllBytes(tmpFile.toPath());
          String base64str = Base64.getEncoder().encodeToString(data);
          StringBuilder sb = new StringBuilder();
          sb.append("data:");
          sb.append(contentType);
          sb.append(";base64,");
          sb.append(base64str);

          userKycData
              .getKycImages()
              .add(new KycImage(FilenameUtils.getName(s3ObjectSummary.getKey()), sb.toString()));
        } catch (IOException e) {
          throw new CustomException(e);
        } finally {
          IOUtils.closeQuietly(fos);
          FileUtils.deleteQuietly(tmpFile);
        }
      }
    }
    userKycData.setUserNotes(userNoteService.findByList(userKyc.getUserId()));
    
    List<UserKyc> userKycList = userKycService.findByUserId(user.getId()).orElse(new ArrayList<>());
    UserKyc latestUserKycHaveEmail = userKycList.stream().filter(u -> !StringUtils.isEmpty(u.getEmail()) && u.getId().compareTo(id) > 0)
      .sorted((u1, u2) -> u1.getId().compareTo(u2.getId()))
      .findFirst().orElse(null);
    
    if(latestUserKycHaveEmail != null) {
      userKycData.setEmail(latestUserKycHaveEmail.getEmail());
    } else {
      userKycData.setEmail(user.getEmail());
    }
    
    return ResponseEntity.ok(userKycData);
  }

  /*
   * あるユーザーの各種Kyc件数情報等を取得するためのAPI
   */
  @GetMapping("/information")
  public ResponseEntity<UserKycInformation> getInformation(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId)
      throws Exception {

    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    UserKycInformation userKycInformation = new UserKycInformation();

    List<UserKyc> userKycList =
        userKycService
            .findByUserId(userId)
            .orElseThrow(() -> new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER));

    // 申請回数カウント
    userKycInformation.setApplicationCount(
        userKycList.stream().filter(userKyc -> (userKyc.getKycType() != KycType.NONE)).count());
    // 否認回数カウント
    userKycInformation.setNgCount(
        userKycList.stream()
            .filter(
                userKyc ->
                    (userKyc.getKycStatus() == KycStatus.INFORMATION_REQUIRED
                        || userKyc.getKycStatus() == KycStatus.DOCUMENT_REJECTED))
            .count());
    // 情報更新回数カウント
    userKycInformation.setUpdateCount(
        userKycList.stream()
            .filter(
                userKyc ->
                    userKyc.getKycStatus() == KycStatus.DONE)
            .count());
    if (userKycList.size() > 0) {
      // idがもっとも大きいKyc履歴(=最新のKyc履歴)を取得する
      UserKyc latestKyc =
          userKycList.stream()
              .sorted(Comparator.comparing(UserKyc::getId).reversed())
              .collect(Collectors.toList())
              .get(0);
      // 最新Kyc履歴が「完了」しているかを判定
      userKycInformation.setIsFixedLatest(
          latestKyc.getKycStatus() == KycStatus.DONE
                  && latestKyc.getKycMailStatus() == KycMailStatus.MAIL_SENT
              || latestKyc.getKycStatus() == KycStatus.REJECTED
                  && latestKyc.getKycMailStatus() == KycMailStatus.MAIL_SENT
              || latestKyc.getKycStatus() == KycStatus.DOCUMENT_REJECTED
                  && latestKyc.getKycMailStatus() == KycMailStatus.MAIL_SENT
              || latestKyc.getKycStatus() == KycStatus.INFORMATION_REQUIRED
                  && latestKyc.getKycMailStatus() == KycMailStatus.MAIL_SENT
              || latestKyc.getKycStatus() == KycStatus.CHANGE_ATTR_APPLY_REQUESTED
                  && latestKyc.getKycMailStatus() == KycMailStatus.MAIL_SENT);
    } else {
      // Kyc履歴が0件の場合は完了しているとみなす(通常はありえない)
      userKycInformation.setIsFixedLatest(true);
    }
    return ResponseEntity.ok(userKycInformation);
  }

  @PutMapping
  public ResponseEntity<UserKyc> put(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody UserKycPutForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    UserKyc userKyc = userKycService.findOne(form.getId());
    User user = userService.findOne(userKyc.getUserId());
    if (user == null) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
    }

    final var oldKycStatus = userKyc.getKycStatus();
    final var newKycStatus = form.getKycStatus();
    final var oldKycMailStatus = userKyc.getKycMailStatus();
    final var newKycMailStatus = form.getKycMailStatus();

    // ステータス遷移のチェック
    if (!canTransitStatus(oldKycStatus, newKycStatus)) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_KYC_STATUS_TRANSITION);
    }

    if (!StringUtils.isEmpty(form.getJudgingComment())) {
      UserNoteCreateForm userNoteCreateForm = new UserNoteCreateForm();
      userNoteCreateForm.setUserId(user.getId());
      userNoteCreateForm.setNote(form.getJudgingComment());
      userNoteCreateForm.setCurrentKycStatus(newKycStatus);
      userNoteCreateForm.setOperator(adminUser.getEmail());
      userNoteCreateForm.setType(UserNoteTypeEnum.JUDGING);
      userNoteService.addUserNote(userNoteCreateForm);
    }
    if (!StringUtils.isEmpty(form.getAmlCftComment())) {
      UserNoteCreateForm userNoteCreateForm = new UserNoteCreateForm();
      userNoteCreateForm.setUserId(user.getId());
      userNoteCreateForm.setNote(form.getAmlCftComment());
      userNoteCreateForm.setCurrentKycStatus(newKycStatus);
      userNoteCreateForm.setOperator(adminUser.getEmail());
      userNoteCreateForm.setType(UserNoteTypeEnum.AML);
      userNoteService.addUserNote(userNoteCreateForm);
    }

    // ステータスが変更されない場合は、既存のレコードを更新する
    if (oldKycStatus == newKycStatus) {
      customTransactionManager.execute(entityManager -> {
        userKyc.setJudgingComment(form.getJudgingComment());
        userKyc.setAmlCftComment(form.getAmlCftComment());
        userKyc.setAntisocialStatus(form.getAntisocialStatus());
        userKyc.setUpdatedAt(new Date());
        userKycService.save(userKyc, entityManager);
      });
      return ResponseEntity.ok(userKyc);
    }

    // KycStatusが変更になる場合は、新しくレコードを作成する
    // Response用にexecuteの上に作成する
    final var newUserKyc = new UserKyc(user.getId());
    customTransactionManager.execute(entityManager -> {
      newUserKyc.setKycStatus(newKycStatus);
      newUserKyc.setOperator(adminUser.getId().toString());
      newUserKyc.setKycMailStatus(form.getKycMailStatus());
      newUserKyc.setJudgingComment(form.getJudgingComment());
      newUserKyc.setAmlCftComment(form.getAmlCftComment());
      newUserKyc.setAntisocialStatus(form.getAntisocialStatus());
      // KycTypeは変わらないので元のKycTypeを設定する
      newUserKyc.setKycType(userKyc.getKycType());
      if (oldKycMailStatus != newKycMailStatus && newKycMailStatus == KycMailStatus.MAIL_SENT) {
        newUserKyc.setMailSendAt(new Date());
      }

      if (newKycStatus.mailNoreplyType.isPresent()) {
        final var mailNoreplyType = newKycStatus.mailNoreplyType.get();
        final var mailNoreplyEntity = mailNoreplyService.findOne(mailNoreplyType);
        final var kycSubStatuses = form.getKycSubStatuses();
        final var kycSubStatusesSize = kycSubStatuses.size();
        final var isKycSubStatusOtherOnly = kycSubStatusesSize == 1 && kycSubStatuses.get(0) == KycSubStatus.OTHER;
        if (newKycStatus == KycStatus.INFORMATION_REQUIRED && !isKycSubStatusOtherOnly) {
          final var contents = new StringBuilder();
          final var filteringOtherKycSubs = kycSubStatuses.stream()
              .filter(kycSub -> kycSub != KycSubStatus.OTHER).toList();
          final var filteringOtherKycSubsSize = filteringOtherKycSubs.size();
          for (var i = 0; i < filteringOtherKycSubsSize; i++) {
            final var subStatus = filteringOtherKycSubs.get(i);
            contents.append(String.format("%d. %s", (i + 1), subStatus.mailContent));
            contents.append(System.getProperty("line.separator"));
            contents.append(System.getProperty("line.separator"));
          }
          final var message = MessageFormat.format(mailNoreplyEntity.getContents(), contents.toString());
          sendMail(mailNoreplyEntity.getFromAddress(), user.getEmail(),
              mailNoreplyEntity.getTitle(), message);
        } else if (newKycStatus != KycStatus.INFORMATION_REQUIRED) {
          sendMail(mailNoreplyEntity.getFromAddress(), user.getEmail(),
              mailNoreplyEntity.getTitle(), mailNoreplyEntity.getContents());
        }
        newUserKyc.setKycMailStatus(KycMailStatus.MAIL_SENT);
        newUserKyc.setMailSendAt(new Date());
      }

      if (newKycStatus == KycStatus.ACCOUNT_OPENING_DONE
          || newKycStatus == KycStatus.CORPORATE_PERSONAL_ACCOUNT_OPENING_DONE
          || newKycStatus == KycStatus.DONE) {
        // セキュリティレベルが2になる
        user.setLevel(2);
      }
      newUserKyc.setUserInfoId(user.getUserInfoId());
      newUserKyc.setUserInfoCorporateId(user.getUserInfoCorporateId());
      userKycService.save(newUserKyc, entityManager);

      // UserKycSubの登録
      for (var kycSubStatus: form.getKycSubStatuses()) {
        final var kycSubStatusEntity = new UserKycSub();
        kycSubStatusEntity.setKycId(newUserKyc.getId());
        kycSubStatusEntity.setKycSubStatus(kycSubStatus);
        userKycSubService.save(kycSubStatusEntity, entityManager);
      }

      // Userの更新
      user.setUserStatus(newKycStatus.userStatus);
      user.setKycStatus(newKycStatus);
      user.setUserKycId(newUserKyc.getId());
      userService.save(user, entityManager);
    });

    return ResponseEntity.ok(newUserKyc);
  }

  // UserInfo UserCorporateInfoの更新
  @PutMapping("/info")
  public ResponseEntity<UserKyc> addInfo(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody UserKycInfoPutForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    UserKyc userKyc = userKycService.findOne(form.getId());
    if (userKyc == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_KYC_NOT_FOUND);
    }
    List<UserInfo> userInfoList = userInfoService.findByCondition(userKyc.getUserId(), null, null, null, false);
    List<UserInfoCorporate> userInfoCorporateList = 
        userInfoCorporateService.findByCondition(userKyc.getUserId(), null, null, null, false);
    Long userInfoId = null;
    Long userInfoCorporateId = null;
    if (ObjectUtils.isNotEmpty(userInfoList)) {
      userInfoId = userInfoList.get(0).getId();
    } else if (ObjectUtils.isNotEmpty(userInfoCorporateList)) {
      userInfoCorporateId = userInfoCorporateList.get(0).getId();
    } else {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_NOT_FOUND);
    }
    
    UserKyc userKycNew = new UserKyc(userKyc.getUserId());
    userKycNew.setOperator(adminUser.getId().toString());
    userKycNew.setUserInfoId(userInfoId);
    userKycNew.setUserInfoCorporateId(userInfoCorporateId);
    // KYCステータス
    userKycNew.setKycStatus(userKyc.getKycStatus());
    // 制裁チェック
    userKycNew.setAntisocialStatus(userKyc.getAntisocialStatus());
    // 承認/否認メール送信日時
    userKycNew.setMailSendAt(userKyc.getMailSendAt());
    // eKYC/ファイルアップロード
    userKycNew.setKycType(userKyc.getKycType());
    // メール送信状況
    userKycNew.setKycMailStatus(userKyc.getKycMailStatus());
    // 審査コメント
    userKycNew.setJudgingComment(userKyc.getJudgingComment());
    // AML/CFT用コメント
    userKycNew.setAmlCftComment(userKyc.getAmlCftComment());
    userKycNew.setChangeType("03");
    userKycService.save(userKycNew);
    // 不備/謝絶理由
    List<UserKycSub> userKycSubList = userKycSubService.findByKycId(userKyc.getId());
    for(UserKycSub userKycSub : userKycSubList) {
      UserKycSub userKycSubNew = new UserKycSub();
      userKycSubNew.setKycSubStatus(userKycSub.getKycSubStatus());
      userKycSubNew.setKycId(userKycNew.getId());
      userKycSubService.save(userKycSubNew);
    }
    User userForUpdate = userService.findById(userKyc.getUserId());
    userForUpdate.setUserKycId(userKycNew.getId());
    userService.save(userForUpdate);
    
    return ResponseEntity.ok(userKycNew);
  }

  // 新規Kyc履歴の作成(属性変更)
  @PostMapping
  public ResponseEntity<UserKyc> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestParam("userId") Long userId)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    User user = userService.findOne(userId);
    if (user == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
    }

    UserKyc userKyc = new UserKyc(userId);
    userKyc.setKycStatus(KycStatus.CHANGE_ATTR_INQUIRED);
    userKyc.setOperator(adminUser.getId().toString());
    userKyc.setUserInfoId(user.getUserInfoId());
    userKyc.setUserInfoCorporateId(user.getUserInfoCorporateId());
    userKycService.save(userKyc);

    // ユーザーのkycIdを更新する
    user.setUserKycId(userKyc.getId());
    user.setKycStatus(userKyc.getKycStatus());
    userService.save(user);
    return ResponseEntity.ok(userKyc);
  }

  /*
   * KycStatusの不正な状態遷移をチェックする
   */
  private boolean canTransitStatus(KycStatus from, KycStatus to) {
    if (from == to) {
      return true;
    }
    return from.nextStatuses().contains(to);
  }

  // メール自動送信
  private void sendMail(String emailFrom, String emailTo, String title, String contents)
      throws Exception {
    sesManager.send(emailFrom, emailTo, title, contents);
  }
}
