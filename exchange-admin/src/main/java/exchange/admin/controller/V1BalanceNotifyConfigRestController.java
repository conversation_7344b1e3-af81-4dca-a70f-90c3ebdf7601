package exchange.admin.controller;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.entity.BalanceNotifyConfig;
import exchange.common.exception.CustomException;
import exchange.common.model.request.BalanceNotifyConfigUpdateForm;
import exchange.common.service.BalanceNotifyConfigService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/balance-notify-config")
public class V1BalanceNotifyConfigRestController extends ExchangeAdminController {
  private final BalanceNotifyConfigService balanceNotifyConfigService;

  @GetMapping
  public ResponseEntity<List<BalanceNotifyConfig>> get(@AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "targetPos", required = false) Exchange targetPos,
      @RequestParam(value = "enabled", required = false) Boolean enabled) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    if (id != null) {
      List<BalanceNotifyConfig> balanceNotifyConfigs = new ArrayList<>();
      balanceNotifyConfigs.add(balanceNotifyConfigService.findOne(id));
      return ResponseEntity.ok(balanceNotifyConfigs);
    } else if (currency != null || userId != null || targetPos != null || enabled != null) {
      List<BalanceNotifyConfig> balanceNotifyConfigs =
          balanceNotifyConfigService.findAllByCondition(currency, userId, targetPos, enabled);
      return ResponseEntity.ok(balanceNotifyConfigs);
    } else {
      return ResponseEntity.ok(balanceNotifyConfigService.findAll());
    }
  }
  
  @PutMapping
  public ResponseEntity<BalanceNotifyConfig> update(@AuthenticationPrincipal AdminUser adminUser,
      @Valid @RequestBody BalanceNotifyConfigUpdateForm form) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    BalanceNotifyConfig balanceNotifyConfig = balanceNotifyConfigService.findOne(form.getId());

    if (balanceNotifyConfig == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }
    customTransactionManager.execute(
        entityManager -> {
          List<BalanceNotifyConfig> balanceNotifyConfigs = balanceNotifyConfigService.findAll();
          balanceNotifyConfigs.forEach((config) -> {
            if(!config.getMailTo().equals(form.getMailTo())) {
              config.setMailTo(form.getMailTo());
              balanceNotifyConfigService.save(config);
            }
          });
          balanceNotifyConfig.setEnabled(form.isEnabled());
          balanceNotifyConfig.setMailTo(form.getMailTo());
          balanceNotifyConfig.setDefaultBalance(form.getDefaultBalance());
          balanceNotifyConfig.setLimitBalancePercent(form.getLimitBalancePercent());
          balanceNotifyConfigService.save(balanceNotifyConfig);
        });
    
    return ResponseEntity.ok(balanceNotifyConfig);
  }

}
