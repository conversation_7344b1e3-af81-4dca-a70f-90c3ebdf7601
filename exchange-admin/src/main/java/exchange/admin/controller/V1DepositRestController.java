package exchange.admin.controller;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import exchange.common.constant.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.request.DepositCsvPostForm;
import exchange.admin.model.request.DepositPostForm;
import exchange.common.component.CsvDownloadManager;
import exchange.common.component.DataSourceManager;
import exchange.common.entity.Deposit;
import exchange.common.entity.DepositAccount;
import exchange.common.exception.CustomException;
import exchange.common.model.response.DepositReportData;
import exchange.common.model.response.PageData;
import exchange.common.service.DepositAccountService;
import exchange.common.service.DepositService;
import exchange.common.service.SygnaDepositTransferService;
import exchange.common.service.UserService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/deposit")
public class V1DepositRestController extends ExchangeAdminController {

  private final UserService userService;
  private final DepositService depositService;
  private final SygnaDepositTransferService sygnaDepositTransferService;
  private final DepositAccountService depositAccountService;
  private final DataSourceManager dataSourceManager;

  @Autowired private final CsvDownloadManager<DepositReportData> downloadManager;

  private final String reportPreFix = "暗号資産入金一覧";

  @GetMapping
  public ResponseEntity<PageData<Deposit>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "distinction", required = false) String distinction,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    PageData<Deposit> pg = depositService.findByConditionPageData(
    		userId, Currency.valueOfName(currency), distinction, null, dateFrom, dateTo, number, size, true);

    if(ObjectUtils.isNotEmpty(pg.getContent())) {
    	pg.getContent().stream()
        .forEach(d -> d.setAmount(d.getCurrency().getScaledAmount(d.getAmount(), RoundingMode.FLOOR)));
    }

    return ResponseEntity.ok(pg);
  }

  @GetMapping("/approve")
  public ResponseEntity<PageData<Deposit>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "distinction", required = false) String distinction,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "sygnaNotifyFlg", required = false) String sygnaNotifyFlg,
      @RequestParam(value = "depositStatus", required = false) DepositStatus depositStatus,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<Deposit> depositList = new ArrayList<>();

    if ("NOTIFY".equals(sygnaNotifyFlg) && "Transfer".equals(distinction)) {
    	return ResponseEntity.ok(new PageData<Deposit>(number, size, 0, null));
    }

    String sql = "";
    String sqlCount = "";
    String sqlWhileDeposit = "";
    String sqlWhileNotify = "";

    if (sygnaNotifyFlg == null || "DEPOSIT".equals(sygnaNotifyFlg)) {
    	sql = """
    			select 
    			d.id,
                d.user_id,
                d.currency,
                d.deposit_account_id,
                d.deposit_channel,
                d.deposit_type,
                d.deposit_purpose,
                d.amount,
                d.asset_amount,
                d.fee,
                d.jpy_conversion,
                d.address,
                d.transaction_id,
                d.distinction,
                d.transaction_index,
                d.deposit_status,
                d.comment,
                d.ownertype,
                d.recipienttype,
                d.last_name,
                d.first_name,
                d.last_name_kana,
                d.first_name_kana,
                d.last_name_english,
                d.first_name_english,
                d.legalname,
                d.legalname_kana,
                d.legalname_english,
                d.addresstype,
                d.exchange,
                d.area,
                d.aregion,
                d.purpose,
                d.risk_score,
                d.transaction_hash,
                d.sygna_tx_id,
                d.sanction_match,
                d.sygna_notify_flg,
                d.chainalysis_max_alert_level,
                d.created_at,
                d.updated_at
    			FROM deposit d
    			WHERE
    			1=1
    			""";

    	sqlCount = """
    			select 
    			count(d.id)
    			FROM deposit d
    			WHERE
    			1=1
    			""";

        if (userId != null) {
        	sqlWhileDeposit = " AND d.user_id = :uid ";
        }
        if (currency != null) {
        	sqlWhileDeposit += " AND d.currency = :currency ";
        }
        if (distinction != null) {
        	sqlWhileDeposit += " AND d.distinction = :distinction ";
        }
        if (depositStatus != null) {
        	sqlWhileDeposit += " AND d.deposit_status = :status ";
        }
        if (dateFrom != null) {
        	sqlWhileDeposit += " AND d.created_at >= :from ";
        }
        if (dateTo != null) {
        	sqlWhileDeposit += " AND d.created_at <= :to ";
        }
    }

    if (sygnaNotifyFlg == null  && (distinction == null || (distinction != null && "IN".equals(distinction)))) {
    	sqlWhileDeposit += " UNION ALL ";
    }

    sql += sqlWhileDeposit;
    sqlCount += sqlWhileDeposit;

    if ((sygnaNotifyFlg == null || "NOTIFY".equals(sygnaNotifyFlg)) && (distinction == null || (distinction != null && "IN".equals(distinction)))) {
    	sql += " SELECT "
            + " sdt.id,"
            + " sdt.user_id, "
            + " sdt.currency, "
            + " 0 as deposit_account_id, "
            + " '' as deposit_channel, "
            + " '' as deposit_type, "
            + " '' as deposit_purpose, "
            + " sdt.amount, "
            + " 0 as asset_amount, "
            + " 0 as fee, "
            + " 0 as jpy_conversion, "
            + " sdt.addr_from as address, "
            + " 0 as transaction_id, "
            + " \"IN\" as distinction, "
            + " 0 as transaction_index, "
            + " sdt.deposit_status, "
            + " '' as comment, "
            + " '' as ownertype, "
            + " '' as recipienttype, "
            + " '' as last_name, "
            + " '' as first_name, "
            + " '' as last_name_kana, "
            + " '' as first_name_kana, "
            + " '' as last_name_english, "
            + " '' as first_name_english, "
            + " '' as legalname, "
            + " '' as legalname_kana, "
            + " '' as legalname_english, "
            + " '' as addresstype, "
            + " '' as exchange, "
            + " '' as area, "
            + " '' as aregion, "
            + " '' as purpose, "
            + " 0 as risk_score, "
            + " '' as transaction_hash, "
            + " sdt.sygna_tx_id, "
            + " 0 as sanction_match, "
            + " \"NOTIFY\" as sygna_notify_flg, "
			+ "'' as chainalysis_max_alert_level, "
            + " sdt.created_at, "
            + " sdt.updated_at "
    			+ "	FROM "
    			+ "	sygna_deposit_transfer sdt "
    			+ "	WHERE "
    			+ "	1=1 "
    			+ " AND sdt.status IN (1,4,6,8,14) ";

    	sqlCount += " SELECT "
    			+ " count(sdt.id) "
    			+ "	FROM "
    			+ "	sygna_deposit_transfer sdt "
    			+ "	WHERE "
    			+ "	1=1 "
    			+ " AND sdt.status IN (1,4,6,8,14) ";
    	if (userId != null) {
    		sqlWhileNotify = " AND sdt.user_id = :uid ";
        }
        if (currency != null) {
        	sqlWhileNotify += " AND sdt.currency = :currency ";
        }
        if (depositStatus != null) {
        	sqlWhileNotify += " AND sdt.deposit_status = :status ";
        }
        if (dateFrom != null) {
        	sqlWhileNotify += " AND sdt.created_at >= :from ";
        }
        if (dateTo != null) {
        	sqlWhileNotify += " AND sdt.created_at <= :to ";
        }
    }

    sql += sqlWhileNotify;
    sqlCount += sqlWhileNotify;

    sql += " ORDER BY created_at DESC ";

    EntityManager entityManager =
            dataSourceManager.getMasterEntityManagerFactory().createEntityManager();

    PageData<Deposit> pg = null;

    try {
    	Query query = entityManager.createNativeQuery(sql);
        Query queryCount = entityManager.createNativeQuery(sqlCount);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withLocale(Locale.JAPAN)
                .withZone(ZoneId.systemDefault());
        Date date = new Date();
        if (dateFrom != null) {
        	query.setParameter("from", dateFrom != null ? Instant.ofEpochMilli(dateFrom) : formatter.format(Instant.ofEpochMilli(date.getTime())));
        	queryCount.setParameter("from", dateFrom != null ? Instant.ofEpochMilli(dateFrom) : formatter.format(Instant.ofEpochMilli(date.getTime())));
        }
        if (dateTo != null) {
        	query.setParameter("to", dateTo != null ? Instant.ofEpochMilli(dateTo): formatter.format(Instant.ofEpochMilli(date.getTime())));
        	queryCount.setParameter("to", dateTo != null ? Instant.ofEpochMilli(dateTo): formatter.format(Instant.ofEpochMilli(date.getTime())));
        }
        if (userId != null) {
        	query.setParameter("uid", userId);
        	queryCount.setParameter("uid", userId);
        }
        if (currency != null) {
        	query.setParameter("currency", currency);
        	queryCount.setParameter("currency", currency);
        }
        if (distinction != null && (!"NOTIFY".equals(sygnaNotifyFlg))) {
        	query.setParameter("distinction", distinction);
        	queryCount.setParameter("distinction", distinction);
        }
        if (depositStatus != null) {
        	query.setParameter("status", depositStatus.toString());
        	queryCount.setParameter("status", depositStatus.toString());
        }

        List<Object[]> listSum = queryCount.getResultList();
        int sizeSum = 0;
        if (listSum.size() > 1) {
        	sizeSum = Integer.parseInt(String.valueOf(listSum.get(0))) + Integer.parseInt(String.valueOf(listSum.get(1)));
        } else {
        	sizeSum = Integer.parseInt(String.valueOf(listSum.get(0)));
        }


        if (number * size != 0) {
        	query = query.setFirstResult(number * size);
          }
        if (size != Integer.MAX_VALUE) {
        	query = query.setMaxResults(size);
          }

        List<Object[]> list = query.getResultList();

        BigDecimal amount = new BigDecimal(0.0);
        BigDecimal fee = new BigDecimal(0.0);
        SimpleDateFormat formatterOut = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (Object[] obj : list) {
        	Deposit deposit = new Deposit();
        	deposit.setId(Long.valueOf(String.valueOf(obj[0])));
        	deposit.setUserId(Long.valueOf(String.valueOf(obj[1])));
        	deposit.setCurrency(Currency.valueOfName(String.valueOf(obj[2])));
        	deposit.setDepositAccountId(Long.valueOf(String.valueOf(obj[3])));
        	deposit.setDepositChannel(obj[4] == null?
        	    DepositChannel.UNKNOWN : DepositChannel.valueOfName(String.valueOf(obj[4])));
        	deposit.setDepositType(obj[5] == null || obj[5].toString().isEmpty()? null : DepositType.valueOf(String.valueOf(obj[5])));
        	deposit.setDepositPurpose(obj[6] == null || obj[6].toString().isEmpty() ? null : DepositPurpose.valueOf(String.valueOf(obj[6])));
        	if ((obj[7].toString()).isEmpty()) {
        		deposit.setAmount(BigDecimal.ZERO);
        	} else {
        		amount = new BigDecimal(obj[7].toString());
        		deposit.setAmount(amount);
        	}
        	deposit.setAssetAmount(obj[8] == null || obj[8].toString().isEmpty() ? null : new BigDecimal(obj[8].toString()));
        	if ((obj[9].toString()).isEmpty()) {
        		deposit.setFee(BigDecimal.ZERO);
        	} else {
        		fee = new BigDecimal(obj[9].toString());
        		deposit.setFee(fee);
        	}
        	deposit.setJpyConversion(obj[10] == null || obj[10].toString().isEmpty() ? null : new BigDecimal(String.valueOf(obj[10])));
        	deposit.setAddress(String.valueOf(obj[11]));
        	deposit.setTransactionId(obj[12] == null || obj[12].toString().isEmpty() ? null : String.valueOf(obj[12]));
        	deposit.setDistinction(String.valueOf(obj[13]));
        	deposit.setTransactionIndex(obj[14] == null || obj[14].toString().isEmpty()? null : Long.valueOf(String.valueOf(obj[14])));
        	deposit.setDepositStatus(DepositStatus.valueOf(String.valueOf(obj[15])));
        	deposit.setComment(obj[16] == null || obj[16].toString().isEmpty()? null : String.valueOf(obj[16]));
        	deposit.setOwnertype(obj[17] == null || obj[17].toString().isEmpty()? null : String.valueOf(obj[17]));
        	deposit.setRecipienttype(obj[18] == null || obj[18].toString().isEmpty()? null : String.valueOf(obj[18]));
        	deposit.setLast_name(obj[19] == null || obj[19].toString().isEmpty() ? null : String.valueOf(obj[19]));
        	deposit.setFirst_name(obj[20] == null || obj[20].toString().isEmpty() ? null : String.valueOf(obj[20]));
        	deposit.setLast_name_kana(obj[21] == null || obj[21].toString().isEmpty() ? null : String.valueOf(obj[21]));
        	deposit.setFirst_name_kana(obj[22] == null || obj[22].toString().isEmpty() ? null : String.valueOf(obj[22]));
        	deposit.setLast_name_english(obj[23] == null || obj[23].toString().isEmpty() ? null : String.valueOf(obj[23]));
        	deposit.setFirst_name_english(obj[24] == null || obj[24].toString().isEmpty()? null : String.valueOf(obj[24]));
        	deposit.setLegalname(obj[25] == null || obj[25].toString().isEmpty()? null : String.valueOf(obj[25]));
        	deposit.setLegalname_kana(obj[26] == null || obj[26].toString().isEmpty() ? null : String.valueOf(obj[26]));
        	deposit.setLast_name_english(obj[27] == null || obj[27].toString().isEmpty() ? null : String.valueOf(obj[27]));
        	deposit.setAddresstype(obj[28] == null || obj[28].toString().isEmpty()? null : String.valueOf(obj[28]));
        	deposit.setExchange(obj[29] == null || obj[29].toString().isEmpty() ? null : String.valueOf(obj[29]));
        	deposit.setArea(obj[30] == null || obj[30].toString().isEmpty()? null : String.valueOf(obj[30]));
        	deposit.setAregion(obj[31] == null || obj[31].toString().isEmpty() ? null : String.valueOf(obj[31]));
        	deposit.setPurpose(obj[32] == null || obj[32].toString().isEmpty()? null : String.valueOf(obj[32]));
        	deposit.setRiskScore(obj[33] == null || obj[33].toString().isEmpty()? null : Integer.valueOf(String.valueOf(obj[33])));
        	deposit.setTransactionHash(obj[34] == null || obj[34].toString().isEmpty()? null:String.valueOf(obj[34]));
        	deposit.setSygnaTxId(obj[35] == null || obj[35].toString().isEmpty()? null:String.valueOf(obj[35]));
        	deposit.setSanctionMatch(obj[36] == null || obj[36].toString().isEmpty() ? false : Boolean.valueOf(String.valueOf(obj[36])));
        	deposit.setSygnaNotifyFlg(obj[37] == null || obj[37].toString().isEmpty()? null:String.valueOf(obj[37]));
			if (null != obj[38]) {
				deposit.setChainalysisMaxAlertLevel(ChainalysisAlertLevel.ofCode((String)obj[38]));
			}
        	deposit.setCreatedAt(formatterOut.parse(String.valueOf(obj[39])));
        	deposit.setUpdatedAt(formatterOut.parse(String.valueOf(obj[40])));
        	DepositAccount depositAccount = depositAccountService.findByUserIdAndCurrency(Long.valueOf(String.valueOf(obj[1])), Currency.valueOfName(String.valueOf(obj[2])));
        	deposit.setDepositAccount(depositAccount);
        	depositList.add(deposit);
        }

        if(ObjectUtils.isNotEmpty(depositList)) {
          depositList.stream()
            .forEach(d -> d.setAmount(d.getCurrency().getScaledAmount(d.getAmount(), RoundingMode.FLOOR)));
          depositList = depositList.stream()
              .sorted(Comparator.comparing(Deposit::getCreatedAt).reversed()).collect(Collectors.toList());
        }
        pg = new PageData<Deposit>(number, size, sizeSum, depositList);

    }finally {
        entityManager.clear();
        entityManager.close();
      }

    return ResponseEntity.ok(pg);
  }

  @GetMapping("/detail")
  public ResponseEntity<Deposit> get(
		  @RequestParam(value = "depositId", required = false) Long depositId){
	  return ResponseEntity.ok(
			  depositService.findOne(depositId));
  }

  @GetMapping("/download")
  public String download(
      HttpServletResponse response,
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "distinction", required = false) String distinction,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo)
      throws Exception {

    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    List<DepositReportData> reportDataList = new ArrayList<DepositReportData>();

    List<Deposit> res =
        depositService.findAllByCondition(userId, Currency.valueOfName(currency),
            distinction,dateFrom, dateTo, null, null, null);
    for (Deposit deposit : res) {
      reportDataList.add(new DepositReportData().setProperties(deposit));
    }
    downloadManager.download(response, reportDataList, reportPreFix, true);
    return null;
  }

  @PostMapping
  public ResponseEntity<Deposit> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody DepositPostForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    if (userService.findOne(form.getUserId()) == null) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
    }

    return ResponseEntity.ok(
        depositService.found(
            form.getUserId(),
            Currency.valueOfName(form.getCurrency()),
            form.getDepositAccountId(),
            form.getDepositType(),
            form.getDepositPurpose(),
            form.getAmount(),
            form.getFee(),
            form.getAddress(),
            form.getTransactionId(),
            form.getTransactionIndex(),
            form.getComment()));
  }

  @PutMapping("/apply")
  public ResponseEntity<Void> put(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "id") Long id,
      @RequestParam(value = "depositStatus") DepositStatus depositStatus,
      @RequestParam(value = "sygnaNotifyFlg") String sygnaNotifyFlg)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    if("NOTIFY".equals(sygnaNotifyFlg)) {
      sygnaDepositTransferService.apply(id, depositStatus);
    } else {
      depositService.apply(id, depositStatus);
    }
    return ResponseEntity.ok().build();
  }

  @PutMapping("/reject")
  public ResponseEntity<Void> reject(
      @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    depositService.reject(id);
    return ResponseEntity.ok().build();
  }

  @DeleteMapping("/cancel")
  public ResponseEntity<Deposit> delete(
      @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    return ResponseEntity.ok(depositService.cancel(id));
  }

  @PostMapping("/csvregist")
  public ResponseEntity<Serializable> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody DepositCsvPostForm[] res)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    // userId exist?
    List<Long> inputUserIds = new ArrayList<Long>();
    for (DepositCsvPostForm form : res) {
      if (!inputUserIds.contains(form.getUserId())) {
        inputUserIds.add(form.getUserId());
        if (userService.findOne(form.getUserId()) == null) {
          throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
        }
      }
    }

    ArrayList<Deposit> deposits = new ArrayList<>();

    for (DepositCsvPostForm form : res) {
      Deposit deposit = new Deposit();
      deposit.setUserId(form.getUserId());
      deposit.setCurrency(Currency.valueOfName(form.getCurrency()));
      deposit.setDepositAccountId(form.getDepositAccountId());
      deposit.setAmount(form.getAmount());
      deposit.setFee(form.getFee());
      deposit.setAddress(form.getAddress());
      deposit.setTransactionId(form.getTransactionId());
      deposit.setDepositStatus(DepositStatus.valueOf(form.getDepositStatus()));
      deposit.setComment(form.getComment());
      deposit.setCreatedAt(FormatUtil.parse(form.getCreatedAt(), FormatPattern.YYYYMMDD));
      deposit.setJpyConversion(BigDecimal.valueOf(0));

      deposits.add(depositService.save(deposit));
    }

    return ResponseEntity.ok(deposits);
  }
}
