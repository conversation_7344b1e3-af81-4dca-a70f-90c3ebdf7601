package exchange.admin.controller;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.response.ReportWithdrawalAccount;
import exchange.common.component.CsvDownloadManager;
import exchange.common.component.DataSourceManager;
import exchange.common.constant.Currency;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report-withdrawal-account")
public class V1ReportWithdrawalAccountController {
  private final DataSourceManager dataSourceManager;
  private final CsvDownloadManager<ReportWithdrawalAccount> downloadManager;

  @GetMapping()
  public void download(HttpServletResponse response, @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currencyString,
      @RequestParam(value = "address", required = false) String address,
      @RequestParam(value = "destinationTag", required = false) String destinationTag) throws Exception {

    Currency currency = Currency.valueOfName(currencyString);
    String sql = """
                SELECT
                    wa.id as id,
                    wa.user_id as user_id,
                    wa.currency as currency,
                    wa.address as address,
                    wa.label as label,
                    wa.destination_tag as destination_tag,
                    wa.enabled as enabled,
                    wa.created_at as created_at,
                    wa.updated_at as updated_at
                FROM
                    withdrawal_account wa
                WHERE
                    1 = 1
            """;
    if (userId != null) {
      sql += " and wa.user_id = :userId";
    }
    if (currency != null) {
      sql += " and wa.currency = :currency";
    }
    if (address != null) {
      sql += " and (wa.address = :address or wa.address like CONCAT(:address, '@', '%'))";
    }
    if (destinationTag != null) {
      sql += " and wa.address like CONCAT('%', '@', :destinationTag)";
    }
    
    EntityManager entityManager =
            dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    List<ReportWithdrawalAccount> statements = new ArrayList<>();
    
    try {
    	Query query = entityManager.createNativeQuery(sql);

        if (userId != null) {
          query.setParameter("userId", userId);
        }
        if (currency != null) {
          query.setParameter("currency", currency.getName());
        }
        if (address != null) {
          query.setParameter("address", address);
        }
        if (destinationTag != null) {
          query.setParameter("destinationTag", destinationTag);
        }
        @SuppressWarnings("unchecked")
        List<Object[]> list = query.getResultList();

        for (int i = 0; i < list.size(); i++) {
          ReportWithdrawalAccount statement = new ReportWithdrawalAccount();
            Object[] rec = list.get(i);
            int index = String.valueOf(rec[3]).indexOf("@");
            statement.setUserId(String.valueOf(rec[1]));
            statement.setCurrency(String.valueOf(rec[2]));
            statement.setAddress(
                String.valueOf(rec[2]).equals(Currency.XRP.name()) && index > 0 ? 
                String.valueOf(rec[3]).substring(0, index) :
                String.valueOf(rec[3]));
            statement.setDestinationTag(
                String.valueOf(rec[2]).equals(Currency.XRP.name()) && index > 0 ? 
                String.valueOf(rec[3]).substring(index + 1) :
                "");
            statement.setEnabled(String.valueOf(rec[6]));
            statements.add(statement);
        }
    }finally {
        entityManager.clear();
        entityManager.close();
      }

    downloadManager.download(response, statements, "report_withdrawal_account", true);
   }
}
