package exchange.admin.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.IEOBoProcessStatus;
import exchange.common.entity.IEOFiles;
import exchange.common.entity.IEORecruitInfo;
import exchange.common.exception.CustomException;
import exchange.common.model.response.IEORecruitInfoData;
import exchange.common.service.IEOFilesService;
import exchange.common.service.IEORecruitInfoService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/ieo-recruit")
public class V1IEORecruitInfoRestController extends ExchangeAdminController {
  private final IEORecruitInfoService ieoRecruitInfoService;
  private final IEOFilesService ieoFilesService;

  @GetMapping
  public ResponseEntity<IEORecruitInfoData> get(@AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(name = "id", required = true) Long id) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    IEORecruitInfo ieoRecruitInfo = ieoRecruitInfoService.findOne(id);
    
    IEORecruitInfo latestIEORecruitInfo = new IEORecruitInfo();
    List<IEORecruitInfo> ieoRecruitInfoList = ieoRecruitInfoService.findAll();
    if(ObjectUtils.isNotEmpty(ieoRecruitInfoList)) {
      latestIEORecruitInfo = ieoRecruitInfoService.findAll()
          .stream().max(Comparator.comparing(p -> p.getId())).get();
    }
    
    IEORecruitInfoData ieoRecruitInfoData = new IEORecruitInfoData().setProperties(ieoRecruitInfo,
        latestIEORecruitInfo.getBoProcessStatus(), latestIEORecruitInfo.getId());
    return ResponseEntity.ok(ieoRecruitInfoData);
  }

  @PutMapping
  public ResponseEntity<IEORecruitInfoData> update(@AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(name = "id", required = true) Long id,
      @RequestParam(name = "deal", required = true) String deal,
      @RequestParam(name = "branchNameTicker", required = true) String branchNameTicker,
      @RequestParam(name = "branchNameKana", required = true) String branchNameKana,
      @RequestParam(name = "outline", required = true) String outline,
      @RequestParam(name = "recruitDateFrom", required = true) String recruitDateFrom,
      @RequestParam(name = "recruitDateTo", required = true) String recruitDateTo,
      @RequestParam(name = "raffleDate", required = true) String raffleDate,
      @RequestParam(name = "quotaDateFrom", required = true) String quotaDateFrom,
      @RequestParam(name = "quotaDateTo", required = true) String quotaDateTo,
      @RequestParam(name = "saleAmount", required = true) BigDecimal saleAmount,
      @RequestParam(name = "unitPrice", required = true) BigDecimal unitPrice,
      @RequestParam(name = "feeRatio", required = true) BigDecimal feeRatio,
      @RequestParam(name = "shareAmount", required = true) BigDecimal shareAmount,
      @RequestParam(name = "applySharesMax", required = true) BigDecimal applySharesMax,
      @RequestParam(name = "applySharesMin", required = true) BigDecimal applySharesMin,
      @RequestParam(name = "disclosureInfo", required = false) MultipartFile disclosureInfo,
      @RequestParam(name = "saleResultDisclosureInfo", required = false) MultipartFile saleResultDisclosureInfo,
      @RequestParam(name = "regularInfoYear", required = false) String regularInfoYear,
      @RequestParam(name = "regularInfoMonth1", required = false) String regularInfoMonth1,
      @RequestParam(name = "regularInfoMonth2", required = false) String regularInfoMonth2,
      @RequestParam(name = "regularInfoMonth3", required = false) String regularInfoMonth3,
      @RequestParam(name = "regularInfoMonth4", required = false) String regularInfoMonth4,
      @RequestParam(name = "regularDisclosureInfo1", required = false) MultipartFile regularDisclosureInfo1,
      @RequestParam(name = "regularDisclosureInfo2", required = false) MultipartFile regularDisclosureInfo2,
      @RequestParam(name = "regularDisclosureInfo3", required = false) MultipartFile regularDisclosureInfo3,
      @RequestParam(name = "regularDisclosureInfo4", required = false) MultipartFile regularDisclosureInfo4,
      @RequestParam(name = "addDisclosureInfo1", required = false) MultipartFile addDisclosureInfo1,
      @RequestParam(name = "addDisclosureInfo2", required = false) MultipartFile addDisclosureInfo2,
      @RequestParam(name = "addDisclosureInfo3", required = false) MultipartFile addDisclosureInfo3) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    
    IEORecruitInfo ieoRecruitInfo = new IEORecruitInfo();
    if(!id.equals(0l)) {
      ieoRecruitInfo = ieoRecruitInfoService.findOne(id);

      if (ieoRecruitInfo == null) {
        throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
      }
    }

    ieoRecruitInfo.setDeal(deal);
    ieoRecruitInfo.setBranchNameTicker(branchNameTicker);
    ieoRecruitInfo.setBranchNameKana(branchNameKana);
    ieoRecruitInfo.setOutline(outline);
    ieoRecruitInfo.setRecruitDateFrom(new Date(Long.valueOf(recruitDateFrom)));
    ieoRecruitInfo.setRecruitDateTo(new Date(Long.valueOf(recruitDateTo)));
    ieoRecruitInfo.setRaffleDate(new Date(Long.valueOf(raffleDate)));
    ieoRecruitInfo.setQuotaDateFrom(new Date(Long.valueOf(quotaDateFrom)));
    ieoRecruitInfo.setQuotaDateTo(new Date(Long.valueOf(quotaDateTo)));
    if(id.equals(0l)) {
      ieoRecruitInfo.setBoProcessStatus(IEOBoProcessStatus.INIT);
    }
    ieoRecruitInfo.setSaleAmount(saleAmount);
    ieoRecruitInfo.setUnitPrice(unitPrice);
    ieoRecruitInfo.setFeeRatio(feeRatio.divide(new BigDecimal(100)));
    ieoRecruitInfo.setShareAmount(shareAmount);
    ieoRecruitInfo.setApplySharesMax(applySharesMax);
    ieoRecruitInfo.setApplySharesMin(applySharesMin);
    IEORecruitInfo ieoRecruitInfoUpdated = ieoRecruitInfoService.save(ieoRecruitInfo);
    // ファイル保存
    fileSave(disclosureInfo, ieoRecruitInfoUpdated.getId(), "NORMAL", null, null);
    fileSave(saleResultDisclosureInfo, ieoRecruitInfoUpdated.getId(), "RESULT", null, null);
    fileSave(addDisclosureInfo1, ieoRecruitInfoUpdated.getId(), "ADD1", null, null);
    fileSave(addDisclosureInfo2, ieoRecruitInfoUpdated.getId(), "ADD2", null, null);
    fileSave(addDisclosureInfo3, ieoRecruitInfoUpdated.getId(), "ADD3", null, null);
    fileSave(regularDisclosureInfo1, ieoRecruitInfoUpdated.getId(), "REGULAR1", regularInfoYear, regularInfoMonth1);
    fileSave(regularDisclosureInfo2, ieoRecruitInfoUpdated.getId(), "REGULAR2", regularInfoYear, regularInfoMonth2);
    fileSave(regularDisclosureInfo3, ieoRecruitInfoUpdated.getId(), "REGULAR3", regularInfoYear, regularInfoMonth3);
    fileSave(regularDisclosureInfo4, ieoRecruitInfoUpdated.getId(), "REGULAR4", regularInfoYear, regularInfoMonth4);
    
    IEORecruitInfo latestIEORecruitInfo = new IEORecruitInfo();
    List<IEORecruitInfo> ieoRecruitInfoList = ieoRecruitInfoService.findAll();
    if(ObjectUtils.isNotEmpty(ieoRecruitInfoList)) {
      latestIEORecruitInfo = ieoRecruitInfoService.findAll()
          .stream().max(Comparator.comparing(p -> p.getId())).get();
    }
    
    IEORecruitInfoData ieoRecruitInfoData = new IEORecruitInfoData().setProperties(
        ieoRecruitInfoService.findOne(ieoRecruitInfoUpdated.getId()), latestIEORecruitInfo.getBoProcessStatus(),
        latestIEORecruitInfo.getId());
    return ResponseEntity.ok(ieoRecruitInfoData);
  }
  
  private void fileSave(MultipartFile fileInfo, Long ieoRecuritId, String type, String year, String month) throws IOException {
    if(fileInfo != null && !ObjectUtils.isEmpty(fileInfo.getBytes())) {
      List<IEOFiles> oldFiles = ieoFilesService.findByCondition(type, ieoRecuritId, year, null);
      if(ObjectUtils.isEmpty(oldFiles)) {
        IEOFiles ieoFile = new IEOFiles();
        ieoFile.setIeoRecruitId(ieoRecuritId);
        ieoFile.setFileName(fileInfo.getOriginalFilename());
        ieoFile.setFile(fileInfo.getBytes());
        ieoFile.setFileType(type);
        ieoFile.setYear(year);
        ieoFile.setMonth(month);
        ieoFilesService.save(ieoFile);
      } else {
        IEOFiles ieoFile = oldFiles.get(0);
        ieoFile.setFileName(fileInfo.getOriginalFilename());
        ieoFile.setFile(fileInfo.getBytes());
        ieoFile.setMonth(month);
        ieoFilesService.save(ieoFile);
      }
    }
  }
}