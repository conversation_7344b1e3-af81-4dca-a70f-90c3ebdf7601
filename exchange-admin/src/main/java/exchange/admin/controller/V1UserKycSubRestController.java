package exchange.admin.controller;

import exchange.admin.entity.AdminUser;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.KycStatus;
import exchange.common.constant.KycSubStatus;
import exchange.common.exception.CustomException;
import exchange.common.service.UserKycSubService;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/admin/v1/user-kyc-sub")
@RequiredArgsConstructor
@RestController
public class V1UserKycSubRestController extends ExchangeAdminController {

  private final UserKycSubService userKycSubService;

  @GetMapping("statuses")
  public ResponseEntity<List<KycSubStatus>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "kycSubStatus", required = false) KycStatus kycStatus)
      throws CustomException {
    // permission check
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    if (kycStatus == null) {
      return ResponseEntity.ok(Arrays.stream(KycSubStatus.values()).toList());
    }
    final var filteringKycSubStatuses = Arrays.stream(KycSubStatus.values())
        .filter(kycSubStatus -> kycSubStatus.parentKycStatus == kycStatus).toList();
    return ResponseEntity.ok(filteringKycSubStatuses);
  }
}
