package exchange.admin.controller;

import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.TmsStatus;
import exchange.common.constant.ViewVariables;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.UneconomicalCryptoDepositUserReportData;
import exchange.common.model.response.UneconomicalCryptoDepositUserResponse;
import exchange.common.service.UneconomicalCryptoDepositUserService;
import exchange.common.util.DateUnit;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/uneconomical_crypto_deposit_user")
public class V1ReportUneconomicalCryptoDepositUserController extends ExchangeAdminController {

  private final UneconomicalCryptoDepositUserService service;
  private final CsvDownloadManager<UneconomicalCryptoDepositUserReportData> downloadManager;

  @GetMapping()
  public ResponseEntity<PageData<UneconomicalCryptoDepositUserResponse>> get(HttpServletResponse response,
  @AuthenticationPrincipal AdminUser adminUser,
  @RequestParam(value = "userId", required = false) Long userId,
  @RequestParam(value = "dateFrom", required = false) Long dateFrom,
  @RequestParam(value = "dateTo", required = false) Long dateTo,
  @RequestParam(value = "status", required = false) String status,
  @RequestParam(
    value = "number",
    required = false,
    defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
    Integer number,
  @RequestParam(
      value = "size",
      required = false,
      defaultValue = "" + ViewVariables.DEFAULT_SIZE)
      Integer size
  )
  throws Exception {

    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    // DBからデータを取得する
    var statusVal = status != null ? TmsStatus.valueOf(status) : null; 
    var pageable = PageRequest.of(number, size);
    var data = service.findAllByCondition(userId, dateFrom, dateTo, statusVal, pageable);

    var reportData = new ArrayList<UneconomicalCryptoDepositUserResponse>();
    for (var d : data.getContent()) {
      var report = new UneconomicalCryptoDepositUserResponse();
      report.setId(d.getId());
      report.setUserId(d.getUserId());
      report.setEmail(d.getEmail());
      report.setDepositCount(d.getDepositCount());
      report.setTmsStatus(d.getTmsStatus());
      report.setTargetAt(DateUnit.toFormatSlashDate(d.getTargetAt()));
      report.setCreatedAt(DateUnit.toFormatSlashDateTime(d.getCreatedAt()));
      reportData.add(report);
    }

    return ResponseEntity.ok(new PageData<>(data.getNumber(), data.getSize(), data.getTotalElements(), reportData));
  }

  @GetMapping("/download")
  public void download(HttpServletResponse response,
  @AuthenticationPrincipal AdminUser adminUser,
  @RequestParam(value = "userId", required = false) Long userId,
  @RequestParam(value = "dateFrom", required = false) Long dateFrom,
  @RequestParam(value = "dateTo", required = false) Long dateTo,
  @RequestParam(value = "status", required = false) String status)
  throws Exception {

    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    // DBからデータを取得する
    var statusVal = status != null ? TmsStatus.valueOf(status) : null; 
    var data = service.findAllByCondition(userId, dateFrom, dateTo, statusVal, Pageable.unpaged());

    var reportData = new ArrayList<UneconomicalCryptoDepositUserReportData>();
    for (var d : data.getContent()) {
      var report = new UneconomicalCryptoDepositUserReportData();
      report.setId(d.getId());
      report.setUserId(d.getUserId());
      report.setEmail(d.getEmail());
      report.setDepositCount(d.getDepositCount());
      report.setTmsStatus(d.getTmsStatus().displayName);
      report.setTargetAt(DateUnit.toFormatSlashDate(d.getTargetAt()));
      report.setCreatedAt(DateUnit.toFormatSlashDateTime(d.getCreatedAt()));
      reportData.add(report);
    }

    // ダウンロード情報を返却する
    downloadManager.download(response, reportData, "report_excessive_deposit_user_by_one_time", true);
  }

  @PutMapping("/apply")
  public ResponseEntity<Void> put(
      @AuthenticationPrincipal AdminUser adminUser, 
      @RequestParam(value = "id") Long id,
      @RequestParam(value = "tmsStatus") TmsStatus tmsStatus)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    service.apply(id, tmsStatus);
    return ResponseEntity.ok().build();
  }
}
