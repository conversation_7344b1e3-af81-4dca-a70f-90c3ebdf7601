package exchange.admin.controller;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.service.AdminUserService;
import exchange.common.component.CsvDownloadManager;
import exchange.common.component.CustomLogger;
import exchange.common.component.DataSourceManager;
import exchange.common.component.SesManager;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.LineFeed;
import exchange.common.constant.MailNoreplyType;
import exchange.common.constant.ViewVariables;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.constant.WithdrawalType;
import exchange.common.entity.Asset;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.User;
import exchange.common.entity.Withdrawal;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.entity.WithdrawalAudit;
import exchange.common.exception.CustomException;
import exchange.common.model.request.WithdrawalCsvForm;
import exchange.common.model.request.WithdrawalForm;
import exchange.common.model.request.WithdrawalInputForm;
import exchange.common.model.request.WithdrawalPutForm;
import exchange.common.model.response.PageData;
import exchange.common.model.response.WithdrawalReportData;
import exchange.common.service.AssetService;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.MailNoreplyService;
import exchange.common.service.UserService;
import exchange.common.service.WithdrawalAccountService;
import exchange.common.service.WithdrawalService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/withdrawal")
public class V1WithdrawalRestController extends ExchangeAdminController {

  private final AssetService assetService;
  private final AdminUserService adminUserService;
  private final CurrencyConfigService currencyConfigService;
  private final MailNoreplyService mailNoreplyService;
  private final SesManager sesManager;
  private final UserService userService;
  private final WithdrawalAccountService withdrawalAccountService;
  private final WithdrawalService withdrawalService;

  private static final CustomLogger log = new CustomLogger(ExchangeAdminController.class.getName());

  private final DataSourceManager dataSourceManager;

  @Autowired private final CsvDownloadManager<WithdrawalReportData> downloadManager;

  private final String reportPreFix = "暗号資産出金一覧";

  @GetMapping
  public ResponseEntity<PageData<Withdrawal>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size,
      @RequestParam(value = "distinction", required = false) String distinction)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis(): null;
    return ResponseEntity.ok(
        withdrawalService.findByConditionPageData(
            userId, Currency.valueOfName(currency), dateFrom, dateTo, distinction, null, number, size, true));
  }
  
  @GetMapping("/detail")
  public ResponseEntity<WithdrawalAccount> get(
		  @RequestParam(value = "withdrawalId", required = false) Long withdrawalId){
	  Withdrawal withdrawal = withdrawalService.findOne(withdrawalId);
	  return ResponseEntity.ok(
			  withdrawal.getWithdrawalAccount());
  }

  @GetMapping("/approve")
  public ResponseEntity<PageData<Withdrawal>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "withdrawalStatus", required = false) String withdrawalStatus,
      @RequestParam(value = "distinction", required = false) String distinction,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis(): null;
    return ResponseEntity.ok(
        withdrawalService.findByConditionPageData(
            userId,
            Currency.valueOfName(currency),
            dateFrom,
            dateTo,
            distinction,
            WithdrawalStatus.valueOfName(withdrawalStatus),
            number,
            size,
            true));
  }

  @GetMapping("/download")
  public String download(
      HttpServletResponse response,
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currency,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "distinction",required = false) String distinction)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
    List<WithdrawalReportData> reportDataList = new ArrayList<WithdrawalReportData>();

    List<Withdrawal> res =
        withdrawalService.findAllByCondition(
            userId, Currency.valueOfName(currency), dateFrom, dateTo,
            null, null, null, distinction);

    EntityManager em = dataSourceManager.getMasterEntityManagerFactory().createEntityManager();

    for (Withdrawal withdrawal : res) {
      // check performance [if huge records]
      List<WithdrawalAudit> audits =
          em.createQuery(
                  "SELECT t FROM WithdrawalAudit t where t.withdrawalId = :val",
                  WithdrawalAudit.class)
              .setParameter("val", withdrawal.getId())
              .getResultList();
      reportDataList.add(new WithdrawalReportData().setProperties(withdrawal, audits));
    }
    downloadManager.download(response, reportDataList, reportPreFix, true);

    return null;
  }

  @PostMapping
  public ResponseEntity<Withdrawal> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody WithdrawalForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    WithdrawalAccount withdrawalAccount =
        withdrawalAccountService.findOne(form.getWithdrawalAccountId());
    CurrencyConfig currencyConfig =
        currencyConfigService.findByCurrency(withdrawalAccount.getCurrency());
    return ResponseEntity.ok(
        withdrawalService.request(
            adminUser.getId(),
            currencyConfig,
            form.getAmount(),
            form.getComment(),
            withdrawalAccount));
  }

  @PostMapping("/formregist")
  public ResponseEntity<Withdrawal> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody WithdrawalInputForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    if (userService.findOne(form.getUserId()) == null) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
    }

    Asset asset = assetService.findOrCreate(form.getUserId(), form.getCurrency());
    if (form.getAmount().compareTo(asset.getOnhandAmount().subtract(asset.getLockedAmount()))
        == 1) {
      // amountが利用可能なassetより大きければエラー
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_AMOUNT);
    }
    if (form.getAmount().scale() > form.getCurrency().getPrecision()) {
      // amountの小数点以下の桁数が通貨のそれより大きければエラー
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_AMOUNT);
    }

    return ResponseEntity.ok(
        withdrawalService.applyNew(
            form.getUserId(),
            form.getCurrency(),
            WithdrawalType.COMPULSORY_WITHDRAWAL,
            form.getWithdrawalPurpose(),
            form.getAmount(),
            form.getWithdrawalFee(),
            form.getTransactionFee(),
            form.getTransactionId(),
            form.getWithdrawalAccountId(),
            form.getAddress(),
            form.getComment(),
            FormatUtil.parse(form.getCreatedAt(), FormatPattern.YYYY_MM_DD)));
  }

  @PostMapping("/csvregist")
  public ResponseEntity<Serializable> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody WithdrawalCsvForm[] res)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    // userId exist?
    List<Long> inputUserIds = new ArrayList<Long>();
    for (WithdrawalCsvForm form : res) {
      if (!inputUserIds.contains(form.getUserId())) {
        inputUserIds.add(form.getUserId());
        if (userService.findOne(form.getUserId()) == null) {
          throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
        }
      }
    }

    ArrayList<Withdrawal> withdrawals = new ArrayList<>();

    for (WithdrawalCsvForm form : res) {
      Withdrawal withdrawal = new Withdrawal();
      withdrawal.setUserId(form.getUserId());
      withdrawal.setCurrency(Currency.valueOfName(form.getCurrency()));
      withdrawal.setWithdrawalAccountId(form.getWithdrawalAccountId());
      withdrawal.setAmount(form.getAmount());
      withdrawal.setWithdrawalFee(form.getWithdrawalFee());
      withdrawal.setTransactionFee(form.getTransactionFee());
      withdrawal.setAddress(form.getAddress());
      withdrawal.setTransactionId(form.getTransactionId());
      withdrawal.setWithdrawalStatus(WithdrawalStatus.valueOf(form.getWithdrawalStatus()));
      withdrawal.setComment(form.getComment());
      withdrawal.setCreatedAt(FormatUtil.parse(form.getCreatedAt(), FormatPattern.YYYYMMDD));
      withdrawal.setJpyConversion(BigDecimal.valueOf(0));

      withdrawals.add(withdrawalService.save(withdrawal));
    }

    return ResponseEntity.ok(withdrawals);
  }

  @PutMapping
  public ResponseEntity<Void> put(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody WithdrawalPutForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    switch (form.getWithdrawalStatus()) {
      case DONE -> {
        Withdrawal withdrawal = withdrawalService.findOne(form.getId());
        if (adminUser.getEmail().contains(withdrawal.getUpdatedBy())) {
          throw new CustomException(
              ErrorCode.REQUEST_ERROR_ADMIN_WITHDRAWAL_CANNOT_APPROVED_SAME_USER);
        }
        withdrawalService.apply(form.getId());
        AdminUser admiUserWithdrawal =
            adminUserService.loadUserByUsername(withdrawal.getUpdatedBy());
        log.info(
            getClass().getName(),
            "withdrawal APPROVED admin_user_id = "
                + String.valueOf(admiUserWithdrawal.getId())
                + " withdrawal DONE admin_user_id = "
                + String.valueOf(adminUser.getId()));
        User user = userService.findOne(withdrawal.getUserId());
        MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.WITHDRAWAL_DONE);
        sesManager.send(
            mailNoreply.getFromAddress(),
            user.getEmail(),
            mailNoreply.getTitle(),
            mailNoreply.getContents(user.getAntiPhishingCode(), null, LineFeed.HTML));
      }
      case REJECTED -> withdrawalService.cancel(form.getId());
      case APPROVED,FIRST_AML_EXAMING,WAITING_SECOND_AML_EXAM,
        SECOND_AML_EXAMING,WAITING_ADD_INFO,ADD_INFO_CHECKING,AML_APPROVED -> {
          Withdrawal withdrawal = withdrawalService.findOne(form.getId());
          withdrawal.setWithdrawalStatus(form.getWithdrawalStatus());
          withdrawalService.saveapply(withdrawal);
      }
      default -> throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
    }

    return ResponseEntity.ok().build();
  }

  @DeleteMapping
  public ResponseEntity<Withdrawal> delete(
      @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    return ResponseEntity.ok(withdrawalService.cancel(id));
  }
}
