package exchange.admin.controller;

import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.exception.CustomException;
import exchange.common.model.request.WithdrawalAccountForm;
import exchange.common.model.response.PageData;
import exchange.common.service.WithdrawalAccountService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/withdrawal-account")
public class V1WithdrawalAccountRestController extends ExchangeAdminController {

  private final WithdrawalAccountService withdrawalAccountService;

  @GetMapping
  public ResponseEntity<PageData<WithdrawalAccount>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currencyString,
      @RequestParam(value = "address", required = false) String address,
      @RequestParam(value = "destinationTag", required = false) String destinationTag,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    Currency currency = Currency.valueOfName(currencyString);
    return ResponseEntity.ok(
        withdrawalAccountService.findByConditionPageData(
            userId, currency, address, destinationTag, number, size));
  }

  @PostMapping
  public ResponseEntity<WithdrawalAccount> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody WithdrawalAccountForm form)
      throws CustomException {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    WithdrawalAccount withdrawalAccount = new WithdrawalAccount();
    withdrawalAccount.setUserId(adminUser.getId());
    withdrawalAccount.setCurrency(Currency.valueOf(form.getCurrency()));
    withdrawalAccount.setLabel(form.getLabel());
    withdrawalAccount.setAddress(form.getAddress());
    withdrawalAccount.setDestinationTag(form.getDestinationTag());
    return ResponseEntity.ok(withdrawalAccountService.save(withdrawalAccount));
  }

  @DeleteMapping
  public ResponseEntity<WithdrawalAccount> delete(
      @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    WithdrawalAccount withdrawalAccount = withdrawalAccountService.findOne(id);

    if (!adminUser.getId().equals(withdrawalAccount.getUserId())) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
    }

    withdrawalAccountService.delete(withdrawalAccount);
    return ResponseEntity.ok(withdrawalAccount);
  }
}
