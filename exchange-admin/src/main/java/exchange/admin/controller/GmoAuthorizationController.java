package exchange.admin.controller;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import exchange.common.auth.AuthTypeEnum;
import exchange.common.auth.OAuth;
import exchange.common.config.GmoAuthorizationTokenConfiguration;

import exchange.common.config.GmoConfig;
import exchange.common.entity.AppConfiguration;
import exchange.common.repos.AppConfigurationRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/admin/v1")
@RestController
@Slf4j
public class GmoAuthorizationController {
    @Autowired
    GmoConfig gmoConfig;

    @Value("${coin.cus.host-external:@null}")
    String hostExternal ;

    @Autowired
    AppConfigurationRepository appConfigurationRepository;

    private final String configurationKey="exchange.common.config.GmoAuthorizationTokenConfiguration";

    @GetMapping("/gmo/authorization")
    public ResponseEntity<String> authorization(HttpSession session) throws JsonProcessingException {
        //to generate state to prevent CSRF
        List<String> scopeList = gmoConfig.getScope();
        String scopes = scopeList.stream().collect(Collectors.joining(StringUtils.SPACE));
        String hashKey= RandomStringUtils.randomAlphabetic(5,10);
        String redirectUri=hostExternal+gmoConfig.getRedirectUri();
        OAuth oAuth = new OAuth(gmoConfig.getClientId(), gmoConfig.getSecret(),redirectUri,scopes, AuthTypeEnum.CLIENT_SECRET_BASIC,hashKey);
        AppConfiguration appConfiguration = appConfigurationRepository.findFirstByKey(configurationKey);
        ObjectMapper obj = new ObjectMapper();
        GmoAuthorizationTokenConfiguration tokenConfiguration = obj.readValue(appConfiguration.getValue(), GmoAuthorizationTokenConfiguration.class);;
        tokenConfiguration.setHashKey(hashKey);
        tokenConfiguration.setSessionId(session.getId());
        appConfiguration.setValue(obj.writeValueAsString(tokenConfiguration));
        appConfigurationRepository.save(appConfiguration);
        String uri = null;
        try {
            log.info("generate state with hashKey:{},sessionId:{}",hashKey,session.getId());
            uri = oAuth.authorizationGetUrl(session.getId());

        } catch (Exception e) {
            log.warn("request gmo to authorization fail for:{}",e.getMessage());
        }
       return ResponseEntity.ok(uri);
    }
}
