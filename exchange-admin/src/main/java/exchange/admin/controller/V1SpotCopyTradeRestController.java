package exchange.admin.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.TradeType;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.CoverOrderConfig;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SpotCopyTradeNotCoveredSumData;
import exchange.common.service.CoverOrderConfigService;
import exchange.common.service.SymbolService;
import exchange.common.util.DateUnit;
import exchange.spot.entity.SpotCopyTrade;
import exchange.spot.service.SpotCopyTradeService;
import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;

// @Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/spot/copy-trade")
public class V1SpotCopyTradeRestController extends ExchangeAdminController {

  private final CoverOrderConfigService coverOrderConfigService;
  private final SymbolService symbolService;

  // adminでは無効な通貨ペア(currencyPairConfig.enabled=false)も処理対象とするため
  // paramのsymbolIdの有効性チェック不要

  @GetMapping("/not-covered-sum-page")
  public ResponseEntity<PageData<SpotCopyTradeNotCoveredSumData>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "currencyPair", required = false) CurrencyPair currencyPair,
      @RequestParam(value = "exchange", required = true) Exchange exchange,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    PageData<SpotCopyTradeNotCoveredSumData> pg =
        new PageData<SpotCopyTradeNotCoveredSumData>(number, size, 0, null);

    List<SpotCopyTradeNotCoveredSumData> spotCopyTradeNotCoveredSumDataList =
        new ArrayList<SpotCopyTradeNotCoveredSumData>();

    List<Symbol> symbols = symbolService.findAllListByCondition(TradeType.SPOT, currencyPair);
    if (CollectionUtils.isEmpty(symbols)) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    for (Symbol symbol : symbols) {
      // 未カバーのコピー注文約定履歴を取得
      List<SpotCopyTrade> spotCopyTrades =
          SpotCopyTradeService.getBean(symbol)
              .findAllByCondition(
                  symbol.getId(),
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  dateFrom,
                  dateTo,
                  null,
                  null,
                  null,
                  null,
                  null,
                  exchange,
                  null,
                  null,
                  null,
                  true);

      CoverOrderConfig coverOrderConfig =
          coverOrderConfigService.findOne(symbol.getId(), exchange, true);

      if (CollectionUtils.isEmpty(spotCopyTrades) || coverOrderConfig == null) {
        continue;
      }
      //      log.info(
      //          "averagePrice,"
      //              +
      // SpotCopyTradeService.getBean(symbol).getAverageCopyTradePrice(spotCopyTrades));

      SpotCopyTradeNotCoveredSumData tableData =
          new SpotCopyTradeNotCoveredSumData()
              .setProperties(
                  exchange,
                  symbol.getId(),
                  symbol.getCurrencyPair(),
                  spotCopyTrades.stream()
                      .filter(trade -> OrderSide.BUY.equals(trade.getOrderSide()))
                      .map(SpotCopyTrade::getAmount)
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  spotCopyTrades.stream()
                      .filter(trade -> OrderSide.SELL.equals(trade.getOrderSide()))
                      .map(SpotCopyTrade::getAmount)
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  coverOrderConfig.getMinOrderAmount(),
                  // 画面表示用に桁数処理を行う
                  symbol
                      .getCurrencyPair()
                      .getScaledPrice(
                          SpotCopyTradeService.getBean(symbol)
                              .getAverageCopyTradePrice(spotCopyTrades),
                          RoundingMode.FLOOR));

      spotCopyTradeNotCoveredSumDataList.add(tableData);
    }

    return CollectionUtils.isEmpty(spotCopyTradeNotCoveredSumDataList)
        ? ResponseEntity.ok(pg)
        : ResponseEntity.ok(
            pg.createPageData(
                number,
                size,
                spotCopyTradeNotCoveredSumDataList.size(),
                spotCopyTradeNotCoveredSumDataList));
  }
}
