package exchange.admin.controller;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.ExchangeSummary;
import exchange.common.exception.CustomException;
import exchange.common.model.response.ExchangeSummaryReportData;
import exchange.common.model.response.PageData;
import exchange.common.service.ExchangeSummaryService;
import exchange.common.util.DateUnit;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/exchange-summary")
public class V1ExchangeSummaryRestController extends ExchangeAdminController {

  private final ExchangeSummaryService exchangeSummaryService;
  private final CsvDownloadManager<ExchangeSummaryReportData> downloadManager;

  @GetMapping
  public ResponseEntity<PageData<ExchangeSummary>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      HttpServletRequest request,
      @RequestParam(value = "currency", required = false) Currency currency,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws CustomException {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
    Date dateToDate =
        dateTo != null ? DateUnit.getTommorowStartDate(new Date(dateTo)) : null;

    PageData<ExchangeSummary> basePageData =
        exchangeSummaryService.findByConditionPageData(
            currency, dateFromDate, dateToDate, true, number, size);
    List<ExchangeSummary> exchangeSummaryList = basePageData.getContent();
    List<ExchangeSummary> summaryList = new ArrayList<ExchangeSummary>();
    for (ExchangeSummary summary : exchangeSummaryList) {

      summary.setCurrentAmount(
          summary.getCurrency().getScaledAmount(summary.getCurrentAmount(), RoundingMode.FLOOR));
      summary.setJpyConversion(
          summary.getCurrency().getScaledAmount(summary.getJpyConversion(), RoundingMode.FLOOR));
      summary.setDepositAmount(
          summary.getCurrency().getScaledAmount(summary.getDepositAmount(), RoundingMode.FLOOR));
      summaryList.add(summary);
    }
    PageData<ExchangeSummary> pageData =
        new PageData<ExchangeSummary>(
            basePageData.getNumber(),
            basePageData.getSize(),
            basePageData.getTotalElements(),
            summaryList);

    return ResponseEntity.ok(pageData);
  }

  @GetMapping("/download")
  public void download(
      @AuthenticationPrincipal AdminUser adminUser,
      HttpServletResponse response,
      @RequestParam(value = "currency", required = false) Currency currency,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
    Date dateToDate =
        dateTo != null ? DateUnit.getTommorowStartDate(new Date(dateTo)) : null;

    List<ExchangeSummary> exchangeSummaryList =
        exchangeSummaryService.findByCondition(currency, dateFromDate, dateToDate, true);

    List<ExchangeSummaryReportData> reportDataList = new ArrayList<ExchangeSummaryReportData>();
    exchangeSummaryList
        .stream()
        .map(
            exchangeSummary -> {
              ExchangeSummaryReportData report = new ExchangeSummaryReportData();
              report.setProperties(exchangeSummary);
              return report;
            })
        .forEach(reportDataList::add);
    String fileNamePrefix = "exchange_summary";
    downloadManager.download(
        response, reportDataList, fileNamePrefix, ExchangeSummaryReportData.getReportHeader());
  }
}
