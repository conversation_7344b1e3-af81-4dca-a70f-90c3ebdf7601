package exchange.admin.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.DepositAccount;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.service.DepositAccountService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/deposit-account")
public class V1DepositAccountRestController extends ExchangeAdminController {

  private final DepositAccountService depositAccountService;

  @GetMapping
  public ResponseEntity<PageData<DepositAccount>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "currency", required = false) String currencyString,
      @RequestParam(value = "address", required = false) String address,
      @RequestParam(value = "destinationTag", required = false) String destinationTag,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    Currency currency = Currency.valueOfName(currencyString);
    return ResponseEntity.ok(
        depositAccountService.findByConditionPageData(
            userId, currency, address, destinationTag, number, size));
  }
}
