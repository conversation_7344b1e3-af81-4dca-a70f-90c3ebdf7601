package exchange.admin.controller;

import java.sql.Date;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.request.NewsPostForm;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.NewsType;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.News;
import exchange.common.exception.CustomException;
import exchange.common.model.request.NewsPutForm;
import exchange.common.model.response.PageData;
import exchange.common.service.NewsService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/news")
public class V1NewsRestController extends ExchangeAdminController {
  private final NewsService newsService;

  @GetMapping
  public ResponseEntity<PageData<News>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "newsType", required = false) String newsType,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    return ResponseEntity.ok(
        newsService.findByConditionPageData(
            dateFrom, dateTo, NewsType.valueOfName(newsType), number, size));
  }

  @GetMapping("/{id}")
  public ResponseEntity<News> get(
      @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    return ResponseEntity.ok(newsService.findOne(id));
  }

  @PostMapping
  public ResponseEntity<News> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody NewsPostForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    News news = new News();
    news.setNewsType(NewsType.valueOf(form.getNewsType()));
    news.setTitle(form.getTitle());
    news.setLink(form.getLink());
    news.setContents(form.getContents());
    Date sqlDate = java.sql.Date.valueOf(form.getDate());
    news.setDate(sqlDate);
    news.setEnabled(form.isEnabled());

    return ResponseEntity.ok(newsService.save(news));
  }

  @PutMapping
  public ResponseEntity<News> put(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody NewsPutForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    News news = newsService.findOne(form.getId());
    if (news == null) {
      news = new News();
    }

    news.setNewsType(NewsType.valueOf(form.getNewsType()));
    news.setTitle(form.getTitle());
    news.setLink(form.getLink());
    news.setContents(form.getContents());
    Date sqlDate = java.sql.Date.valueOf(form.getDate());
    news.setDate(sqlDate);
    news.setEnabled(form.isEnabled());

    return ResponseEntity.ok(newsService.save(news));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<News> delete(
      @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    News news = newsService.findOne(id);
    if (news == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_NEWS_NOT_FOUND);
    }
    newsService.delete(news);
    return ResponseEntity.ok(news);
  }
}
