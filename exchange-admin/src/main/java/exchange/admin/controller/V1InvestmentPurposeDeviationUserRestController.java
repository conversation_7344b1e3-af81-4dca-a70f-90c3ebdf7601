package exchange.admin.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.InvestmentPurposeDeviationUser;
import exchange.common.exception.CustomException;
import exchange.common.model.response.InvestmentPurposeDeviationUserReportData;
import exchange.common.model.response.PageData;
import exchange.common.service.InvestmentPurposeDeviationUserService;
import exchange.common.util.DateUnit;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/investment-purpose-deviation-user")
public class V1InvestmentPurposeDeviationUserRestController extends ExchangeAdminController {

  private final InvestmentPurposeDeviationUserService investmentPurposeDeviationUserService;
  private final CsvDownloadManager<InvestmentPurposeDeviationUserReportData> downloadManager;

  @GetMapping
  public ResponseEntity<PageData<InvestmentPurposeDeviationUser>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(
              value = "number",
              required = false,
              defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(
              value = "size",
              required = false,
              defaultValue = "" + ViewVariables.DEFAULT_SIZE)
          Integer size)
      throws CustomException {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Date createdAtFrom = dateFrom == null ? null : new Date(dateFrom);
    Date createdAtTo = dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo));
    return ResponseEntity.ok(
        investmentPurposeDeviationUserService.findByCondition(
            userId, createdAtFrom, createdAtTo, number, size));
  }

  @GetMapping("/download")
  public void download(
      @AuthenticationPrincipal AdminUser adminUser,
      HttpServletResponse response,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Date createdAtFrom = dateFrom == null ? null : new Date(dateFrom);
    Date createdAtTo = dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo));
    List<InvestmentPurposeDeviationUserReportData> reportDataList =
        new ArrayList<InvestmentPurposeDeviationUserReportData>();
    PageData<InvestmentPurposeDeviationUser> pageData =
        investmentPurposeDeviationUserService.findByCondition(
            userId, createdAtFrom, createdAtTo, 0, Integer.MAX_VALUE);
    for (InvestmentPurposeDeviationUser investmentPurposeDeviationUser : pageData.getContent()) {
      reportDataList.add(
          new InvestmentPurposeDeviationUserReportData()
              .setProperties(investmentPurposeDeviationUser));
    }

    String fileNamePrefix = "investmentPurposeDeviationUser";
    downloadManager.download(
        response,
        reportDataList,
        fileNamePrefix,
        InvestmentPurposeDeviationUserReportData.getReportHeader());
  }
}
