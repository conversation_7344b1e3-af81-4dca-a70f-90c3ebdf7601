package exchange.admin.controller;

import exchange.common.model.request.FiatDepositPutForm;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.component.CsvDownloadManager;
import exchange.common.component.DataSourceManager;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.FiatDepositStatus;
import exchange.common.constant.ReportLabel;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.FiatDeposit;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.request.FiatDepositPostForm;
import exchange.common.model.response.FiatDepositReportData;
import exchange.common.model.response.PageData;
import exchange.common.service.BankAccountService;
import exchange.common.service.FiatDepositService;
import exchange.common.service.UserService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/fiat-deposit")
public class V1FiatDepositRestController extends ExchangeAdminController {

  private final UserService userService;
  private final BankAccountService bankAccountService;
  private final FiatDepositService fiatDepositService;
  private final DataSourceManager dataSourceManager;

  @Autowired private final CsvDownloadManager<FiatDepositReportData> downloadManager;

  private final String reportPreFix = "日本円入金履歴_";

  @GetMapping
  public ResponseEntity<PageData<FiatDeposit>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "fiatDepositStatus", required = false) String fiatDepositStatus,
      @RequestParam(value = "email", required = false) String email,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "updatedAtFrom", required = false) Long updatedAtFrom,
      @RequestParam(value = "updatedAtTo", required = false) Long updatedAtTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
//    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
    Long userIdLong = 0L;
    User emailString = new User();
    emailString = userService.findByEmail(email);
    if (userId != null) {
		if (emailString != null) {
			if (emailString.getId().equals(userId)) {
				userIdLong = userId;
			}
		} else {
			if (StringUtils.isEmpty(email)) {
				userIdLong = userId;
			}
		}
	} else {
		if (emailString != null) {
			userIdLong = emailString.getId();
		} else {
			if (StringUtils.isEmpty(email)) {
				userIdLong = null;
			}
		}
	}
    return ResponseEntity.ok(
        fiatDepositService.findByConditionPageData(userIdLong, FiatDepositStatus.valueOfName(fiatDepositStatus), dateFrom, dateTo, updatedAtFrom, updatedAtTo, number, size));
  }

  @GetMapping("/{id}")
  public ResponseEntity<FiatDeposit> get(
      @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    return ResponseEntity.ok(fiatDepositService.findOne(id));
  }

  @GetMapping("/download")
  public void download(
      HttpServletResponse response,
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "fiatDepositStatus", required = false) String fiatDepositStatus,
      @RequestParam(value = "email", required = false) String email,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
	  @RequestParam(value = "updatedAtFrom", required = false) Long updatedAtFrom,
	  @RequestParam(value = "updatedAtTo", required = false) Long updatedAtTo)
      throws Exception {
//    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
//    updatedAtTo = updatedAtTo != null ? updatedAtTo + DateUnit.DAY.getMillis() : null;
    String sql =
        """
            SELECT
                fd.id,
                fd.created_at,
                CASE WHEN u.user_info_id IS NOT NULL THEN CONCAT(ui.last_name, ' ', ui.first_name)
                  ELSE CASE WHEN u.user_info_corporate_id IS NOT NULL THEN uic.name ELSE '' END END,
                fd.user_id,
                fd.amount,
                fd.fiat_deposit_status,
                fd.comment,
                pd.output_name
            FROM
                fiat_deposit fd
            LEFT JOIN
              paypay_deposit pd ON pd.fiat_deposit_id = fd.id
            LEFT JOIN
              user u ON fd.user_id = u.id
            LEFT JOIN
              user_info ui ON ui.id = u.user_info_id
            LEFT JOIN
              user_info_corporate uic ON uic.id = u.user_info_corporate_id
            WHERE 1 = 1
                and fd.created_at >= :from
                and fd.created_at <= :to
                and fd.updated_at >= :updatedAtfrom
                and fd.updated_at <= :updatedAtto
        """;

    // dynamic sql
    if (userId != null) sql += " and fd.user_id = :uid ";
    
    if (fiatDepositStatus != null) sql += " and fd.fiat_deposit_status = :fiatdepositstatus";
    
    if (email != null) sql += " and u.email = :email"; 
    sql += " ORDER BY fd.id, fd.created_at ";
    
    EntityManager entityManager =
            dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    List<FiatDepositReportData> statements = new ArrayList<>();
    
    try {
    	Query query =
        		entityManager
                .createNativeQuery(sql);

        // parameter for dynamic sql
        if (userId != null) query.setParameter("uid", userId);
        
        if (fiatDepositStatus != null) query.setParameter("fiatdepositstatus", fiatDepositStatus);
        
        if (email != null) query.setParameter("email", email);

        DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withLocale(Locale.JAPAN)
                .withZone(ZoneId.systemDefault());
        
        query.setParameter(
            "from", dateFrom != null ? formatter.format(Instant.ofEpochMilli(dateFrom)) : "2000-01-01");
        query.setParameter(
            "to", dateTo != null ? formatter.format(Instant.ofEpochMilli(dateTo)) : "2100-01-01");
        query.setParameter(
            "updatedAtfrom", updatedAtFrom != null ? formatter.format(Instant.ofEpochMilli(updatedAtFrom)) : "2000-01-01");
        query.setParameter(
            "updatedAtto", updatedAtTo != null ? formatter.format(Instant.ofEpochMilli(updatedAtTo)) : "2100-01-01");
        
        @SuppressWarnings("unchecked")
        List<Object[]> list = query.getResultList();
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        for (int i = 0; i < list.size(); i++) {
          FiatDepositReportData statement = new FiatDepositReportData();
          Object[] rec = list.get(i);
          statement.setId(String.valueOf(rec[0]));
          statement.setCreatedAt(FormatUtil.formatJst(FormatUtil.parse(String.valueOf(rec[1]), FormatPattern.YYYY_MM_DD_HH_MM_SS_S), FormatPattern.YYYY_MM_DD_HH_MM_SS_S));
          statement.setUsername(String.valueOf(rec[2]));
          statement.setUserId(String.valueOf(rec[3]));
          statement.setAmount(toStrigForReportNotByCurrency(new BigDecimal(String.valueOf(rec[4])).stripTrailingZeros(), numberFormat));
          statement.setStatus(ReportLabel.FiatDepositStatus.valueOfName(String.valueOf(rec[5])).getLabel());
          statement.setComment(String.valueOf(rec[6]));
          statement.setOutputName(String.valueOf(rec[7]) != null ? String.valueOf(rec[7]) : "");
          statements.add(statement);
        }
    }finally {
        entityManager.clear();
        entityManager.close();
      }
    
    downloadManager.download(response, statements, reportPreFix, true);
  }
  
  private static String toStrigForReportNotByCurrency(BigDecimal value, NumberFormat numberFormat) {
	  // stripTrailingZeros() 末尾0除去
	  // toPlainString() 指数表記にならないようにString変換
	  String strValue = value.toPlainString();
	  // 整数部3桁区切り
	  int decimalPointIndex = strValue.indexOf(".");
	  if (decimalPointIndex < 0) {
	    // 小数点なし
	    return numberFormat.format(Long.valueOf(strValue));
	  } else {
	    // 小数点あり
	    String seisu = strValue.substring(0, decimalPointIndex);
	    return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
	  }
	}

  @PostMapping
  public ResponseEntity<FiatDeposit> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody FiatDepositPostForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    if (userService.findOne(form.getUserId()) == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }
    if (bankAccountService.findOne(form.getBankAccountId()) == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }
    FiatDeposit fiatDeposit = new FiatDeposit();
    fiatDeposit.setUserId(form.getUserId());
    fiatDeposit.setBankAccountId(form.getBankAccountId());
    fiatDeposit.setAmount(form.getAmount());
    fiatDeposit.setFee(form.getFee());
    fiatDeposit.setFiatDepositStatus(FiatDepositStatus.valueOf(form.getFiatDepositStatus()));
    fiatDeposit.setComment(form.getComment());

    return ResponseEntity.ok(fiatDepositService.save(fiatDeposit));
  }

  @PutMapping
  public ResponseEntity<Object> put(@AuthenticationPrincipal AdminUser adminUser,
      @Valid @RequestBody FiatDepositPutForm form) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    if (FiatDepositStatus.valueOf(form.getFiatDepositStatus()) == FiatDepositStatus.DONE) {
      fiatDepositService.applyWithComment(
          form.getId(),
          form.getComment());
      return ResponseEntity.ok().build();
    }
    fiatDepositService.update(
        form.getId(),
        FiatDepositStatus.valueOf(form.getFiatDepositStatus()),
        form.getComment());
    return ResponseEntity.ok().build();
  }
}
