package exchange.admin.controller;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.response.SpotTradeAdminTableData;
import exchange.common.component.CsvDownloadManager;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeAction;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.PosTradeReportData;
import exchange.common.service.SymbolService;
import exchange.pos.entity.PosTrade;
import exchange.pos.service.PosTradeService;
import exchange.spot.entity.SpotTradeAdaJpy;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Timed
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/v1/pos/trade")
public class V1PosTradeRestController extends ExchangeAdminController {

    private final SymbolService symbolService;
    private final CsvDownloadManager<PosTradeReportData> downloadManager;
    private final PosTradeService posTradeService;

    @GetMapping("/allForPos")
    public ResponseEntity<PageData<SpotTradeAdminTableData>> getAllForPos(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "symbolId", required = true) Long symbolId,
            @RequestParam(value = "userIds", required = false) List<Long> userIds,
            @RequestParam(value = "exceptUserIds", required = false) List<Long> exceptUserIds,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "tradeAction", required = false) TradeAction tradeAction,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderChannel", required = false) OrderChannel orderChannel,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
            throws Exception {
        if (!(hasAdminAuthority(adminUser)
                || hasUpdateAuthority(adminUser)
                || hasViewerAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }

        PageData<SpotTradeAdminTableData> pg =
                new PageData<SpotTradeAdminTableData>(number, size, 0, null);
        PageData<SpotTradeAdminTableData> temp =
                new PageData<SpotTradeAdminTableData>(number, size, 0, null);

        if (symbolId == null) {
            return ResponseEntity.ok(pg);
        }

        Symbol symbol = symbolService.findOne(symbolId);

        // 約定一覧取得
        List<PosTrade> posTradesMain =
                posTradeService.findByConditionForBo(
                        symbolId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderSide,
                        orderChannel,
                        number,
                        size,
                        false);

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosTrade> posTradeHistoryList =
                posTradeService.findAllFromHistoryForBo(
                        symbol,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFromDate,
                        dateToDate,
                        null,
                        tradeAction,
                        orderSide,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderChannel);

        // merge & sort
        posTradesMain.addAll(posTradeHistoryList);
        posTradesMain.sort((x, y) -> y.getId().compareTo(x.getId()));
        List<PosTrade> posTrades = posTradesMain; // ソート処理

        // PageData作成
        //   spotTrades + spotTradesHistory合算後count
        Long count = (long) posTrades.size();
        PageData<PosTrade> data =
                createPageData(posTrades, count, number, size);

        // 編集
        CurrencyPair currencyPair = symbol.getCurrencyPair();
        for (int i = 0; i < data.getContent().size(); i++) {
            // 桁数揃え
            SpotTradeAdminTableData tableData =
                    new SpotTradeAdminTableData().setProperties(createSpotTrade(data.getContent().get(i)));
            tableData.setAmount(currencyPair.getPosScaledAmount(tableData.getAmount(), RoundingMode.DOWN));
            tableData.setPrice(currencyPair.getScaledPrice(tableData.getPrice(), RoundingMode.DOWN));
            tableData.setFee(currencyPair.getScaledAsset(tableData.getFee(), RoundingMode.DOWN));
            temp.getContent().add(tableData);
        }

        temp.addTotalElements(data.getTotalElements()); // 全件数を取得するためにループは最後まで回す
        pg = new PageData<SpotTradeAdminTableData>(
                number, size, temp.getTotalElements(), temp.getContent());

        return ResponseEntity.ok(pg);
    }

    @GetMapping("/download")
    public String download(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "symbolId", required = true) Long symbolId,
            @RequestParam(value = "userIds", required = false) List<Long> userIds,
            @RequestParam(value = "exceptUserIds", required = false) List<Long> exceptUserIds,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "tradeAction", required = false) TradeAction tradeAction,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderChannel", required = false) OrderChannel orderChannel)
            throws Exception {
        if (!(hasAdminAuthority(adminUser)
                || hasUpdateAuthority(adminUser)
                || hasViewerAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }

        List<PosTradeReportData> reportDataList = new ArrayList<PosTradeReportData>();
        Symbol symbol = symbolService.findOne(symbolId);
        if (symbol == null) {
            return null;
        }

        // 約定一覧取得
        List<PosTrade> posTrades =
                posTradeService.findByConditionForBo(
                        symbolId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        tradeAction,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderSide,
                        orderChannel,
                        0,
                        Integer.MAX_VALUE,
                        false);

        if (posTrades == null) {
            posTrades = new ArrayList<>();
        }
        for (PosTrade posTrade : posTrades) {
            reportDataList.add(
                    new PosTradeReportData().setProperties(posTrade, symbol.getCurrencyPair()));
        }

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosTrade> posTradeHistoryList =
                posTradeService.findAllFromHistoryForBo(
                        symbol,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFromDate,
                        dateToDate,
                        null,
                        tradeAction,
                        orderSide,
                        orderTypes != null ? orderTypes.get(0) : null,
                        orderChannel);

        for (PosTrade posTrade : posTradeHistoryList) {
            reportDataList.add(
                    new PosTradeReportData().setProperties(posTrade, symbol.getCurrencyPair()));
        }
        reportDataList.sort((x, y) -> x.getId().compareTo(y.getId()));

        String fileNamePrefix = "posTrade";
        downloadManager.download(
                response, reportDataList, fileNamePrefix, PosTradeReportData.getReportHeader());

        return null;
    }

    public PageData<PosTrade> createPageData(List<PosTrade> content, Long count, Integer number, Integer size) {
        List<PosTrade> pageContents = new ArrayList<PosTrade>();

        int maxSize = (number * size + size) > content.size() ? content.size() : (number * size + size);

        for (int i = number * size; i < maxSize; i++) {

            pageContents.add(content.get(i));
        }

        return new PageData<PosTrade>(number, size, count, pageContents);
    }

    public SpotTradeAdaJpy createSpotTrade(PosTrade trade) {
        SpotTradeAdaJpy spotTradeBaseTemp = new SpotTradeAdaJpy();
        BeanUtils.copyProperties(trade, spotTradeBaseTemp);
        spotTradeBaseTemp.setTargetUserId(0L);
        spotTradeBaseTemp.setTargetOrderId(0L);
        return spotTradeBaseTemp;
    }
}
