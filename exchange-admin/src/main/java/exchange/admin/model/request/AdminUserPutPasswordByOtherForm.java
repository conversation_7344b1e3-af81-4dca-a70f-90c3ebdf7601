package exchange.admin.model.request;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

public class AdminUserPutPasswordByOtherForm extends AdminForm {

  @Getter @Setter @NotNull private Long adminUserId;

  @Getter
  @Setter
  @NotNull
  @Length(min = 8, message = "Passwordが8文字未満です。")
  private String password;

  @JsonIgnore
  @AssertTrue(message = "Passwordは英数字と次の記号が必要です。!\"#$%&'*+-/=?@^_`{};:|~")
  public boolean isIncludeRequiresChar() {
    return isIncludeRequiresChar(password);
  }
}
