package exchange.admin.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import exchange.common.constant.DepositPurpose;
import exchange.common.constant.DepositType;
import lombok.Getter;
import lombok.Setter;

public class DepositPostForm {
  @Getter
  @Setter
  @NotNull
  private Long userId;

  @Getter
  @Setter
  @NotNull
  private String currency;

  @Getter
  @Setter
  @NotNull
  private Long depositAccountId;

  @Getter
  @Setter
  @NotNull
  private BigDecimal amount;

  @Getter
  @Setter
  @NotNull
  private BigDecimal fee;

  @Getter
  @Setter
  @NotNull
  private String address;

  @Getter
  @Setter
  private String transactionId;

  @Getter
  @Setter
  private Long transactionIndex;

  @Getter
  @Setter
  private String depositStatus;

  @Getter
  @Setter
  private DepositType depositType;

  @Getter
  @Setter
  private DepositPurpose depositPurpose;

  @Getter
  @Setter
  private String comment;

  @Getter
  @Setter
  @NotNull
  private String createdAt;

}
