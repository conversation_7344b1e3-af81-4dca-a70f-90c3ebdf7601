package exchange.admin.model.response;

import com.amazonaws.services.ec2.model.DescribeFleetHistoryRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Authority;
import exchange.common.constant.Prefecture;
import exchange.common.constant.ReportLabel;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserKyc;
import exchange.common.exception.CustomException;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@JsonPropertyOrder({
  "ユーザーID",
  "ユーザータイプ",
  "ユーザーステータス",
  "経由",
  "UUID",
  "KYCステータス",
  "制裁チェック",
  "メールアドレス",
  "ユーザー名",
  "郵便番号",
  "住所",
  "KYCステータス更新日時",
  "内部者該当",
  "自社口座",
  "在留カード期限",
  "作成日時",
  "更新日時"
})
@Slf4j
public class ReportUserNormal {

  @Getter
  @Setter
  @JsonProperty("ユーザーID")
  private String userId = "";

  @Getter
  @Setter
  @JsonProperty("ユーザータイプ")
  private String userAuthority = "";

  @Getter
  @Setter
  @JsonProperty("ユーザーステータス")
  private String userStatus = "";
  
  @Getter
  @Setter
  @JsonProperty("経由")
  private String affiliateFrom = "";
  
  @Getter
  @Setter
  @JsonProperty("UUID")
  private String uuid = "";


  @Getter
  @Setter
  @JsonProperty("KYCステータス")
  private String kycStatus = "";

  @Getter
  @Setter
  @JsonProperty("メールアドレス")
  private String email = "";

  @Getter
  @Setter
  @JsonProperty("ユーザー名")
  private String userName = "";

  @Getter
  @Setter
  @JsonProperty("郵便番号")
  private String zipCode = "";

  @Getter
  @Setter
  @JsonProperty("住所")
  private String address = "";

  @Getter
  @Setter
  @JsonProperty("KYCステータス更新日時")
  private String kyc_status_updated_at = "";

  @Getter
  @Setter
  @JsonProperty("制裁チェック")
  private String antisocialStatus = "";

  @Getter
  @Setter
  @JsonProperty("内部者該当")
  private String insider = "";
  
  @Getter
  @Setter
  @JsonProperty("自社口座")
  private String insideAccountFlg = "";

  @Getter
  @Setter
  @JsonProperty("在留カード期限")
  private String residenceCardExpiredAt = "";

  @Getter
  @Setter
  @JsonProperty("作成日時")
  private String createdAt = "";
  
  @Getter
  @Setter
  @JsonProperty("更新日時")
  private String updatedAt = "";

  public ReportUserNormal(User user) throws CustomException {
    this.userId = user.getId().toString();
    String authority = "";
    try {
      authority = user.getAuthorities().get(0).getAuthority();
    } catch (Exception e) {
      log.error("authority not found!" + user.getId().toString());
      throw e;
    }
    this.userAuthority = ReportLabel.Authority.valueOfName(authority).getLabel();
    this.userStatus =
        ReportLabel.UserStatus.valueOfName(user.getUserStatus().toString()).getLabel();
    this.affiliateFrom = user.getAffiliateInfo() != null ? user.getAffiliateInfo().getAffiliateName() : "";
    this.uuid = user.getUuid() != null ? user.getUuid() : "";
    this.kycStatus = ReportLabel.KycStatus.valueOfName(user.getKycStatus().toString()).getLabel();
    this.email = user.getEmail();
    this.insider = user.isInsider() ? "有" : "無";
    this.insideAccountFlg = user.isInsideAccountFlg() ? "自社" : "非自社";
    this.createdAt = 
    	FormatUtil.formatJst(user.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
    this.updatedAt =
        FormatUtil.formatJst(user.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
    // 名前・住所情報
    if (authority == Authority.PERSONAL.toString()) {
      // 個人ユーザー
      UserInfo userInfo = user.getUserInfo();
      if (userInfo != null) {
        this.userName = userInfo.getLastName() + userInfo.getFirstName();
        this.zipCode = userInfo.getZipCode();
        String building = userInfo.getBuilding();
        this.address =
            Prefecture.valueOfCode(userInfo.getPrefecture()).getKanjiName()
                + userInfo.getCity()
                + userInfo.getAddress()
                + (building == null ? "" : building);
        this.residenceCardExpiredAt = FormatUtil.formatJst(userInfo.getResidenceCardExpiredAt(), FormatPattern.YYYY_MM_DD_SLASH);
      }
    } else if (authority == Authority.CORPORATE.toString()) {
      // 法人ユーザー
      UserInfoCorporate userInfoCorporate = user.getUserInfoCorporate();
      if (userInfoCorporate != null) {
        this.userName = userInfoCorporate.getName();
        this.zipCode = userInfoCorporate.getZipCode();
        String building = userInfoCorporate.getBuilding();
        this.address =
            Prefecture.valueOfCode(userInfoCorporate.getPrefecture()).getKanjiName()
                + userInfoCorporate.getCity()
                + userInfoCorporate.getAddress()
                + (building == null ? "" : building);
      }
    }
    // KYC情報
    UserKyc userKyc = user.getUserKyc();
    if (userKyc != null) {
      this.kyc_status_updated_at =
          FormatUtil.formatJst(userKyc.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
      this.antisocialStatus = ReportLabel.RewardAntisocialStatus.valueOfName(userKyc.getAntisocialStatus().toString()).getLabel();
    }
  }
}
