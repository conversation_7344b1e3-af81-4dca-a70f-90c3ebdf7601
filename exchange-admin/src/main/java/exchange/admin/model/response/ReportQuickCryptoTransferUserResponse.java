package exchange.admin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.entity.QuickCryptoTransferUser;
import exchange.common.util.DateUnit;

@JsonPropertyOrder({
    "ID",
    "ユーザーID",
    "メールアドレス",
    "作成日時",
    "TMSステータス",
    "対象日時"
})
public record ReportQuickCryptoTransferUserResponse(

    @JsonProperty("ID")
    Long id,

    @JsonProperty("ユーザーID")
    Long userId,

    @JsonProperty("メールアドレス")
    String email,

    @JsonProperty("作成日時")
    String createdAt,

    @JsonProperty("TMSステータス")
    String tmsStatus,

    @JsonProperty("対象日時")
    String targetAt) {

  public static ReportQuickCryptoTransferUserResponse create(final QuickCryptoTransferUser entity) {
    final var data = new ReportQuickCryptoTransferUserResponse(
        entity.getId(),
        entity.getUserId(),
        entity.getUser().getEmail(),
        DateUnit.toFormatSlashDateTime(entity.getCreatedAt()),
        entity.getTmsStatus().displayName,
        DateUnit.toFormatSlashDate(entity.getTargetAt())
    );
    return data;
  }
}