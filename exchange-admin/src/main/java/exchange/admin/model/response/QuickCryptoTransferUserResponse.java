package exchange.admin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.QuickCryptoTransferUser;
import exchange.common.util.DateUnit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class QuickCryptoTransferUserResponse {

  @Getter
  @Setter
  private Long id;

  @Getter
  @Setter
  private Long userId;

  @Getter
  @Setter
  private String email;

  @Getter
  @Setter
  private String createdAt;

  @Getter
  @Setter
  private TmsStatus tmsStatus;

  @Getter
  @Setter
  private String targetAt;

  public static QuickCryptoTransferUserResponse create(final QuickCryptoTransferUser entity) {
    final var data = new QuickCryptoTransferUserResponse();
    data.setId(entity.getId());
    data.setUserId(entity.getUserId());
    data.setEmail(entity.getUser().getEmail());
    data.setCreatedAt(DateUnit.toFormatSlashDateTime(entity.getCreatedAt()));
    data.setTmsStatus(entity.getTmsStatus());
    data.setTargetAt(DateUnit.toFormatSlashDate(entity.getTargetAt()));
    return data;
  }
}