package exchange.admin.model.response;

import lombok.Getter;
import lombok.Setter;

public class UserKycInformation {

  // 申請回数
  @Getter @Setter public long applicationCount = 0;

  // 否認回数
  @Getter @Setter public long ngCount = 0;

  // 本人情報変更回数
  @Getter @Setter public long updateCount = 0;

  // 最新KYCが完了しているか
  @Getter public boolean isFixedLatest = false;

  public void setIsFixedLatest(boolean _isFixedLatest) {
    this.isFixedLatest = _isFixedLatest;
  }
}
