package exchange.admin.model.response;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import exchange.common.constant.Authority;
import exchange.common.constant.Gender;
import exchange.common.constant.Prefecture;
import exchange.common.constant.ReportLabel;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserInfoCorporateAgent;
import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.entity.UserInfoCorporateRepresentative;
import exchange.common.entity.UserKyc;
import exchange.common.exception.CustomException;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@JsonPropertyOrder({
  "ユーザーID",
  "ユーザータイプ",
  "ユーザーステータス",
  "KYCステータス",
  "メールアドレス",
  "ユーザー名",
  "ユーザー名カナ",
  "郵便番号",
  "住所",
  "KYCステータス更新日時",
  "制裁チェック",
  "内部者該当",
  "自社口座",
  "在留カード期限",
  "生年月日（設立日）",
  "決算月",
  "性別",
  "電話番号",
  "職業",
  "勤務先（または屋号）",
  "部署・役職",
  "事業内容",
  "収入源",
  "年商・年収",
  "金融資産額",
  "代表者名",
  "カナ代表者",
  "代表者役職",
  "代表者郵便番号",
  "代表者住所",
  "代表者生年月日",
  "代表者の外国PEPsチェック",
  "実質的支配者の申告",
  "実質的支配者(1人目)名",
  "カナ実質的支配者(1人目)",
  "実質的支配者(1人目)役職",
  "実質的支配者(1人目)郵便番号",
  "実質的支配者(1人目)住所",
  "実質的支配者(1人目)生年月日",
  "実質的支配者(1人目)の外国PEPsチェック",
  "実質的支配者(2人目)名",
  "カナ実質的支配者(2人目)",
  "実質的支配者(2人目)役職",
  "実質的支配者(2人目)郵便番号",
  "実質的支配者(2人目)住所",
  "実質的支配者(2人目)生年月日",
  "実質的支配者(2人目)の外国PEPsチェック",
  "実質的支配者(3人目)名",
  "カナ実質的支配者(3人目)",
  "実質的支配者(3人目)役職",
  "実質的支配者(3人目)郵便番号",
  "実質的支配者(3人目)住所",
  "実質的支配者(3人目)生年月日",
  "実質的支配者(3人目)の外国PEPsチェック",
  "実質的支配者(4人目)名",
  "カナ実質的支配者(4人目)",
  "実質的支配者(4人目)役職",
  "実質的支配者(4人目)郵便番号",
  "実質的支配者(4人目)住所",
  "実質的支配者(4人目)生年月日",
  "実質的支配者(4人目)の外国PEPsチェック",
  "担当者名",
  "カナ担当者",
  "担当者役職",
  "担当者郵便番号",
  "担当者住所",
  "担当者生年月日",
  "担当者の外国PEPsチェック",
  "主なご利用目的",
  "投資目的",
  "投資経験(仮想通貨)",
  "投資経験(FX)",
  "投資経験(株式)",
  "投資経験(投資信託)",
  "申込経緯",
  "経由",
  "UUID"
})
@Slf4j
public class ReportUserDetail {

  @Getter
  @Setter
  @JsonProperty("ユーザーID")
  private String userId = "";

  @Getter
  @Setter
  @JsonProperty("ユーザータイプ")
  private String userAuthority = "";

  @Getter
  @Setter
  @JsonProperty("ユーザーステータス")
  private String userStatus = "";
  
  @Getter
  @Setter
  @JsonProperty("経由")
  private String affiliateFrom = "";
  
  @Getter
  @Setter
  @JsonProperty("UUID")
  private String uuid = "";

  @Getter
  @Setter
  @JsonProperty("KYCステータス")
  private String kycStatus = "";

  @Getter
  @Setter
  @JsonProperty("メールアドレス")
  private String email = "";

  @Getter
  @Setter
  @JsonProperty("ユーザー名")
  private String userName = "";

  @Getter
  @Setter
  @JsonProperty("ユーザー名カナ")
  private String userKana = "";

  @Getter
  @Setter
  @JsonProperty("郵便番号")
  private String zipCode = "";

  @Getter
  @Setter
  @JsonProperty("住所")
  private String address = "";

  @Getter
  @Setter
  @JsonProperty("KYCステータス更新日時")
  private String kycStatusUpdatedAt = "";

  @Getter
  @Setter
  @JsonProperty("制裁チェック")
  private String antisocialStatus = "";

  @Getter
  @Setter
  @JsonProperty("内部者該当")
  private String insider = "";
  
  @Getter
  @Setter
  @JsonProperty("自社口座")
  private String insideAccountFlg = "";

  @Getter
  @Setter
  @JsonProperty("在留カード期限")
  private String residenceCardExpiredAt = "";

  @Getter
  @Setter
  @JsonProperty("生年月日（設立日）")
  private String birthdayOrEstablishedDay = "";

  @Getter
  @Setter
  @JsonProperty("決算月")
  private String accountingMonth = "";

  @Getter
  @Setter
  @JsonProperty("性別")
  private String gender = "";

  @Getter
  @Setter
  @JsonProperty("電話番号")
  private String phoneNumber = "";

  @Getter
  @Setter
  @JsonProperty("職業")
  private String occupation = "";

  @Getter
  @Setter
  @JsonProperty("業種")
  private String industry = "";

  @Getter
  @Setter
  @JsonProperty("勤務先（または屋号）")
  private String workPlace = "";

  @Getter
  @Setter
  @JsonProperty("部署・役職")
  private String position = "";

  @Getter
  @Setter
  @JsonProperty("事業内容")
  private String bussinessContent = "";
  
  @Getter
  @Setter
  @JsonProperty("収入源")
  private String priceFrom = "";

  @Getter
  @Setter
  @JsonProperty("年商・年収")
  private String incomeOrSales = "";

  @Getter
  @Setter
  @JsonProperty("金融資産額")
  private String financialAssets = "";

  @Getter
  @Setter
  @JsonProperty("代表者名")
  private String representativeName = "";

  @Getter
  @Setter
  @JsonProperty("カナ代表者")
  private String representativeKana = "";

  @Getter
  @Setter
  @JsonProperty("代表者役職")
  private String representativePosition = "";

  @Getter
  @Setter
  @JsonProperty("代表者郵便番号")
  private String representativeZipCode = "";

  @Getter
  @Setter
  @JsonProperty("代表者住所")
  private String representativeAddress = "";

  @Getter
  @Setter
  @JsonProperty("代表者生年月日")
  private String representativeBirthday = "";

  @Getter
  @Setter
  @JsonProperty("代表者の外国PEPsチェック")
  private String representativeForeignPeps = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者の申告")
  private String ultimateBeneficialOwnership = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(1人目)名")
  private String owner1Name = "";

  @Getter
  @Setter
  @JsonProperty("カナ実質的支配者(1人目)")
  private String owner1Kana = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(1人目)役職")
  private String owner1Position = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(1人目)郵便番号")
  private String owner1ZipCode = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(1人目)住所")
  private String owner1Address = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(1人目)生年月日")
  private String owner1Birthday = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(1人目)の外国PEPsチェック")
  private String owner1ForeignPeps = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(2人目)名")
  private String owner2Name = "";

  @Getter
  @Setter
  @JsonProperty("カナ実質的支配者(2人目)")
  private String owner2Kana = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(2人目)役職")
  private String owner2Position = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(2人目)郵便番号")
  private String owner2ZipCode = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(2人目)住所")
  private String owner2Address = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(2人目)生年月日")
  private String owner2Birthday = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(2人目)の外国PEPsチェック")
  private String owner2ForeignPeps = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(3人目)名")
  private String owner3Name = "";

  @Getter
  @Setter
  @JsonProperty("カナ実質的支配者(3人目)")
  private String owner3Kana = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(3人目)役職")
  private String owner3Position = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(3人目)郵便番号")
  private String owner3ZipCode = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(3人目)住所")
  private String owner3Address = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(3人目)生年月日")
  private String owner3Birthday = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(3人目)の外国PEPsチェック")
  private String owner3ForeignPeps = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(4人目)名")
  private String owner4Name = "";

  @Getter
  @Setter
  @JsonProperty("カナ実質的支配者(4人目)")
  private String owner4Kana = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(4人目)役職")
  private String owner4Position = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(4人目)郵便番号")
  private String owner4ZipCode = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(4人目)住所")
  private String owner4Address = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(4人目)生年月日")
  private String owner4Birthday = "";

  @Getter
  @Setter
  @JsonProperty("実質的支配者(4人目)の外国PEPsチェック")
  private String owner4ForeignPeps = "";

  @Getter
  @Setter
  @JsonProperty("担当者名")
  private String agentName = "";

  @Getter
  @Setter
  @JsonProperty("カナ担当者")
  private String agentKana = "";

  @Getter
  @Setter
  @JsonProperty("担当者役職")
  private String agentPosition = "";

  @Getter
  @Setter
  @JsonProperty("担当者郵便番号")
  private String agentZipCode = "";

  @Getter
  @Setter
  @JsonProperty("担当者住所")
  private String agentAddress = "";

  @Getter
  @Setter
  @JsonProperty("担当者生年月日")
  private String agentBirthday = "";

  @Getter
  @Setter
  @JsonProperty("担当者の外国PEPsチェック")
  private String agentForeignPeps = "";

  @Getter
  @Setter
  @JsonProperty("主なご利用目的")
  private String purpose = "";

  @Getter
  @Setter
  @JsonProperty("投資目的")
  private String investmentPurpose = "";

  @Getter
  @Setter
  @JsonProperty("投資経験(仮想通貨)")
  private String cryptoExperience = "";

  @Getter
  @Setter
  @JsonProperty("投資経験(FX)")
  private String fxExperience = "";

  @Getter
  @Setter
  @JsonProperty("投資経験(株式)")
  private String stocksExperience = "";

  @Getter
  @Setter
  @JsonProperty("投資経験(投資信託)")
  private String fundExperience = "";

  @Getter
  @Setter
  @JsonProperty("申込経緯")
  private String applicationHistory = "";

  @Getter
  @Setter
  @JsonProperty("申込経緯(その他)")
  private String applicationHistoryOther = "";

  public ReportUserDetail(User user, List<UserInfoCorporateOwner> owners) throws CustomException {
    this.userId = user.getId().toString();
    String authority = "";
    try {
      authority = user.getAuthorities().get(0).getAuthority();
    } catch (Exception e) {
      log.error("authority not found!" + user.getId().toString());
      throw e;
    }
    this.userAuthority = ReportLabel.Authority.valueOfName(authority).getLabel();
    this.userStatus =
        ReportLabel.UserStatus.valueOfName(user.getUserStatus().toString()).getLabel();
    this.affiliateFrom = user.getAffiliateInfo() != null ? user.getAffiliateInfo().getAffiliateName() : "";
    this.uuid = user.getUuid() != null ? user.getUuid() : "";
    this.kycStatus = ReportLabel.KycStatus.valueOfName(user.getKycStatus().toString()).getLabel();
    this.email = user.getEmail();
    this.insider = user.isInsider() ? "有" : "無";
    this.insideAccountFlg = user.isInsideAccountFlg() ? "自社" : "非自社";

    // 名前・住所情報等
    if (authority == Authority.PERSONAL.toString()) {
      // 個人ユーザー
      UserInfo userInfo = user.getUserInfo();
      if (userInfo != null) {
        this.userName = userInfo.getLastName() + userInfo.getFirstName();
        this.userKana = userInfo.getLastKana() + userInfo.getFirstKana();
        this.zipCode = userInfo.getZipCode();
        String building = userInfo.getBuilding();
        this.address =
            Prefecture.valueOfCode(userInfo.getPrefecture()).getKanjiName()
                + userInfo.getCity()
                + userInfo.getAddress()
                + (building == null ? "" : building);
        this.birthdayOrEstablishedDay = userInfo.getBirthday();
        this.gender = Gender.valueOfCode(userInfo.getGender()).getKanjiName();
        this.phoneNumber = userInfo.getPhoneNumber();
        this.occupation =
            ReportLabel.Occupation.valueOfCode(userInfo.getOccupation()).getKanjiName();
        if (userInfo.getIndustry() != null) {
        	this.industry =
                    ReportLabel.Industry.valueOfCode(userInfo.getIndustry()).getKanjiName();
        }
        if (userInfo.getWorkPlace() != null) {
        	this.workPlace = userInfo.getWorkPlace();
        }
        if (userInfo.getPosition() != null) {
            this.position = userInfo.getPosition();
        }
    	if (userInfo.getPriceFrom() != null) {
    		this.priceFrom = ReportLabel.PriceFrom.valueOfCode(userInfo.getPriceFrom()).getKanjiName();
    	}
        this.incomeOrSales = ReportLabel.Income.valueOfCode(userInfo.getIncome()).getKanjiName();
        this.financialAssets =
            ReportLabel.Income.valueOfCode(userInfo.getFinancialAssets()).getKanjiName();
        this.purpose = ReportLabel.Purpose.valueOfCode(userInfo.getPurpose()).getKanjiName();
        this.investmentPurpose =
            ReportLabel.InvestmentPurpose.valueOfCode(userInfo.getInvestmentPurposes())
                .getKanjiName();
        this.cryptoExperience =
            ReportLabel.Experience.valueOfCode(userInfo.getCryptoExperience()).getKanjiName();
        this.fxExperience =
            ReportLabel.Experience.valueOfCode(userInfo.getFxExperience()).getKanjiName();
        this.stocksExperience =
            ReportLabel.Experience.valueOfCode(userInfo.getStocksExperience()).getKanjiName();
        this.fundExperience =
            ReportLabel.Experience.valueOfCode(userInfo.getFundExperience()).getKanjiName();
        this.applicationHistory =
            ReportLabel.ApplicationHistory.valueOfCode(userInfo.getApplicationHistory())
                .getKanjiName();
        this.applicationHistoryOther = userInfo.getApplicationHistoryOther();
        this.residenceCardExpiredAt = FormatUtil.formatJst(userInfo.getResidenceCardExpiredAt(),
            FormatPattern.YYYY_MM_DD_SLASH);
      }
    } else if (authority == Authority.CORPORATE.toString()) {
      // 法人ユーザー
      UserInfoCorporate userInfoCorporate = user.getUserInfoCorporate();
      if (userInfoCorporate != null) {
        this.userName = userInfoCorporate.getName();
        this.userKana = userInfoCorporate.getNameKana();
        this.zipCode = userInfoCorporate.getZipCode();
        String building = userInfoCorporate.getBuilding();
        this.address =
            Prefecture.valueOfCode(userInfoCorporate.getPrefecture()).getKanjiName()
                + userInfoCorporate.getCity()
                + userInfoCorporate.getAddress()
                + (building == null ? "" : building);
        this.birthdayOrEstablishedDay =
            String.format("%04d", userInfoCorporate.getEstablishedYear())
                + String.format("%02d", userInfoCorporate.getEstablishedMonth())
                + String.format("%02d", userInfoCorporate.getEstablishedDay());
        this.accountingMonth = String.format("%02d", userInfoCorporate.getAccountingMonth());
        this.phoneNumber = userInfoCorporate.getPhoneNumber();
        this.bussinessContent = userInfoCorporate.getBusinessContent();
        this.incomeOrSales =
            ReportLabel.Income.valueOfCode(userInfoCorporate.getSales()).getKanjiName();
        this.financialAssets =
            ReportLabel.Income.valueOfCode(userInfoCorporate.getFinancialAssets()).getKanjiName();
        this.purpose =
            ReportLabel.Purpose.valueOfCode(userInfoCorporate.getPurpose()).getKanjiName();
        this.investmentPurpose =
            ReportLabel.InvestmentPurpose.valueOfCode(userInfoCorporate.getInvestmentPurposes())
                .getKanjiName();
        this.cryptoExperience =
            ReportLabel.Experience.valueOfCode(userInfoCorporate.getCryptoExperience())
                .getKanjiName();
        this.fxExperience =
            ReportLabel.Experience.valueOfCode(userInfoCorporate.getFxExperience()).getKanjiName();
        this.stocksExperience =
            ReportLabel.Experience.valueOfCode(userInfoCorporate.getStocksExperience())
                .getKanjiName();
        this.fundExperience =
            ReportLabel.Experience.valueOfCode(userInfoCorporate.getFundExperience())
                .getKanjiName();
        this.applicationHistory =
            ReportLabel.ApplicationHistory.valueOfCode(userInfoCorporate.getApplicationHistory())
                .getKanjiName();
        this.applicationHistoryOther = userInfoCorporate.getApplicationHistoryOther();

        // 代表者
        UserInfoCorporateRepresentative representative = userInfoCorporate.getRepresentative();
        this.representativeName = representative.getLastName() + representative.getFirstName();
        this.representativeKana = representative.getLastKana() + representative.getFirstKana();
        this.representativePosition = representative.getPosition();
        this.representativeZipCode = representative.getZipCode();
        this.representativeAddress =
            Prefecture.valueOfCode(representative.getPrefecture()).getKanjiName()
                + representative.getCity()
                + representative.getAddress()
                + (building == null ? "" : building);
        this.representativeBirthday = representative.getBirthday();
        //        this.representativeForeignPeps =

        // 担当者
        UserInfoCorporateAgent agent = userInfoCorporate.getAgent();
        if (agent != null) {
          this.agentName = agent.getLastName() + agent.getFirstName();
          this.agentKana = agent.getLastKana() + agent.getFirstKana();
          this.agentPosition = agent.getPosition();
          this.agentZipCode = agent.getZipCode();
          this.agentAddress =
              Prefecture.valueOfCode(agent.getPrefecture()).getKanjiName()
                  + agent.getCity()
                  + agent.getAddress()
                  + (building == null ? "" : building);
          //          this.agentBirthday = agent.getBirthday();
          //          this.agentForeignPeps =
          // ReportLabel.ForeignPeps.valueOfCode(agent.isForeignPeps).getKanjiName();

        }

        // 実質的支配者
        this.ultimateBeneficialOwnership =
            ReportLabel.UltimateBeneficialOwnership.valueOfCode(
                    userInfoCorporate.isUltimateBeneficialOwnership())
                .getKanjiName();

        // 実質的支配者1
        if (owners.size() > 0) {
          UserInfoCorporateOwner owner1 = owners.get(0);
          this.owner1Name = owner1.getLastName() + owner1.getFirstName();
          this.owner1Kana = owner1.getLastKana() + owner1.getFirstKana();
          this.owner1Position = owner1.getPosition();
          this.owner1ZipCode = owner1.getZipCode();
          this.owner1Address =
              Prefecture.valueOfCode(owner1.getPrefecture()).getKanjiName()
                  + owner1.getCity()
                  + owner1.getAddress()
                  + (building == null ? "" : building);
          //          this.owner1Birthday = owner1.getBirthday();
          this.owner1ForeignPeps =
              ReportLabel.ForeignPeps.valueOfCode(owner1.isForeignPeps()).getKanjiName();
        }
        // 実質的支配者2
        if (owners.size() > 1) {
          UserInfoCorporateOwner owner2 = owners.get(1);
          this.owner2Name = owner2.getLastName() + owner2.getFirstName();
          this.owner2Kana = owner2.getLastKana() + owner2.getFirstKana();
          this.owner2Position = owner2.getPosition();
          this.owner2ZipCode = owner2.getZipCode();
          this.owner2Address =
              Prefecture.valueOfCode(owner2.getPrefecture()).getKanjiName()
                  + owner2.getCity()
                  + owner2.getAddress()
                  + (building == null ? "" : building);
          //          this.owner2Birthday = owner2.getBirthday();
          this.owner2ForeignPeps =
              ReportLabel.ForeignPeps.valueOfCode(owner2.isForeignPeps()).getKanjiName();
        }

        // 実質的支配者3
        if (owners.size() > 2) {
          UserInfoCorporateOwner owner3 = owners.get(2);
          this.owner3Name = owner3.getLastName() + owner3.getFirstName();
          this.owner3Kana = owner3.getLastKana() + owner3.getFirstKana();
          this.owner3Position = owner3.getPosition();
          this.owner3ZipCode = owner3.getZipCode();
          this.owner3Address =
              Prefecture.valueOfCode(owner3.getPrefecture()).getKanjiName()
                  + owner3.getCity()
                  + owner3.getAddress()
                  + (building == null ? "" : building);
          //          this.owner3Birthday = owner3.getBirthday();
          this.owner3ForeignPeps =
              ReportLabel.ForeignPeps.valueOfCode(owner3.isForeignPeps()).getKanjiName();
        }

        // 実質的支配者4
        if (owners.size() > 3) {
          UserInfoCorporateOwner owner4 = owners.get(3);
          this.owner4Name = owner4.getLastName() + owner4.getFirstName();
          this.owner4Kana = owner4.getLastKana() + owner4.getFirstKana();
          this.owner4Position = owner4.getPosition();
          this.owner4ZipCode = owner4.getZipCode();
          this.owner4Address =
              Prefecture.valueOfCode(owner4.getPrefecture()).getKanjiName()
                  + owner4.getCity()
                  + owner4.getAddress()
                  + (building == null ? "" : building);
          //          this.owner4Birthday = owner4.getBirthday();
          this.owner4ForeignPeps =
              ReportLabel.ForeignPeps.valueOfCode(owner4.isForeignPeps()).getKanjiName();
        }
      }
    }
    // KYC情報
    UserKyc userKyc = user.getUserKyc();
    if (userKyc != null) {
      this.kycStatusUpdatedAt =
          FormatUtil.formatJst(userKyc.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
      this.antisocialStatus = ReportLabel.RewardAntisocialStatus.valueOfName(userKyc.getAntisocialStatus().toString()).getLabel();
    }
  }
}
