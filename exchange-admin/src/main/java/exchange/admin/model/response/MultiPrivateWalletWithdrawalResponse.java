package exchange.admin.model.response;

import exchange.common.constant.TmsStatus;
import exchange.common.entity.MultiPrivateWalletWithdrawalUser;
import exchange.common.util.DateUnit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
public class MultiPrivateWalletWithdrawalResponse {

  @Getter
  @Setter
  private Long id;

  @Getter
  @Setter
  private Long userId;

  @Getter
  @Setter
  private String email;

  @Getter
  @Setter
  private String createdAt;

  @Getter
  @Setter
  private TmsStatus tmsStatus;

  @Getter
  @Setter
  private String targetAt;

  public static MultiPrivateWalletWithdrawalResponse create(final MultiPrivateWalletWithdrawalUser entity) {
    final var data = new MultiPrivateWalletWithdrawalResponse();
    data.setId(entity.getId());
    data.setUserId(entity.getUserId());
    data.setEmail(entity.getUser().getEmail());
    data.setCreatedAt(DateUnit.toFormatSlashDateTime(entity.getCreatedAt()));
    data.setTmsStatus(entity.getTmsStatus());
    data.setTargetAt(DateUnit.toFormatSlashDate(entity.getTargetAt()));
    return data;
  }
}
