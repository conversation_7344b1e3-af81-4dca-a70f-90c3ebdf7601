package exchange.admin.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import exchange.common.constant.Currency;
import exchange.common.constant.StakingStatusConstants;
import exchange.common.constant.SygnaConstants;
import exchange.common.constant.TradeType;
import exchange.common.entity.Deposit;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.entity.Withdrawal;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.spot.entity.SpotTrade;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Date;
import java.util.Map;

@Slf4j
@JsonPropertyOrder({
        "顧客ID", "利用者の氏名又は名称", "通貨", "利用者の暗号資産を管理する者の氏名又は名称",
        "売付、買付", "約定年月日", "暗号資産の数量", "暗号資産の単価",
        "ステーキング申込又は報酬", "ステーキング申込日又は報酬日", "ステーキング申込又は報酬数量",
        "受入れ又は引出しの別", "入出金日", "入出金額", "約定代金",
        "手数料", "差引残高", "注文ID", "約定ID", "入出金ID", "取引タイプ",
})
public record ReportCryptoCurrencyStatement(
        // "顧客ID", "利用者の氏名又は名称", "通貨", "利用者の仮想通貨を管理する者の氏名又は名称",
        @JsonProperty("顧客ID")
        String userId, // 顧客ID
        @JsonProperty("利用者の氏名又は名称")
        String userName, // 利用者の氏名又は名称
        @JsonProperty("通貨")
        String currency, // 通貨
        @JsonProperty("利用者の暗号資産を管理する者の氏名又は名称")
        String custodian, // 利用者の仮想通貨を管理する者の氏名又は名称

        // "売付、買付", "約定年月日", "暗号資産の数量", "暗号資産の単価",
        @JsonProperty("売付、買付")
        String orderSide, // 売付、買付
        @JsonProperty("約定年月日")
        String tradedAt, // 約定年月日
        @JsonProperty("暗号資産の数量")
        String tradedAmount, // 暗号資産の数量
        @JsonProperty("暗号資産の単価")
        String tradePrice, // 暗号資産の単価

        // "ステーキング申込又は報酬", "ステーキング申込日又は報酬日", "ステーキング申込又は報酬数量",
        @JsonProperty("ステーキング申込又は報酬")
        String status, // ステーキング申込又は報酬
        @JsonProperty("ステーキング申込日又は報酬日")
        String applydateoramounttoaccountdate, // ステーキング申込日又は報酬日
        @JsonProperty("ステーキング申込又は報酬数量")
        String applyamountoramounttoaccount, // ステーキング申込又は報酬数量

        // "受入れ又は引出しの別", "入出金日", "入出金額", "約定代金",
        @JsonProperty("受入れ又は引出しの別")
        String depositWithdrawalName, // 受入れ又は引出しの別
        @JsonProperty("入出金日")
        String depositWithdrawalDate, // 入出金日
        @JsonProperty("入出金額")
        String depositWithdrawalAmount, // 入出金額
        @JsonProperty("約定代金")
        String tradeSumPrice, // 約定代金

        // "手数料", "差引残高", "注文ID", "約定ID", "入出金ID",
        @JsonProperty("手数料")
        String fee, // 手数料
        @JsonProperty("差引残高")
        String balance, // 差引残高
        @JsonProperty("注文ID")
        String orderId, // 注文ID
        @JsonProperty("約定ID")
        String tradeId, // 約定ID
        @JsonProperty("入出金ID")
        String depositWithdrawalId, // 入出金ID
        @JsonProperty("取引タイプ")
        String tradeType, // 取引タイプ

        // その他
        @JsonIgnore
        String id, // 約定ID or 入出金ID
        @JsonIgnore
        Long userIdLong, // ユーザIDのLong値
        @JsonIgnore
        BigDecimal balanceBigDecimal, // balanceのBigDecimal
        @JsonIgnore
        Long date // 作成日時
) {
    /** 利用者の暗号資産を管理する者の氏名又は名称 */
    private final static String CUSTODIAN = "BACKSEAT暗号資産交換業株式会社";
  //約定Base
  public static ReportCryptoCurrencyStatement create(
          Map<Long, Symbol> symbolMap, User user, SpotTrade spotTrade, BigDecimal balance) {
	  NumberFormat numberFormat = NumberFormat.getNumberInstance();
      if (user == null) {
          log.error("ReportError: spotOrderId = " + spotTrade.getId() + " userId = " + spotTrade.getUserId());
      }
      final var currency = symbolMap.get(spotTrade.getSymbolId()).getCurrencyPair()
              .getBaseCurrency();
      final var nextBalance = spotTrade.nextBaseBalance(balance);
      String tradeTypeName = "";
      if (symbolMap.get(spotTrade.getSymbolId()).getTradeType() == TradeType.SPOT) {
          tradeTypeName = SygnaConstants.SPOT_TRADE_TYPE_NAME;
      } else {
          tradeTypeName = SygnaConstants.POS_TRADE_TYPE_NAME;
      }

      return new ReportCryptoCurrencyStatement(
              user.getId().toString(), // 顧客ID
              user.getName(), // 顧客の氏名
              currency.name(), // 通貨
              CUSTODIAN, // 利用者の仮想通貨を管理する者の氏名又は名称
              spotTrade.getOrderSide().displayName, // 売付、買付
              FormatUtil.formatJst(spotTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 約定年月日
              toStrigForReport(currency, spotTrade.getAmount(), numberFormat), // 暗号資産の数量
              toStrigForReport(currency, spotTrade.getPrice(), numberFormat), // 暗号資産の単価
              "", // ステーキング申込又は報酬
              "", // ステーキング申込日又は報酬日
              "", // ステーキング申込又は報酬数量
              "", // 受入れ又は引出しの別
              "", // 入出金日
              "", // 入出金額
              "", // 約定代金
              "", // 手数料
              toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
              spotTrade.getOrderId().toString(), // 注文ID
              spotTrade.getId().toString(), // 約定ID
              "", // 入出金ID
              tradeTypeName, // 取引タイプ
              spotTrade.getId().toString(), // 約定ID or 入出金ID
              user.getId(), // ユーザID
              nextBalance, // balanceのBigDecimal
              spotTrade.getCreatedAt().getTime() // 作成日時
      );
  }

    // 暗号資産入金
    public static ReportCryptoCurrencyStatement create(
            User user,
            Deposit deposit,
            BigDecimal balance) {
    	NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.error("ReportError: depositId = " + deposit.getId() + " userId = " + deposit.getUserId());
        }
        final var currency = deposit.getCurrency();
        final var nextBalance = deposit.nextBalance(balance);
        return new ReportCryptoCurrencyStatement(
            user.getId().toString(), // 顧客ID
            user.getName(), // 顧客の氏名
            currency.name(), // 通貨
            CUSTODIAN, // 利用者の仮想通貨を管理する者の氏名又は名称
            "", // 売付、買付
            "", // 約定年月日
            "", // 暗号資産の数量
            "", // 暗号資産の単価
            "", // ステーキング申込又は報酬
            "", // ステーキング申込日又は報酬日
            "", // ステーキング申込又は報酬数量
            "入金", // 受入れ又は引出しの別
            FormatUtil.formatJst(deposit.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 入出金日
            toStrigForReport(currency, deposit.getAmount(), numberFormat), // 入出金額
            "", // 約定代金
            toStrigForReport(currency, deposit.getFee(), numberFormat), // 手数料
            toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
            "", // 注文ID
            "", // 約定ID
            deposit.getId().toString(), // 入出金ID
            "", // 取引タイプ
            deposit.getId().toString(), // 約定ID or 入出金ID
            user.getId(), // ユーザID
            nextBalance, // balanceのBigDecimal
            deposit.getUpdatedAt().getTime() // 作成日時
        );
    }

    // 暗号資産出金
    public static ReportCryptoCurrencyStatement create(User user, Withdrawal withdrawal, BigDecimal balance) {
    	NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.error("ReportError: withdrawalId = " + withdrawal.getId() + " userId = " + withdrawal.getUserId());
        }
        final var currency = withdrawal.getCurrency();
        final var nextBalance = withdrawal.nextBalance(balance);
        return new ReportCryptoCurrencyStatement(
                user.getId().toString(), // 顧客ID
                user.getName(), // 顧客の氏名
                currency.name(), // 通貨
                CUSTODIAN, // 利用者の仮想通貨を管理する者の氏名又は名称
                "", // 売付、買付
                "", // 約定年月日
                "", // 暗号資産の数量
                "", // 暗号資産の単価
                "", // ステーキング申込又は報酬
                "", // ステーキング申込日又は報酬日
                "", // ステーキング申込又は報酬数量
                "出金", // 受入れ又は引出しの別
                FormatUtil.formatJst(withdrawal.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 入出金日
                toStrigForReport(currency, withdrawal.getAmount(), numberFormat), // 入出金額
                "", // 約定代金
                toStrigForReport(currency, withdrawal.getWithdrawalFee(), numberFormat), // 手数料
                toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
                "", // 注文ID
                "", // 約定ID
                withdrawal.getId().toString(), // 入出金ID
                "", // 取引タイプ
                withdrawal.getId().toString(), // 約定ID or 入出金ID
                user.getId(), // ユーザID
                nextBalance, // balanceのBigDecimal
                withdrawal.getUpdatedAt().getTime() // 作成日時
        );
    }

    //staking
    public static ReportCryptoCurrencyStatement create(User user, StakingApplyDetailList stakingApplyDetail, BigDecimal balance) {
    	NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.error("ReportError: stakingId = " + " userId = " + stakingApplyDetail.getUserId());
        }
        final var currency = stakingApplyDetail.getCurrency();
        final var nextBalance = stakingApplyDetail.nextBalance(balance, stakingApplyDetail);
        String status = "";
        Date date = new Date();
        BigDecimal amount = new BigDecimal(0.0);

        if (stakingApplyDetail.getStakingStatus().equals(StakingStatusConstants.Apply)) {
            status = StakingStatusConstants.Apply;
            date = stakingApplyDetail.getApplyOrAmountToAccountDate();
            amount = stakingApplyDetail.getApplyAmount();
        }

        if (stakingApplyDetail.getStakingStatus().equals(StakingStatusConstants.NOT_CONRINUE)) {
            status = StakingStatusConstants.NOT_CONRINUE;
            date = stakingApplyDetail.getApplyOrAmountToAccountDate();
            amount = stakingApplyDetail.getApplyAmount();
        }

        if (stakingApplyDetail.getStakingStatus().equals(StakingStatusConstants.NOT_CONRINUE_REWARD)) {
            status = StakingStatusConstants.NOT_CONRINUE_REWARD;
            date = stakingApplyDetail.getApplyOrAmountToAccountDate();
            amount = stakingApplyDetail.getRewardAccumulate();
        }

        if (stakingApplyDetail.getStakingStatus().equals(StakingStatusConstants.CANCEL_FUNDED)) {
            status = StakingStatusConstants.CANCEL_FUNDED;
            date = stakingApplyDetail.getApplyOrAmountToAccountDate();
            amount = stakingApplyDetail.getAmountToAccount();
        }

        if (stakingApplyDetail.getStakingStatus().toString().equals(StakingStatusConstants.CONRINUE_REWARD)) {
            status = StakingStatusConstants.CONRINUE_REWARD;
            date = stakingApplyDetail.getApplyOrAmountToAccountDate();
            amount = stakingApplyDetail.getAmountToAccount();
        }

        return new ReportCryptoCurrencyStatement(
                user.getId().toString(), // 顧客ID
                user.getName(), // 顧客の氏名
                currency.name(), // 通貨
                CUSTODIAN, // 利用者の仮想通貨を管理する者の氏名又は名称
                "", // 売付、買付
                "", // 約定年月日
                "", // 暗号資産の数量
                "", // 暗号資産の単価
                status, // ステーキング申込又は報酬
                FormatUtil.formatJst(date, FormatPattern.YYYY_MM_DD_HH_MM_SS), // ステーキング申込日又は報酬日
                toStrigForReport(currency, amount, numberFormat), // ステーキング申込又は報酬数量
                "", // 受入れ又は引出しの別
                "", // 入出金日
                "", // 入出金額
                "", // 約定代金
                "", // 手数料
                toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
                "", // 注文ID
                "", // 約定ID
                "", // 入出金ID
                "", // 取引タイプ
                "", // 約定ID or 入出金ID
                user.getId(), // ユーザID
                nextBalance, // balanceのBigDecimal
                stakingApplyDetail.getApplyOrAmountToAccountDate().getTime() // 作成日時
        );
    }
    private static String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
	  // stripTrailingZeros() 末尾0除去
	  // toPlainString() 指数表記にならないようにString変換
	  String strValue = currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
      // 整数部3桁区切り
	  int decimalPointIndex = strValue.indexOf(".");
	  if (decimalPointIndex < 0) {
	    // 小数点なし
	    return numberFormat.format(Long.valueOf(strValue));
	  } else {
	    // 小数点あり
	    String seisu = strValue.substring(0, decimalPointIndex);
	    return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
	  }
	}
}
