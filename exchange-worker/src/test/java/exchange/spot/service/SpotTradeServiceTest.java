package exchange.spot.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import exchange.common.component.DataSourceManager;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeAction;
import exchange.common.constant.TradeType;
import exchange.common.entity.Symbol;
import exchange.spot.entity.SpotTradeAdaJpy;

@SpringBootTest(classes = SpotTradeServiceTest.TestConfiguration.class)
public class SpotTradeServiceTest {

  @Autowired
  private SpotTradeAdaJpyService spotTradeAdaJpyService;

  @Autowired
  private DataSourceManager dataSourceManager;

  @Configuration
  @ComponentScan(basePackages = {"exchange.common", "exchange.spot"})
  @EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class,
      DataSourceTransactionManagerAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
  public static class TestConfiguration {

  }

  SpotTradeAdaJpy createTestData() {
    var localdate = LocalDate.now().minusDays(2);
    var created = Date.from(localdate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    var o1 = new SpotTradeAdaJpy();
    o1.setSymbolId(1L);
    o1.setUserId(1L);
    o1.setOrderSide(OrderSide.BUY);
    o1.setPrice(BigDecimal.ZERO);
    o1.setAmount(BigDecimal.ZERO);
    o1.setTradeAction(TradeAction.TAKER);
    o1.setOrderId(1L);
    o1.setAmount(new BigDecimal("1"));
    o1.setFee(BigDecimal.ZERO);
    o1.setUserId(1L);
    o1.setCreatedAt(created);
    o1.setUpdatedAt(created);
    o1.setTargetOrderId(1L);
    o1.setTargetUserId(1L);
    o1.setOrderType(OrderType.LIMIT);
    o1.setOrderChannel(OrderChannel.PC_WEB);
    o1.setJpyConversion(BigDecimal.ZERO);
    o1.setAssetAmount(BigDecimal.ZERO);
    return o1;
  }

  @Test
  public void testFindArchiveTarget() throws Exception {

      var symbol = new Symbol();
      symbol.setId(1L);
      symbol.setProperties(TradeType.SPOT, CurrencyPair.ADA_JPY);
      
      var em = dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
      var tx = em.getTransaction();
      tx.begin();

      // create test data
      var testData = new ArrayList<SpotTradeAdaJpy>();
      for (var i = 0; i < 3; i++) {
        var o1 = createTestData();
        em.persist(o1);
        em.flush();
        testData.add(o1);
      }
      tx.commit();

      var LIMIT = 2;
      var result = spotTradeAdaJpyService.findArchiveTarget(symbol, LIMIT);
      assertEquals(result.size(), LIMIT);

      // clean test data
      for (var o : testData) {
        spotTradeAdaJpyService.delete(o);
      }
  }
}
