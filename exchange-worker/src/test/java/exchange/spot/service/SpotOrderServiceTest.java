package exchange.spot.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import exchange.common.component.DataSourceManager;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderOperator;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.entity.Symbol;
import exchange.spot.entity.SpotOrderAdaJpy;


@SpringBootTest(classes = SpotTradeServiceTest.TestConfiguration.class)
public class SpotOrderServiceTest {


  @Autowired
  private SpotOrderAdaJpyService spotOrderAdaJpyService;

  @Autowired
  private DataSourceManager dataSourceManager;

  @Configuration
  @ComponentScan(basePackages = {"exchange.common", "exchange.spot"})
  @EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class,
      DataSourceTransactionManagerAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
  public static class TestConfiguration {

  }

  SpotOrderAdaJpy createTestData() {
    var localdate = LocalDate.now().minusDays(2);
    var created = Date.from(localdate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    var o1 = new SpotOrderAdaJpy();
    o1.setSymbolId(1L);
    o1.setUserId(1L);
    o1.setOrderSide(OrderSide.BUY);
    o1.setPrice(BigDecimal.ZERO);
    o1.setAmount(BigDecimal.ZERO);
    o1.setAmount(new BigDecimal("1"));
    o1.setRemainingAmount(new BigDecimal("1"));
    o1.setUserId(1L);
    o1.setCreatedAt(created);
    o1.setUpdatedAt(created);
    o1.setOrderType(OrderType.LIMIT);
    o1.setOrderStatus(OrderStatus.FULLY_FILLED);
    o1.setOrderChannel(OrderChannel.PC_WEB);
    o1.setOrderOperator(OrderOperator.USER);
    return o1;
  }

  @Test
  void testFindInactive() {
    var symbol = new Symbol();
    symbol.setId(1L);
    symbol.setProperties(TradeType.SPOT, CurrencyPair.ADA_JPY);

    var em = dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    var tx = em.getTransaction();
    tx.begin();

    // create test data
    var testData = new ArrayList<SpotOrderAdaJpy>();
    for (var i = 0; i < 3; i++) {
      var o1 = createTestData();
      em.persist(o1);
      em.flush();
      testData.add(o1);
    }
    tx.commit();

    var LIMIT = 2;
    var result = spotOrderAdaJpyService.findInactive(symbol.getId(), LIMIT);
    assertEquals(result.size(), LIMIT);

    // clean test data
    for (var o : testData) {
      spotOrderAdaJpyService.delete(o);
    }
  }
}
