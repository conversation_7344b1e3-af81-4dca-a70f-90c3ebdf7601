package exchange.common.service;

import exchange.common.constant.Currency;
import exchange.common.entity.AssetSummary;
import exchange.worker.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/11/7
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class AssetSummaryServiceTest {

	@Resource
	private AssetSummaryService assetSummaryService;

	private List<AssetSummary> newList(Long userId, int size) {
		List<AssetSummary> list = new ArrayList<>();
		for (int i = 0; i < size; i ++) {
			AssetSummary assetSummary = new AssetSummary();
			assetSummary.setUserId(userId);
      		assetSummary.setTargetAt(new Date());
			assetSummary.setCurrency(Currency.ADA);
			assetSummary.setCurrentAmount(BigDecimal.valueOf(i));
			list.add(assetSummary);
		}
		return list;
	}
//	@Test
//	public void test1() throws Exception {
//		int count = 1000000;
//		exec("单个循环插入，共%s条".formatted(count), count, -1L, list -> list.forEach(u -> assetSummaryService.save(u)));
//		exec("循环批量插入，每次100条，共%s条".formatted(count), count, -2L, list -> assetSummaryService.batchSave(list, 100));
//		exec("循环批量插入，每次500条，共%s条".formatted(count), count, -3L, list -> assetSummaryService.batchSave(list, 500));
//		exec("循环批量插入，每次1000条，共%s条".formatted(count), count, -4L, list -> assetSummaryService.batchSave(list, 1000));
//		exec("循环批量插入，每次2000条，共%s条".formatted(count), count, -5L, list -> assetSummaryService.batchSave(list, 2000));
//	}

	private void exec(String info, int count, Long userId, Run run) throws Exception {
		List<AssetSummary> list = newList(userId, count);
		printTimes(info, list, run);
	}

	private void printTimes(String info, List<AssetSummary> list, Run run) throws Exception {
		long start = System.currentTimeMillis();
		run.run(list);
		long end = System.currentTimeMillis();
		System.out.println(String.format("%s 耗时：%s ms", info, end - start));
	}

	public interface Run {
		void run(List<AssetSummary> list) throws Exception;
	}
}
