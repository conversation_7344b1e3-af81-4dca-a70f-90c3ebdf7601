package exchange.worker.worker;

import exchange.worker.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @author: wen.y
 * @date: 2024/12/10
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class UserAntiSocialCheckerTest {

	@Resource
	private UserAntiSocialChecker userAntiSocialChecker;

	@Test
	public void test1() throws Exception {
		userAntiSocialChecker.execute(null, null);
	}
}
