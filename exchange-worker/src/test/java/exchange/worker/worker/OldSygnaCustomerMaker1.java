package exchange.worker.worker;

import exchange.common.constant.Currency;
import exchange.common.constant.KycStatus;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.SygnaCustomer;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.SygnaCustomerService;
import exchange.common.service.UserService;
import exchange.worker.component.Worker;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class OldSygnaCustomerMaker1 extends Worker {

  private final UserService userService;
  private final OldSygnaCustomerService1 sygnaCustomerService;
  private final CurrencyConfigService currencyConfigService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("===========================Sygna Customer作成開始/SygnaCustomerMaker start=======================================");
    // KYCステータスが「口座開設完了」、「法人口座開設完了」　または　「承認（変更完了）」
    List<User> userList = userService.findByKycStatus(
        new KycStatus[] {KycStatus.ACCOUNT_OPENING_DONE,
            KycStatus.DONE,
            KycStatus.CORPORATE_PERSONAL_ACCOUNT_OPENING_DONE});
    if(ObjectUtils.isEmpty(userList)) {
      log.info("Sygna Customer作成対象がありません。");
    } else {
      List<User> userForCreateCustomer =
          userList.stream()
          .filter(x -> ObjectUtils.isEmpty(sygnaCustomerService.findOneByUserId(x.getId())))
          .collect(Collectors.toList());
      sygnaCustomerService.createCustomers(userForCreateCustomer);
      List<User> userForUpdateCustomer =
          userList.stream()
          .filter(x -> checkUpdateCustomer(x))
          .collect(Collectors.toList());
      if(ObjectUtils.isEmpty(userForUpdateCustomer)) {
        log.info("Sygna Customer更新対象がありません。");
      } else {
        sygnaCustomerService.updateCustomers(userForUpdateCustomer);
      }
    }

    log.info("===========================Sygna Customer作成終了/SygnaCustomerMaker finish=======================================");
  }

  private boolean checkUpdateCustomer(User user) {
    SygnaCustomer sygnaCustomer = sygnaCustomerService.findOneByUserId(user.getId());
    //　有効な通貨資産取得
    List<CurrencyConfig> currencyConfigs = currencyConfigService.findAllByCondition(null, true);
    List<Currency> currencyMst = new ArrayList<>();
    for (CurrencyConfig currencyConfig : currencyConfigs) {
      currencyMst.add(currencyConfig.getCurrency());
    }
    //　JPYは暗号資産処理対象外
    currencyMst.remove(Currency.JPY);
    // 暗号資産反映チェック・プロファイル変更チェック
    if(ObjectUtils.isNotEmpty(sygnaCustomer)
        && (sygnaCustomer.isUserInfoChangeFlg() || !sygnaCustomerService.checkAddressCreated(sygnaCustomer))) {
      return true;
    }
    return false;
  }
}
