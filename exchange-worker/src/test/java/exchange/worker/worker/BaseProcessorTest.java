package exchange.worker.worker;

import exchange.common.constant.CancelReason;
import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.entity.AssetSummary;
import exchange.common.entity.Deposit;
import exchange.common.entity.FiatDeposit;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.Withdrawal;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotTrade;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.TestInstance;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = {exchange.worker.Application.class})
@AutoConfigureWebTestClient
@ActiveProfiles({"test"})
public abstract class BaseProcessorTest {
  protected static final List<Symbol> symbols;

  static {
    symbols = new ArrayList<>();
    symbols.add(createSymbol(4L, TradeType.CFD, CurrencyPair.ADA_JPY));
  }

  protected List<Symbol> createSymbols() {
    return symbols.stream().filter(symbol -> symbol.getTradeType().isCfd()).toList();
  }

  private static Symbol createSymbol(Long id, TradeType tradeType, CurrencyPair currencyPair) {
    final var symbol = new Symbol();
    symbol.setId(id);
    symbol.setTradeType(tradeType);
    symbol.setCurrencyPair(currencyPair);
    return symbol;
  }

  protected static Long getSymbolId(CurrencyPair currencyPair) {
    return symbols.stream()
        .filter(symbol -> symbol.getTradeType().isCfd())
        .filter(symbol -> symbol.getCurrencyPair().equals(currencyPair))
        .findFirst()
        .get()
        .getId();
  }

  protected static Long getSpotSymbolId(CurrencyPair currencyPair) {
    return symbols.stream()
        .filter(symbol -> symbol.getTradeType().isSpot())
        .filter(symbol -> symbol.getCurrencyPair().equals(currencyPair))
        .findFirst()
        .get()
        .getId();
  }

  protected static User createPersonalUser(Long id, boolean tradeUncapped, String lastName, String firstName) {
    final var user = new User();
    user.setId(id);
    user.setTradeUncapped(tradeUncapped);

    final var userInfo = new UserInfo();
    userInfo.setLastName(lastName);
    userInfo.setFirstName(firstName);
    user.setUserInfo(userInfo);
    return user;
  }

  protected static User createCorporateUser(Long id, boolean tradeUncapped, String corporateName) {
    final var user = new User();
    user.setId(id);
    user.setTradeUncapped(tradeUncapped);

    final var userInfo = new UserInfo();
    userInfo.setLastName("");
    userInfo.setFirstName("");
    user.setUserInfo(userInfo);

    final var userInfoCorporate = new UserInfoCorporate();
    userInfoCorporate.setName(corporateName);
    user.setUserInfoCorporate(userInfoCorporate);
    return user;
  }

  protected static BigDecimal big(String value) {
    return new BigDecimal(value);
  }

  protected static BigDecimal toJpy(BigDecimal decimal) {
    return Currency.JPY.getScaledAmount(decimal);
  }

  // yyyy/MM/dd HH:mm:ss
  protected static Date toDate(String strDate) {
    final var zdt = ZonedDateTime.parse(
        strDate + "+0000",
        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ssZ"));
    return Date.from(zdt.toInstant());
  }

  protected static Date toDateSSS(String strDate) {
    final var ldt =
        LocalDateTime.parse(strDate, DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.SSS"));
    return Date.from(ZonedDateTime.of(ldt, ZoneId.of("UTC")).toInstant());
  }
  // ---------------------------------------------------------------------------------------------
  protected static AssetSummary createAssetSummary(
      Long id, // id
      Long userId, // ユーザID
      Currency currency, // 通貨
      BigDecimal currentAmount // 現在資産
  ) {
    final var assetSummary = new AssetSummary();
    assetSummary.setId(id);
    assetSummary.setUserId(userId);
    assetSummary.setCurrency(currency);
    assetSummary.setCurrentAmount(currentAmount);
    return assetSummary;
  }

  protected static SpotOrder createOrder(
      Long id, // 注文ID
      Long userId, // ユーザID
      CurrencyPair currencyPair, // 通貨ペア
      OrderSide orderSide, // 売買
      OrderType orderType, // 買い方
      OrderStatus orderStatus, // 注文状況
      BigDecimal price, // 価格
      BigDecimal amount, // 数量
      BigDecimal remainingAmount, // 残数量
      CancelReason cancelReason, // キャンセル理由
      Date createdAt, // 作成日時(約定日時),
      Date updatedAt // 更新日時(約定日時)
  ) {
    final var order = new SpotOrder() {};
    order.setId(id);
    order.setSymbolId(getSymbolId(currencyPair));
    order.setUserId(userId);
    order.setOrderSide(orderSide);
    order.setOrderType(orderType);
    order.setOrderStatus(orderStatus);
    order.setPrice(price);
    order.setAmount(amount);
    order.setRemainingAmount(remainingAmount);
    order.setCancelReason(cancelReason);
    order.setCreatedAt(createdAt);
    order.setUpdatedAt(updatedAt);
    return order;
  }

  protected static SpotTrade createTrade(
      Long id, // 約定ID
      Long orderId, // 注文ID
      Long userId, // ユーザID
      CurrencyPair currencyPair, // 通貨ペア
      OrderSide orderSide, // 売買
      BigDecimal price, // 価格
      BigDecimal amount, // 数量
      BigDecimal assetAmount, // 約定代金
      BigDecimal fee, // 約定手数料
      Date createdAt // 作成日時(約定日時)
  ) {
    final var trade = new SpotTrade() {};
    trade.setId(id);
    trade.setOrderId(orderId);
    trade.setSymbolId(getSymbolId(currencyPair));
    trade.setUserId(userId);
    trade.setOrderSide(orderSide);
    trade.setPrice(price);
    trade.setAmount(amount);
    trade.setAssetAmount(assetAmount);
    trade.setFee(fee);
    trade.setCreatedAt(createdAt);
    return trade;
  }

  protected static FiatDeposit createFiatDeposit(Long id, Long userId, BigDecimal amount, BigDecimal fee, Date updatedAt) {
    final var deposit = new FiatDeposit();
    deposit.setId(id);
    deposit.setUserId(userId);
    deposit.setAmount(amount);
    deposit.setFee(fee);
    deposit.setUpdatedAt(updatedAt);
    return deposit;
  }

  protected static FiatWithdrawal createFiatWithdrawal(Long id, Long userId, BigDecimal amount, BigDecimal fee, Date updatedAt) {
    final var withdrawal = new FiatWithdrawal();
    withdrawal.setId(id);
    withdrawal.setUserId(userId);
    withdrawal.setAmount(amount);
    withdrawal.setFee(fee);
    withdrawal.setUpdatedAt(updatedAt);
    return withdrawal;
  }

  protected static Deposit createDeposit(Long id, Long userId, Currency currency, BigDecimal amount, BigDecimal fee, Date updatedAt) {
    final var deposit = new Deposit();
    deposit.setId(id);
    deposit.setUserId(userId);
    deposit.setCurrency(currency);
    deposit.setAmount(amount);
    deposit.setFee(fee);
    deposit.setUpdatedAt(updatedAt);
    return deposit;
  }

  protected static Withdrawal createWithdrawal(Long id, Long userId, Currency currency, BigDecimal amount, BigDecimal fee, Date updatedAt) {
    final var withdrawal = new Withdrawal();
    withdrawal.setId(id);
    withdrawal.setUserId(userId);
    withdrawal.setCurrency(currency);
    withdrawal.setAmount(amount);
    withdrawal.setWithdrawalFee(fee);
    withdrawal.setUpdatedAt(updatedAt);
    return withdrawal;
  }
}
