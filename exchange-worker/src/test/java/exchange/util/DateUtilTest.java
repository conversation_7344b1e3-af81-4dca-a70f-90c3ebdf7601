package exchange.util;

import exchange.common.util.DateUtil;
import exchange.common.util.FormatUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.TimeZone;

/**
 * @author: wen.y
 * @date: 2025/1/24
 */
public class DateUtilTest {

	@Test
	public void test1() {
		Date d1 = DateUtil.parse("2025-01-24", "yyyy-MM-dd");
		Date d2 = DateUtil.parseFromJst("2025-01-24", "yyyy-MM-dd");
		Date d3 = DateUtil.parseFromUtc("2025-01-24", "yyyy-MM-dd");

		System.out.println("----->");
    	System.out.println("d1 format:" + DateUtil.format(d1, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d1 format to jst:" + DateUtil.formatToJst(d1, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d1 format to utc:" + DateUtil.formatToUtc(d1, "yyyy-MM-dd HH:mm:ss"));

		System.out.println("----->");
		System.out.println("d2 format:" + DateUtil.format(d2, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d2 format to jst:" + DateUtil.formatToJst(d2, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d2 format to utc:" + DateUtil.formatToUtc(d2, "yyyy-MM-dd HH:mm:ss"));

		System.out.println("----->");
		System.out.println("d3 format:" + DateUtil.format(d3, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d3 format to jst:" + DateUtil.formatToJst(d3, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d3 format to utc:" + DateUtil.formatToUtc(d3, "yyyy-MM-dd HH:mm:ss"));
	}

	@Test
	public void test2() {
		Date d1 = DateUtil.nowDate();
		Date d2 = DateUtil.nowJstDate();
		Date d3 = DateUtil.nowUtcDate();

		System.out.println("----->");
		System.out.println("d1 format:" + DateUtil.format(d1, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d1 format to jst:" + DateUtil.formatToJst(d1, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d1 format to utc:" + DateUtil.formatToUtc(d1, "yyyy-MM-dd HH:mm:ss"));

		System.out.println("----->");
		System.out.println("d2 format:" + DateUtil.format(d2, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d2 format to jst:" + DateUtil.formatToJst(d2, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d2 format to utc:" + DateUtil.formatToUtc(d2, "yyyy-MM-dd HH:mm:ss"));

		System.out.println("----->");
		System.out.println("d3 format:" + DateUtil.format(d3, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d3 format to jst:" + DateUtil.formatToJst(d3, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d3 format to utc:" + DateUtil.formatToUtc(d3, "yyyy-MM-dd HH:mm:ss"));
	}

	@Test
	public void test3() {
		TimeZone timeZone1 = TimeZone.getDefault();
		TimeZone timeZone2 = TimeZone.getTimeZone("Asia/Tokyo");
		TimeZone timeZone3 = TimeZone.getTimeZone("UTC");

		System.out.println(DateUtil.getTimeZoneDiff(timeZone1, timeZone2));
		System.out.println(DateUtil.getTimeZoneDiff(timeZone1, timeZone3));
		System.out.println(DateUtil.getTimeZoneDiff(timeZone2, timeZone3));

		System.out.println(DateUtil.getTimeZoneDiffMinutes(timeZone1, timeZone2));
		System.out.println(DateUtil.getTimeZoneDiffMinutes(timeZone1, timeZone3));
		System.out.println(DateUtil.getTimeZoneDiffMinutes(timeZone2, timeZone3));

    	System.out.println(DateUtil.getTimeZoneDiffHours(timeZone1, timeZone2));
		System.out.println(DateUtil.getTimeZoneDiffHours(timeZone1, timeZone3));
		System.out.println(DateUtil.getTimeZoneDiffHours(timeZone2, timeZone3));
	}

	@Test
	public void test4() {
    	System.out.println(DateUtil.formatToUtc(DateUtil.getDayBeginForJst(), "yyyy-MM-dd HH:mm:ss"));
		System.out.println(DateUtil.formatToUtc(DateUtil.getDayEndForJst(), "yyyy-MM-dd HH:mm:ss"));
	}

	@Test
	public void test5() {
		// 起動日時の取得
		Date today = new Date();
		// 処理対象日の取得
		Date yesterday = new Date(today.getTime() - 60 * 60 * 24 * 1000);
		String targetDay = FormatUtil.formatJst(yesterday, FormatUtil.FormatPattern.YYYYMMDD);
		System.out.println(targetDay);
	}

	@Test
	public void test6() {
		final var dateFrom = Instant
				.ofEpochMilli(System.currentTimeMillis())
				.atZone(ZoneId.of("Asia/Tokyo"))
				.truncatedTo(ChronoUnit.DAYS);
		final var dateTo = dateFrom.plusDays(1);
		Date d1 = Date.from(dateFrom.toInstant());
		Date d2 = Date.from(dateTo.toInstant());

		System.out.println("----->");
		System.out.println("d1 format:" + DateUtil.format(d1, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d1 format to jst:" + DateUtil.formatToJst(d1, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d1 format to utc:" + DateUtil.formatToUtc(d1, "yyyy-MM-dd HH:mm:ss"));

		System.out.println("----->");
		System.out.println("d2 format:" + DateUtil.format(d2, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d2 format to jst:" + DateUtil.formatToJst(d2, "yyyy-MM-dd HH:mm:ss"));
		System.out.println("d2 format to utc:" + DateUtil.formatToUtc(d2, "yyyy-MM-dd HH:mm:ss"));

//		----->
//		d1 format:2025-02-06 23:00:00
//		d1 format to jst:2025-02-07 00:00:00
//		d1 format to utc:2025-02-06 15:00:00
//		----->
//		d2 format:2025-02-07 23:00:00
//		d2 format to jst:2025-02-08 00:00:00
//		d2 format to utc:2025-02-07 15:00:00
	}
}
