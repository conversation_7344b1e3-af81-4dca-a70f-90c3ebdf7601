package exchange.util;

import exchange.common.auth.AuthTypeEnum;
import exchange.common.auth.AuthUtil;
import org.junit.jupiter.api.Test;

/**
 * @author: wen.y
 * @date: 2025/2/10
 */
public class AuthUtilTest {

	private static final String gmoClientId = "Av1khkZhhRXB8ppG";

	private static final String gmoSecret = "8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iq";

	@Test
	public void getAuthorizationTest() throws Exception {
		String authorization = AuthUtil.getAuthorization(AuthTypeEnum.CLIENT_SECRET_BASIC, gmoClientId, gmoSecret);
    	System.out.println(authorization);
		// QXYxa2hrWmhoUlhCOHBwRzo4UEJwRUt1Y0I0SWt0eHJabmdLOTl3cE5TeHZGV09jcGFtR05aU0JyS25LanQ5UThJcQ==
	}

}
