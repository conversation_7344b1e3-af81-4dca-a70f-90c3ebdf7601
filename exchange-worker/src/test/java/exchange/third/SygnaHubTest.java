package exchange.third;

import exchange.common.component.SygnaHubManager;
import exchange.common.entity.Withdrawal;
import exchange.common.model.response.SygnaHubResponse;
import exchange.common.sygna.SygnaHubClient;
import exchange.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wen.y
 * @date: 2024/8/7
 */
@Slf4j
@SpringBootTest(classes = exchange.worker.Application.class)
@ActiveProfiles({"local"})
public class SygnaHubTest {
	@Resource
	private SygnaHubManager sygnaHubManager;

	/**
	* txRes.getRiskScore()
	* SEVERE, HIGH, MEDIUM, LOW
	* @throws Exception
	*/
	@Test
	public void getTransactions() throws Exception {
		List<String> ids = Lists.newArrayList(
				/*"4ef743af-99c3-4c57-9c31-e807080fcfd5",
				"74673321-b03e-4530-884b-1ecae7876710",
				"03cb4013-aaab-4c2b-bc78-c07e19a40e1a",
				"5c109437-0ccb-4c35-993b-d473fcf64a3c",
				"745a6cf3-c615-4316-89c8-c996684462a0"*/
				"b65c6d01-231b-4b7c-b158-99b884086775"
		);
		String txIds = ids.stream().collect(Collectors.joining("&id="));
		SygnaHubResponse<List<SygnaHubClient.TransactionsRes>> transactionsRes =
				sygnaHubManager.getTransactions(txIds);
		List<SygnaHubClient.TransactionsRes> transactionsList = transactionsRes.getData();
		log.info("resp: {}", JsonUtil.encode(transactionsList));

	}

}
