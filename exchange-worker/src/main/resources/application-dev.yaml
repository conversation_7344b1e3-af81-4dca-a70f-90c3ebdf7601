cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
aws:
  s3:
    year-report-bucket:
      name: year-report.cb-exchange-dev
# refered by logback-spring.xml
common:
  log:
    console:
      appender: CONSOLE_JSON
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: worker.dev.cxr-inc.com
    environment: dev
  datasource:
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
wallet:
  staking-retry-end-time: 23 #JST 23：00:00
exchange-pos:
  base-trade:
    coinbook:
      api-key: 00000000000000000000000000000000eb5dc73fd738ad64b73727ede5bd4998
      secret: f1a98810b68b0fe1c70efb162cc421bf8a0a0d1282b8669e8e1e9de48a8ca7ba

report:
  crypto-token-apply:
    email:
      to: ["<EMAIL>", "<EMAIL>"]

dowjones:
  device: "coinbook-dev1"
  username: <EMAIL>
  password: F98dgFjXVJuqvVNk
  client-id: Z1lpD6emDa3G2o9CVtd3tUnpuUKw6VciLvJ9XQjn