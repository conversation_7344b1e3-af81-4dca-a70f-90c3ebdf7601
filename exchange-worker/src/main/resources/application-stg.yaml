cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
aws:
  s3:
    year-report-bucket:
      name: year-report.cb-exchange-stg
# refered by logback-spring.xml
common:
  log:
    console:
      appender: CONSOLE_JSON
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: worker.stg.cxr-inc.com
    environment: stg
  datasource:
    master:
      maximum-pool-size: 150
      leak-detection-threshold: 3000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
exchange-pos:
  base-trade:
    coinbook:
      api-key:
      secret:

report:
  crypto-token-apply:
    email:
      to: ["<EMAIL>", "<EMAIL>"]

dowjones:
  device: "coinbook-stg"
