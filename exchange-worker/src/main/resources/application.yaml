async:
  core-pool-size: 85
#  queue-capacity: 60
#  max-pool-size: 60
aws:
  credentials:
    access-key:
    secret-key:
    salt:
  ses:
    host: email-smtp.ap-northeast-1.amazonaws.com
    port: 587
    username:
    password:
  s3:
    year-report-bucket:
      name:
cloud:
  aws:
    credentials:
      access-key:
      secret-key:
      use-default-aws-credentials-chain: false
    region:
      auto: false
      static: ap-northeast-1
    stack:
      auto: false
# refered by logback-spring.xml
common:
  log:
    console:
      appender: CONSOLE_DEFAULT
dealing:
  cb:
    host:
    port:
  okcoin:
    host:
    apiKey:
    secret:
    passPhrase:
management:
  server:
    port: 8082
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,manual"
      probes:
        enabled: true
  endpoints:
    metrics:
      enabled: false
    prometheus:
      enabled: false
    web:
      base-path: /actuator
      exposure:
        include: "*"
  metrics:
    export:
      cloudwatch:
        batchSize: 20
        enabled: false
        namespace: exchange-worker
        step: 1m
      prometheus:
        enabled: false
    web:
      server:
        request:
          autotime:
            # https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready-metrics-spring-mvc
            enabled: true
          metric-name: http.server.requests
server:
  port: 8080
  servlet:
    session:
      timeout: 1d
      cookie:
        max-age: 7d
        name: JSESSIONID
        path: /
        secure: true
  shutdown: graceful
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: localhost
      port: 6379
  datasource:
    master:
      driver-class-name: org.mariadb.jdbc.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.MySQL5Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.pos.entity
      url: ***********************************************************************
      username: exchange
      password: Exchange123
      minimum-idle: 10
      maximum-pool-size: 66
    historical:
      driver-class-name: org.postgresql.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.PostgreSQL82Dialect
      packages-to-scan: exchange.common.entity,exchange.spot.entity,exchange.pos.entity
      url: *****************************************
      username: exchange
      password: Exchange123
      minimum-idle: 10
      maximum-pool-size: 66
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  main:
    banner-mode: off
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant-path-matcher
  session:
    store-type: none
  web:
    resources:
      add-mappings: false
swagger:
  enabled: false
gmo:
  stg-base-endpoint: https://stg-api.gmo-aozora.com/ganb/api
  accounts-uri: /corporation/v1/accounts
  deposit-transactions: /corporation/v1/va/deposit-transactions
  refresh-token-interval: 3600000 #1hour
  authorization-token: /auth/v1/token
  issue-account-uri: /corporation/v1/va/issue
  holder-name-kana: COIN
  client-id: Av1khkZhhRXB8ppG
  secret: 8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iq
  redirect-uri: /app/v1/gmo/oauth
  bulk-transfer-uri: /corporation/v1/bulktransfer/request
  bulk-transfer-status-uri: /corporation/v1/bulktransfer/status
  bulk-transfer-request-result-uri: /corporation/v1/bulktransfer/request-result
  scope:
    - private:account #振込入金口座
    - private:virtual-account
    - private:transfer
    - private:bulk-transfer
refinitiv:
  host: api-worldcheck.refinitiv.com
  api-key: 0e3ac955-cdbf-454c-a7dc-135b480bb292
  secret-key: DvVA3G6jHJ6IP5pXFAOb94foBEVEhOzEzW3CwSUibZqC/VfGkFKRG6DI9z4DG4GQCqYvp6Zal2J5V6aq0AVU8w==
  group-id: 5jb7o7megh5v1fjy6i2pfcn0r

ekyc:
  secret: 10b94f00011817a999bb6ab46191ef33
  token: G1AQBouAWfEpHDYgw8kUMVxu
  api-auth-key: crA8fhBv9x
  enterprise-id: TENWCK0002
  transition-url: https://coinbook.co.jp/complete.html
  url:
    applicant-id: https://sand-nexwaykyc.nexlink2.jp/api/v1/applicants/profiles
    ekyc-url: https://ekyc-enter.dev-polaris.com/api/ekyc/v1/createUrl
    bpo-result: https://sand-nexwaykyc.nexlink2.jp/api/v1/applicants/%s/include_ekyc
chainalysis:
  token: f0560979c09da1c011a28fa50820ebbcf12eb6cc048ea07f57b2613a4b829df9
  base-endpoint: https://api.chainalysis.com
  alert-level-limit: LOW
elliptic:
  key: f408fa7499d9c05d3eb195f84fd2232e
  secret: 7ec064293391f681a433144702bcf790
  base-endpoint: https://aml-api.elliptic.co
  analyses-uri: /v2/wallet
  result-uri: /v2/wallet
  risk-score-limit: 1
wallet:
  client-id: api_user
  client-secret: 123456
  base-url: https://wallet-gateway-stg-internal.coinbook.co.jp
  timeout: 6000
  username: <EMAIL>
  apiKey: 63804f6c3b6c4cb38e72b62b6c08b13c
  testnet: true
  cold-customer-wallet-id: 121
  hot-own-wallet-id: 101
  hot-own-address:
    ada: addr_test1vr5k7cwmzwgxh7pkz6utj4hgmfkg2e2g3eysfevxknuxrtceryw6w
    nidt: "******************************************"
    btc: mqYCeAK3PZS6Wu5Nz8KEvzutVDvYEtCjtE
    eth: "******************************************"
    xrp: rpkKseuzffUKnhEH9X5wtUWx864jjqyRjk
  staking-platform:
    ada: Cardano Preprod
  staking-prepare-time: 14 #JST 14：00:00
  staking-retry-end-time: 17 #JST 17：00:00
  staking-wallet-id: 122
  staking-source-address:
    ada: addr_test1qzkudy2nvtcf93374tmdznzpfs806dy69qa6eng4sjhrfj4dc6g4xchsjtrra2hk69xyznqwl56f52pm4nx3tp9wxn9qnsfaq9
    eth: "******************************************"
sygna:
  hub-base-url: https://cb-sygna-stg-api.coinbook.co.jp/v1
  account: <EMAIL>
  credential: Coinbo@k6
  withdrawal-customer-id: 53611e8b-017d-46ed-8bee-5b99f5d09369
  wait-time: 1500000 #25分
  token-cache-seconds: 3000

exchange-websocket:
  redis-pubsub-cache:
    enabled: true # true: cache the message to avoid duplicate sending
    expire-in-minutes: 5 # cache will be expired in the specified period, the message will be sent even duplicate


report:
  crypto-token-apply:
    email:
      to: ["<EMAIL>"]

dowjones:
  base-auth-url: https://accounts.dowjones.com
  base-api-Url: https://api.dowjones.com
  username:
  password:
  client-id:
  search-type: PRECISE
  content-set: ["Watchlist"]
  filter-content-category: ["WL"]
  count: 300