package exchange.worker.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/10/21
 */
@Data
@Component
@ConfigurationProperties(prefix = "report.crypto-token-apply")
public class CryptoTokenApplyReportConfig {
	private Email email;

	@Data
	public static class Email {
		private List<String> to;
		private List<String> cc;
		private List<String> bcc;
	}

}
