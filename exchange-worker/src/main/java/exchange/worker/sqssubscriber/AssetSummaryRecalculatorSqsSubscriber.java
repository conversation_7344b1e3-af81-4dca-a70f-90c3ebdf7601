package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;

import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.AssetSummaryRecalculator;

@Component
public class AssetSummaryRecalculatorSqsSubscriber extends SqsSubscriber<AssetSummaryRecalculator> {

  @Override
  public Class<AssetSummaryRecalculator> getWorkerClass() {
    return AssetSummaryRecalculator.class;
  }
}
