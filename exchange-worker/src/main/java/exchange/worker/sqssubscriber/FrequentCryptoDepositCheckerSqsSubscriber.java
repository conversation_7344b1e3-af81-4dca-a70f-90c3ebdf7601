package exchange.worker.sqssubscriber;

import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.FrequentCryptoDepositChecker;
import org.springframework.stereotype.Component;

/**
 * TMS
 * 暗号資産高頻度入庫検知ワーカー
 */
@Component
public class FrequentCryptoDepositCheckerSqsSubscriber extends
    SqsSubscriber<FrequentCryptoDepositChecker> {

  @Override
  public Class<FrequentCryptoDepositChecker> getWorkerClass() {
    return FrequentCryptoDepositChecker.class;
  }
}
