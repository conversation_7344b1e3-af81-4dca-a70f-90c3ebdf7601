package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.BulkTransferExecutor;

@Component
public class BulkTransferExecutorSqsSubscriber extends SqsSubscriber<BulkTransferExecutor> {

  @Override
  public Class<BulkTransferExecutor> getWorkerClass() {
    return BulkTransferExecutor.class;
  }
}
