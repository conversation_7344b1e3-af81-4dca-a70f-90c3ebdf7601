package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.ExchangeSummaryCalculator;

@Component
public class ExchangeSummarySqsSubscriber extends SqsSubscriber<ExchangeSummaryCalculator> {

  @Override
  public Class<ExchangeSummaryCalculator> getWorkerClass() {
    return ExchangeSummaryCalculator.class;
  }
}
