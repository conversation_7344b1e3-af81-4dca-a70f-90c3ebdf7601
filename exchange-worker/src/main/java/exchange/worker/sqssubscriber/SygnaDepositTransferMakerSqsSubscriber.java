package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.SygnaDepositTransferMaker;

@Component
public class SygnaDepositTransferMakerSqsSubscriber extends SqsSubscriber<SygnaDepositTransferMaker> {

  @Override
  public Class<SygnaDepositTransferMaker> getWorkerClass() {
    return SygnaDepositTransferMaker.class;
  }

}
