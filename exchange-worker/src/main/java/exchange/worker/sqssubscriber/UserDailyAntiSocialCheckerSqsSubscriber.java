package exchange.worker.sqssubscriber;

import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.UserDailyAntiSocialChecker;
import org.springframework.stereotype.Component;

/**
 * @author: wen.y
 * @date: 2024/12/31
 */
@Component
public class UserDailyAntiSocialCheckerSqsSubscriber extends SqsSubscriber<UserDailyAntiSocialChecker> {
	@Override
	public Class<UserDailyAntiSocialChecker> getWorkerClass() {
		return UserDailyAntiSocialChecker.class;
	}
}
