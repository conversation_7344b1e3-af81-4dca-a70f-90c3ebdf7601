package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.StakingAutoRewardRecordGetter;

@Component
public class StakingAutoRewardRecordGetterSqsSubscriber extends SqsSubscriber<StakingAutoRewardRecordGetter> {

  @Override
  public Class<StakingAutoRewardRecordGetter> getWorkerClass() {
    return StakingAutoRewardRecordGetter.class;
  }

}
