package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.InvestmentPurposeDeviationChecker;

@Component
public class InvestmentPurposeDeviationCheckerSqsSubscriber
    extends SqsSubscriber<InvestmentPurposeDeviationChecker> {

  @Override
  public Class<InvestmentPurposeDeviationChecker> getWorkerClass() {
    return InvestmentPurposeDeviationChecker.class;
  }
}
