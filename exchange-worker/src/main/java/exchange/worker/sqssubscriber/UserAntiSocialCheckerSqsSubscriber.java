package exchange.worker.sqssubscriber;

import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.UserAntiSocialChecker;
import exchange.worker.worker.UserEkycBpoResultUpdater;
import org.springframework.stereotype.Component;

@Component
public class UserAntiSocialCheckerSqsSubscriber extends SqsSubscriber<UserAntiSocialChecker> {

  @Override
  public Class<UserAntiSocialChecker> getWorkerClass() {
    return UserAntiSocialChecker.class;
  }
}
