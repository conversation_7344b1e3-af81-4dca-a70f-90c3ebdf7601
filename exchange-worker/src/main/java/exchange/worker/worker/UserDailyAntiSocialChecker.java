package exchange.worker.worker;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import exchange.common.config.DowJonesConfig;
import exchange.common.constant.*;
import exchange.common.dal.master.bo.user.AntiSocialCheckUserBO;
import exchange.common.dal.master.mapper.user.UserKycMapper;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.entity.dowjones.DowJonesSamCase;
import exchange.common.service.UserService;
import exchange.common.service.dowjones.DowJonesAntisocialCheckService;
import exchange.common.service.dowjones.DowJonesSamCaseService;
import exchange.common.util.IdUtil;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: wen.y
 * @date: 2024/12/31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserDailyAntiSocialChecker extends Worker {

	private final DowJonesConfig dowJonesConfig;
	private final UserKycMapper userKycMapper;
	private final UserService userService;
	private final DowJonesSamCaseService dowJonesSamCaseService;
	private final DowJonesAntisocialCheckService dowJonesAntisocialCheckService;

	private final Set<UserStatus> excludedUserStatuses = EnumSet.of(
			UserStatus.REVIEWING,
			UserStatus.LEFT
	);

	@Override
	public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
		log.info("************************ UserDailyAntiSocialChecker started **************************************");

		String tid = IdUtil.getUuid();
		long startTime = System.currentTimeMillis();

		try {
			List<User> userList = userService.findByUserAll();
			log.info("[tid={}] Retrieved {} total users for anti-social check", tid, userList.size());

			// Process personal users (batch processing)
			List<User> personalUserList = filterPersonalUsers(userList);
			processPersonalUsers(tid, personalUserList);

			// Process corporate users (individual processing)
			List<User> corporateUserList = filterCorporateUsers(userList);
			processCorporateUsers(tid, corporateUserList);

			// Update user anti-social status based on risk check results
			updateUserAntisocialStatus(tid);

		} catch (Exception e) {
			log.error("[tid={}] Error occurred during UserDailyAntiSocialChecker execution", tid, e);
			throw e;
		} finally {
			long endTime = System.currentTimeMillis();
			long durationSeconds = (endTime - startTime) / 1000;
			log.info("************************ UserDailyAntiSocialChecker completed in {} seconds **************************************", durationSeconds);
		}
	}

	/**
	 * Filter personal users based on criteria
	 */
	private List<User> filterPersonalUsers(List<User> userList) {
		List<User> personalUserList = userList.stream()
				.filter(user -> user.getUserInfoId() != null)
				.filter(user -> !excludedUserStatuses.contains(user.getUserStatus()))
				.toList();

		log.info("Filtered {} personal users for processing", personalUserList.size());
		return personalUserList;
	}

	/**
	 * Filter corporate users based on criteria
	 */
	private List<User> filterCorporateUsers(List<User> userList) {
		List<User> corporateUserList = userList.stream()
				.filter(user -> user.getUserInfoCorporateId() != null)
				.toList();

		log.info("Filtered {} corporate users for processing", corporateUserList.size());
		return corporateUserList;
	}

	/**
	 * Process personal users in batches
	 */
	private void processPersonalUsers(String tid, List<User> personalUserList) throws Exception {
		if (CollectionUtils.isEmpty(personalUserList)) {
			log.info("[tid={}] No personal users to process", tid);
			return;
		}

		log.info("[tid={}] Starting to process {} personal users", tid, personalUserList.size());

		DowJonesSamCase dowJonesSamCase = dowJonesSamCaseService.findByDowJonesSamCase(tid, UserTypeEnum.PERSONAL);
		if (dowJonesSamCase == null) {
			log.error("[tid={}] Failed to create or find Dow Jones SAM case for personal users", tid);
			return;
		}

		List<AntiSocialCheckUserBO> userPersonalAntiSocialCheckList = new ArrayList<>();
		for (User user : personalUserList) {
			AntiSocialCheckUserBO antiSocialCheckUserBO = getAntiSocialCheckUserBO(user);
			userPersonalAntiSocialCheckList.add(antiSocialCheckUserBO);
		}

		this.processBatchPersonalUsers(tid, userPersonalAntiSocialCheckList, dowJonesSamCase.getDowJonesSamCaseId());
		log.info("[tid={}] Completed processing personal users", tid);
	}

	/**
	 * Process corporate users individually
	 */
	private void processCorporateUsers(String tid, List<User> corporateUserList) {
		if (CollectionUtils.isEmpty(corporateUserList)) {
			log.info("[tid={}] No corporate users to process", tid);
			return;
		}

		log.info("[tid={}] Starting to process {} corporate users", tid, corporateUserList.size());

		for (User user : corporateUserList) {
			AntiSocialCheckUserBO antiSocialCheckUserBO = getAntiSocialCheckUserBO(user);
			this.processSingleCorporateUser(tid, antiSocialCheckUserBO);
		}

		log.info("[tid={}] Completed processing corporate users", tid);
	}

	/**
	 * Update user anti-social status based on risk check results
	 */
	private void updateUserAntisocialStatus(String tid) throws Exception {
		log.info("[tid={}] Starting to update user anti-social status based on risk check results", tid);

		Map<Long, Boolean> riskCheckResults = dowJonesAntisocialCheckService.checkRisk(tid);
		if (CollectionUtils.isEmpty(riskCheckResults)) {
			log.info("[tid={}] No risk check results found, skipping status update", tid);
			return;
		}

		log.info("[tid={}] Processing risk check results for {} users", tid, riskCheckResults.size());

		Map<Long, List<Long>> caseIdToUserIdsMap = new HashMap<>();
		int updatedCount = 0;
		int skippedCount = 0;

		for (Map.Entry<Long, Boolean> entry : riskCheckResults.entrySet()) {
			Long userId = entry.getKey();
			Boolean hasRisk = entry.getValue();

			Optional<User> matchedUser = Optional.ofNullable(userService.findById(userId));
			if (matchedUser.isEmpty()) {
				log.warn("[tid={}] User not found, skipping update - userId: {}", tid, userId);
				skippedCount++;
				continue;
			}

			User user = matchedUser.get();
			if (shouldSkipUpdate(user)) {
				log.debug("[tid={}] Skipping user status update due to invalid state - userId: {}, currentStatus: {}/{}",
						tid, userId, user.getUserStatus(), user.getKycStatus());
				skippedCount++;
				continue;
			}

			Long caseId = user.getCaseId();
			caseIdToUserIdsMap
					.computeIfAbsent(caseId, k -> new ArrayList<>())
					.add(userId);

			AntisocialStatus antisocialStatus = hasRisk ? AntisocialStatus.CONFIRMATION_REQUIRED : AntisocialStatus.OK;
			userKycMapper.updateAntisocialStatusById(user.getUserKycId(), antisocialStatus);
			updatedCount++;

			log.debug("[tid={}] Updated anti-social status for user: {}, hasRisk: {}, status: {}",
					tid, userId, hasRisk, antisocialStatus);
		}

		// Process case groups to update remaining users update
		processCaseGroupsUpdate(tid, caseIdToUserIdsMap);

		log.info("[tid={}] Completed anti-social status update - updated: {}, skipped: {}",
				tid, updatedCount, skippedCount);
	}

	/**
	 * Process case groups to update remaining users in the same case
	 */
	private void processCaseGroupsUpdate(String tid, Map<Long, List<Long>> caseIdToUserIdsMap) {
		log.debug("[tid={}] Processing {} case groups for remaining user updates", tid, caseIdToUserIdsMap.size());

		caseIdToUserIdsMap.forEach((caseId, userIds) -> {
			List<User> userGroupAll = userService.findByUserStatusAndIds(userIds, caseId);
			if (CollectionUtils.isNotEmpty(userGroupAll)) {
				for (User user : userGroupAll) {
					if (!AntisocialStatus.OK.equals(user.getUserKyc().getAntisocialStatus())) {
						userKycMapper.updateAntisocialStatusById(user.getUserKycId(), AntisocialStatus.OK);
						log.debug("[tid={}] Updated remaining user in case group - userId: {}, caseId: {}",
								tid, user.getId(), caseId);
					}
				}
			}
		});
	}

	/**
	 * Process personal users in batches (renamed from asyncSolve for clarity)
	 */
	public void processBatchPersonalUsers(String tid, List<AntiSocialCheckUserBO> userPersonalAntiSocialCheckList, String caseId) throws Exception {
		log.info("[tid={}] Starting batch processing for {} personal users", tid, userPersonalAntiSocialCheckList.size());

		List<List<AntiSocialCheckUserBO>> groupedLists = new ArrayList<>();
		Long count = dowJonesConfig.getCount();

		for (int i = 0; i < userPersonalAntiSocialCheckList.size(); i += count.intValue()) {
			int endIndex = Math.min(i + count.intValue(), userPersonalAntiSocialCheckList.size());
			List<AntiSocialCheckUserBO> subList = userPersonalAntiSocialCheckList.subList(i, endIndex);
			groupedLists.add(subList);
		}

		log.info("[tid={}] Created {} batches for processing (batch size: {})", tid, groupedLists.size(), count);
		dowJonesAntisocialCheckService.processAntiSocialCheckInBatches(tid, groupedLists, caseId);
		log.info("[tid={}] Completed batch processing for personal users", tid);
	}

	/**
	 * Process single corporate user (renamed from normalSolve for clarity)
	 */
	public void processSingleCorporateUser(String tid, AntiSocialCheckUserBO userBO) {
		log.debug("[tid={}] Processing corporate user: userId={}, userKycId={}", tid, userBO.getId(), userBO.getUserKycId());

		AntiSocialCheckBusinessTypeEnum businessType = AntiSocialCheckBusinessTypeEnum.USER_CONTINUOUS_SCREENING;
		try {
			Boolean hasRisk = dowJonesAntisocialCheckService.hasRisk(tid, businessType, null, DowJonesApiTypeEnum.SAM, userBO.getId());

			if (hasRisk != null) {
				AntisocialStatus antisocialStatus = hasRisk ? AntisocialStatus.CONFIRMATION_REQUIRED : AntisocialStatus.OK;
				userKycMapper.updateAntisocialStatusById(userBO.getUserKycId(), antisocialStatus);
				log.info("[tid={}] Corporate user processing completed: userId={}, userKycId={}, hasRisk={}, status={}",
						tid, userBO.getId(), userBO.getUserKycId(), hasRisk, antisocialStatus);
			} else {
				log.warn("[tid={}] Risk check returned null result for corporate user: userId={}, userKycId={}",
						tid, userBO.getId(), userBO.getUserKycId());
			}
		} catch (Exception e) {
			log.error("[tid={}] Error processing corporate user: userId={}, userKycId={}",
					tid, userBO.getId(), userBO.getUserKycId(), e);
		}
	}

	/**
	 * Convert User entity to AntiSocialCheckUserBO
	 */
	private static AntiSocialCheckUserBO getAntiSocialCheckUserBO(User user) {
		AntiSocialCheckUserBO antiSocialCheckUserBO = new AntiSocialCheckUserBO();
		antiSocialCheckUserBO.setId(user.getId());
		antiSocialCheckUserBO.setEmail(user.getEmail());
		antiSocialCheckUserBO.setUserStatus(user.getUserStatus());
		antiSocialCheckUserBO.setUserKycId(user.getUserKycId());
		antiSocialCheckUserBO.setKycStatus(user.getKycStatus());

		// Set personal user information if available
		if (user.getUserInfo() != null) {
			antiSocialCheckUserBO.setUserInfoId(user.getUserInfoId());
			antiSocialCheckUserBO.setUserInfoFirstName(user.getUserInfo().getFirstName());
			antiSocialCheckUserBO.setUserInfoLastName(user.getUserInfo().getLastName());
			antiSocialCheckUserBO.setUserInfoBirthday(user.getUserInfo().getBirthday());
			antiSocialCheckUserBO.setUserInfoGender(user.getUserInfo().getGender());
		}

		// Set corporate user information if available
		if (user.getUserInfoCorporate() != null) {
			antiSocialCheckUserBO.setUserInfoCorporateId(user.getUserInfoCorporateId());
			antiSocialCheckUserBO.setUserInfoCorporateName(user.getUserInfoCorporate().getName());
		}

		return antiSocialCheckUserBO;
	}

	/**
	 * Check if user status update should be skipped based on current status
	 */
	private boolean shouldSkipUpdate(User user) {
		return excludedUserStatuses.contains(user.getUserStatus());
	}
}
