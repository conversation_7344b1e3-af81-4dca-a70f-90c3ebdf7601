package exchange.worker.worker;

import java.util.Date;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import exchange.common.entity.Symbol;
import exchange.common.service.GmoDepositService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Component
@Slf4j
public class GmoDepositReconcileExecutor extends Worker {

  private final GmoDepositService gmoDepositService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("===========================GmoDepositReconcileExecutor start=======================================");

    String targetDateFrom = (String) params.get("targetDateFrom");
    String targetDateTo = (String) params.get("targetDateTo");
    Date today = new Date();
    // 処理対象日の取得
    Date yesterday = new Date(today.getTime() - 60 * 60 * 24 * 1000);
    String targetDay = FormatUtil.formatJst(yesterday, FormatPattern.YYYY_MM_DD);
    if (StringUtils.isEmpty(targetDateFrom)) {
      targetDateFrom = targetDay;
    }
    if (StringUtils.isEmpty(targetDateTo)) {
      targetDateTo = targetDay;
    }
    
    Date targetFrom = FormatUtil.parse(targetDateFrom, FormatPattern.YYYY_MM_DD);
    Date targetTo = FormatUtil.parse(targetDateTo, FormatPattern.YYYY_MM_DD);
    
    if (targetTo.before(targetFrom)) {
      log.error("Parameter Check Error: targetTo: " + targetTo + " をtargetFrom: " + targetFrom + " 以降に設定してください。");
    } else {
      log.info(
          "Reconcile START : targetDateFrom is "
              + targetDateFrom
              + ",targetDateTo is "
              + targetDateTo);
      gmoDepositService.reCalculate(targetDateFrom, targetDateTo);
    }
    
    log.info("===========================GmoDepositReconcileExecutor end=======================================");
    
  }

}