package exchange.worker.worker;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.common.component.CustomTransactionManager;
import exchange.worker.component.Worker;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.entity.ApiInfo;
import exchange.common.entity.CopyOrderConfig;
import exchange.common.entity.Symbol;
import exchange.common.entity.SystemConfig;
import exchange.common.exception.CustomException;
import exchange.common.service.ApiInfoService;
import exchange.common.service.CopyOrderConfigService;
import exchange.common.service.SystemConfigService;
import exchange.common.util.JsonUtil;
import exchange.spot.entity.SpotCopyTrade;
import exchange.spot.entity.SpotTrade;
import exchange.spot.service.SpotCopyTradeService;
import exchange.spot.service.SpotTradeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class SpotCopyTradeGetter extends Worker {

  private static final Exchange COPY_EXCHANGE = Exchange.COINBOOK;

  private final ApiInfoService apiInfoService;

  private final CopyOrderConfigService copyOrderConfigService;

  private final CustomTransactionManager customTransactionManager;

  private final SystemConfigService systemConfigService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    Long symbolId = symbol.getId();

    log.info("spotCopyTradeGetterLog,symbolId," + symbolId + ",start");

    // spot_tradeからコピー済みの最新のCopyTrade.exchangeId(=spot_trade.id)を取得
    SpotCopyTrade latestCopyTrade =
        SpotCopyTradeService.getBean(symbol).findLatest(symbol.getId(), COPY_EXCHANGE);

    // コピー注文を発注する運用口座のuser_idリストを取得
    // copyOrderConfigのapiInfoIdに紐づくapiInfoのuserIdをsystemConfigのmarketMakerUserIdsから取得
    CopyOrderConfig copyOrderConfig = copyOrderConfigService.findOne(symbolId, COPY_EXCHANGE, true);
    if (copyOrderConfig == null) {
      log.error("spotCopyTradeGetterLog,symbolId," + symbolId + ",copy order config not found");
      throw new CustomException(
          ErrorCode.REQUEST_ERROR_COPY_ORDER_CONFIG_NOT_FOUND, "symbolId," + symbolId);
    }
    ApiInfo marketMakerApiInfo = apiInfoService.findOne(copyOrderConfig.getCopyOrderApiInfoId());

    SystemConfig systemConfig =
        systemConfigService.findByCondition(null, null, "marketMakerUserId");
    if (systemConfig == null || marketMakerApiInfo == null) {
      log.error(
          "spotCopyTradeGetterLog,symbolId," + symbolId + ",marketmaker user or apiInfo not found");
      throw new CustomException(
          ErrorCode.REQUEST_ERROR_MARKETMAKER_USER_NOT_FOUND, "symbolId," + symbolId);
    }

    List<Long> marketMakerUserIds =
        Arrays.asList(systemConfig.getValue().split(","))
            .stream()
            .map(marketMakerUserId -> Long.valueOf(marketMakerUserId))
            .filter(marketMakerUserId -> marketMakerUserId.equals(marketMakerApiInfo.getUserId()))
            .collect(Collectors.toList());

    log.info(
        "DBMITO,spotCopyTradeGetterLog,symbolId,"
            + symbolId
            + ",marketMakerUserIds,"
            + JsonUtil.encode(marketMakerUserIds));

    if (CollectionUtils.isEmpty(marketMakerUserIds)) {
      log.error("spotCopyTradeGetterLog,symbolId," + symbolId + ",marketmaker user not found");
      throw new CustomException(
          ErrorCode.REQUEST_ERROR_MARKETMAKER_USER_NOT_FOUND, "symbolId," + symbolId);
    }

    // 最新のexchangeIdを含まずそれ以降を取得 = 最新のexchangeId+1以降
    Long idFrom = (latestCopyTrade == null) ? Long.valueOf(0) : latestCopyTrade.getExchangeId();
    idFrom++;

    // コピーするspot_tradeを取得：最新のexchangeIdを含まずそれ以降のspot_trade（運用口座分)を取得
    List<SpotTrade> spotTrades =
        SpotTradeService.getBean(symbol)
            .findAllByCondition(
                symbolId,
                marketMakerUserIds,
                null,
                null,
                idFrom,
                null,
                null,
                null,
                null,
                null,
                null,
                null);

    // コピー対象無しの場合スキップ
    if (CollectionUtils.isEmpty(spotTrades)) {
      log.info("spotCopyTradeGetterLog,symbolId," + symbolId + ",spotTrades is empty");
      return;
    }

    // 昇順sort
    spotTrades.sort((o1, o2) -> o1.getId().compareTo(o2.getId()));

    // SpotCopyTradeにコピーする
    /** 価格・数量はspot_trade登録時に桁数処理済 */
    customTransactionManager.execute(
        entityManager -> {
          // 複数件insertで1コミット
          for (SpotTrade spotTrade : spotTrades) {
            SpotCopyTradeService.getBean(symbol)
                .add(
                    symbolId,
                    spotTrade.getUserId(),
                    spotTrade.getOrderSide(),
                    spotTrade.getOrderType(),
                    spotTrade.getPrice(),
                    spotTrade.getAmount(),
                    spotTrade.getTradeAction(),
                    spotTrade.getOrderId(),
                    spotTrade.getFee(),
                    spotTrade.getJpyConversion(),
                    spotTrade.getTargetOrderId(),
                    spotTrade.getTargetUserId(),
                    spotTrade.getAssetAmount(),
                    Exchange.COINBOOK,
                    spotTrade.getId(),
                    null,
                    entityManager);
          }
        });

    if (latestCopyTrade == null) {
      log.info(
          "spotCopyTradeGetterLog,end,symbolId,"
              + symbolId
              + ",idFrom,"
              + idFrom
              + ",spotTrades.size,"
              + spotTrades.size()
              + ",spotTrades.firstId,"
              + spotTrades.get(0).getId());
    } else {
      log.info(
          "spotCopyTradeGetterLog,end,symbolId,"
              + symbolId
              + ",latestCopyTrade.id,"
              + latestCopyTrade.getId()
              + ",latestCopyTrade.exchangeId,"
              + latestCopyTrade.getExchangeId()
              + ",idFrom,"
              + idFrom
              + ",spotTrades.size,"
              + spotTrades.size()
              + ",spotTrades.firstId,"
              + spotTrades.get(0).getId());
    }

    log.info("spotCopyTradeGetterLog,symbolId," + symbolId + ",end");
  }
}
