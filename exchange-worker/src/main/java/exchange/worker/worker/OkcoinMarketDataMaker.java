package exchange.worker.worker;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.model.response.OkcoinMarketDataResponse;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.OkcoinService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.pos.entity.PosCandlestick;
import exchange.pos.service.PosCandlestickService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class OkcoinMarketDataMaker extends Worker {

  private final OkcoinService okcoinService;
  private final PosCandlestickService posCandleStickService;
  private final CurrencyPairConfigService currencyPairConfigService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
log.info("===========================OKCOINから終値取得開始/OkcoinMarketDataMaker start=======================================");
    
    Date today = new Date();
    Date targetday = new Date(today.getTime() - 60 * 60 * 24 * 1000 * 2);
    String targetDayString = FormatUtil.formatJst(targetday, FormatPattern.YYYYMMDD);
    log.info("OkcoinMarketDataMaker.log.targetDay:" + targetDayString);
    targetDayString = targetDayString  + "150000";
    Date targetAt = FormatUtil.parse(targetDayString, FormatPattern.YYYYMMDDHHMMSS);
    Date targetTo = new Date(targetAt.getTime() + 1000); // 対象日の00:00:01
    // ETHのsymbol id:7
    List<PosCandlestick> posCandlestickExist = 
        posCandleStickService.findByCondition(7l, CandlestickType.P1D, targetAt, targetTo);
    List<CurrencyPairConfig> currencyPairConfig = currencyPairConfigService
        .findAllByCondition(TradeType.POS, CurrencyPair.ETH_JPY, true);
    if (!CollectionUtils.isEmpty(currencyPairConfig)) {
      log.info("販売所のETHが解放されたので、okcoinから取得必要がありません");
      return;
    }
    if(ObjectUtils.isEmpty(posCandlestickExist)) {
      try {
        OkcoinMarketDataResponse res = okcoinService.getMarketData(targetAt);
        List<String> marketData = res.get(0);
        String close = null;
        if(marketData != null) {
          close = marketData.get(4);
        }
        if(close != null) {
          PosCandlestick posCandleStick = new PosCandlestick();
          posCandleStick.setSymbolId(7l);
          posCandleStick.setTargetAt(targetAt);
          posCandleStick.setCandlestickType(CandlestickType.P1D);
          posCandleStick.setClose(new BigDecimal(close));
          posCandleStick.setFixed(true);
          posCandleStickService.save(posCandleStick);
        } else {
          log.error("OKCOINから終値の取得が失敗しました。targetDay:" + targetDayString);
        }
      } catch(Exception e) {
        log.error("OKCOINから終値の取得が失敗しました。targetDay:" + targetDayString + "error:" + e.getMessage());
      }
    }
    
    log.info("===========================OKCOINから終値取得終了/OkcoinMarketDataMaker finish=======================================");
  }
}
