package exchange.worker.worker;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.constant.StakingStatus;
import exchange.common.entity.StakingApplyDetail;
import exchange.common.entity.StakingInfo;
import exchange.common.entity.Symbol;
import exchange.common.repos.StakingNetworkProfitRepository;
import exchange.common.service.StakingApplyDetailService;
import exchange.common.service.StakingInfoService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class StakingRewardUpdater extends Worker {

  private final StakingInfoService stakingInfoService;
  private final StakingApplyDetailService stakingApplyDetailService;
  private final StakingNetworkProfitRepository stakingNetworkProfitRepository;
  
  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("===========================顧客未確定報酬更新開始/StakingRewardUpdater start=======================================");
    
    // 報酬計算
    String currentDateString = FormatUtil.formatJst(new Date(), FormatPattern.YYYYMMDD);
    Date currentDate = FormatUtil.parseJst(currentDateString, FormatPattern.YYYYMMDD);
    long todayFrom = currentDate.getTime();
    long todayTo = todayFrom + 1000*3600*24;

    List<StakingInfo> stakingInfoList = stakingInfoService.findAll();
    Set<Currency> currencySet = 
        stakingInfoList.stream().map(StakingInfo::getCurrency).collect(Collectors.toSet());
    // 予期yearRate取得
    Map<Currency, BigDecimal> yearRateMap = new HashMap<>();
    for(Currency currency : currencySet) {
      BigDecimal latestProfit = stakingNetworkProfitRepository.latestProfit(currency.name());
      yearRateMap.put(currency, latestProfit);
    }
    // 異常ケース：ステーキングデータの報酬付与日などを変更
    List<StakingApplyDetail> stakeFailedList = stakingApplyDetailService.findRewardTargetByCondition(
        new StakingStatus[] {StakingStatus.WAIT_STAKING,
            StakingStatus.APPLING}, 
        todayFrom, 
        null,
        null);
    stakingApplyDetailUpdate(stakeFailedList);
    
    // 正常ケース
    List<StakingApplyDetail> rewardStakingList = stakingApplyDetailService.findRewardTargetByCondition(
        new StakingStatus[] {StakingStatus.APPLIED,
            StakingStatus.EXPIRED,
            StakingStatus.EXPIRED_WAIT_FUND}, 
        todayFrom, 
        todayFrom,
        null);
    for(StakingApplyDetail rewardStaking : rewardStakingList) {
      if(rewardStaking.getRecentRewardDate() != null && rewardStaking.getRecentRewardDate().getTime() >= todayFrom 
          && rewardStaking.getRecentRewardDate().getTime() < todayTo) {
        log.info("今日の報酬が付与しました。StakingApplyDetailID:" + rewardStaking.getId());
      } else {
        // ネット予想年率　- ＣＢ設定年率
        BigDecimal yearRate = BigDecimal.ZERO;
        yearRate = 
            yearRateMap.get(rewardStaking.getCurrency()).subtract(rewardStaking.getStakingInfo().getCbFeeYearRate());
        if(yearRate.compareTo(BigDecimal.ZERO) < 0) {
          yearRate = BigDecimal.ZERO;
        }
        // 昨日報酬（currency精度）
        BigDecimal yesterdayReward = 
            rewardStaking.getApplyAmount().multiply(yearRate).
            divide(new BigDecimal(100*365), rewardStaking.getCurrency().getPrecision(), RoundingMode.DOWN);
        // 報酬（累加）
        rewardStaking.setRewardAccumulate(rewardStaking.getRewardAccumulate().add(yesterdayReward));
        rewardStaking.setRecentRewardDate(new Date());
        rewardStaking.setYearRate(yearRate);
        rewardStaking.setUpdatedBy("WORKER");
        stakingApplyDetailService.save(rewardStaking);
      }
    }
    log.info("===========================顧客未確定報酬更新終了/StakingRewardUpdater finish=======================================");
  }
  
  private void stakingApplyDetailUpdate(List<StakingApplyDetail> stakingFailedList) {
    for(StakingApplyDetail stakingFailed : stakingFailedList) {
      stakingFailed.setStakingDatePlan(
          stakingApplyDateUpdate(stakingFailed.getStakingDatePlan(), stakingFailed.getCurrency()));
      stakingFailed.setRewardStartDate(
          stakingApplyDateUpdate(stakingFailed.getRewardStartDate(), stakingFailed.getCurrency()));
      stakingFailed.setExpirationDate(
          stakingApplyDateUpdate(stakingFailed.getExpirationDate(), stakingFailed.getCurrency()));
      stakingFailed.setCancelDisableDateFrom(
          stakingApplyDateUpdate(stakingFailed.getCancelDisableDateFrom(), stakingFailed.getCurrency()));
      stakingFailed.setCancelDisableDateTo(
          stakingApplyDateUpdate(stakingFailed.getCancelDisableDateTo(), stakingFailed.getCurrency()));
      stakingFailed.setCancelDate(
          stakingApplyDateUpdate(stakingFailed.getCancelDate(), stakingFailed.getCurrency()));
      stakingFailed.setUpdatedBy("WORKER");
      stakingApplyDetailService.save(stakingFailed);
    }
  }
  
  // ADA:5日加算 ETH:1日加算
  private Date stakingApplyDateUpdate(Date date, Currency currency) {
    if(Currency.ETH.equals(currency)) {
      return new Date(date.getTime() + 3600*1000*24);
    }
    return new Date(date.getTime() + 3600*1000*24*5);
  }
}
