package exchange.worker.worker;

import java.util.Map;

import org.springframework.stereotype.Component;

import exchange.worker.component.Worker;
import exchange.common.entity.Symbol;
import exchange.spot.service.SpotTradeService;

@Component
public class SpotOrderExecutor extends Worker {

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) {
    SpotTradeService.getBean(symbol).trade(symbol);
  }
}
