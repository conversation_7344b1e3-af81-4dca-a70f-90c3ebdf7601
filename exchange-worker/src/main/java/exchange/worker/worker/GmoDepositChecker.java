package exchange.worker.worker;

import exchange.common.entity.Symbol;

import exchange.common.service.GmoDepositService;
import exchange.worker.component.Worker;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RequiredArgsConstructor
public class GmoDepositChecker extends Worker {

  private final GmoDepositService gmoDepositService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info(" ==============================depositChecker start==============================");
    gmoDepositService.calculate();
    log.info(" ==============================depositChecker end================================");

  }
}
