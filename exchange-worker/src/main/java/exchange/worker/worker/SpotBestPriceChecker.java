package exchange.worker.worker;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.worker.component.Worker;
import exchange.common.constant.ErrorCode;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.util.CalculatorUtil;
import exchange.common.util.DateUnit;
import exchange.spot.entity.SpotBestPrice;
import exchange.spot.entity.SpotBestPriceOkcoin;
import exchange.spot.service.SpotBestPriceOkcoinService;
import exchange.spot.service.SpotBestPriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class SpotBestPriceChecker extends Worker {

  private final CurrencyPairConfigService currencyPairConfigService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("spotBestPriceCheckerLog,symbolId," + symbol.getId() + ",start");

    try {
      spotBestPriceCheck(symbol);
    } catch (Exception e) {
      throw new CustomException(
          ErrorCode.DEALING_ERROR_BEST_PRICE_CHECK, "symbolId," + symbol.getId() + ",msg," + e);
    } finally {
      log.info("spotBestPriceCheckerLog,symbolId," + symbol.getId() + ",end");
    }
  }

  private void spotBestPriceCheck(Symbol symbol) throws Exception {
    Long symbolId = symbol.getId();

    // Asks売り板、Bids買い板
    List<BigDecimal> marketBestAsks = new ArrayList<>();
    List<BigDecimal> marketBestBids = new ArrayList<>();

    /** 乖離検知期間from取得 */
    CurrencyPairConfig currencyPairConfig =
        currencyPairConfigService.findOne(symbol.getTradeType(), symbol.getCurrencyPair());
    if (currencyPairConfig == null || currencyPairConfig.isEnabled() == false) {
      // 有効な通貨ペア設定が無い場合
      log.error("spotBestPriceCheckerLog,symbolId," + symbolId + ",currency pair config not found");
      throw new CustomException(
          ErrorCode.DEALING_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND, "symbolId," + symbolId);
    }

    BigDecimal spikePercent = currencyPairConfig.getSpikePercent();
    Integer spikeMinutes = currencyPairConfig.getSpikeMinutes();
    Integer spikeCount = currencyPairConfig.getSpikeCount();

    if (spikePercent == null
        || spikePercent.signum() <= 0
        || spikeMinutes == null
        || spikeMinutes <= 0
        || spikeCount == null
        || spikeCount <= 0) {
      // いずれかがnull or 初期値の場合、検知処理スキップ
      log.info("spotBestPriceCheckerLog,symbolId," + symbolId + ",skip because no spike setting");
      return;
    }

    Long createdAtFrom = new Date().getTime() - spikeMinutes * DateUnit.MINUTE.getMillis();

    /** Okcoin最新ベストプライス取得(乖離検知期間以内) */
    List<SpotBestPriceOkcoin> spotBestPriceOkcoins =
        SpotBestPriceOkcoinService.getBean(symbol)
            .findLatestFromHistory(symbol, new Date(createdAtFrom));
    if (!CollectionUtils.isEmpty(spotBestPriceOkcoins)) {
      marketBestAsks.add(spotBestPriceOkcoins.get(0).getBestAsk());
      marketBestBids.add(spotBestPriceOkcoins.get(0).getBestBid());
    }

    //    log.info(
    //        "DBMITO,spotBestPriceCheckerLog,symbolId,"
    //            + symbolId
    //            + ",spotBestPriceOkcoins,"
    //            + JsonUtil.encode(spotBestPriceOkcoins));

    /** マーケットベストプライス取得(コピー注文スプレッドは加味しない) */
    // 外部板合算後ask,bidいずれかが空の場合、エラー検知対象
    // (外部板取得worker, コピー注文workerでは検知しない)
    if (CollectionUtils.isEmpty(marketBestAsks) || CollectionUtils.isEmpty(marketBestBids)) {
      log.error(
          "spotBestPriceCheckerLog,symbolId,"
              + symbolId
              + ",check from date,"
              + new Date(createdAtFrom)
              + ",spikeMinutes,"
              + spikeMinutes
              + ",market orderbook not found");
      throw new CustomException(
          ErrorCode.DEALING_ERROR_ORDERBOOK_NOT_FOUND, "symbolId," + symbolId);
    }

    marketBestAsks.sort((o1, o2) -> o1.compareTo(o2));
    marketBestBids.sort((o1, o2) -> o2.compareTo(o1));

    //    log.info(
    //        "DBMITO,spotBestPriceCheckerLog,symbolId,"
    //            + symbolId
    //            + ",marketBestAsks,"
    //            + JsonUtil.encode(marketBestAsks)
    //            + ",marketBestBids,"
    //            + JsonUtil.encode(marketBestBids));

    BigDecimal marketBestAsk = marketBestAsks.get(0);
    BigDecimal marketBestBid = marketBestBids.get(0);

    //    log.info(
    //        "DBMITO,spotBestPriceCheckerLog,symbolId,"
    //            + symbolId
    //            + ",marketBestAsk,"
    //            + JsonUtil.encode(marketBestAsk)
    //            + ",marketBestBid,"
    //            + JsonUtil.encode(marketBestBid));

    /** EDAベストプライス取得(乖離検知期間以内・乖離MAX回数分の件数・降順) */
    List<SpotBestPrice> spotBestPrices =
        SpotBestPriceService.getBean(symbol)
            .findAllFromHistory(
                symbol, null, null, null, new Date(createdAtFrom), null, false, spikeCount);

    //    log.info(
    //        "DBMITO,spotBestPriceCheckerLog,EDAbestprice,symbolId,"
    //            + symbolId
    //            + ",createdAtFrom,"
    //            + createdAtFrom
    //            + ",spikeCount,"
    //            + spikeCount
    //            + ",spotBestPrices,"
    //            + JsonUtil.encode(spotBestPrices));

    if (CollectionUtils.isEmpty(spotBestPrices)) {
      // EDA板が空のためエラー検知
      log.error(
          "spotBestPriceCheckerLog,symbolId,"
              + symbolId
              + ",check from date,"
              + new Date(createdAtFrom)
              + ",spikeMinutes,"
              + spikeMinutes
              + ",EDA orderbook not found");
      throw new CustomException(
          ErrorCode.DEALING_ERROR_ORDERBOOK_NOT_FOUND, "symbolId," + symbolId);
    }

    List<BigDecimal> bestAsks =
        spotBestPrices.stream().map(SpotBestPrice::getBestAsk).collect(Collectors.toList());
    List<BigDecimal> bestBids =
        spotBestPrices.stream().map(SpotBestPrice::getBestBid).collect(Collectors.toList());

    //    log.info(
    //        "DBMITO,spotBestPriceCheckerLog,symbolId,"
    //            + symbolId
    //            + ",bestAsks,"
    //            + JsonUtil.encode(bestAsks)
    //            + ",bestBids,"
    //            + JsonUtil.encode(bestBids));

    if (CollectionUtils.isEmpty(bestAsks) || CollectionUtils.isEmpty(bestBids)) {
      // EDA板ask,bidいずれかが空ならエラー検知
      log.error(
          "spotBestPriceCheckerLog,symbolId,"
              + symbolId
              + ",check from date,"
              + new Date(createdAtFrom)
              + ",spikeMinutes,"
              + spikeMinutes
              + ",EDA orderbook not found");
      throw new CustomException(
          ErrorCode.DEALING_ERROR_ORDERBOOK_NOT_FOUND, "symbolId," + symbolId);
    }

    /** 実勢価格乖離チェック */
    // 実勢価格(スプレッド加味しない)を基準に、EDAベストプライスとの乖離%が閾値以内の価格がなければ検知

    for (BigDecimal bestAsk : bestAsks) {
      //      log.info(
      //          "DBMITO,spotBestPriceCheckerLog,symbolId,"
      //              + symbolId
      //              + ",bestAsk,"
      //              + bestAsk
      //              + ",bestAsk.subtract(marketBestAsk).abs(),"
      //              + bestAsk.subtract(marketBestAsk).abs()
      //              + ",marketBestAsk,"
      //              + marketBestAsk
      //              + ",diff_rate,"
      //              + CalculatorUtil.divide(bestAsk.subtract(marketBestAsk).abs(), marketBestAsk)
      //              + ",spikePercent,"
      //              + spikePercent);
      if (CalculatorUtil.divide(bestAsk.subtract(marketBestAsk).abs(), marketBestAsk)
              .compareTo(CalculatorUtil.calculatePercentage(spikePercent))
          <= 0) {
        // x分以内の直近x個でいずれかが乖離閾値%以内であれば乖離無しとして終了 => bidチェックへ
        for (BigDecimal bestBid : bestBids) {
          //          log.info(
          //              "DBMITO,spotBestPriceCheckerLog,symbolId,"
          //                  + symbolId
          //                  + ",bestBid,"
          //                  + bestBid
          //                  + ",bestBid.subtract(marketBestBid).abs(),"
          //                  + bestBid.subtract(marketBestBid).abs()
          //                  + ",marketBestBid,"
          //                  + marketBestBid
          //                  + ",diff_rate,"
          //                  + CalculatorUtil.divide(bestBid.subtract(marketBestBid).abs(),
          // marketBestBid)
          //                  + ",spikePercent,"
          //                  + spikePercent);
          if (CalculatorUtil.divide(bestBid.subtract(marketBestBid).abs(), marketBestBid)
                  .compareTo(CalculatorUtil.calculatePercentage(spikePercent))
              <= 0) {
            // x分以内の直近x個でいずれかが乖離閾値%以内であれば乖離無しとして終了 => 処理終了
            return;
          }
        }
      }
    }

    throw new CustomException(
        ErrorCode.DEALING_ERROR_BEST_PRICE_CHECK, "symbolId," + symbol.getId());
  }
}
