package exchange.worker.worker;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.Currency;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.entity.Candlestick;
import exchange.common.entity.Symbol;
import exchange.common.entity.Withdrawal;
import exchange.common.repos.WithdrawalRepository;
import exchange.common.service.WithdrawalService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.pos.service.PosCandlestickService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class WithdrawalStatusChecker extends Worker {

  private final WithdrawalService withdrawalService;
  private final PosCandlestickService posCandlestickService;
  private final WithdrawalRepository withdrawalRepository;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    
    String targetAtFromStr = (String) params.get("targetAtFrom");
    Date targetAt = new Date();
    if (!StringUtils.isEmpty(targetAtFromStr)) {
      targetAt = FormatUtil.parse(targetAtFromStr, FormatPattern.YYYYMMDD);
      log.info("**********出金の円換算額修正 開始**********");
      log.info("TargetAtFrom is :" + targetAtFromStr);
      updateWithdrawalJpyConversion(targetAt);
      log.info("**********出金の円換算額修正 終了**********");
      return;
    }
    
    log.info("===========================送金結果取得開始/WithdrawalStatusChecker start=======================================");

    //　トランザクション結果取得
    List<Withdrawal> waitTxResult = 
        new ArrayList<Withdrawal>(
            withdrawalService.findByCondition(
                null, 
                null, 
                null, 
                null, 
                null, 
                null, 
                WithdrawalStatus.FUNDING, 
                null, 
                0, 
                Integer.MAX_VALUE));
    log.info(
            "Waiting for get transaction result: {} records ready for processing",
            waitTxResult.size());
    for (Withdrawal withdrawal : waitTxResult) {
      withdrawalService.updateStatusWithTxResult(withdrawal.getId());
    }

    log.info("===========================送金結果取得終了/WithdrawalStatusChecker finish=======================================");
  }

  private void updateWithdrawalJpyConversion(Date targetAt) {
    List<Withdrawal> withdrawalList = withdrawalService
        .findByCondition(
            null,
            Currency.ETH,
            null,
            null,
            targetAt.getTime(),
            null,
            WithdrawalStatus.DONE,
            null,
            0,
            Integer.MAX_VALUE);
     
    for (Withdrawal withdrawal : withdrawalList) {
      Date updTime = withdrawal.getUpdatedAt();
      BigDecimal oldJpyConversion = withdrawal.getJpyConversion();
      BigDecimal jpyConversion = getJpyConversionFromOkcoin(updTime);
      withdrawalRepository.updateById(withdrawal.getId(), jpyConversion);
      log.info("出金ID： " + withdrawal.getId() + " 円換算額は「" + oldJpyConversion + "」から「" + jpyConversion + "」に変更しました。");
    }
    
  }
  
  private BigDecimal getJpyConversionFromOkcoin(Date updTime) {
    BigDecimal result = null;
    Candlestick latest = posCandlestickService.findOneByConditionForJpyConversion(7l, CandlestickType.P1D,
        null, updTime.getTime(), false);
    if (latest == null) {
      result = BigDecimal.ZERO;
    } else {
      result = latest.getClose();
    }
    
    return result;
  }
  
}
