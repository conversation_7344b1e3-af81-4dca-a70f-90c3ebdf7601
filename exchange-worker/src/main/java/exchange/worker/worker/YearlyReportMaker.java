package exchange.worker.worker;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfWriter;
import exchange.common.component.RedisManager;
import exchange.common.component.S3Manager;
import exchange.common.component.S3Manager.Bucket;
import exchange.common.config.S3Config;
import exchange.common.constant.KycStatus;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.entity.UserKyc;
import exchange.common.entity.YearlyReportCreateInfo;
import exchange.common.pdf.PdfPageXofYEventHelper;
import exchange.common.service.ReportService;
import exchange.common.service.UserKycService;
import exchange.common.service.UserService;
import exchange.common.service.YearlyReportCreateInfoService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class YearlyReportMaker extends Worker {

  private final S3Manager s3Manager;
  private final S3Config s3Config;
  private final ReportService reportService;
  private final UserService userService;
  private final YearlyReportCreateInfoService yearlyReportCreateInfoService;
  private final RedisManager redisManager;
  private final UserKycService userKycService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("===========================年間報告作成開始/YearlyReportMaker start=======================================");
    
    Bucket bucket = new Bucket(s3Config.getYearReportBucket().getName());
    log.info(s3Config.getYearReportBucket().getName());
    Calendar calendar = Calendar.getInstance();
    int targetYear = calendar.get(Calendar.YEAR) - 1;
    if(ObjectUtils.isEmpty(params.get("targetAt"))) {
      int currentMonth = calendar.get(Calendar.MONTH) + 1;
      int currentday = calendar.get(Calendar.DAY_OF_MONTH);
      int currenthour = calendar.get(Calendar.HOUR_OF_DAY);
      if(currentMonth == 12 && currentday == 31 && currenthour >= 15) {
        targetYear = targetYear + 1;
      }
    } else {
      targetYear = (int) params.get("targetAt");
    }
    String createdToString = targetYear + "1231" + "150000";
    Date createdTo = FormatUtil.parse(createdToString, FormatPattern.YYYYMMDDHHMMSS);
    String firstDay = targetYear + "0101";
    String lastDay = targetYear + "1231";
    List<User> userList = userService.findByCreatedAt(createdTo.getTime());
    List<YearlyReportCreateInfo> createdUser = yearlyReportCreateInfoService.findByCondition(targetYear, "SUCCESS", null);
    List<Long> uncreatedUserList = 
        userList.stream()
          .map(User::getId).filter(u -> createCheck(userKycService.findByUserId(u).orElse(new ArrayList<>()), createdTo))
          .filter(u -> !createdUser.stream().map(YearlyReportCreateInfo::getUserId).toList().contains(u)).collect(Collectors.toList());
    
    for(Long userId : uncreatedUserList) {
      File tempFile = null;
      try {
        log.info("yearly report creating.userId:{}, targetYear:{}", userId, targetYear);
        tempFile = File.createTempFile("yearlyReport", ".pdf");
        OutputStream outputStream = new FileOutputStream(tempFile);
        
        Rectangle pageSize= new Rectangle(PageSize.A4.getHeight(), PageSize.A4.getWidth());
        pageSize.rotate();
        Document document = new Document();
        document.setPageSize(pageSize);
        PdfWriter pdfWriter = PdfWriter.getInstance(document, outputStream);
        pdfWriter.setPageEvent(new PdfPageXofYEventHelper());
        document.open();
        reportService.createdPDFContent(document, userId, firstDay, lastDay, true);
        document.close();
        FileInputStream fis = new FileInputStream(tempFile);
        byte[] byteArray = new byte[(int) tempFile.length()];
        fis.read(byteArray);
        fis.close();
        String path = targetYear + "/yearlyReport_" + userId + "_" + targetYear + ".pdf";
        s3Manager.putBytes(bucket, path, byteArray);
        List<YearlyReportCreateInfo> failedUser = 
            yearlyReportCreateInfoService.findByCondition(targetYear, "FAILED", userId);
        if(ObjectUtils.isNotEmpty(failedUser)) {
          YearlyReportCreateInfo infoForUpdate = failedUser.get(0);
          infoForUpdate.setStatus("SUCCESS");
          infoForUpdate.setErrorMsg(null);
          infoForUpdate.setUpdatedBy("WORKER");
          yearlyReportCreateInfoService.save(infoForUpdate);
        } else {
          YearlyReportCreateInfo createInfo = new YearlyReportCreateInfo();
          createInfo.setUserId(userId);
          createInfo.setYear(targetYear);
          createInfo.setStatus("SUCCESS");
          createInfo.setCreatedBy("WORKER");
          createInfo.setUpdatedBy("WORKER");
          yearlyReportCreateInfoService.save(createInfo);
        }
      } catch (Exception e) {
        log.warn("yearly report create error.userId:{}, targetYear:{}.error message:{}", 
            userId, targetYear, e.getMessage());
        List<YearlyReportCreateInfo> failedUser = 
            yearlyReportCreateInfoService.findByCondition(targetYear, "FAILED", userId);
        if(ObjectUtils.isNotEmpty(failedUser)) {
          YearlyReportCreateInfo infoForUpdate = failedUser.get(0);
          infoForUpdate.setStatus("FAILED");
          infoForUpdate.setErrorMsg(e.getMessage());
          infoForUpdate.setUpdatedBy("WORKER");
          yearlyReportCreateInfoService.save(infoForUpdate);
        } else {
          YearlyReportCreateInfo createInfo = new YearlyReportCreateInfo();
          createInfo.setUserId(userId);
          createInfo.setYear(targetYear);
          createInfo.setStatus("FAILED");
          createInfo.setErrorMsg(e.getMessage());
          createInfo.setCreatedBy("WORKER");
          createInfo.setUpdatedBy("WORKER");
          yearlyReportCreateInfoService.save(createInfo);
        }
      } finally {
        FileUtils.deleteQuietly(tempFile);
      }
    }
    
    List<YearlyReportCreateInfo> createdUserLatest = yearlyReportCreateInfoService.findByCondition(targetYear, "SUCCESS", null);
    List<Long> uncreatedUserListLatest = 
        userList.stream()
          .map(User::getId).filter(u -> createCheck(userKycService.findByUserId(u).orElse(new ArrayList<>()), createdTo))
          .filter(u -> !createdUserLatest.stream().map(YearlyReportCreateInfo::getUserId).toList().contains(u)).collect(Collectors.toList());
    
    if(ObjectUtils.isEmpty(uncreatedUserListLatest) && ObjectUtils.isEmpty(redisManager.get("yearly_report_created:" + targetYear))) {
      redisManager.set("yearly_report_created:" + targetYear, "DONE");
    }
    
    log.info("===========================年間報告作成終了/YearlyReportMaker finish=======================================");
  }
  
  private boolean createCheck(List<UserKyc> userKycList, Date createdTo) {
    if(userKycList.stream().anyMatch(s -> s.getCreatedAt().compareTo(new Date(createdTo.getTime() - 1000 * 60 * 60 * 24 * 365l)) < 0 
        && (s.getKycStatus() == KycStatus.ACCOUNT_CLOSED))) {
      return false;
    } else {
      if(userKycList.stream().anyMatch(s -> s.getCreatedAt().compareTo(createdTo) < 0
          && (s.getKycStatus() == KycStatus.ACCOUNT_OPENING_DONE || s.getKycStatus() == KycStatus.CORPORATE_PERSONAL_ACCOUNT_OPENING_DONE))) {
        return true;
      } else {
        return false;
      }
    }
  }
}
