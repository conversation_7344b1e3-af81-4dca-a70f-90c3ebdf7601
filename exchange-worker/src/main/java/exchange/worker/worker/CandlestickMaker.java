package exchange.worker.worker;

import java.util.Date;
import java.util.Map;

import org.springframework.stereotype.Component;

import exchange.worker.component.Worker;
import exchange.common.constant.CandlestickType;
import exchange.common.entity.Symbol;
import exchange.common.service.CandlestickService;

@Component
public class CandlestickMaker extends Worker {

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) {
    Date date = new Date();

    for (CandlestickType candlestickType : CandlestickType.values()) {
      CandlestickService.getBean(symbol).make(symbol, candlestickType, date);
    }
  }
}
