package exchange.worker.worker;

import java.util.ArrayList;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.worker.component.Worker;
import exchange.common.constant.Exchange;
import exchange.common.entity.Symbol;
import exchange.common.model.response.OrderbookData;
import exchange.common.service.OkcoinService;
import exchange.common.service.OrderbookService;
import exchange.spot.service.SpotBestPriceOkcoinService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class OkcoinOrderbookGetter extends Worker {

  private static final Exchange EXCHANGE = Exchange.OKCOIN;
  private final OkcoinService okcoinService;
  private final OrderbookService orderbookService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    /** 外部から取得した板をredis保存 + ベストask,bidのみredshiftアーカイブ */
    try {
      OrderbookData orderbookData = okcoinService.getOrderbook(symbol);

      if (orderbookService.isMinusSpread(orderbookData)) {
        // マイナススプレッドの場合、板Invalidとして処理対象外とする
        log.warn("getOkcoinOrderbookLog,symbolId," + symbol.getId() + ",minus spread");
        orderbookService.set(
            EXCHANGE,
            symbol,
            new OrderbookData(symbol.getId(), new ArrayList<>(), new ArrayList<>()));
      } else {
        orderbookService.set(EXCHANGE, symbol, orderbookData);
        // ベストask,bidアーカイブ(ask bidいずれか空の場合はスキップ)
        if (!CollectionUtils.isEmpty(orderbookData.getAsks())
            && !CollectionUtils.isEmpty(orderbookData.getBids())) {
          SpotBestPriceOkcoinService.getBean(symbol)
              .archive(symbol, orderbookData.getBestAsk(), orderbookData.getBestBid());
        }
      }
    } catch (Exception e) {
      // エラー・外部板取得失敗時は空データでクリアする(アラートはコピー注文処理で制御する)
      // エラー・外部取得板失敗時・外部取得板が空の場合、ベストask,bidアーカイブスキップ
      // ベストask bidアーカイブdbエラーの場合もorderbookをクリアして整合性を取り、そこで検知が可能
      log.warn(
          "getOkcoinOrderbookLog,symbolId,"
              + symbol.getId()
              + ",OkcoinOrderbook not found,msg,"
              + e);
      orderbookService.set(
          EXCHANGE,
          symbol,
          new OrderbookData(symbol.getId(), new ArrayList<>(), new ArrayList<>()));
    }
  }
}
