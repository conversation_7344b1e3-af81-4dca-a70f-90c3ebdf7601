package exchange.worker.worker;

import exchange.common.constant.DepositStatus;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.common.entity.Deposit;
import exchange.common.entity.Symbol;
import exchange.common.service.FrequentCryptoDepositUserService;
import exchange.common.service.DepositService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.worker.component.Worker;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class FrequentCryptoDepositChecker extends Worker {

  private final DepositService depositService;
  private final FrequentCryptoDepositUserService cryptHighFrequencyDepositService;

  private final Integer DETECTION_THRESHOLD = 3;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    String paramTargetAt = (String) params.get("targetAt");
    if (paramTargetAt == null) {
      // 現在時刻を基本日時とする
      final var date = System.currentTimeMillis();
      // 日時からfromToを作成する
      // date = 2022/02/02 12:12:12 → from = 2022/02/02 00:00:00, to = 2022/02/03 00:00:00
      final var fromToPair = DateUnit.createFromTo(date);
      final var fromDate = DateUnit.toYesterday(fromToPair.getLeft());
      final var toDate = DateUnit.toYesterday(fromToPair.getRight());
      execute(fromDate, toDate);
    } else {
      Date date = FormatUtil.parseJst(paramTargetAt, FormatPattern.YYYYMMDD);
      final var fromToPair = DateUnit.createFromTo(date.getTime());
      execute(fromToPair.getLeft(), fromToPair.getRight());
    }
  }

  void execute(Date fromDate, Date toDate) {

    // 対象日付のデータを全て削除する
    delete(fromDate);

    // DBからデータを取得
    final var readDto = read(fromDate, toDate);

    // 出力DTOを生成する
    final var writeDtos = process(readDto);

    // DBに書き込み
    write(writeDtos);
  }

  ReadData read(final Date from, final Date to) {
    final var deposits = depositService.findByCondition(null, null, null, null,
        from.getTime(), to.getTime(),
        DepositStatus.DONE, null,
        0, Integer.MAX_VALUE).stream().toList();
    return new ReadData(deposits, from);
  }

  List<FrequentCryptoDepositUser> process(final ReadData readDto) {
    // 検知対象のUserId
    final var detectionUserIds = new ArrayList<Long>();

    // UserIdでグルーピングする
    final var userIdGroupingMap = readDto.deposits.stream()
        .collect(Collectors.groupingBy(entity -> entity.getUserId()));

    for (var depositEntry : userIdGroupingMap.entrySet()) {
      // 日付昇順前提のループ処理なので、処理にしておく
      final var deposits = depositEntry.getValue().
          stream().sorted(Comparator.comparing(Deposit::getUpdatedAt))
          .toList();
      final var depositsSize = deposits.size();
      for (var i = 0; i < depositsSize - 1; i++) {
        final var base = deposits.get(i);
        final var baseTime = base.getUpdatedAt().getTime() + DateUnit.HOUR.getMillis();
        final var counts = new ArrayList<Deposit>();
        counts.add(base);
        for (var j = i + 1; j < depositsSize; j++) {
          final var target = deposits.get(j);
          // 対象の時間 <= x <= 対象の時間から1時間後
          if (base.getUpdatedAt().getTime() <= target.getUpdatedAt().getTime() &&
              target.getUpdatedAt().getTime() <= baseTime) {
            counts.add(target);
          }
        }

        // 検知閾値を超えたユーザーがいた場合は検知リストに入れて、break
        if (counts.size() >= DETECTION_THRESHOLD) {
          detectionUserIds.add(depositEntry.getKey());
          break;
        }
      }
    }

    final var targetAt = readDto.dateFrom;
    return detectionUserIds.stream().map(userId -> {
      final var deposits = userIdGroupingMap.get(userId);
      final var entity = new FrequentCryptoDepositUser();
      entity.setTargetAt(targetAt);
      entity.setUserId(userId);
      entity.setDepositCount(deposits.size());
      entity.setTmsStatus(TmsStatus.OPEN);
      return entity;
    }).toList();
  }

  void write(final List<FrequentCryptoDepositUser> writeDtos) {
    for (var writeDto : writeDtos) {
      cryptHighFrequencyDepositService.save(writeDto);
    }
  }

  void delete(final Date targetAt) {
    final var deleteTargetEntities = cryptHighFrequencyDepositService.findByCondition(null, null,
        targetAt, targetAt);
    for (var entity : deleteTargetEntities) {
      cryptHighFrequencyDepositService.delete(entity);
    }
  }

  public record ReadData(
      List<Deposit> deposits,
      Date dateFrom
  ) {

  }
}
