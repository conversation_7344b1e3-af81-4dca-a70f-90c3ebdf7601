package exchange.worker.worker;

import exchange.common.constant.TradeType;
import exchange.common.entity.Symbol;
import exchange.pos.service.PosOrderService;
import exchange.spot.service.SpotOrderService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class SpotOrderArchiver extends Worker {

  private final PosOrderService posOrderService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) {
    if(symbol.getTradeType() == TradeType.SPOT){
      log.info("SpotOrderServiceArchiver start execute  symbolID:" + symbol.getId() + " tradeType:" + symbol.getTradeType());
      SpotOrderService.getBean(symbol).archive(symbol);
    }else if(symbol.getTradeType() == TradeType.POS){
      log.info("posOrderServiceArchiver start execute  symbolID:" + symbol.getId() + " tradeType:" + symbol.getTradeType());
      posOrderService.archive(symbol);
    }else {
      log.info("SpotTradeArchiver not execute. symbolID:" + symbol.getId() + " tradeType:" + symbol.getTradeType());
    }
  }
}
