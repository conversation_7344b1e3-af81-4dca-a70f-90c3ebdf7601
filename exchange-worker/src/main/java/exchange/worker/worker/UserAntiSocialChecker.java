package exchange.worker.worker;

import exchange.common.constant.*;
import exchange.common.dal.master.bo.user.AntiSocialCheckUserBO;
import exchange.common.dal.master.mapper.user.UserInfoMapper;
import exchange.common.dal.master.mapper.user.UserMapper;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.entity.UserKyc;
import exchange.common.repos.UserKycRepository;
import exchange.common.repos.UserRepository;
import exchange.common.service.UserEkycStatusChangeHistoryService;
import exchange.common.service.dowjones.DowJonesAntisocialCheckService;
import exchange.common.util.AopHolder;
import exchange.common.util.CollectionUtil;
import exchange.common.util.IdUtil;
import exchange.common.util.JsonUtil;
import exchange.worker.component.Worker;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserAntiSocialChecker extends Worker {
    private final UserMapper userMapper;
	private final UserInfoMapper userInfoMapper;
	private final UserKycRepository userKycRepository;
	private final UserRepository userRepository;
	private final UserEkycStatusChangeHistoryService userEkycStatusChangeHistoryService;
	private final DowJonesAntisocialCheckService dowJonesAntisocialCheckService;

	private AopHolder<UserAntiSocialChecker> aopHolder = AopHolder.newAopHolder();

	/**
	 * Refer to：{@link exchange.common.service.AntiSocialCheckService#antiSocialCheck()}
	 * @param symbol
	 * @param params
	 * @throws Exception
	 */
	@Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        String tid = IdUtil.getUuid();
        long startTime = System.currentTimeMillis();
        log.info("[tid={}] UserAntiSocialChecker start", tid);

        try {
			List<AntiSocialCheckUserBO> antiSocialCheckUserBOList =  userMapper.findAntiSocialCheckUserList();
			if (CollectionUtil.isEmpty(antiSocialCheckUserBOList)) {
				log.info("[tid={}] UserAntiSocialChecker wait process user count={}", tid, 0);
				return;
			}
			log.info("[tid={}] UserAntiSocialChecker wait process user count={}", tid, antiSocialCheckUserBOList.size());

			for (AntiSocialCheckUserBO userBO : antiSocialCheckUserBOList) {
				String tmpTid = String.format("%s-USER%s", tid, userBO.getId());
				AntiSocialCheckContext context = new AntiSocialCheckContext()
						.setUserBO(userBO)
						.setTid(tmpTid);
				if (null != userBO.getUserInfoId()) {
					solvePersonal(context);
				} else {
					solveCorporate(context);
				}
			}
		} catch (Exception e) {
			log.error("[tid={}] UserAntiSocialChecker execute error。errorMsg={} ", tid, e.getMessage(), e);
        } finally {
			long endTime = System.currentTimeMillis();
			long seconds = (endTime - startTime) / 1000;
			log.info("[tid={}] UserAntiSocialChecker end. 実行時間: {} minute {} seconds", tid, seconds / 60, seconds % 60);
        }
    }

	public void solvePersonal(AntiSocialCheckContext context) {
		String tid = context.getTid();
		AntiSocialCheckUserBO userBO = context.getUserBO();

		try {
			log.info("[tid={}] UserAntiSocialChecker solvePersonal start: userId={},userBO={}", tid, userBO.getId(), JsonUtil.encode(userBO));
			Boolean hasRisk = dowJonesAntisocialCheckService.hasRisk(tid, AntiSocialCheckBusinessTypeEnum.USER_INFO_CHANGE, null, DowJonesApiTypeEnum.RCS, userBO.getId());
			log.info("[tid={}] UserAntiSocialChecker solvePersonal userId={},hasRisk={}", tid, userBO.getId(), hasRisk);
			if (null == hasRisk) {
				log.info("[tid={}] UserAntiSocialChecker solvePersonal userId={} Waiting for the next time", tid, userBO.getId());
				return;
			}
			context.setHasRisk(hasRisk);

			aopHolder.currentProxy().saveAntiSocialCheckResult(context);

			log.info("[tid={}] UserAntiSocialChecker solvePersonal finished: userId={},kycId={}",
					tid, userBO.getId(), (null != context.getUserKyc()) ? context.getUserKyc().getId() : null );
		} catch (Exception e) {
			log.error("[tid={}] UserAntiSocialChecker solvePersonal error: userId={},errorMs={}", tid, userBO.getId(), e.getMessage(), e);
		}
	}

	public void solveCorporate(AntiSocialCheckContext context) {
		String tid = context.getTid();
		AntiSocialCheckUserBO userBO = context.getUserBO();

		try{
			log.info("[tid={}] UserAntiSocialChecker solveCorporate. start: userId={},userBO={}", tid, userBO.getId(), JsonUtil.encode(userBO));
			// 法人に対しては、1回のみの取り扱い（口座開設後）
			Boolean isUserAntiSocialChecked = userMapper.checkIsUserAntiSocialChecked(userBO.getId());
			if (isUserAntiSocialChecked) {
				log.info("[tid={}] UserAntiSocialChecker solveCorporate userId={},isUserAntiSocialChecked=true. skip", tid, userBO.getId());
				return;
			}

			Boolean hasRisk = dowJonesAntisocialCheckService.hasRisk(tid, AntiSocialCheckBusinessTypeEnum.USER_INFO_CHANGE, null, DowJonesApiTypeEnum.SAM, userBO.getId());
			log.info("[tid={}] UserAntiSocialChecker solveCorporate. userId={},hasRisk={}", tid, userBO.getId(), hasRisk);
			if (null == hasRisk) {
				log.info("[tid={}] UserAntiSocialChecker solveCorporate userId={} Waiting for the next time", tid, userBO.getId());
				return;
			}
			context.setHasRisk(hasRisk);

			aopHolder.currentProxy().saveAntiSocialCheckResult(context);

			log.info("[tid={}] UserAntiSocialChecker solveCorporate finished: userId={},kycId={}",
					tid, userBO.getId(), (null != context.getUserKyc()) ? context.getUserKyc().getId() : null );
		} catch (Exception e) {
			log.error("[tid={}] UserAntiSocialChecker solveCorporate error: userId={},errorMs={}", tid, userBO.getId(), e.getMessage(), e);
		}
	}

	@Transactional(rollbackFor = Exception.class, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
	public void saveAntiSocialCheckResult(AntiSocialCheckContext context) {
		String tid = context.getTid();
		AntiSocialCheckUserBO userBO = context.getUserBO();
		KycData kycData = context.getHasRisk() ? KycData.EXISTS : KycData.NONE;

		UserKyc userKyc = null;

		if (null != userBO.getUserInfoId()) {
			userInfoMapper.updateAntisocialStatusById(userBO.getUserInfoId(), kycData.getCode());
		}

		userKyc = saveUserKyc(tid, userBO.getId(), userBO.getUserInfoId(), userBO.getUserInfoCorporateId(), kycData);
		context.setUserKyc(userKyc);

		User user = userRepository.findById(userBO.getId()).get();
		KycStatus beforeStatus = user.getKycStatus();
		KycStatus currentStatus = KycData.NONE == kycData ? KycStatus.FIRST_SCREENING : KycStatus.ANTI_SOCIAL_CHECKED;
		user.setKycStatus(currentStatus);
		user.setUpdatedAt(new Date());
		user.setUserKycId(userKyc.getId());
		userRepository.save(user);

		// change customer status from `beforeStatus` to ANTI_SOCIAL_CHECKED
		userEkycStatusChangeHistoryService.createAntiSocialHistory(user.getId(), beforeStatus, currentStatus, "[UserAntiSocialCheckJob]: update status to " + currentStatus.name());
	}

	public UserKyc saveUserKyc(String tid, Long  userId, Long userInfoId, Long userInfoCorporateId, KycData kycFlag) {
		if (KycData.EXISTS == kycFlag) {
			UserKyc userKyc = new UserKyc(userId);
			userKyc.setKycStatus(KycStatus.ANTI_SOCIAL_CHECKED);
			userKyc.setJudgingComment(StringUtils.EMPTY);
			userKyc.setAmlCftComment(StringUtils.EMPTY);
			userKyc.setAntisocialStatus(AntisocialStatus.CONFIRMATION_REQUIRED);
			userKyc.setCreatedAt(new Date());
			userKyc.setUpdatedAt(new Date());
			userKyc.setOperator(CommonConstants.WORKER);
			userKyc.setUserInfoId(userInfoId);
			userKyc.setUserInfoCorporateId(userInfoCorporateId);
			return userKycRepository.save(userKyc);
		} else if (KycData.NONE == kycFlag) {
			UserKyc userKyc;
			{
				userKyc = new UserKyc(userId);
				userKyc.setKycStatus(KycStatus.ANTI_SOCIAL_CHECKED);
				userKyc.setJudgingComment(StringUtils.EMPTY);
				userKyc.setAmlCftComment(StringUtils.EMPTY);
				userKyc.setAntisocialStatus(AntisocialStatus.OK);
				userKyc.setCreatedAt(new Date());
				userKyc.setUpdatedAt(new Date());
				userKyc.setOperator(CommonConstants.WORKER);
				userKyc.setUserInfoId(userInfoId);
				userKyc.setUserInfoCorporateId(userInfoCorporateId);
				userKycRepository.save(userKyc);
			}
			{
				userKyc = new UserKyc(userId);
				userKyc.setKycStatus(KycStatus.FIRST_SCREENING);
				userKyc.setJudgingComment(StringUtils.EMPTY);
				userKyc.setAmlCftComment(StringUtils.EMPTY);
				userKyc.setAntisocialStatus(AntisocialStatus.OK);
				userKyc.setCreatedAt(new Date());
				userKyc.setUpdatedAt(new Date());
				userKyc.setOperator(CommonConstants.WORKER);
				userKyc.setUserInfoId(userInfoId);
				userKyc.setUserInfoCorporateId(userInfoCorporateId);
				userKycRepository.save(userKyc);
			}
			return userKyc;
		} else {
			log.info("[tid={}] UserAntiSocialChecker kycFlag No corresponding results were matched. userId={},userInfoId={}", tid, userId, userInfoId);
			return null;
		}
	}

	@Accessors(chain = true)
	@Data
	public static class AntiSocialCheckContext {
		private String tid;
		private AntiSocialCheckUserBO userBO;
		private UserKyc userKyc;
		private Boolean hasRisk;
	}
}
