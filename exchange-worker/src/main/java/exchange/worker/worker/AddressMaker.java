package exchange.worker.worker;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.common.component.DataSourceManager;
import exchange.common.component.WalletManager;
import exchange.common.constant.Currency;
import exchange.common.constant.UserStatus;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.DepositAccount;
import exchange.common.entity.Symbol;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.response.WalletResponse;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.DepositAccountService;
import exchange.common.service.UserService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class AddressMaker extends Worker {

  private final DepositAccountService depositAccountService;
  private final DataSourceManager dataSourceManager;
  private final CurrencyConfigService currencyConfigService;
  private final UserService userService;
  @Autowired
  private WalletManager walletManager;
  
  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    
    String newCurrency = (String) params.get("newCurrency");
    if (StringUtils.isEmpty(newCurrency)) {
      // 既存通貨・暗号資産アドレス作成
      existCurrencyAddressMaker();
    } else {
      // 新規通貨・暗号資産アドレス作成
      newCurrencyAddressMaker();
    }

  }

  /**
   * 新規通貨・暗号資産アドレス作成
   * @throws CustomException 
   */
  private void newCurrencyAddressMaker() throws CustomException {
    log.info("===========================新規通貨・暗号資産アドレス作成開始/NewCurrencyAddressMaker start=======================================");
    
    //　有効な新規通貨資産取得
    List<CurrencyConfig> currencyConfigs = currencyConfigService.findByConditionWithFirstSetFlg(null, true, true);
    List<Currency> currencyMst = new ArrayList<>();
    for (CurrencyConfig currencyConfig : currencyConfigs) {
      currencyMst.add(currencyConfig.getCurrency());
    }
    //　JPYは暗号資産処理対象外
    currencyMst.remove(Currency.JPY);
    log.info("新規通貨処理待ち: {}件", currencyMst.size());
    List<User> userList = userService.findByStatus(UserStatus.ACTIVE);
    if (CollectionUtils.isEmpty(userList)) {
      log.info(
          "NewCurrencyAddressMaker: There is not any ACTIVE User!");
      return;
    }

    List<Long> userIdList = userList.stream().map(User::getId).collect(Collectors.toList());
    for (Currency currency : currencyMst) {
      List<DepositAccount> accounts = 
          depositAccountService.findByCondition(
              null,
              currency.getName(), 
              null, 
              null, 
              0, 
              Integer.MAX_VALUE);

      List<Long> userIdWithoutAccountList = userIdList;
      if (!CollectionUtils.isEmpty(accounts)) {
        List<Long> userIdFromAccountList = accounts.stream().map(DepositAccount::getUserId).collect(Collectors.toList());
        // 暗号資産アドレス既に作成されたユーザーを除く
        userIdWithoutAccountList = userIdList.stream()
            .filter(i -> !userIdFromAccountList.contains(i))
            .collect(Collectors.toList());
      }

      log.info(
          "create {} addresses for users: {} records ready for processing", currency.getName(),
          userIdWithoutAccountList.size());
      
      if (!CollectionUtils.isEmpty(userIdWithoutAccountList)) {
        Long successCount = 0L;
        for (Long userId : userIdWithoutAccountList) {
          try {
            WalletResponse ws = walletManager.getAddress(
                userId,
                currency.getName());
            if(ws.getCode() == 20000) {
              DepositAccount depositAccount = new DepositAccount();
              depositAccount.setCurrency(currency);
              depositAccount.setAddress(ws.getData().toString());
              depositAccount.setUserId(userId);
              depositAccount.setEnabled(true);
              depositAccountService.save(depositAccount);
              successCount++;
            } else {
              log.warn("暗号資産アドレス作成エラー:" + ws.getMessage() + 
                  "userId:" + userId + "currency:" + currency.getName());
            }
          } catch(Exception e) {
            log.warn("暗号資産アドレス作成エラー:" + e.getMessage() + 
                "userId:" + userId + "currency:" + currency.getName());
          }
        }
        if (successCount == 0L) {
          log.error("newCurrencyAddressMakerLog, address maker of {} is all failed.",currency.getName());
          //throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        } else {
          CurrencyConfig currencyConfig = currencyConfigService.findByCurrency(currency);
          currencyConfig.setFirstSetFlg(false);
          currencyConfigService.save(currencyConfig);
          log.info("NewCurrencyAddressMaker：通貨{}に対して、{}個ユーザーの中で{}個ユーザーのアドレスが作成しました。", currency.getName(), userIdWithoutAccountList.size(), successCount);
        }
      }

    }
    

    log.info("===========================新規通貨・暗号資産アドレス作成終了/NewCurrencyAddressMaker finish=======================================");
    
  }

  /**
   * 既存通貨・暗号資産アドレス作成
   */
  private void existCurrencyAddressMaker() {
    log.info("===========================既存通貨・暗号資産アドレス作成開始/ExistCurrencyAddressMaker start=======================================");
    
    String sqlUserCurrency = "";
    String sqlCurrencyAddress = "";
    sqlUserCurrency = """
        select
        CONCAT(a.id , '_' ,b1.currency) as userCurrency
        from user a CROSS JOIN currency_config b1
        where a.user_status = 'ACTIVE'
        and b1.currency <> 'JPY'
        and b1.enabled = true
        and b1.firstSetFlg = false
        order by a.id, b1.currency
        """;

    sqlCurrencyAddress = """
        select CONCAT(c.user_id ,'_' , c.currency) as userCurrency
        from deposit_account c
        INNER JOIN currency_config b2 on c.currency = b2.currency
        where
        b2.currency <> 'JPY'
        and b2.enabled = true
        and b2.firstSetFlg = false
        order by c.user_id, c.currency
        """;

    EntityManager entityManager =
        dataSourceManager.getMasterEntityManagerFactory().createEntityManager();

    List<String> userCurrencyAllList = new ArrayList<String>();
    List<String> userCurrencyWithAddressList = new ArrayList<String>();
    List<String> userCurrencyWithoutAddressList = new ArrayList<String>();
    try {
      log.info("ExistCurrencyAddressMaker　SQL実施開始");
      Query queryUserCurrency = entityManager.createNativeQuery(sqlUserCurrency);
      Query queryCurrencyAddress = entityManager.createNativeQuery(sqlCurrencyAddress);

      List<String> userCurrencyList = queryUserCurrency.getResultList();
      List<String> userCurrencyAddress = queryCurrencyAddress.getResultList();
      log.info("ExistCurrencyAddressMaker　SQL実施終了");

      userCurrencyAllList.addAll(userCurrencyList);
      userCurrencyWithAddressList.addAll(userCurrencyAddress);
      userCurrencyWithoutAddressList = userCurrencyAllList.stream()
          .filter(i -> !userCurrencyWithAddressList.contains(i)).collect(Collectors.toList());
      log.info("ExistCurrencyAddressMaker：処理待ちのデータが{}件です。", userCurrencyWithoutAddressList.size());

    } finally {
      entityManager.clear();
      entityManager.close();
    }
    
    Long successCount = 0L;
    for(String userCurrencyWithoutAddress : userCurrencyWithoutAddressList) {
      String[] userCurrencyWithoutAddressStrs = userCurrencyWithoutAddress.split("_");
      if (userCurrencyWithoutAddressStrs.length > 0) {
        Long userId = Long.valueOf(userCurrencyWithoutAddress.split("_")[0]);
        Currency currency = Currency.valueOfName(userCurrencyWithoutAddress.split("_")[1]);
        try {
          WalletResponse ws = walletManager.getAddress(userId, currency.getName());
          if (ws.getCode() == 20000) {
            DepositAccount depositAccount = new DepositAccount();
            depositAccount.setCurrency(currency);
            depositAccount.setAddress(ws.getData().toString());
            depositAccount.setUserId(userId);
            depositAccount.setEnabled(true);
            depositAccountService.save(depositAccount);
            successCount++;
          } else {
            log.warn("暗号資産アドレス作成エラー:" + ws.getMessage() + "userId:" + userId + "currency:"
                + currency.getName());
          }
        } catch (Exception e) {
          log.error("暗号資産アドレス作成エラー:" + e.getMessage() + "userId:" + userId + "currency:"
              + currency.getName());
        } 
      }
       
    }
    log.info("ExistCurrencyAddressMaker：{}件の中で、{}件が成功に作成しました。", userCurrencyWithoutAddressList.size(), successCount);

    log.info("===========================既存通貨・暗号資産アドレス作成終了/ExistCurrencyAddressMaker finish=======================================");
    
  }
}