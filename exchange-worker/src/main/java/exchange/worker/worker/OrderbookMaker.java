package exchange.worker.worker;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import exchange.worker.component.Worker;
import exchange.common.entity.Symbol;
import exchange.common.model.response.OrderbookData;
import exchange.common.model.response.OrderbookData.Orderbook;
import exchange.common.service.OrderbookService;
import exchange.spot.entity.SpotOrder;
import exchange.spot.service.SpotBestPriceService;
import exchange.spot.service.SpotOrderService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class OrderbookMaker extends Worker {

  private final OrderbookService orderbookService;

  private void addOrderbook(CopyOnWriteArrayList<Orderbook> orderbooks, SpotOrder spotOrder) {
    if (orderbooks.size() > 0) {
      Orderbook orderbook = orderbooks.get(orderbooks.size() - 1);

      if (orderbook.getPrice().compareTo(spotOrder.getPrice()) == 0) {
        orderbook.setAmount(orderbook.getAmount().add(spotOrder.getRemainingAmount()));
      } else {
        orderbooks.add(new Orderbook(spotOrder.getPrice(), spotOrder.getRemainingAmount()));
      }
    } else {
      orderbooks.add(new Orderbook(spotOrder.getPrice(), spotOrder.getRemainingAmount()));
    }
  }

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    CopyOnWriteArrayList<Orderbook> asks = new CopyOnWriteArrayList<>();
    CopyOnWriteArrayList<Orderbook> bids = new CopyOnWriteArrayList<>();

    // 板作成用の注文リストを取得(成行除く) ＆ 価格昇順ソート＆id昇順ソート
    List<SpotOrder> spotOrders = SpotOrderService.getBean(symbol).findForOrderbook(symbol.getId());

    if (CollectionUtils.isEmpty(spotOrders)) {
      // 未約定指値が0件の場合 => orderBook空データ作成 & ベストask,bid保存無し
      orderbookService.set(symbol, new OrderbookData(symbol.getId(), asks, bids));
      return;
    }

    // 板Data作成(同一価格サマリ)
    spotOrders.forEach(
        spotOrder -> addOrderbook(spotOrder.getOrderSide().isSell() ? asks : bids, spotOrder));

    OrderbookData orderbookData = new OrderbookData(symbol.getId(), asks, bids);

    // sort
    asks.sort((o1, o2) -> o1.getPrice().compareTo(o2.getPrice()));
    bids.sort((o1, o2) -> o2.getPrice().compareTo(o1.getPrice()));

    // 板作成(redis)
    orderbookService.set(symbol, orderbookData);
    // ベストask,bidのみredshiftアーカイブ
    // ・ask,bidいずれか空ならスキップ
    if (!CollectionUtils.isEmpty(asks) && !CollectionUtils.isEmpty(bids)) {
      SpotBestPriceService.getBean(symbol)
          .archive(symbol, orderbookData.getBestAsk(), orderbookData.getBestBid());
    }
  }
}
