package exchange.worker.worker;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.Symbol;
import exchange.common.entity.Withdrawal;
import exchange.common.service.DepositService;
import exchange.common.service.WithdrawalService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class SygnaMailProtocolStatusChecker extends Worker {

  private final WithdrawalService withdrawalService;
  private final DepositService depositService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) {
    log.info("===========================Sygna　EmailProtocol通知ステータス同期開始/SygnaMailProtocolStatusChecker start=======================================");
    
    //　出金
    // Sygna連携対象取得
    List<Withdrawal> waitWithdrawal = 
        new ArrayList<Withdrawal>(
            withdrawalService.findByCondition(
                null, 
                null, 
                null, 
                null, 
                null, 
                null, 
                WithdrawalStatus.VASP_MAIL_SENDED, 
                null, 
                0, 
                Integer.MAX_VALUE));
    if (ObjectUtils.isEmpty(waitWithdrawal)) {
      log.info("出金・Sygna EmailProtocol通知ステータス取得対象がありません.");
    } else {
      try {
        withdrawalService.updateStatusEmail(waitWithdrawal);
      } catch(Exception e) {
        log.error("暗号資産出金をメール通知でエラーが発生しました。" + e.getMessage());
      }
    }
    
    //　入金
    // Sygna連携対象取得
    List<Deposit> waitDeposit = 
        new ArrayList<Deposit>(
            depositService.findByCondition(
                null, 
                null, 
                null, 
                null, 
                null, 
                null, 
                DepositStatus.MAIL_SENDED, 
                null, 
                0, 
                Integer.MAX_VALUE));
    if (ObjectUtils.isEmpty(waitDeposit)) {
      log.info("入金・Sygna EmailProtocol通知ステータス取得対象がありません.");
    } else {
      try {
        depositService.updateStatusEmail(waitDeposit);
      } catch(Exception e) {
        log.error("暗号資産入金をメール通知でエラーが発生しました。" + e.getMessage());
      }
    }
    
    log.info("===========================Sygna　EmailProtocol通知ステータス同期終了/SygnaMailProtocolStatusChecker finish=======================================");
  }
}
