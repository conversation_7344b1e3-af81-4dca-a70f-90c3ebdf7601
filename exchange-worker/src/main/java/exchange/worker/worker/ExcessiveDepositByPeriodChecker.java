package exchange.worker.worker;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.entity.Symbol;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.ExcessiveDepositByPeriodService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ExcessiveDepositByPeriodChecker extends Worker {

  @Autowired
  ExcessiveDepositByPeriodService service;

  @Autowired
  CurrencyConfigService currencyConfigService;
  
  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    // memo: symbolは使わない
    String targetDateParam= (String) params.get("targetAt"); // ex.20211016
    LocalDate target;
    if (targetDateParam == null) {
      target = LocalDate.now(ZoneId.of("Asia/Tokyo")).minusDays(1);
    } else {
      target = LocalDate.parse(targetDateParam, DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    var currencies = currencyConfigService.findAll();
    List<Currency> currencyList = new  ArrayList<Currency>();
    for (var c : currencies) {
      if (c.getCurrency() == Currency.JPY) {
        continue;
      }
      var currency = c.getCurrency();
      currencyList.add(currency);
    }
    service.execute(currencyList, target);
  }
}
