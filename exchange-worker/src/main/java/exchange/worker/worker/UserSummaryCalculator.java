package exchange.worker.worker;

import java.util.Date;
import java.util.Map;
import org.springframework.stereotype.Component;
import exchange.worker.component.Worker;
import exchange.common.entity.Symbol;
import exchange.common.service.UserSummaryService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class UserSummaryCalculator extends Worker {

  private final UserSummaryService userSummaryService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    // 起動日時の取得
    Date today = new Date();
    // 処理対象日の取得
    Date yesterday = new Date(today.getTime() - 60 * 60 * 24 * 1000);
    String targetDay = FormatUtil.formatJst(yesterday, FormatPattern.YYYYMMDD);
    // 計算処理の呼び出し
    userSummaryService.calculate(targetDay);
  }
}
