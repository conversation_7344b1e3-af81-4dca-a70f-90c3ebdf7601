package exchange.worker.worker;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import exchange.common.component.WalletManager;
import exchange.common.constant.Currency;
import exchange.common.constant.StakingControlOperationStatus;
import exchange.common.constant.StakingControlOperations;
import exchange.common.constant.StakingStatus;
import exchange.common.constant.WalletConstants;
import exchange.common.entity.StakingApplyDetail;
import exchange.common.entity.StakingControl;
import exchange.common.entity.StakingOperationRecord;
import exchange.common.entity.StakingPool;
import exchange.common.entity.Symbol;
import exchange.common.model.response.StakeOperation;
import exchange.common.model.response.WalletResponse;
import exchange.common.service.StakingApplyDetailService;
import exchange.common.service.StakingControlService;
import exchange.common.service.StakingOperationRecordService;
import exchange.common.service.StakingPoolService;
import exchange.worker.component.Worker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class StakingStatusChecker extends Worker {

  private final StakingOperationRecordService stakingOperationRecordService;
  private final StakingControlService stakingControlService;
  private final StakingApplyDetailService stakingApplyDetailService;
  private final StakingPoolService stakingPoolService;
  @Autowired
  private WalletManager walletManager;
  
  private static final int max = 50;
  
  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info("===========================stake・unstake状態取得開始/StakingStatusChecker start=======================================");

    List<StakingControl> stakingControlList = 
        stakingControlService.findByCondition(
            null, 
            null, 
            null, 
            new StakingControlOperationStatus[] {
              StakingControlOperationStatus.PENDING,
              StakingControlOperationStatus.PROCESSING
            },
            null);
    
    if(ObjectUtils.isEmpty(stakingControlList)) {
      log.info("stake・unstake状態取得対象がありません。");
    } else {
      List<List<StakingControl>> resultList = 
          walletManager.averageAssign(stakingControlList, max);
      for(List<StakingControl> subList : resultList) {
        String operationIds = 
            subList.stream().map(StakingControl::getOperationId).
            filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.joining(","));
        WalletResponse operationRecordRes = walletManager.getOperationRecord(operationIds);
        if (operationRecordRes.getCode() == WalletConstants.SUCCESS) {
          List<StakeOperation> stakeOperationList = (List<StakeOperation>) operationRecordRes.getData();
          for(StakeOperation stakeOperation : stakeOperationList) {
            boolean nextStakeTargetFlg = false;
            StakingControl stakingControlForUpdate = 
                stakingControlService.findOneByOperationId(stakeOperation.getId());
            // 操作がSTAKE/UNSTAKEの場合、StakingApplyDetailを更新
            List<StakingApplyDetail> stakingDetailList = 
                stakingApplyDetailService.findByStakingControlId(stakingControlForUpdate.getId());
            if(StakingControlOperationStatus.SUCCESS.equals(
                StakingControlOperationStatus.valueOfName(stakeOperation.getStatus()))) {
              // stake処理成功、顧客状態を「ステーキング中」に変更
              if(StakingControlOperations.STAKE.equals(stakingControlForUpdate.getOperation())) {
                for(StakingApplyDetail stakeSuccess : stakingDetailList) {
                  if(StakingStatus.WAIT_STAKING.equals(stakeSuccess.getStakingStatus())) {
                    if(stakeSuccess.getStakingDatePlan() == null ||
                        stakeSuccess.getStakingDatePlan().getTime() + 3600*1000*24 >= System.currentTimeMillis()) {
                      stakeSuccess.setStakingStatus(StakingStatus.APPLIED);
                      stakeSuccess.setUpdatedBy("WORKER");
                      stakingApplyDetailService.save(stakeSuccess);
                    } else {
                      stakingApplyDetailUpdate(stakeSuccess);
                      nextStakeTargetFlg = true;
                    }
                  }
                }
              // unstake処理成功、顧客状態を「返金待ち」に変更
              } else if(StakingControlOperations.UNSTAKE.equals(stakingControlForUpdate.getOperation())) {
                if(Currency.ADA.equals(stakingControlForUpdate.getCurrency()) ||
                    (Currency.ETH.equals(stakingControlForUpdate.getCurrency()) && stakeOperation.isActivated())) {
                  for(StakingApplyDetail unstakeSuccess : stakingDetailList) {
                    switch (unstakeSuccess.getStakingStatus()) {
                      case CANCEL_APPLY -> {
                        unstakeSuccess.setStakingStatus(StakingStatus.CANCEL_WAIT_FUND);
                        unstakeSuccess.setUpdatedBy("WORKER");}
                      case EXPIRED -> {
                        unstakeSuccess.setStakingStatus(StakingStatus.EXPIRED_WAIT_FUND);
                        unstakeSuccess.setUpdatedBy("WORKER");}
                      default -> {break;}
                    }
                    stakingApplyDetailService.save(unstakeSuccess);
                  }
                }
                if(Currency.ETH.equals(stakingControlForUpdate.getCurrency()) && 
                    stakeOperation.isActivated()) {
                  // staking poolの数量に反映
                  StakingPool stakingPoolForUpdate =
                      stakingPoolService.findByCondition(stakingControlForUpdate.getCurrency());
                  if(stakingPoolForUpdate != null) {
                    if(stakingControlForUpdate.getUnstakeAmountBak() != null) {
                      stakingPoolForUpdate.setAmount(
                          stakingPoolForUpdate.getAmount().add(stakingControlForUpdate.getUnstakeAmountPlan())
                            .subtract(stakingControlForUpdate.getUnstakeAmountBak()));
                      stakingPoolService.save(stakingPoolForUpdate);
                    }
                  }
                }
              }
            }
            stakingControlForUpdate.setNextStakeTargetFlg(nextStakeTargetFlg);
            // OperationStatus更新
            if(Currency.ETH.equals(stakingControlForUpdate.getCurrency())
                && StakingControlOperationStatus.SUCCESS.equals(
                    StakingControlOperationStatus.valueOfName(stakeOperation.getStatus()))
                && !stakeOperation.isActivated()) {
              stakingControlForUpdate.setOperationStatus(
                  StakingControlOperationStatus.PROCESSING);
            } else {
              stakingControlForUpdate.setOperationStatus(
                  StakingControlOperationStatus.valueOfName(stakeOperation.getStatus()));
            }
            stakingControlService.save(stakingControlForUpdate);
            // 操作記録更新
            stakingOperationRecordSave(stakeOperation);
            // 自動報酬集計
            if(Currency.ETH.equals(Currency.valueOfName(stakeOperation.getSymbol()))
                && StakingControlOperationStatus.SUCCESS.equals(
                    StakingControlOperationStatus.valueOfName(stakeOperation.getStatus()))
                && StakingControlOperations.REWARD.equals(stakingControlForUpdate.getOperation())) {
              StakingOperationRecord stakingOperationRecordForUpdate
                = stakingOperationRecordService.findOneByOperationId(stakeOperation.getId());
              // 報酬取得成功の時間
              long dateTo = stakingOperationRecordForUpdate.getUpdatedAt().getTime();
              long dateFrom = 0l;
              StakingOperationRecord previousWithdraw = 
                  stakingOperationRecordService.findPreviousWithdraw(Currency.ETH, dateTo);
              if(previousWithdraw != null) {
                // 前回報酬取得成功の時間
                dateFrom = previousWithdraw.getUpdatedAt().getTime();
              }
              List<StakingOperationRecord> autoRewardRecords = 
                  stakingOperationRecordService.findAutoRewardByUpdatedAt(Currency.ETH, dateFrom, dateTo);
              BigDecimal rewardAmount = BigDecimal.ZERO;
              rewardAmount = autoRewardRecords.stream().filter(r -> r.getAmount() != null)
                  .map(StakingOperationRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
              stakingOperationRecordForUpdate.setAutoRewardAmount(rewardAmount);
              stakingOperationRecordService.save(stakingOperationRecordForUpdate);
            }
          }
        } else {
          log.info(operationRecordRes.getMessage());
        }
      }
    }
    
    log.info("===========================stake・unstake状態取得終了/StakingStatusChecker finish=======================================");
  }
  
  private void stakingOperationRecordSave(StakeOperation stakeOperation) {
    // 「staking_operation_record」テーブル更新
    StakingOperationRecord stakingOperationRecord = 
        stakingOperationRecordService.findOneByOperationId(stakeOperation.getId());
    if(stakingOperationRecord != null) {
      BigDecimal amount = BigDecimal.ZERO;
      if (ObjectUtils.isNotEmpty(stakeOperation.getAmount())) {
        amount = new BigDecimal(stakeOperation.getAmount());
      }
      stakingOperationRecord.setAmount(amount);
      BigDecimal gasfee = BigDecimal.ZERO;
      if (ObjectUtils.isNotEmpty(stakeOperation.getGasFee())) {
        gasfee = new BigDecimal(stakeOperation.getGasFee());
      }
      stakingOperationRecord.setGasFee(gasfee);
      stakingOperationRecord.setOperationStatus(stakeOperation.getStatus());
      stakingOperationRecord.setActivated(stakeOperation.isActivated());
      stakingOperationRecord.setWalletUpdateTime(new Date(stakeOperation.getUpdateTime()));
      stakingOperationRecordService.save(stakingOperationRecord);
    }
  }
  
  private void stakingApplyDetailUpdate(StakingApplyDetail stakingApplyDetail) {
    stakingApplyDetail.setStakingDatePlan(
        stakingApplyDateUpdate(stakingApplyDetail.getStakingDatePlan(), stakingApplyDetail.getCurrency()));
    stakingApplyDetail.setRewardStartDate(
        stakingApplyDateUpdate(stakingApplyDetail.getRewardStartDate(), stakingApplyDetail.getCurrency()));
    stakingApplyDetail.setExpirationDate(
        stakingApplyDateUpdate(stakingApplyDetail.getExpirationDate(), stakingApplyDetail.getCurrency()));
    stakingApplyDetail.setCancelDisableDateFrom(
        stakingApplyDateUpdate(stakingApplyDetail.getCancelDisableDateFrom(), stakingApplyDetail.getCurrency()));
    stakingApplyDetail.setCancelDisableDateTo(
        stakingApplyDateUpdate(stakingApplyDetail.getCancelDisableDateTo(), stakingApplyDetail.getCurrency()));
    stakingApplyDetail.setCancelDate(
        stakingApplyDateUpdate(stakingApplyDetail.getCancelDate(), stakingApplyDetail.getCurrency()));
    stakingApplyDetail.setUpdatedBy("WORKER");
    stakingApplyDetailService.save(stakingApplyDetail);
  }
  
  // ADA:5日加算 ETH:1日加算
  private Date stakingApplyDateUpdate(Date date, Currency currency) {
    if(Currency.ETH.equals(currency)) {
      return new Date(date.getTime() + 3600*1000*24);
    }
    return new Date(date.getTime() + 3600*1000*24*5);
  }
}
