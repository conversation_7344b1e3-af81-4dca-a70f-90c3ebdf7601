package exchange.worker.worker;

import exchange.common.constant.DepositStatus;
import exchange.common.constant.FiatWithdrawalStatus;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.QuickCryptoToJpyUser;
import exchange.common.entity.Symbol;
import exchange.common.service.DepositService;
import exchange.common.service.FiatWithdrawalService;
import exchange.common.service.QuickCryptoToJpyUserService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.worker.component.Worker;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class QuickCryptoToJpyChecker extends Worker {

  private final DepositService depositService;
  private final FiatWithdrawalService fiatWithdrawalService;
  private final QuickCryptoToJpyUserService quickCryptoToJpyUserService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    String paramTargetAt = (String) params.get("targetAt");
    if (paramTargetAt == null) {
      // 現在時刻を基本日時とする
      final var date = System.currentTimeMillis();
      // 日時からfromToを作成する
      // date = 2022/02/02 12:12:12 → from = 2022/02/02 00:00:00, to = 2022/02/03 00:00:00
      final var fromToPair = DateUnit.createFromTo(date);
      final var fromDate = DateUnit.toYesterday(fromToPair.getLeft());
      final var toDate = DateUnit.toYesterday(fromToPair.getRight());
      execute(fromDate, toDate);
    } else {
      Date date = FormatUtil.parseJst(paramTargetAt, FormatPattern.YYYYMMDD);
      final var fromToPair = DateUnit.createFromTo(date.getTime());
      execute(fromToPair.getLeft(), fromToPair.getRight());
    }
  }

  void execute(Date fromDate, Date toDate) {
    // 対象日付のデータを全て削除する
    delete(fromDate);

    // DBからデータを取得
    final var readDto = read(fromDate, toDate);

    // 出力DTOを生成する
    final var writeDtos = process(readDto);

    // DBに書き込み
    write(writeDtos);
  }

  ReadData read(final Date from, final Date to) {
    final var deposits = depositService.findByCondition(null, null, null, null,
        from.getTime(), to.getTime(),
        DepositStatus.DONE, null,
        0, Integer.MAX_VALUE).stream().toList();

    final var targetFiatWithdrawalStatuses = List.of(FiatWithdrawalStatus.APPROVING,
        FiatWithdrawalStatus.WAITTING, FiatWithdrawalStatus.DONE);
    final var fiatWithdrawals = fiatWithdrawalService.findByCondition(null, from.getTime(),
            to.getTime(), null, null, targetFiatWithdrawalStatuses, 0, Integer.MAX_VALUE).stream()
        .toList();

    return new ReadData(deposits, fiatWithdrawals, from);
  }

  List<QuickCryptoToJpyUser> process(final ReadData readDto) {
    // 検知対象のUserId
    final var detectionUserIds = new ArrayList<Long>();

    // UserIdでグルーピングする
    final var userIdGroupingDepositsMap = readDto.deposits.stream()
        .collect(Collectors.groupingBy(entity -> entity.getUserId()));

    final var userIdGroupingWithdrawalsMap = readDto.fiatWithdrawals.stream()
        .collect(Collectors.groupingBy(entity -> entity.getUserId()));

    for (var depositEntry : userIdGroupingDepositsMap.entrySet()) {
      final var userId = depositEntry.getKey();
      final var deposits = depositEntry.getValue();
      final var fiatWithdrawals = userIdGroupingWithdrawalsMap.get(userId);

      if (fiatWithdrawals == null) {
        // 入金レコードが存在しない場合は次のユーザーへ進む
        continue;
      }

      for (var deposit : deposits) {
        // 入金データの作成時間に1時間を足し、それ以下の時間だった場合検知対象とする
        final var baseTime = deposit.getUpdatedAt().getTime() + DateUnit.HOUR.getMillis();
        final var isQuickCryptoToJpy = fiatWithdrawals.stream()
            .filter(fiatWithdrawal -> deposit.getUpdatedAt().getTime() <= fiatWithdrawal.getCreatedAt().getTime())
            .anyMatch(fiatWithdrawal -> fiatWithdrawal.getCreatedAt().getTime() <= baseTime);
        if (isQuickCryptoToJpy) {
          // 入庫後短期出金検知対象のユーザーの場合は検知対象リストに追加しbreak、次のユーザーへ進む
          detectionUserIds.add(userId);
          break;
        }
      }
    }

    // 検知対象リストからテーブルのEntityを作成する
    return detectionUserIds.stream().map(detectionUserId -> {
      final var entity = new QuickCryptoToJpyUser();

      entity.setTargetAt(readDto.dateFrom);
      entity.setUserId(detectionUserId);
      entity.setTmsStatus(TmsStatus.OPEN);

      return entity;
    }).toList();
  }

  void write(final List<QuickCryptoToJpyUser> writeDtos) {
    for (var writeDto : writeDtos) {
      quickCryptoToJpyUserService.save(writeDto);
    }
  }

  void delete(final Date targetAt) {
    final var deleteTargetEntities = quickCryptoToJpyUserService.findByCondition(null, null,
        targetAt, targetAt, 0, Integer.MAX_VALUE);
    for (var entity : deleteTargetEntities) {
      quickCryptoToJpyUserService.delete(entity);
    }
  }

  record ReadData(
      List<Deposit> deposits,
      List<FiatWithdrawal> fiatWithdrawals,
      Date dateFrom
  ) {

  }
}
