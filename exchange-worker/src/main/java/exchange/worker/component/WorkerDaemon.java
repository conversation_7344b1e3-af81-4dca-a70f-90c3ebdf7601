package exchange.worker.component;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
@Scope("singleton")
public class WorkerDaemon implements SmartLifecycle, ApplicationContextAware {

  private final ThreadPoolTaskExecutor taskExecutor;
  private final SqsManager sqsManager;

  private static ApplicationContext APPLICATION_CONTEXT;

  private boolean running;

  private static boolean shutdown;

  @SuppressWarnings("rawtypes")
  private List<SqsSubscriber> subscribers = new ArrayList<>();

  @Override
  @SuppressFBWarnings
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    APPLICATION_CONTEXT = applicationContext;
  }

  public static WorkerDaemon getBean() {
    return APPLICATION_CONTEXT.getBean(WorkerDaemon.class);
  }

  @Override
  @SuppressFBWarnings
  public void start() {
    if (running) {
      log.error("daemon is already started");
      return;
    }

    log.info("start");
    running = true;
    shutdown = false;
  }

  @Override
  @SuppressFBWarnings
  public void stop() {
    if (!running) {
      log.error("daemon is already stooped");
      return;
    }

    log.info("start shutdown");
    running = false;
    shutdown = true;

    sqsManager.shutdown();

    taskExecutor.setAwaitTerminationMillis(1000L * 20);
    taskExecutor.shutdown();

    log.info("finish shutdown");
  }

  public static boolean isShutdown() {
    return shutdown;
  }

  @Override
  public boolean isRunning() {
    return running;
  }

  @SuppressWarnings("rawtypes")
  public void addSqsSubscriber(Class<? extends SqsSubscriber> clazz) {
    SqsSubscriber bean = APPLICATION_CONTEXT.getBean(clazz);
    subscribers.add(bean);
    taskExecutor.execute(bean);
  }
}
