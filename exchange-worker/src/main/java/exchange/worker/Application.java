package exchange.worker;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.ApplicationContext;
import exchange.common.component.CustomLogger;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.component.WorkerDaemon;
import exchange.worker.sqssubscriber.*;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(
    scanBasePackages = {"exchange"},
    exclude = {
      DataSourceAutoConfiguration.class,
      DataSourceTransactionManagerAutoConfiguration.class,
      HibernateJpaAutoConfiguration.class
    })
public class Application {

  @SuppressWarnings("unchecked")
  private enum SubscriberGroup {
    ALL(
        AssetSummaryCalculatorSqsSubscriber.class,
        AssetSummaryRecalculatorSqsSubscriber.class,
        CandlestickMakerSqsSubscriber.class,
        CandlestickRemakerSqsSubscriber.class,
        CircuitBreakerSqsSubscriber.class,
        ExchangeSummarySqsSubscriber.class,
        FinancialAssetsDeviationCheckerSqsSubscriber.class,
        HighValueTraderCheckerSqsSubscriber.class,
        InvestmentPurposeDeviationCheckerSqsSubscriber.class,
        OkcoinOrderbookGetterSqsSubscriber.class,
        OrderbookMakerSqsSubscriber.class,
        SameIpCheckerSqsSubscriber.class,
        SpoofingCheckerSqsSubscriber.class,
        SpotBestPriceCheckerSqsSubscriber.class,
        SpotCopyOrdererSqsSubscriber.class,
        SpotCoverOrdererSqsSubscriber.class,
        SpotCoverOrderUpdaterSqsSubscriber.class,
        SpotOrderArchiverSqsSubscriber.class,
        SpotOrderExecutorSqsSubscriber.class,
        SpotTradeArchiverSqsSubscriber.class,
        SpotCopyTradeGetterSqsSubscriber.class,
        TickerMakerSqsSubscriber.class,
        TradesMakerSqsSubscriber.class,
        UserAttributeCheckerSqsSubscriber.class,
        UserSummaryCalculatorSqsSubscriber.class,
        WashTradingCheckerSqsSubscriber.class,
        GmoDepositCheckerSqsSubscriber.class,
        GmoTokenUpdaterSqsSubscriber.class,
        GmoDepositReconcileSqsSubscriber.class,
        IssueVACheckerSqsSubscriber.class,
        BulkTransferExecutorSqsSubscriber.class,
        BulkTransferResultCheckerSqsSubscriber.class,
        UserEkycBpoResultUpdaterSqsSubscriber.class,
        UserAntiSocialCheckerSqsSubscriber.class,
        AddressMakerSqsSubscriber.class,
        DepositMakerSqsSubscriber.class,
        ChainalysisAddressCheckerSqsSubscriber.class,
        WithdrawalMakerSqsSubscriber.class,
        WithdrawalStatusCheckerSqsSubscriber.class,
        ExcessiveDepositByOneTimeCheckerSqsSubscriber.class,
        ExcessiveDepositByPeriodCheckerSqsSubscriber.class,
        UneconomicalCryptoDepositCheckerSqsSubscriber.class,
        FrequentCryptoDepositCheckerSqsSubscriber.class,
        QuickCryptoToJpyCheckerSqsSubscriber.class,
        QuickCryptoTransferCheckerSqsSubscriber.class,
        MultiPrivateWalletWithdrawalCheckerSqsSubscriber.class,
        SygnaCustomerMakerSqsSubscriber.class,
        SygnaDepositTransferMakerSqsSubscriber.class,
        SygnaMailProtocolStatusCheckerSqsSubscriber.class,
        SygnaStatusCheckerSqsSubscriber.class,
        SygnaStatusUpdaterSqsSubscriber.class,
        SygnaTransferMakerSqsSubscriber.class,
        SygnaVASPMakerSqsSubscriber.class,
        StakingCalculatorSqsSubscriber.class,
        StakingMakerSqsSubscriber.class,
        StakingRewardMakerSqsSubscriber.class,
        StakingRewardRecordGetterSqsSubscriber.class,
        StakingRewardUpdaterSqsSubscriber.class,
        StakingStatusCheckerSqsSubscriber.class,
        StakingSummaryGetterSqsSubscriber.class,
        StakingFundMakerSqsSubscriber.class,
        StakingAutoRewardRecordGetterSqsSubscriber.class,
        OkcoinMarketDataSqsSubscriber.class,
        YearlyReportMakerSqsSubscriber.class,
        DowJonesSamCaseUpdaterSqsSubscriber.class,
        UserDailyAntiSocialCheckerSqsSubscriber.class,
        CryptoTokenApplyReportMakerSqsSubscriber.class,
        CryptoTokenApplyUpdaterMakerSqsSubscriber.class
        ),

    CandlestickMaker(CandlestickMakerSqsSubscriber.class);

    @SuppressWarnings("rawtypes")
    private final Class<? extends SqsSubscriber>[] clazzes;

    SubscriberGroup(@SuppressWarnings({"rawtypes"}) Class<? extends SqsSubscriber>... clazzes) {
      this.clazzes = clazzes;
    }
  }

  private static final CustomLogger log = new CustomLogger(Application.class.getName());
  private static final String LOG_GROUP = "application";

  private static String getSubscriberGroupName(String[] args) {
    for (String arg : args) {
      if (arg.startsWith("-")) {
        continue;
      }

      return arg;
    }

    return null;
  }

  @SuppressWarnings("rawtypes")
  private static void subscribeQueues(
      ApplicationContext applicationContext, String subscribeGroupName) {
    log.info(LOG_GROUP, "subscribeGroupName = " + subscribeGroupName);

    SubscriberGroup subscriberGroup =
        subscribeGroupName == null
            ? SubscriberGroup.ALL
            : SubscriberGroup.valueOf(subscribeGroupName);

    if (subscriberGroup == null) {
      log.severe(LOG_GROUP, "subscriberGroup is null");
      subscriberGroup = SubscriberGroup.ALL;
      //      return;
    }

    WorkerDaemon daemon = applicationContext.getBean(WorkerDaemon.class);

    for (Class<? extends SqsSubscriber> clazz : subscriberGroup.clazzes) {
      daemon.addSqsSubscriber(clazz);
    }
  }

  public static void main(String[] args) {
    ApplicationContext applicationContext = SpringApplication.run(Application.class, args);

    String subscribeGroupName = getSubscriberGroupName(args);

    subscribeQueues(applicationContext, subscribeGroupName);
  }
}
